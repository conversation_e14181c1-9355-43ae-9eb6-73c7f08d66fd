# 页面标题
VUE_APP_TITLE = develop通州管理系统

# 开发环境配置
ENV = 'development'

# 若依管理系统/开发环境
VUE_APP_BASE_API = '/prod-api'

# 路由懒加载
VUE_CLI_BABEL_TRANSPILE_MODULES = true

# Mock配置 - 是否启用Mock拦截器
VUE_APP_ENABLE_MOCK = false

# Mock配置 - 生产环境禁用Mock拦截器
VUE_APP_ENABLE_MOCK = false

# 现场服务部署
VUE_APP_TRAGET_IP = 'http://auth.qingruilin.com:65004'

# maxKB AI助手配置
VUE_APP_MAXKB_BASE_URL = 'http://************:65002'
VUE_APP_MAXKB_API_KEY = 'application-e47ffd96f3a22bae1b16bd3c05595335'
VUE_APP_MAXKB_APPLICATION_ID = '6c4eaa985d5c3672'
VUE_APP_MAXKB_ID = '9e4a8c50-6173-11f0-b28f-0242ac120003'

# 开发环境性能优化
# 禁用 source map 以提高构建速度
VUE_APP_GENERATE_SOURCEMAP = false

# 启用文件系统缓存
VUE_APP_CACHE_TYPE = filesystem

# 减少编译时的内存使用
VUE_APP_PARALLEL = false
