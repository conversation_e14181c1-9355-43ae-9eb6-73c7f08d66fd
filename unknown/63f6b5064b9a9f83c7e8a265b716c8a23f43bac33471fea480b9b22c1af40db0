<template>
  <el-dialog
    title="设置显示字段"
    :visible.sync="dialogVisible"
    width="30%"
    top="15vh"
    @close="handleClose"
  >
    <div class="field-settings-container">
      <el-checkbox-group v-model="selectedFields">
        <el-checkbox 
          v-for="field in availableFields" 
          :key="field.key"
          :label="field.key"
          class="field-checkbox"
        >
          {{ field.label }}
        </el-checkbox>
      </el-checkbox-group>
    </div>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { addDynamicTable } from '@/api/system/dynamicTable'

export default {
  name: 'FieldDisplaySettings',
  props: {
    // 控制弹窗显示
    visible: {
      type: Boolean,
      default: false
    },
    // 可用字段列表
    availableFields: {
      type: Array,
      default: () => []
    },
    // 当前已选中的字段
    currentFields: {
      type: Array,
      default: () => []
    },
    // 列表页面的接口地址
    apiUrl: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      selectedFields: []
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        // 弹窗打开时，初始化选中字段
        this.selectedFields = [...this.currentFields]
      }
    },
    currentFields: {
      handler(newVal) {
        this.selectedFields = [...newVal]
      },
      immediate: true
    }
  },
  methods: {
    // 处理确认
    async handleConfirm() {
      this.loading = true
      try {
        // 调用接口保存字段设置
        const params = {
          url: this.apiUrl,
          fields: this.selectedFields.join('|')
        }
        
        const response = await addDynamicTable(params)
        
        if (response.code === 200) {
          this.$message.success('字段设置保存成功')
          // 触发保存成功事件，传递选中的字段
          this.$emit('save-success', this.selectedFields)
          this.dialogVisible = false
        } else {
          this.$message.error(response.msg || '保存失败')
        }
      } catch (error) {
        console.error('保存字段设置失败:', error)
        this.$message.error('保存字段设置失败')
      } finally {
        this.loading = false
      }
    },
    
    // 处理取消
    handleCancel() {
      this.dialogVisible = false
    },
    
    // 处理弹窗关闭
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
.field-settings-container {
  .field-checkbox {
    display: block;
    margin-bottom: 15px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  

}

.dialog-footer {
  text-align: right;
}
::v-deep .el-dialog:not(.is-fullscreen) {
    margin-top: 15vh !important;
}
</style>
