var BMapGLLib=window.BMapGLLib=BMapGLLib||{},BMAP_DRAWING_MARKER="marker",BMAP_DRAWING_POLYLINE="polyline",BMAP_DRAWING_CIRCLE="circle",BMAP_DRAWING_RECTANGLE="rectangle",BMAP_DRAWING_POLYGON="polygon";!function(){var E={guid:"$BAIDU$"},t=(window[E.guid]={},E.extend=function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t},E.lang=E.lang||{},E.lang.guid=function(){return"TANGRAM__"+(window[E.guid]._counter++).toString(36)},window[E.guid]._counter=window[E.guid]._counter||1,window[E.guid]._instances=window[E.guid]._instances||{},E.lang.Class=function(t){this.guid=t||E.lang.guid(),window[E.guid]._instances[this.guid]=this},window[E.guid]._instances=window[E.guid]._instances||{},E.lang.isString=function(t){return"[object String]"==Object.prototype.toString.call(t)},E.lang.isFunction=function(t){return"[object Function]"==Object.prototype.toString.call(t)},E.lang.Class.prototype.toString=function(){return"[object "+(this._className||"Object")+"]"},E.lang.Class.prototype.dispose=function(){for(var t in delete window[E.guid]._instances[this.guid],this)E.lang.isFunction(this[t])||delete this[t];this.disposed=!0},E.lang.Event=function(t,e){this.type=t,this.returnValue=!0,this.target=e||null,this.currentTarget=null},E.lang.Class.prototype.addEventListener=function(t,e,n){if(E.lang.isFunction(e)){this.__listeners||(this.__listeners={});var i,o=this.__listeners;if("string"==typeof n&&n){if(/[^\w\-]/.test(n))throw"nonstandard key:"+n;i=e.hashCode=n}"object"!=typeof o[t=0!=t.indexOf("on")?"on"+t:t]&&(o[t]={}),i=i||E.lang.guid(),e.hashCode=i,o[t][i]=e}},E.lang.Class.prototype.removeEventListener=function(t,e){if(E.lang.isFunction(e))e=e.hashCode;else if(!E.lang.isString(e))return;this.__listeners||(this.__listeners={}),0!=t.indexOf("on")&&(t="on"+t);var n=this.__listeners;n[t]&&n[t][e]&&delete n[t][e]},E.lang.Class.prototype.dispatchEvent=function(t,e){for(var n in E.lang.isString(t)&&(t=new E.lang.Event(t)),this.__listeners||(this.__listeners={}),e=e||{})t[n]=e[n];var i=this.__listeners,o=t.type;if(t.target=t.target||this,t.currentTarget=this,0!=o.indexOf("on")&&(o="on"+o),E.lang.isFunction(this[o])&&this[o].apply(this,arguments),"object"==typeof i[o])for(n in i[o])i[o][n].apply(this,arguments);return t.returnValue},E.lang.inherits=function(t,e,n){var i,o,a=t.prototype,s=new Function;for(i in s.prototype=e.prototype,o=t.prototype=new s,a)o[i]=a[i];(t.prototype.constructor=t).superClass=e.prototype,"string"==typeof n&&(o._className=n)},E.dom=E.dom||{},E._g=E.dom._g=function(t){return E.lang.isString(t)?document.getElementById(t):t},E.g=E.dom.g=function(t){return"string"==typeof t||t instanceof String?document.getElementById(t):t&&t.nodeName&&(1==t.nodeType||9==t.nodeType)?t:null},E.insertHTML=E.dom.insertHTML=function(t,e,n){var i;return(t=E.dom.g(t)).insertAdjacentHTML?t.insertAdjacentHTML(e,n):(i=t.ownerDocument.createRange(),"AFTERBEGIN"==(e=e.toUpperCase())||"BEFOREEND"==e?(i.selectNodeContents(t),i.collapse("AFTERBEGIN"==e)):(i[(e="BEFOREBEGIN"==e)?"setStartBefore":"setEndAfter"](t),i.collapse(e)),i.insertNode(i.createContextualFragment(n))),t},E.ac=E.dom.addClass=function(t,e){t=E.dom.g(t);for(var n=e.split(/\s+/),i=t.className,o=" "+i+" ",a=0,s=n.length;a<s;a++)o.indexOf(" "+n[a]+" ")<0&&(i+=(i?" ":"")+n[a]);return t.className=i,t},E.event=E.event||{},E.event._listeners=E.event._listeners||[],E.on=E.event.on=function(e,t,n){t=t.replace(/^on/i,""),e=E._g(e);var i=function(t){n.call(e,t)},o=E.event._listeners,a=E.event._eventFilter,s=t;return t=t.toLowerCase(),a&&a[t]&&(s=(a=a[t](e,t,i)).type,i=a.listener),e.addEventListener?e.addEventListener(s,i,!1):e.attachEvent&&e.attachEvent("on"+s,i),o[o.length]=[e,t,n,i,s],e},E.un=E.event.un=function(t,e,n){t=E._g(t),e=e.replace(/^on/i,"").toLowerCase();for(var i,o,a=E.event._listeners,s=a.length,r=!n;s--;)(o=a[s])[1]!==e||o[0]!==t||!r&&o[2]!==n||(i=o[4],o=o[3],t.removeEventListener?t.removeEventListener(i,o,!1):t.detachEvent&&t.detachEvent("on"+i,o),a.splice(s,1));return t},E.getEvent=E.event.getEvent=function(t){return window.event||t},E.getTarget=E.event.getTarget=function(t){return(t=E.getEvent(t)).target||t.srcElement},E.preventDefault=E.event.preventDefault=function(t){(t=E.getEvent(t)).preventDefault?t.preventDefault():t.returnValue=!1},E.stopBubble=E.event.stopBubble=function(t){(t=E.getEvent(t)).stopPropagation?t.stopPropagation():t.cancelBubble=!0},E.browser=E.browser||{},/msie (\d+\.\d)/i.test(navigator.userAgent)&&(E.browser.ie=E.ie=document.documentMode||+RegExp.$1),BMapGLLib.DrawingManager=function(t,e){t&&(i.push(this),e=e||{},this.overlays=[],this._initialize(t,e))}),A=(E.lang.inherits(t,E.lang.Class,"DrawingManager"),t.prototype.open=function(){if(1==this._isOpen)return!0;o(this),this._open()},t.prototype.close=function(){if(0==this._isOpen)return!0;var t=this;this._close(),t._map.removeOverlay(A),setTimeout(function(){t._map.enableDoubleClickZoom()},2e3)},t.prototype.setDrawingMode=function(t){this._drawingType!=t&&(o(this),this._setDrawingMode(t))},t.prototype.getDrawingMode=function(){return this._drawingType},t.prototype.enableCalculate=function(){this._enableCalculate=!0,this._addGeoUtilsLibrary()},t.prototype.disableCalculate=function(){this._enableCalculate=!1},t.prototype.enableSorption=function(){this._enableSorption=!0},t.prototype.disableSorption=function(){this._enableSorption=!1},t.prototype.enableGpc=function(){this._enableGpc=!0,this._addGPCLibrary()},t.prototype.disableGpc=function(){this._enableGpc=!1},t.prototype.getOverlays=function(){return this.overlays},t.prototype.addOverlayData=function(t){return this.overlays.push(t)},t.prototype.setOverlaysData=function(t){return this.overlays=t},t.prototype.clearOverlayData=function(t){this._map;for(var e=0;e<this.overlays.length;e++)if(this.overlays[e]===t)return this.overlays.splice(e,1),t},t.prototype.clearOverlay=function(t){var e=this._map;(t=this.clearOverlayData(t))&&e.removeOverlay(t)},t.prototype.clearOverlays=function(){var e=this._map;this.overlays.forEach(function(t){e.removeOverlay(t)}),this.overlays.length=0},t.prototype.setOverlayEdit=function(t,e){["circle","rectangle","polygon","polyline"].includes(t)?(this.clearOverlay(e),this._map.removeOverlay(e),this._drawingType=t,this._open(!0,e)):console.error("暂不支持的编辑类型",t)},t.prototype._initialize=function(t,e){this._map=t,this._opts=e,this._opts.confirmVisible=!1!==e.confirmVisible,this._opts.autoViewport=!1!==e.autoViewport,this._drawingType=e.drawingMode||BMAP_DRAWING_MARKER,e.enableDrawingTool&&this.enableDrawingTool(),void 0!==e.sorptionDistance&&this.setSorptionDistance(e.sorptionDistance),e.sorpOverlays&&this.setSorpOverlays(e.sorpOverlays),!0===e.enableCalculate?this.enableCalculate():this.disableCalculate(),!0===e.enableLimit&&(t=e.limitOptions,this.limit=t),!0===e.enableSorption?this.enableSorption():this.disableSorption(),!0===e.enableGpc?this.enableGpc():this.disableGpc(),this._isOpen=!(!0!==e.isOpen),this._isOpen&&this._open(),this.setPolygonOptions(e.polygonOptions),this.setMarkerOptions(e.markerOptions),this.setCircleOptions(e.circleOptions),this.setPolylineOptions(e.polylineOptions),this.setRectangleOptions(e.rectangleOptions),this.setLabelOptions(e.labelOptions),this.controlButton="right"==e.controlButton?"right":"left",this.skipEditing=void 0!==e.skipEditing&&e.skipEditing},t.prototype.enableDrawingTool=function(){var t=this._opts;this._drawingTool||(t=new e(this,t.drawingToolOptions),this._drawingTool=t),this._map.addControl(this._drawingTool)},t.prototype.disableDrawingTool=function(){this._drawingTool&&this._map.removeControl(this._drawingTool)},t.prototype.setSorptionDistance=function(t){this._sorptionDistance=t||0},t.prototype.setSorpOverlays=function(t){this._sorpOverlays=t||[]},t.prototype.setPolygonOptions=function(t){this.polygonOptions=t||{}},t.prototype.setMarkerOptions=function(t){this.markerOptions=t||{}},t.prototype.setCircleOptions=function(t){this.circleOptions=t||{}},t.prototype.setPolylineOptions=function(t){this.polylineOptions=t||{}},t.prototype.setRectangleOptions=function(t){this.rectangleOptions=t||{}},t.prototype.setLabelOptions=function(t){this.labelOptions=t||{}},t.prototype._open=function(t,e){this._isOpen=!0,this._mask||(this._mask=new n),this._map.addOverlay(this._mask),this._setDrawingMode(this._drawingType,t,e)},t.prototype._setDrawingMode=function(t,e,n){if(this._drawingType=t,this._isOpen)switch(this._mask.__listeners={},t){case BMAP_DRAWING_MARKER:this._bindMarker();break;case BMAP_DRAWING_CIRCLE:this._bindCircle(e,n);break;case BMAP_DRAWING_POLYLINE:case BMAP_DRAWING_POLYGON:this._bindPolylineOrPolygon(e,n);break;case BMAP_DRAWING_RECTANGLE:this._bindRectangle(e,n)}this._drawingTool&&this._isOpen&&this._drawingTool.setStyleByDrawingMode(t)},t.prototype._close=function(){this._isOpen=!1,this._mask&&this._map.removeOverlay(this._mask),this._drawingTool&&this._drawingTool.setStyleByDrawingMode("hander")},t.prototype._skipEditing=function(){var t=this[this._drawingType+"Options"],e=this.skipEditing,n=null;((n=t?void 0===t.skipEditing?n:t.skipEditing:n)||e)&&!1!==n&&document.getElementById("confirmOperate").click()},t.prototype._bindMarker=function(){var e=this,n=this._map;this._mask.addEventListener("click",function(t){t=new BMapGL.Marker(t.point,e.markerOptions);n.addOverlay(t),e.addOverlayData(t),e._dispatchOverlayComplete(t)})},null);function B(t,e){this.limit=t.limit,this.type=t.type,this.point=t.point,this.overlay=t.overlay,this.overlays=t.overlays,this.DrawingManager=e}function O(t,e,n,i,o){this.type=t,this.point=e,this.number=n,this.overlay=i,this.DrawingManager=o}function n(){this._enableEdgeMove=!1}function e(t,e){this.drawingManager=t,e=this.drawingToolOptions=e||{},this._opts={},this.defaultAnchor=BMAP_ANCHOR_TOP_LEFT,this.defaultOffset=new BMapGL.Size(10,10),this.defaultDrawingModes=[BMAP_DRAWING_MARKER,BMAP_DRAWING_CIRCLE,BMAP_DRAWING_POLYLINE,BMAP_DRAWING_POLYGON,BMAP_DRAWING_RECTANGLE],e.drawingModes?this.drawingModes=e.drawingModes:this.drawingModes=this.defaultDrawingModes,e.hasCustomStyle&&(e.anchor&&this.setAnchor(e.anchor),e.offset)&&this.setOffset(e.offset)}t.prototype._bindCircle=function(t,e){var s=this,r=this._map,l=this._mask,p=null,c=[],d=null,h=1,g=null,u=null,v=null,y=null,m={strokeColor:"#4E6DF1",strokeWeight:2},n=new BMapGL.Icon("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAs1JREFUWAnNmb9v01AQx989SzVI8dIBJNZ27Mb/QRpExMiOKAW1Mw3MRVCB2BlRECF/CBtjuwbBwGJL1JXs474vtuU4Tkho4tyTGr9ffvfxvV93VzL/mfYPLndSumqT4buG6Y4MI3+MpyQayc/IEI/Y0DfLW8Ov725cuKYlf2iZ/p3j8FZylT4hQx1m3lvmXSL6zoYH3pZ9PzgNfi367kKA3R634t/REXF6zMa0Fh28rp8IjJjsqb/det3vUVTXp1z3T8DOs/B+kvIHw3y7/OK180Q/PUuPB2+DL/PGsrMaZQrp3tPwJEn488rhIFQ+GGNDBmTN4qht6D7nm3ESfpRBurNeXGk9Ud/3gkf9N/SnOu6UBvE1jcKBSBQBmXWanAJsH0YvGtNcWV0C6WSX6yQ/McVuQ2DNycFW6ddQkdjz6EF54xQg46MkPMfibYimXozsbn872M2PoGKKcc5tHA7IoiDHkuE7DeKGSOP04rqHcL1Klq8VqMj6dgc3jtMgx+mBFjh8DlhwpSI/BiTaR0FTwn0PHnJWiYnPNcHlLNb4uxYmU16h7Qk26+w5bWQZD9hsZmzqRBRDGJsks4JVMgIwN9M1ArIAykRrRHNMwoY1+EMtoLBhDcID05pGMsWsF1DYLPxWrepzPjWcaq2AYLPw+OFUa4MEE9jG1ox4/NoAEYUAkwNEOAJGohZIsICpAITlinCEFkCw5PEbp0GAIVZixGHZOKRzmoQlSwUgvCjESsSG3eDVJ26nMOQeHRgLQBScP0r0EvmNJJFd9onBIOtxMiH80D4MPzUeXZD4zPAseCjHy8QMTmgQqOiAQI5k+pPoayxlwaMqnOOZJXasSRenOVlfKES0JdM6PGu9qoObC5iDqw1g5oBYtIiVGLK9VRzmbgwZC2NWN0Qus/yc2iTlxmoeIRIXhRBHX5bAXrV9XlmmcH1B9DrBTf0b4i99lUEMOuku/wAAAABJRU5ErkJggg==",new BMapGL.Size(20,20)),f=new BMapGL.Icon("data:image/png;base64,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",new BMapGL.Size(40,20),{imageOffset:new BMapGL.Size(0,10)}),i=function(t){("right"!=s.controlButton||1!=t.button&&0!=t.button)&&(d=t.point,t=new BMapGL.Marker(d),n.setImageSize(new BMapGL.Size(20,20)),t.setIcon(n),t.enableDragging(),t.addEventListener("dragstart",o),t.addEventListener("dragging",a),t.addEventListener("dragend",b),r.addOverlay(t),c.push(t),p=new BMapGL.Circle(d,h,s.circleOptions),r.addOverlay(p),l.enableEdgeMove(),l.addEventListener("mousemove",w),E.on(document,"mouseup",M))},w=function(t){h=s._map.getDistance(d,t.point).toFixed(0),p.setRadius(h),r.removeOverlay(A),(A=new BMapGL.Label("半径："+h+"米<br>松开完成绘制",{position:t.point,offset:new BMapGL.Size(10,10)})).setStyle(s.labelOptions),r.addOverlay(A)},M=function(t){var e=r.getViewport(p.getBounds()),e=(--e.zoom,s._opts.autoViewport&&r.setViewport(e),r.removeOverlay(A),new BMapGL.Point(p.getBounds().getNorthEast().lng,d.lat)),n=(l.hide(),(g=new BMapGL.Marker(e)).setIcon(f),g.enableDragging(),u=new BMapGL.Polyline([d,e],m),new BMapGL.Point((p.getBounds().getNorthEast().lng+d.lng)/2,d.lat)),n=(v=new O("circle",n,h,p,s),c=c.concat([g,u,v]),null),n={limit:n=s.limit?s.limit.area:n,type:"circle",point:e,overlay:p,overlays:c},o=s._opts.confirmVisible;if(o&&(y=new B(n,s),r.addOverlay(y)),r.addOverlay(g),r.addOverlay(u),r.addOverlay(v),s._skipEditing(),v.addEventListener("radiuschange",function(t){var t=t.radius,e=(p.setRadius(t),P(d,t,"east")),n=new BMapGL.Point(e.lng,d.lat),e=e.lng>d.lng?(p.getBounds().getNorthEast().lng+d.lng)/2:(p.getBounds().getSouthWest().lng+d.lng)/2,e=new BMapGL.Point(e,d.lat);g.setPosition(n),v.setInfo(e,t),o&&(y.setPosition(n,!0),y.updateWindow()),u.setPath([d,n])}),g.addEventListener("dragging",function(t){var e=new BMapGL.Point(t.latLng.lng,d.lat),n=t.latLng.lng>d.lng?(p.getBounds().getNorthEast().lng+d.lng)/2:(p.getBounds().getSouthWest().lng+d.lng)/2,i=t.latLng.lng>d.lng,n=new BMapGL.Point(n,d.lat);t.target.setPosition(e),v.setInfo(n,s._map.getDistance(d,t.latLng).toFixed(0)),o&&y.setPosition(e,i),u.setPath([d,e]),h=s._map.getDistance(d,t.latLng).toFixed(0),p.setRadius(s._map.getDistance(d,t.latLng))}),g.addEventListener("dragend",function(t){o&&y.updateWindow()}),l.disableEdgeMove(),l.removeEventListener("mousemove",w),l.removeEventListener("mousemove",_),E.un(document,"mouseup",M),r.removeOverlay(l),!o){e=s._calculate(p);s.overlays.push(p),s._dispatchOverlayComplete(p,e);for(var i=0;i<c.length;i++)if(Array.isArray(c[i]))for(var a in c[i])r.removeOverlay(c[i][a]);else r.removeOverlay(c[i]);s.close()}},_=function(t){E.preventDefault(t),E.stopBubble(t),"right"==s.controlButton&&1==t.button||null==d&&i(t)},o=function(t){r.removeOverlay(g),r.removeOverlay(u),r.removeOverlay(v),r.removeOverlay(y)},a=function(t){d=t.latLng,p.setCenter(t.latLng)},b=function(t){d=t.latLng,M(t)};l.addEventListener("mousedown",_),l.addEventListener("mousemove",function(t){E.preventDefault(t),E.stopBubble(t),r.removeOverlay(A),(A=new BMapGL.Label("按下确认中心点，拖拽确认半径",{position:t.point,offset:new BMapGL.Size(10,10)})).setStyle(s.labelOptions),r.addOverlay(A)}),t&&(t=e.getCenter(),e=new BMapGL.Point(e.getBounds().getNorthEast().lng,t.lat),i({point:t}),w({point:e}),M({}))},t.prototype._bindPolylineOrPolygon=function(t,e){var d=this,h=this._map,g=this._mask,u=[],v=null,y=null,i=null,m=!1;function f(t){for(var e=t,n=0,i=0,o=0;o<e.length;o++)n<e[o].lng&&(n=e[o].lng,i=o);return e[i]}var w=function(t){("right"!==d.controlButton||1!==t.button&&0!==t.button)&&(t=t.point,i&&(t=i),u.push(t),v=u.concat(u[u.length-1]),1==u.length?(d._drawingType==BMAP_DRAWING_POLYLINE?y=new BMapGL.Polyline(v,d.polylineOptions):d._drawingType==BMAP_DRAWING_POLYGON&&(y=new BMapGL.Polygon(v,d.polygonOptions)),h.addOverlay(y)):y.setPath(v),m||(m=!0,g.enableEdgeMove(),g.removeEventListener("mousemove",b),g.addEventListener("mousemove",M),g.addEventListener("dblclick",_)))},M=function(t){var e=t.point;if(d._enableSorption){var n=d.getSorptionMatch(e,d.overlays,d._sorptionDistance);if(n&&0<n.length)return i=n[0].point,void y.setPositionAt(v.length-1,n[0].point);if(d._sorpOverlays){n=d.getSorptionMatch(e,d._sorpOverlays,d._sorptionDistance);if(n&&0<n.length)return i=n[0].point,void y.setPositionAt(v.length-1,n[0].point)}}i=null,y.setPositionAt(v.length-1,t.point),h.removeOverlay(A),(A=new BMapGL.Label("单击绘制下一个点，双击完成绘制",{position:t.point,offset:new BMapGL.Size(10,10)})).setStyle(d.labelOptions),h.addOverlay(A)},_=function(t){E.stopBubble(t),m=!1,h.removeOverlay(A),g.disableEdgeMove(),g.removeEventListener("mousedown",w),g.removeEventListener("mousemove",M),g.removeEventListener("mousemove",b),g.removeEventListener("dblclick",_),"right"==d.controlButton?u.push(t.point):E.ie<=8||u.pop();try{if(d._enableGpc&&window.gpcas&&"polygon"===d._drawingType){for(var e=new gpcas.geometry.PolyDefault,n=0;n<u.length;n++)e.addPoint(new gpcas.Point(u[n].lng,u[n].lat));for(var i=0;i<d.overlays.length;i++){for(var o=d.overlays[i].getPath(),a=new gpcas.geometry.PolyDefault,n=0;n<o.length;n++)a.addPoint(new gpcas.Point(o[n].lng,o[n].lat));for(var s=e.difference(a).getPoints(),r=[],n=0;n<s.length;n++)r.push(new BMapGL.Point(s[n].x,s[n].y));for(e=new gpcas.geometry.PolyDefault,n=0;n<s.length;n++)e.addPoint(new gpcas.Point(s[n].x,s[n].y));u=r}}}catch(t){}y.setPath(u);var l,p=h.getViewport(u),p=(--p.zoom,d._opts.autoViewport&&h.setViewport(p),y.enableEditing(),null),p={limit:p=d.limit?"polygon"===d._drawingType?d.limit.area:d.limit.distance:p,type:d._drawingType,point:f(u),overlay:y,overlays:[]},c=d._opts.confirmVisible;c&&(l=new B(p,d),h.addOverlay(l)),d._skipEditing(),y.addEventListener("lineupdate",function(t){t=f(t.currentTarget.getPath());c&&(l.setPosition(t,!0),l.updateWindow())}),u.length=0,v.length=0,h.removeOverlay(g),c||(p=d._calculate(y),d.overlays.push(y),d._dispatchOverlayComplete(y,p),y.disableEditing(),d.close())},b=function(t){E.preventDefault(t),E.stopBubble(t),h.removeOverlay(A),(A=new BMapGL.Label("单击确认起点",{position:t.point,offset:new BMapGL.Size(10,10)})).setStyle(d.labelOptions),h.addOverlay(A)};if(g.addEventListener("mousemove",b),g.addEventListener("mousedown",w),g.addEventListener("dblclick",function(t){E.stopBubble(t)}),t){for(var n=e.getPath(),t=n[n.length-1],o=0;o<n.length;o++)w({point:n[o]});"polyline"===d._drawingType&&w({point:t}),_({point:t})}},t.prototype._bindRectangle=function(t,e){var u=this,v=this._map,y=this._mask,m=null,f=null;function w(t,e){var n=new BMapGL.Point(t.lng,t.lat),i=new BMapGL.Point(e.lng,t.lat),o=new BMapGL.Point(e.lng,e.lat),a=new BMapGL.Point(t.lng,e.lat);return[n,new BMapGL.Point((t.lng+e.lng)/2,t.lat),i,new BMapGL.Point(e.lng,(t.lat+e.lat)/2),o,new BMapGL.Point((t.lng+e.lng)/2,e.lat),a,new BMapGL.Point(t.lng,(t.lat+e.lat)/2)]}function n(t){E.stopBubble(t),E.preventDefault(t),("right"!=u.controlButton||1!=t.button&&0!=t.button)&&(t=f=t.point,m=new BMapGL.Polygon(u._getRectanglePoint(f,t),u.rectangleOptions),v.addOverlay(m),y.enableEdgeMove(),y.addEventListener("mousemove",_),E.on(document,"mouseup",b))}var M=new BMapGL.Icon("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAd9JREFUOBGtVc8rRFEUPufOvELMguTHwkbKjxRlo9gqNpjyFyhTit3I8i1ldpQa5S9QmA1lS9koSn6UZmMxSCyGkPlx3O+9mTe8Zl4zw7eZ7rnn++aee893HlMRzEXFeLh6m2LJTpHwEJG022mcIJZTYbXX2lu/txnilJvO7sDkYnJaC0REqNO993PNTHEiDsfWAru/4vmFaYo6e35dEZIwYh1tPhobNmig20/NjcpKe3rJ0vlNmg5PUnR3n7FiTBwZbGpYNk3OIuCccHIhuQoxv49lNljD4yMG6VMUhT49HRynaGvnU9IZ0Wkcia0HlhxBlCkiOxAz5+u4v8tXVMgdvLjNkLnxbosyB1E+4wEeL5PXuLPQTC1NjBpunud6/yhF0e0PVBNv6Qv0KLwmxHBnKLNSgAMuNKClrNbQKniAUnfm9SfggAtAS9l9RtZrehG99tAJFnTP6n6wmzbfGl7EUnsFrrTbDVYqs4q4FtR20kDTVosClxMK3oQQHFAtHK7WUjA6hGAnOKBSgAMuAC2FqYGmhDdhp0oBDrjQgJayRxBbAwHehJ3KBXLBsfM5DC3H/v81HHIdSYQRpMcX6ekRhjcPjr/KGV+6Unt8xXJlOSfMl/lvAzYviN+/fAK+AW5jAVefzjWGAAAAAElFTkSuQmCC",new BMapGL.Size(10,10)),_=(M.setImageSize(new BMapGL.Size(10,10)),function(t){v.removeOverlay(A),m.setPath(u._getRectanglePoint(f,t.point));var e=w(f,t.point),n=u._map.getDistance(f,e[2]).toFixed(0),e=u._map.getDistance(f,e[6]).toFixed(0);(A=new BMapGL.Label("尺寸："+n+"米 x "+e+"米<br>松开结束绘制",{position:t.point,offset:new BMapGL.Size(10,10)})).setStyle(u.labelOptions),v.addOverlay(A)}),b=function(t){y.hide();for(var s,i=null,r=[],l=w(f,t.point),p=[],t=v.getViewport(l),c=u._opts.confirmVisible,o=(--t.zoom,u._opts.autoViewport&&v.setViewport(t),v.removeOverlay(A),u._map.getDistance(f,l[2]).toFixed(0)),a=u._map.getDistance(f,l[6]).toFixed(0),d=new O("rectangle",l[0],{width:o,height:a},m,u),e=0;e<l.length;e++){var n=new BMapGL.Marker(l[e]);n.setIcon(M),n.enableDragging(),r.push(n),v.addOverlay(n),p[e]=u.mc2ll(n.point),n.addEventListener("mousedown",function(t){i=u.mc2ll(t.target.point)}),n.addEventListener("dragging",function(t){for(var e=t.latLng,n=0;n<p.length;n++)i.lng==p[n].lng&&(l[n].lng=e.lng),i.lat==p[n].lat&&(l[n].lat=e.lat);l=w(l[0],l[4]);for(n=0;n<r.length;n++)r[n].setPosition(l[n]);o=u._map.getDistance(l[0],l[2]).toFixed(0),a=u._map.getDistance(l[0],l[6]).toFixed(0),d.setInfo(l[0],{width:o,height:a}),c&&s.setPosition(l[3],!0),m.setPath(l)}),n.addEventListener("dragend",function(t){for(var e=0;e<r.length;e++)p[e]=u.mc2ll(r[e].point);c&&s.updateWindow()})}d.addEventListener("rectwhchange",function(t){var e=t.width,t=t.height,n=P(l[0],e,"east"),i=P(l[0],t,"south");l[4].lng=n.lng,l[4].lat=i.lat,l=w(l[0],l[4]);for(var o=0;o<r.length;o++)r[o].setPosition(l[o]);d.setInfo(l[0],{width:e,height:t}),c&&s.setPosition(l[3],!0),m.setPath(l);for(var a=0;a<r.length;a++)p[a]=u.mc2ll(r[a].point);c&&s.updateWindow()});var h=[r,d],t=null,t={limit:t=u.limit?u.limit.area:t,type:"rectangle",point:l[3],overlay:m,overlays:h};if(c&&(s=new B(t,u),v.addOverlay(s)),v.addOverlay(d),y.disableEdgeMove(),y.removeEventListener("mousemove",_),y.removeEventListener("mousemove",L),u._skipEditing(),E.un(document,"mouseup",b),v.removeOverlay(y),!c){t=u._calculate(m);u.overlays.push(m),u._dispatchOverlayComplete(m,t);for(e=0;e<h.length;e++)if(Array.isArray(h[e]))for(var g in h[e])v.removeOverlay(h[e][g]);else v.removeOverlay(h[e]);u.close()}},L=function(t){E.preventDefault(t),E.stopBubble(t),v.removeOverlay(A),(A=new BMapGL.Label("按住确认起点，拖拽进行绘制",{position:t.point,offset:new BMapGL.Size(10,10)})).setStyle(u.labelOptions),v.addOverlay(A)};y.addEventListener("mousedown",n),y.addEventListener("mousemove",L),t&&(e=(t=e.getBounds()).getSouthWest(),t=t.getNorthEast(),n({point:e}),_({point:t}),b({point:t}))},t.prototype._calculate=function(t,e){var n={data:0,label:null};if(this._enableCalculate&&BMapGLLib.GeoUtils){switch(t.toString()){case"Polyline":n.data=BMapGLLib.GeoUtils.getPolylineDistance(t);break;case"Polygon":n.data=BMapGLLib.GeoUtils.getPolygonArea(t);break;case"Circle":var i=t.getRadius();n.data=Math.PI*i*i}n.data=!n.data||n.data<0?0:n.data.toFixed(2)}return n},t.prototype._addGeoUtilsLibrary=function(){var t;BMapGLLib.GeoUtils||((t=document.createElement("script")).setAttribute("type","text/javascript"),t.setAttribute("src","//mapopen.cdn.bcebos.com/github/BMapGLLib/GeoUtils/src/GeoUtils.min.js"),document.body.appendChild(t))},t.prototype._addGPCLibrary=function(){var t;window.gpcas||((t=document.createElement("script")).setAttribute("type","text/javascript"),t.setAttribute("src","//mapopen.cdn.bcebos.com/github/BMapGLLib/DrawingManager/src/gpc.js"),document.body.appendChild(t))},t.prototype._addLabel=function(t,e){e=new BMapGL.Label(e,{position:t});return this._map.addOverlay(e),e},t.prototype._getRectanglePoint=function(t,e){return[new BMapGL.Point(t.lng,t.lat),new BMapGL.Point(e.lng,t.lat),new BMapGL.Point(e.lng,e.lat),new BMapGL.Point(t.lng,e.lat)]},t.prototype._dispatchOverlayComplete=function(t,e){var n={overlay:t,drawingMode:this._drawingType};e&&(n.calculate=e.data||null,n.label=e.label||null),this.dispatchEvent(this._drawingType+"complete",t),this.dispatchEvent("overlaycomplete",n)},t.prototype._dispatchOverlayCancel=function(t){var e={overlay:t,drawingMode:this._drawingType};this.dispatchEvent(this._drawingType+"cancel",t),this.dispatchEvent("overlaycancel",e)},t.prototype.getSorptionMatch=function(t,e,n){n=n||20;for(var i=this._map,o=i.pointToPixel(t),a=[],s=0;s<e.length;s++){var r=e[s].getPath(),l=r[0],p=r[r.length-1];l.equals(p)||r.push(r[0]);for(var c=1;c<r.length;c++){var d=i.pointToPixel(r[c-1]),h=i.pointToPixel(r[c]),g=[o.x-d.x,o.y-d.y],u=[h.x-d.x,h.y-d.y],h=[h.x-o.x,h.y-o.y],v=g[0]*u[0]+g[1]*u[1],y=Math.sqrt(Math.pow(g[0],2)+Math.pow(g[1],2))*Math.sqrt(Math.pow(u[0],2)+Math.pow(u[1],2)),v=Math.acos(v/y),y=u[0]*h[0]+u[1]*h[1],h=Math.sqrt(Math.pow(u[0],2)+Math.pow(u[1],2))*Math.sqrt(Math.pow(h[0],2)+Math.pow(h[1],2)),y=Math.acos(y/h);v<Math.PI/2&&y<Math.PI/2&&(h=Math.sqrt(Math.pow(g[0],2)+Math.pow(g[1],2)),y=Math.sqrt(Math.pow(u[0],2)+Math.pow(u[1],2)),g=Math.cos(v)*h/y,y=Math.sin(v)*h,v=[d.x+u[0]*g,d.y+u[1]*g],y<n)&&a.push({point:i.pixelToPoint({x:v[0],y:v[1]}),length:y})}}return a.sort(function(t,e){return t.length-e.length}),0<a.length?a:null},t.prototype.mc2ll=function(t){t=this._map.mercatorToLnglat(t.lng,t.lat);return new BMapGL.Point(t[0],t[1])},t.prototype.ll2mc=function(t){t=this._map.lnglatToMercator(t.lng,t.lat);return new BMapGL.Point(t[0],t[1])},(B.prototype=new BMapGL.Overlay).dispatchEvent=E.lang.Class.prototype.dispatchEvent,B.prototype.addEventListener=E.lang.Class.prototype.addEventListener,B.prototype.removeEventListener=E.lang.Class.prototype.removeEventListener,B.prototype.initialize=function(t){this._map=t;var t="polyline"===this.type?"长度":"面积",e="polyline"===this.type?"万米":"万平方米",n=this.div=document.createElement("div"),t=(n.className="operateWindow",'<div><span id="confirmOperate"></span><span id="cancelOperate"></span><span id="warnOperate">'+t+"不超过"+this.limit/1e4+e+"！</span></div>");return n.innerHTML=t,this._map.getPanes().markerPane.appendChild(n),this.updateWindow(),this._bind(),n},B.prototype._bind=function(){var o=this,a=this._map,s=this.overlay,r=this.overlays;document.getElementById("confirmOperate").addEventListener("click",function(t){var e;a.removeOverlay(o),"rectangle"==o.type?(e=o.DrawingManager._calculate(s,s.getPath()),o.DrawingManager.overlays.push(s)):"circle"==o.type?(e=o.DrawingManager._calculate(s,o.point),o.DrawingManager.overlays.push(s)):"polygon"!=o.type&&"polyline"!=o.type||(e=o.DrawingManager._calculate(s,s.getPath()),o.DrawingManager.overlays.push(s),s.disableEditing()),o.DrawingManager._dispatchOverlayComplete(s,e);for(var n=0;n<r.length;n++)if(Array.isArray(r[n]))for(var i in r[n])a.removeOverlay(r[n][i]);else a.removeOverlay(r[n]);o.DrawingManager.close()}),document.getElementById("cancelOperate").addEventListener("click",function(t){a.removeOverlay(o);for(var e=0;e<r.length;e++)if(Array.isArray(r[e]))for(var n in r[e])a.removeOverlay(r[e][n]);else a.removeOverlay(r[e]);a.removeOverlay(s),o.DrawingManager._dispatchOverlayCancel(s),o.DrawingManager.close()})},B.prototype.updateWindow=function(){var t,e,n;null!==this.domElement&&(t=this.overlay,e=this.limit,"rectangle"==this.type?n=this.DrawingManager._calculate(t,t.getPath()):"circle"==this.type?n=this.DrawingManager._calculate(t,this.point):"polygon"!=this.type&&"polyline"!=this.type||(n=this.DrawingManager._calculate(t,t.getPath())),"[object Number]"===Object.prototype.toString.call(e)&&n.data>e?(document.getElementById("confirmOperate").style.display="none",document.getElementById("warnOperate").style.display="block"):(document.getElementById("confirmOperate").style.display="block",document.getElementById("warnOperate").style.display="none"))},B.prototype.setPosition=function(t,e){this.point=t;t=this._map.pointToOverlayPixel(this.point);e?(this.div.classList.remove("operateLeft"),this.div.style.left=t.x+15+"px"):(this.div.classList.add("operateLeft"),this.div.style.left=t.x-105+"px"),this.div.style.top=t.y-16+"px"},B.prototype.draw=function(){var t=this._map.pointToOverlayPixel(this.point);this.div.style.left=t.x+15+"px",this.div.style.top=t.y-16+"px"},(O.prototype=new BMapGL.Overlay).dispatchEvent=E.lang.Class.prototype.dispatchEvent,O.prototype.addEventListener=E.lang.Class.prototype.addEventListener,O.prototype.removeEventListener=E.lang.Class.prototype.removeEventListener,O.prototype.initialize=function(t){this._map=t;var e,t=this.div=document.createElement("div");return t.className="screenshot","circle"==this.type?e='<div class="circlShot"><span id="screenshotNum">'+this.number+'</span><input id="circleInput" type="text" /><span class="unit">米</span></div>':"rectangle"==this.type&&(e='<div class="rectWH"><div class="wh"><span id="rectWidth">'+this.number.width+'</span><input id="rectWidthInput" type="text" /></div><span class="multiple">x</span><div class="wh"><span id="rectHeight">'+this.number.height+'</span><input id="rectHeightInput" type="text" /></div><span class="unit">米</span></div>'),t.innerHTML=e,this._map.getPanes().markerPane.appendChild(t),this._bind(),t},O.prototype._bind=function(){this.setNumber(this.number),"circle"==this.type?this.bindCircleEvent():this.bindRectEvent()},O.prototype.bindCircleEvent=function(){var n=this,i=document.getElementById("screenshotNum"),o=document.getElementById("circleInput");i.addEventListener("click",function(t){var e=i.innerText;i.style.display="none",o.value=e,o.style.display="inline-block",o.focus()}),o.addEventListener("click",function(t){o.focus()}),o.addEventListener("keydown",function(t){13===t.keyCode&&(t=o.value,o.style.display="none",i.style.display="inline-block",t={radius:i.innerText=t,overlay:n.overlay},n._dispatchRadiusChange(t))}),o.addEventListener("blur",function(t){var e=o.value,e=(o.style.display="none",i.style.display="inline-block",{radius:i.innerText=e,overlay:n.overlay});n._dispatchRadiusChange(e)})},O.prototype.bindRectEvent=function(){var n=this,i=document.getElementById("rectWidth"),o=document.getElementById("rectWidthInput"),a=document.getElementById("rectHeight"),s=document.getElementById("rectHeightInput");o.value=i.innerText,s.value=a.innerText,i.addEventListener("click",function(t){var e=i.innerText;i.style.display="none",o.value=e,o.style.display="inline-block",o.focus()}),a.addEventListener("click",function(t){var e=a.innerText;a.style.display="none",s.value=e,s.style.display="inline-block",s.focus()}),o.addEventListener("click",function(t){o.focus()}),s.addEventListener("click",function(t){s.focus()}),o.addEventListener("keydown",function(t){var e;13===t.keyCode&&(t=o.value,e=s.value,o.style.display="none",s.style.display="none",i.style.display="inline-block",a.style.display="inline-block",t={width:i.innerText=t,height:a.innerText=e,overlay:n.overlay},n._dispatchRectWHChange(t))}),s.addEventListener("keydown",function(t){var e;13===t.keyCode&&(t=o.value,e=s.value,o.style.display="none",s.style.display="none",i.style.display="inline-block",a.style.display="inline-block",t={width:i.innerText=t,height:a.innerText=e,overlay:n.overlay},n._dispatchRectWHChange(t))})},O.prototype.setInfo=function(t,e){this.setNumber(e),this.setPosition(t)},O.prototype.setNumber=function(t){"circle"==this.type?document.getElementById("screenshotNum").textContent=t:(document.getElementById("rectWidth").textContent=t.width,document.getElementById("rectHeight").textContent=t.height)},O.prototype.setPosition=function(t){this.point=t;var t=this._map,e=this.type,t=t.pointToOverlayPixel(this.point);"circle"==e?(this.div.style.left=t.x-30+"px",this.div.style.top=t.y-40+"px"):"rectangle"==e&&(this.div.style.left=t.x+"px",this.div.style.top=t.y-45+"px")},O.prototype.draw=function(){var t=this._map,e=this.type,t=t.pointToOverlayPixel(this.point);"circle"==e?(this.div.style.left=t.x-30+"px",this.div.style.top=t.y-40+"px"):"rectangle"==e&&(this.div.style.left=t.x+"px",this.div.style.top=t.y-45+"px")},O.prototype._dispatchRadiusChange=function(t){this.dispatchEvent("radiuschange",t)},O.prototype._dispatchRectWHChange=function(t){this.dispatchEvent("rectwhchange",t)},(n.prototype=new BMapGL.Overlay).dispatchEvent=E.lang.Class.prototype.dispatchEvent,n.prototype.addEventListener=E.lang.Class.prototype.addEventListener,n.prototype.removeEventListener=E.lang.Class.prototype.removeEventListener,n.prototype.initialize=function(t){var e=this,t=(this._map=t,this.container=document.createElement("div")),n=this._map.getSize();return t.style.cssText="position:absolute;background:transparent;cursor:crosshair;width:"+n.width+"px;height:"+n.height+"px",this._map.addEventListener("resize",function(t){e._adjustSize(t.size)}),this._map.getPanes().floatPane.appendChild(t),this._bind(),t},n.prototype.draw=function(){var t=this._map,e=t.pixelToPoint(new BMapGL.Pixel(0,0)),t=t.pointToOverlayPixel(e);this.container.style.left=t.x+"px",this.container.style.top=t.y+"px"},n.prototype.enableEdgeMove=function(){this._enableEdgeMove=!0},n.prototype.disableEdgeMove=function(){clearInterval(this._edgeMoveTimer),this._enableEdgeMove=!1},n.prototype._bind=function(){function o(t){return{x:t.clientX,y:t.clientY}}function t(e){function t(t){e.point=point,a.dispatchEvent(e)}var n=e.type,i=(e=E.getEvent(e),point=a.getDrawPoint(e),"mousedown"==n&&(s=o(e)),o(e));"click"==n?Math.abs(i.x-s.x)<5&&Math.abs(i.y-s.y)<5&&(r=r&&Math.abs(i.x-r.x)<5&&Math.abs(i.y-r.y)<5?null:(t(),o(e))):t()}for(var a=this,e=(this._map,this.container),s=null,r=null,n=["click","mousedown","mousemove","mouseup","dblclick"],i=n.length;i--;)E.on(e,n[i],t);E.on(e,"mousemove",function(t){a._enableEdgeMove&&a.mousemoveAction(t)})},n.prototype.mousemoveAction=function(t){var e=this._map,n=this,i=e.pointToPixel(this.getDrawPoint(t)),t=(o=(t=t).clientX,a=t.clientY,t.changedTouches&&(o=t.changedTouches[0].clientX,a=t.changedTouches[0].clientY),new BMapGL.Pixel(o,a)),o=t.x-i.x,a=t.y-i.y,i=new BMapGL.Pixel(t.x-o,t.y-a);this._draggingMovePixel=i,e.pixelToPoint(i);this._panByX=this._panByY=0,i.x<=20||i.x>=e.width-20||i.y<=50||i.y>=e.height-10?(i.x<=20?this._panByX=8:i.x>=e.width-20&&(this._panByX=-8),i.y<=50?this._panByY=8:i.y>=e.height-10&&(this._panByY=-8),this._edgeMoveTimer||(this._edgeMoveTimer=setInterval(function(){e.panBy(n._panByX,n._panByY,{noAnimation:!0})},30))):this._edgeMoveTimer&&(clearInterval(this._edgeMoveTimer),this._edgeMoveTimer=null)},n.prototype._adjustSize=function(t){this.container.style.width=t.width+"px",this.container.style.height=t.height+"px"},n.prototype.getDrawPoint=function(t){var e=this._map,n=E.getTarget(t),i=t.offsetX||t.layerX||0,o=t.offsetY||t.layerY||0;for(1!=n.nodeType&&(n=n.parentNode);n&&n!=e.getContainer();)0==n.clientWidth&&0==n.clientHeight&&n.offsetParent&&"TD"==n.offsetParent.nodeName||(i+=n.offsetLeft||0,o+=n.offsetTop||0),n=n.offsetParent;t=new BMapGL.Pixel(i,o);return e.pixelToPoint(t)},(e.prototype=new BMapGL.Control).initialize=function(t){var e=this.container=document.createElement("div"),n=(e.className="BMapGLLib_Drawing",this.panel=document.createElement("div")),i=(n.className="BMapGLLib_Drawing_panel",this.drawingToolOptions&&this.drawingToolOptions.hasCustomStyle&&this.drawingToolOptions.scale&&this._setScale(this.drawingToolOptions.scale),e.appendChild(n),this._generalHtml()),i=(n.appendChild(i),this.tip=document.createElement("div"));return i.className="BMapGLLib_tip",i.innerHTML='<p class="BMapGLLib_tip_title"></p><p class="BMapGLLib_tip_text"></p>',!0===this.drawingToolOptions.enableTips&&n.appendChild(i),this._bind(n),(this.drawingToolOptions.customContainer?E.g(this.drawingToolOptions.customContainer):t.getContainer()).appendChild(e),e},e.prototype._generalHtml=function(t){var i=this,n={hander:"拖动地图"};n[BMAP_DRAWING_MARKER]="画点",n[BMAP_DRAWING_CIRCLE]="圆形工具",n[BMAP_DRAWING_POLYLINE]="画折线",n[BMAP_DRAWING_POLYGON]="多边形工具",n[BMAP_DRAWING_RECTANGLE]="矩形工具";for(var e,o,a=document.createDocumentFragment(),s=0,r=this.drawingModes.length;s<r;s++){var l="BMapGLLib_box BMapGLLib_"+this.drawingModes[s];s==r-1&&(l+=" BMapGLLib_last"),a.appendChild((l=l,e=this.drawingModes[s],o=void 0,(o=document.createElement("a")).className=l,o.href="javascript:void(0)",o.setAttribute("drawingType",e),o.setAttribute("onfocus","this.blur()"),o.addEventListener("mouseenter",function(t){var t=t.target.getAttribute("drawingType"),e=n[t];"hander"===t?(i.tip.children[0].innerText=e,i.tip.children[1].innerText="使用鼠标拖动地图"):(i.tip.className+=" "+t,i.tip.children[0].innerText=e,i.tip.children[1].innerText="使用"+e+"选出目标区域"),i.tip.style.display="block"}),o.addEventListener("mouseleave",function(t){for(var e=t.target.getAttribute("drawingType"),n=" "+i.tip.className.replace(/[\t\r\n]/g,"")+" ";0<=n.indexOf(" "+e+" ");)n=n.replace(" "+e+" "," ");i.tip.className=n.replace(/^\s+|\s+$/g,""),i.tip.style.display="none"}),o))}return a},e.prototype._setScale=function(t){var e=-parseInt((390-390*t)/2,10),n=-parseInt((50-50*t)/2,10);this.container.style.cssText=["-moz-transform: scale("+t+");","-o-transform: scale("+t+");","-webkit-transform: scale("+t+");","transform: scale("+t+");","margin-left:"+e+"px;","margin-top:"+n+"px;","*margin-left:0px;","*margin-top:0px;","margin-left:0px\\0;","margin-top:0px\\0;","filter: progid:DXImageTransform.Microsoft.Matrix(","M11="+t+",","M12=0,","M21=0,","M22="+t+",","SizingMethod='auto expand');"].join("")},e.prototype._bind=function(t){var e=this;E.on(this.panel,"click",function(t){t=E.getTarget(t).getAttribute("drawingType");e.setStyleByDrawingMode(t),e._bindEventByDraingMode(t)})},e.prototype.setStyleByDrawingMode=function(t){if(t)for(var e=this.panel.getElementsByTagName("a"),n=0,i=e.length;n<i;n++){var o,a=e[n];a.getAttribute("drawingType")==t?(o="BMapGLLib_box BMapGLLib_"+t+"_hover",n==i-1&&(o+=" BMapGLLib_last"),a.className=o):a.className=a.className.replace(/_hover/,"")}},e.prototype._bindEventByDraingMode=function(t){var e=this.drawingManager;e._isOpen&&e.getDrawingMode()===t?(e.close(),e._map.enableDoubleClickZoom()):(e.setDrawingMode(t),e.open(),e._map.disableDoubleClickZoom())};var i=[];function o(t){for(var e=i.length;e--;)i[e]!=t&&i[e].close()}function P(t,e,n){var i,o,a,s=t.lng,r=t.lat,e=e/6378800,r=Math.PI/180*r,s=Math.PI/180*s;switch(n){case"North":case"north":case"N":case"n":i=0,o=t.lng;break;case"West":case"west":case"W":case"w":i=90,a=t.lat;break;case"South":case"south":case"S":case"s":i=180,o=t.lng;break;case"East":case"east":case"E":case"e":i=270,a=t.lat;break;default:i=~~n}var l=Math.PI/180*i,p=Math.asin(Math.sin(r)*Math.cos(e)+Math.cos(r)*Math.sin(e)*Math.cos(l)),s=(s-Math.atan2(Math.sin(l)*Math.sin(e)*Math.cos(r),Math.cos(e)-Math.sin(r)*Math.sin(p))+Math.PI)%(2*Math.PI)-Math.PI,l=new BMapGL.Point(o||s*(180/Math.PI),a||p*(180/Math.PI));return l.lng=parseFloat(l.lng.toFixed(6)),l.lat=parseFloat(l.lat.toFixed(6)),l}}();