<!DOCTYPE html>  
<html>  
    <head>  
        <meta name="viewport" content="initial-scale=1.0, user-scalable=no" />  
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />  
        <title>Hello, World</title>  
        <style type="text/css">  
        html{height:100%}  
        body{height:100%;margin:0px;padding:0px}  
        #container{height:100%}  
        </style>  
        
		<script type="text/javascript" src="map_load.js"></script>
        
    </head>  

    <body>  
        <div id="container"></div> 
        <script type="text/javascript"> 
            var outputPath = 'tiles/';    //地图瓦片所在的文件夹
			var fromat = ".png";    //格式 yxh 

			/*var tileLayer = new BMap.TileLayer();
			tileLayer.getTilesUrl = function (tileCoord, zoom) {
				var x = tileCoord.x;
				var y = tileCoord.y;
				var url = outputPath + zoom + '/' + x + '/' + y + fromat;
				return url;
			}
			var tileMapType = new BMap.MapType('tileMapType', tileLayer);*/

			var map = new BMap.Map("container")

			var point = new BMap.Point(116.404, 39.915);  // 创建点坐标  
			map.centerAndZoom(point, 6);                 // 初始化地图，设置中心点坐标和地图级别  
			//添加地图类型控件
	map.addControl(new BMap.MapTypeControl({
		mapTypes:[
            BMAP_NORMAL_MAP,
            BMAP_HYBRID_MAP
        ]}));	  
	map.setCurrentCity("北京");          // 设置地图显示的城市 此项是必须设置的
	map.enableScrollWheelZoom(true);     //开启鼠标滚轮缩放
	//单击获取点击的经纬度
	map.addEventListener("click",function(e){
		alert(e.point.lng + "," + e.point.lat);
	});
        </script>  
    </body>  
</html>