_jsload2&&_jsload2('menu', 'z.object.extend(fc.prototype,{na:function(a,b){if(this.C)return t;this.B=a;this.Cl=b||s;this.Aa();var c=this,e=a.K.Vb;b&&b.z.jf&&(e="pointer");this.C.style.cursor=e;this.Dd&&(this.Dd.style.cursor=e);z.M(document,"mousedown",function(){c.C&&c.U()});z.M(this.C,"click",function(a){c.U();ma(a)});e=this.j.Wa;e||(e=a.Wa);this.Cl?this.Cl.addEventListener("rightclick",function(a){c.C&&c.tJ(a)}):a.addEventListener("rightclickex",function(a){c.C&&(!a.fb&&!a.zb)&&c.tJ(a)});for(var e=0,f=this.xa.length;e<f;e++)"menuitem"== this.xa[e].Mb&&this.xa[e].na(a,this),"divider"==this.xa[e].Mb&&(this.Fe[this.xa[e].Lj].D=Ab(this.C,"<div class=\'BMap_cmDivider\'></div>"));this.$r()},ib:function(){this.B&&(this.na(this.B,this.Cl),this.Kh&&(this.Kh=t,this.show()))},remove:function(){this.C&&(this.C.parentNode.removeChild(this.C),this.C=s);this.Dd&&(this.Dd.parentNode.removeChild(this.Dd),this.Dd=s);for(var a=0,b=this.xa.length;a<b;a++)"menuitem"==this.xa[a].Mb&&(this.xa[a].C=s);this.B=this.Cl=s},Aa:function(){this.C=Ab(this.B.Wa,"<div unselectable=\'on\'></div>"); this.C.className="BMap_contextMenu";var a=this.C.style;a.font="12px "+G.fontFamily;9>z.ca.ia?this.Dd=Ab(this.B.Wa,"<div class=\'BMap_cmShadow\'></div>"):a.MV=a.NP=a.WebkitBoxShadow="1px 2px 6px #666";return this.C},Dw:function(a){if(a&&!("menuitem"!=a.Mb||""==a.Wg||0>=a.Vi)){for(var b=0,c=this.xa.length;b<c;b++)if(this.xa[b]===a)return;this.xa.push(a);this.Kf.push(a);this.B&&(a.na(this.B,this),z.D.Ua(a.Jd(),"BMap_cmLstItem"),1<this.xa.length?"menuitem"==this.xa[this.xa.length-2].Mb&&z.D.Pb(this.xa[this.xa.length- 2].Jd(),"BMap_cmLstItem"):"menuitem"==this.xa[0].Mb&&z.D.Ua(this.xa[0].Jd(),"BMap_cmFstItem"),this.$r())}},removeItem:function(a){if(a&&"menuitem"==a.Mb){for(var b=0,c=this.xa.length;b<c;b++)this.xa[b]===a&&(this.xa[b].remove(),this.xa.splice(b,1),c--);b=0;for(c=this.Kf.length;b<c;b++)this.Kf[b]===a&&(this.Kf[b].remove(),this.Kf.splice(b,1),c--);this.C&&(0<this.xa.length&&"menuitem"==this.xa[this.xa.length-1].Mb&&z.D.Ua(this.xa[this.xa.length-1].Jd(),"BMap_cmLstItem"),this.$r())}},bC:function(){this.xa.push({Mb:"divider", Lj:this.Fe.length});this.Fe.push({D:s});this.C&&(this.Fe[this.Fe.length-1].D=Ab(this.C,"<div class=\'BMap_cmDivider\'></div>"),this.$r())},sF:function(a){if(this.Fe[a]){this.Fe[a].D&&this.Fe[a].D.parentNode&&this.Fe[a].D.parentNode.removeChild(this.Fe[a].D);for(var b=0,c=this.xa.length;b<c;b++)this.xa[b]&&("divider"==this.xa[b].Mb&&this.xa[b].Lj==a)&&(this.xa.splice(b,1),c--),this.xa[b]&&("divider"==this.xa[b].Mb&&this.xa[b].Lj>a)&&this.xa[b].Lj--;this.Fe.splice(a,1);this.$r()}},sa:function(a,b){this.C.style.left= a+"px";this.C.style.top=b+"px";this.Dd&&(this.Dd.style.left=a+1+"px",this.Dd.style.top=b+2+"px")},show:function(){if(this.Kh!=q&&0!=this.Kf.length){this.Kh=q;this.C&&(this.C.style.visibility="visible");this.Dd&&(this.Dd.style.visibility="visible");var a=new P("onopen");a.point=this.fx;a.pixel=this.qs;this.dispatchEvent(a)}},U:function(){if(this.Kh!=t){this.Kh=t;this.C&&(this.C.style.visibility="hidden");this.Dd&&(this.Dd.style.visibility="hidden");var a=new P("onclose");a.point=this.fx;a.pixel=this.qs; this.dispatchEvent(a)}},t_:function(a){if(a&&(this.j.cursor=a,this.C&&(this.C.style.cursor=this.j.cursor),this.Dd))this.Dd.style.cursor=this.j.cursor},$r:function(){this.C&&this.Dd&&(this.Dd.style.width=this.C.offsetWidth+"px",this.Dd.style.height=this.C.offsetHeight+"px")},tJ:function(a){if(0!=this.Kf.length){this.qs=a.lb;this.fx=this.B.xb(this.qs);var b=this.Jd().offsetHeight,c=this.Jd().offsetWidth,e=a.lb.x,f=a.lb.y;a.lb.x+c>this.B.width&&(e=a.lb.x-c);a.lb.y+b>this.B.height&&(f=a.lb.y-b);this.sa(e, f);this.show()}}});T(yf,{addItem:yf.Dw,removeItem:yf.removeItem,addSeparator:yf.bC,removeSeparator:yf.sF});z.object.extend(ic.prototype,{na:function(a,b){if(this.C)return t;this.B=a;this.Eh=b;b.Jd()&&(this.Aa(),this.ba(),this.Ih||(this.Ih=q,this.disable()));return q},remove:function(){this.C&&(this.C.parentNode.removeChild(this.C),this.C=s);this.B=this.Eh=s},ib:function(){this.Eh&&this.B&&this.na(this.B,this.Eh)},Aa:function(){var a=this.j.Dm?"<div"+(this.j.id?" id=\'"+this.j.id+"\'":"")+" unselectable=\'on\'><div style=\'width: 17px;height: 17px;margin-right: 3px;display: inline-block;zoom: 1;*display: inline;vertical-align: middle;background: url("+ this.j.Dm+") no-repeat;\'></div><span style=\'vertical-align: middle;\'>"+this.Wg+"</span></div>":"<div"+(this.j.id?" id=\'"+this.j.id+"\'":"")+" unselectable=\'on\'><span>"+this.Wg+"</span></div>";this.C=Ab(this.Eh.Jd(),a);this.j.Dm?(this.yv=this.C.firstChild,this.Ur=this.C.lastChild):this.Ur=this.C;a=this.C.style;a.padding="2px 6px";a.margin="0 2px";a.fontSize="14px";a.MozUserSelect="none";a.lineHeight="17px";a.width=this.j.width+"px";this.Ih?(a.color="#000",a.cursor="pointer"):(a.color="#aaa",a.cursor= this.B.K.Vb);return this.C},ba:function(){var a=this;z.M(this.C,"click",function(b){a.Ih?a.Kz&&a.Kz.call&&a.Kz.call(a,a.Eh.fx,a.Eh.qs,a.Eh.Cl):ma(b)});z.M(this.C,"mousedown",function(a){ma(a)});z.M(this.C,"mouseover",function(){a.Ih&&(a.C.style.color="#6688cc")});z.M(this.C,"mouseout",function(){a.Ih&&(a.C.style.color="#000")})},cu:function(a){a&&(this.Wg=a+"",this.Ur&&(this.Ur.innerHTML="<span>"+this.Wg+"</span>"))},Rb:function(a){a&&(this.j.Dm=a,this.yv?this.yv.style.background="url("+a+")":(this.C.innerHTML= "<div"+(this.j.id?" id=\'"+this.j.id+"\'":"")+" unselectable=\'on\'><div style=\'width: 17px;height: 17px;margin-right: 3px;display: inline-block;zoom: 1;*display: inline;vertical-align: middle;background: url("+this.j.Dm+") no-repeat;\'></div><span style=\'vertical-align: middle;\'>"+this.Wg+"</span></div>",this.yv=this.C.firstChild,this.Ur=this.C.lastChild))},enable:function(){this.Ih=q;this.C&&(this.C.style.color="#000",this.C.style.cursor="pointer")},disable:function(){this.Ih=t;this.C&&(this.C.style.color= "#aaa",this.C.style.cursor=this.B.K.Vb)}});T(zf,{setIcon:zf.Rb,setText:zf.cu,enable:zf.enable,disable:zf.disable}); ');