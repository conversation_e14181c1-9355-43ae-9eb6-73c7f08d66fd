_jsload2&&_jsload2('hotspot', '<PERSON><PERSON>Ye(function(a){function b(a){var b=this.R;b.Qe=[];if(this.Jh)for(var c in this.Jh)if(!(0==c.indexOf("vector_")&&!this.Sb()||0==c.indexOf("tile_")&&this.Sb())){var i=this.Jh[c],k;for(k in i){var m=i[k];if(!(m.Lh>this.fa()||m.Hf<this.fa())){var n=this.$b(m.ga());n.x<this.width&&n.y<this.height&&a.offsetX<n.x+m.Jv[1]&&(a.offsetX>n.x-m.Jv[3]&&a.offsetY>n.y-m.Jv[0]&&a.offsetY<n.y+m.Jv[2])&&b.Qe.push(m)}}}}function c(){a.R.XK&&0<a.R.Qe.length?(a.platform.style.cursor="pointer",a.R.XK=t):(a.R.Qe=[],a.platform.style.cursor= a.K.Vb)}a.R.ju=new uc("",{za:new O(15,-3),Wp:{border:"1px solid #aaa",background:"#fffec2",whiteSpace:"nowrap",font:"12px "+G.fontFamily,mozBoxShadow:"1px 2px 6px #666",webkitBoxShadow:"1px 2px 6px #666",boxShadow:"1px 2px 6px #666",padding:"2px 4px"}});a.addEventListener("mousemove",function(c){if(!(c.fb&&!(c.fb instanceof oc)||c.zb)){var f=this.R,g=f.Qe.slice(0);b.call(this,c);for(var i=f.Qe.slice(0),k=0;k<g.length;k++)for(var m=0;m<i.length;m++)g[k]===i[m]&&(g.splice(k,1),k--,i.splice(m,1),m--); if(0<g.length){var n=new P("onhotspotout");n.spots=g.slice(0);this.dispatchEvent(n)}if(0==f.Qe.length&&!f.$x)this.platform.style.cursor!=this.K.Vb&&(this.platform.style.cursor=this.K.Vb),f.Up&&(clearTimeout(f.Up),f.Up=s),f.QF=setTimeout(function(){f.ju.U()},400);else if(0<i.length&&(this.platform.style.cursor="pointer",n=new P("onhotspotover"),n.spots=i.slice(0),this.dispatchEvent(n),f.Up&&(clearTimeout(f.Up),f.Up=s),f.QF&&(clearTimeout(f.QF),f.QF=s),n.spots[0].eE()))f.Up=setTimeout(function(){f.ju.dd(n.spots[0].eE()); f.ju.sa(c.point);f.ju.show();a.Ga(f.ju)},400)}});a.addEventListener("clickex",function(a){var c=this.R;a.overlay||(0==c.Qe.length&&b.call(this,a),0<c.Qe.length&&(a=new P("onhotspotclick"),a.spots=c.Qe.slice(0),a.spots.sort(function(a,b){return a.ga().lat-b.ga().lat}),this.dispatchEvent(a),c.Qe.length=0))});a.addEventListener("load",c);a.addEventListener("moveend",c);a.addEventListener("zoomend",c);a.addEventListener("dragend",function(){this.R.XK=q})}); ');