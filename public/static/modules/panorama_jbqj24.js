_jsload2&&_jsload2('panorama', 'Ye.prototype.NC=ha(2,function(){this.Nb.style[De]="";this.xi=this.ii=t;this.Sv("h");this.Sv("v");this.$d(Pe,window);this.$d(Qe);this.$d(Re,window);this.$d(Se,window);this.$d(Te,window);this.options.Dk||(this.$d("DOMMouseScroll"),this.$d("mousewheel"));this.options.wi&&this.$d(Ue);this.options.Pw&&clearInterval(this.XV);this.options.xN&&this.options.xN.call(this)});var dj=1; function ej(a,b){var c=a.width,e=a.height,f=180/Math.pow(2,b),g=1;H()&&(g=2);c=f/(1<c/e?e:c);g=Math.floor(b)+g;for(e=360/(2*Math.pow(2,g-2)*c);512<e;)g++,e=360/(2*Math.pow(2,g-2)*c);g>ji&&(e*=Math.pow(2,g-ji),g=ji);g<dj&&(g=dj);e=Math.round(e);return{qM:g,RK:e}}var fj=5063,gj=5062,hj=5061,ij=5060,ji=5,dj=1; z.extend(Oa.prototype,{ib:function(){this.jR();for(var a=0;a<B.ur.length;a++)B.ur[a](this);this.Za!=s?"inter"===this.Ke?this.EF(this.Za):this.dispatchEvent(new P("onid_changed")):this.Lb!=s&&(this.dispatchEvent(new P("onposition_changed")),this.dispatchEvent(new P("onposition_changed_inner")));this.j.visible?this.show():this.U();this.Of!=s&&this.dispatchEvent(new P("onvisible_poi_type_changed"),{visiblePOIType:this.Of});var a=s,b;for(b in this.Je)a=this.Je[b],delete this.Je[b],this.Ga(a)},au:function(a){this.Of= a||"none";this.dispatchEvent(new P("onvisible_poi_type_changed"),{visiblePOIType:this.Of})},Ga:function(a){this.Je[a.ld]=a;this.dispatchEvent(new P("onadd_overlay"),{overlay:a})},Qb:function(a){delete this.Je[a.ld];this.dispatchEvent(new P("onremove_overlay"),{overlay:a})},z1:function(a,b){var c=L("div"),e=c.style;c.className="pano_m_indoor_exit";c.title=a;c.onclick=b;e.display="none";e.zIndex=1201;c.innerHTML=\'<span style="float:right;margin-right:12px;">\\u51fa\\u53e3</span>\';return c},y1:function(a, b){var c=L("div"),e=c.style;e.position="absolute";e.zIndex=1201;e.right=e.top="10px";e.width="17px";e.height="16px";e.background=\'url("\'+G.qa+\'st-close.png") no-repeat 50% 50%\';H()&&(e.right=e.top="0px",e.width="42.5px",e.height="40px",e.backgroundSize="25.5px 24px");e.cursor="pointer";c.title=a;c.onclick=b;e.display="none";return c},vc:function(a,b,c){"object"===typeof b&&(c=b,b=l);a!=this.Za&&(this.ul=this.Za,this.vl=this._position,this.Za=a,this.CA=s,this.Ke=b||"street",c=c||{kk:t},this.IJ=c.kk, "inter"===b?this.EF(a):this.dispatchEvent(new P("onid_changed")),this.C_(c))},C_:function(a){this.Hv={id:this.Xb(),pov:a.pov}},TX:function(a,b){var c,e;if(this.Hv&&a===this.Hv.id){c={};for(var f=0;f<b.length;f++)e=b[f],c[e]=this.Hv[e];this.Hv=s}return c},sa:function(a,b){a.pb(this.Lb)||(this.ul=this.Za,this.vl=this._position,this.Lb=a,this.Za=s,b=b||{kk:t},this.IJ=b.kk,this.dispatchEvent(new P("onposition_changed_inner")))},Pc:function(a){var b={heading:this.Da.heading,pitch:this.Da.pitch};this.Da= a;a=a=this.Da.pitch;a>this.Vj?a=this.Vj:a<this.Xj&&(a=this.Xj);this.Da.pitch=a;if(b.heading!=this.Da.heading||b.pitch!=this.Da.pitch)this.Br=q,this.dispatchEvent(new P("onpov_changed"))},My:function(a){var b=this.Da,c=a.heading-b.heading,e=a.pitch-b.pitch,f=this;new tb({Ic:30,duration:200,kc:ub.dL,va:function(a){f.Pc({heading:b.heading+a*c,pitch:b.pitch+a*e})},finish:function(){f.Pc(a)}})},Qc:function(a,b){if(a!=this.Mc){a>ae&&(a=ae);a<be&&(a=be);if(a!=this.Mc){this.Mc=a;var c=new P("onzoom_changed_inner"), b=b||{};c.xp=b.xp||t;this.dispatchEvent(c);(!Na()||c.xp)&&this.dispatchEvent(new P("onzoom_changed"))}"cssRender"===this.er()&&this.Pc(this.Da)}},GB:function(){if(this.B){for(var a=this.B.Hx(),b=0;b<a.length;b++)(a[b]instanceof U||a[b]instanceof uc)&&a[b].point&&this.ya.push(a[b]);this.dispatchEvent(new P("onoverlay_changed"))}},Ek:function(){this.Dl.style.visibility="hidden"},Ry:function(){this.dispatchEvent(new P("onoverlay_show"));this.Dl.style.visibility="visible"},show:function(){this.j.visible= q;this.qe&&z.D.show(this.qe);this.j.closeControl&&(this.Bf&&this.B&&this.B.La()===this.C)&&z.D.show(this.Bf);this.gB&&this.gB.show();this.GB();this.dispatchEvent(new P("onvisible_changed"))},U:function(){this.j.visible=t;this.qe&&z.D.U(this.qe);this.j.closeControl&&this.Bf&&z.D.U(this.Bf);this.gB&&this.gB.U();this.dispatchEvent(new P("onvisible_changed"))},GF:function(a){this.B=a;this.GB()},jR:function(){var a=L("div"),b=a.style;b.overflow="hidden";b.position="absolute";b.zIndex=1200;b.top=b.left= "0px";b.height=b.width="100%";b.backgroundColor="#e5e3df";this.qe=a;this.Sh=this.Jj("1");this.Uj=this.Jj("4");this.Dl=this.Jj("5");a.appendChild(this.Sh);a.appendChild(this.Uj);a.appendChild(this.Dl);Na()||(this.or=this.Jj("3"),this.or.style.width=this.C.clientWidth+"px",this.or.style.height=this.C.clientHeight+"px",this.or.style.overflow="hidden",this.or.style.WebkitUserSelect="none",a.appendChild(this.or),ke()&&(this.fw=this.tH(),this.Sh.appendChild(this.fw),this.gw=this.tH(),this.Sh.appendChild(this.gw))); this.C.appendChild(a);"absolute"!=Va(this.C).position&&(this.C.style.position="relative")},Jj:function(a){var b=L("div"),c=b.style;c.position="absolute";c.top=c.left="0";c.zIndex=a||"0";c.WebkitUserSelect="none";return b},tH:function(){var a=L("img");thumbStyle=a.style;thumbStyle.position="absolute";thumbStyle.top=thumbStyle.left="0";thumbStyle.zIndex="1";thumbStyle.opacity="0";thumbStyle.kc=thumbStyle.WebkitTransition="opacity 300ms ease-out";a.onload=function(){this.style.visibility="";this.style.opacity= "1"};return a},La:w("C"),fh:function(){return new O(this.C.clientWidth,this.C.clientHeight)},J3:w("dk"),pY:w("jw"),wO:ba("jw"),EF:function(a){a!=this.CA&&(this.CA=a,this.Za=s,this.dispatchEvent(new P("oniid_changed")),this.dispatchEvent(new P("onid_changed")),Ra(5045,{type:this.j.panoramaRenderer}))},clear:function(){this.Lb=this.Za=this.ea=s;this.bo=[];this.dispatchEvent(new P("onclear"))},Vo:function(){var a=this.ea.bm;this.VR=this.ea.NV;this.clear();this.AH=q;this.vc(a)}});var jj=Oa.prototype; T(jj,{setId:jj.vc,setPosition:jj.sa,setPov:jj.Pc,setZoom:jj.Qc,show:jj.show,hide:jj.U,setPanoramaPOIType:jj.au,addOverlay:jj.Ga,removeOverlay:jj.Qb,exitInter:jj.Vo});function kj(a){a.ng=this;var b=z.platform.Jm?"android":"ios";Na()?(this.Bd=new lj(a),this.nd=new mj(a,this.Bd.Ya),this.Bd.lV(this.nd),"android"===b?Ra(ij):Ra(hj)):(this.Bd=new nj(a),this.nd=new oj(a),"android"===b?Ra(gj):Ra(fj));a.j.linksControl?this.nd.show():this.nd.U();a.j.clickOnRoad?this.nd.kD():this.nd.SC();this.P=a;this.ba();this.MT=new pj(a,this)}B.Tm(function(a){a.ng=new kj(a)});z.lang.ta(kj,z.lang.Ca,"PanoramaRenderer"); z.extend(kj.prototype,{ba:function(){var a=this.P,b=this;a.addEventListener("links_visible_changed",function(){a.j.linksControl===q?b.nd.show():b.nd.U()});a.addEventListener("clickonroad_changed",function(){a.j.clickOnRoad===q?b.nd.kD():b.nd.SC()});a.addEventListener("dataload",function(c){b.ea=c.data;b.Xu();b.lJ(c.data);b.va(a.Ea(),b.ef,b.Yg)});a.addEventListener("pov_changed",function(){b.ea&&b.va(a.Ea(),b.ef,b.Yg)});a.addEventListener("clear",function(){b.ea=s;b.lJ(s);b.Bd.va();b.nd&&b.nd.va()}); a.addEventListener("zoom_changed_inner",function(c){b.ea&&(b.Xu(),b.DU(a.Ea(),b.ef,b.Yg,c.xp))});a.addEventListener("size_changed",function(a){b.zB(a)});a.addEventListener("dblclick",function(){var b=a.fa();4===b?a.Qc(1):a.Qc(b+1);a.dispatchEvent(new P("ondblclickzoomend"))});a.hl=a.fh();setInterval(function(){if(a.Xb()){var c=a.fh();if(!c.pb(a.hl)){a.hl=c;var e=new P("onsize_changed");e.W4=a.hl;e.size=c;a.dispatchEvent(e);c=b.ef;b.Xu();c!=b.ef&&b.Bd.qp&&b.Bd.qp();b.va(a.Ea(),b.ef,b.Yg)}}},80);Na()&& a.addEventListener("refresh",function(){b.Bd.Rr()})},zB:function(){var a=this.ef;this.Xu();a!=this.ef&&this.Bd.qp&&this.Bd.qp();this.Bd.Nk&&this.Bd.Nk();this.nd.Nk&&this.nd.Nk();a=this.P;this.va(a.Ea(),this.ef,this.Yg);"cssRender"===a.er()&&a.Pc(a.Ea())},lJ:function(a){a&&("number"===typeof a.heading&&this.P.Br===t)&&(this.P.Da.heading=a.heading,this.P.Da.pitch=a.pitch);var b=this.P.TX(a&&a.id,["pov"]);b&&b.pov&&(this.P.Da.heading=b.pov.heading,this.P.Da.pitch=b.pov.pitch);if(this.P.AH&&a.indoorPois){for(var b= s,c=this.P.VR,e=0,f=a.indoorPois.length;e<f;e++)c==a.indoorPois[e].panoIId&&(b=a.indoorPois[e]);b&&(this.P.Da.heading=fe(b.pointX-a.rh,b.pointY-a.th),this.P.Da.pitch=0);this.P.AH=t;this.P.F1=s}this.P.Br&&(this.P.Br=t);this.P.ft();a&&a.bm?this.P.Ny("inter"):this.P.Ny("street");this.Bd.setData(a,this.bE(),this.ef);this.nd.setData(a)},DU:function(a,b,c,e){var f=this;this.Bd.Qc(f.bE(),f.ef,{xp:e,XZ:function(a){f.MT.Er(a)}});"cvsRender"!=this.P.dk&&this.Bd.va(a,b,c);this.nd.va(a,b,c)},va:function(a,b, c){this.Bd.va(a,b,c);this.nd.va(a,b,c)},A3:w("ef"),bE:function(){return this.P.fa()},s3:w("Yg"),Xu:function(){var a=ej(this.P.fh(),this.P.fa());this.Yg=a.RK;this.ef=a.qM}});function qj(a){this.P=a}B.Tm(function(a){var b=new qj(a);a.addEventListener("dataload",function(a){b.ea=a.data;b.Mi()});a.addEventListener("zoom_changed_inner",function(){b.ob&&(b.ob.stop(),b.ob=s)})}); qj.prototype.Mi=function(){function a(a,b){I.ng.Bd.Rg||(i=q,N.ob&&N.ob.stop(),k=a,m=b,C=I.Ea().heading,F=I.Ea().pitch,I.dispatchEvent(new P("ontouchstart")))}function b(a,b){i&&!I.Nl&&(A=q,N.QA||(N.QA=q,N.D1=bb()),f(a,b),p=n-a,v=o-b,n=a,o=b,N.rI=bb())}function c(a){i&&(i=t,!A&&(!E&&fa)&&N.pH&&(N.pH=t,N.Wn?(N.Ii&&(clearTimeout(N.Ii),N.Ii=s),10>k-N.Wn.x&&10>m-N.Wn.y&&I.dispatchEvent(new P("ondblclick")),N.Wn=s):(N.Wn={x:k,y:m},N.Ii=setTimeout(function(){var a,b,c=I.WA;a=I.qe;var e=0;for(b=0;a.offsetParent;)e+= a.offsetLeft,b+=a.offsetTop,a=a.offsetParent;a=k-e;b=m-b;Na()&&(a*=2,b*=2);0===c.dM(a,b)&&I.dispatchEvent(new P("onclick"));N.Wn=s},400))),N.QA=t,e(a),E=A=t)}function e(a){if(a){var b=bb()-N.rI;if(!(100<b||10<b&&10>Math.abs(p)&&10>Math.abs(v))){var c;H()?(b=a.changedTouches[0].clientX,c=a.changedTouches[0].clientY):(b=a.clientX,c=a.clientY);a=new Q(k,m);c=new Q(b,c);var b=[0<c.x-a.x?1:-1,0<c.y-a.y?1:-1],e=Math.abs(a.x-c.x),g=0,i=0;0==Math.abs(a.y-c.y)?g=e:(a=Math.abs(a.x-c.x)/Math.abs(a.y-c.y),i= Math.round(Math.sqrt(24336/(1+a*a))),g=Math.round(a*i));-1==b[0]&&(g=-g);-1==b[1]&&(i=-i);N.ob&&N.ob.stop();C=I.Ea().heading;F=I.Ea().pitch;N.ob=new tb({duration:625,Ic:60,kc:function(a){return a*0.3125-0.15625*a*a},va:function(a){a=a*6.4;f(a*g,a*i)},finish:function(){N.ob=s},Kt:function(){N.ob=s}})}}}function f(a,b){x=-a*N.Su;y=b*N.Su;var c=C+x,e=F+y;e>I.Vj&&(e=I.Vj);e<I.Xj&&(e=I.Xj);I.Pc({heading:c,pitch:e})}function g(a){if(I.j.enableScrollWheelZoom){var a=window.event||a,b=new P("onmousewheel"); b.Zp=0<=a.wheelDelta||0>a.detail;var c=new Date;if(!(220>c-pa)&&(pa=c,"path"!=(a.srcElement||a.target).tagName)){var c=la(b,a),e=a.srcElement||a.target,f=a.offsetX||a.layerX||0,g=a.offsetY||a.layerY||0;1!=e.nodeType&&(e=e.parentNode);for(;e&&e!=I.La();){e.aa&&(z.lang.Nc(e.aa)instanceof gb&&(overlay=z.lang.Nc(e.aa)),z.lang.Nc(e.aa)instanceof tc&&(infoWindow=z.lang.Nc(e.aa)));if(!(0==e.clientWidth&&0==e.clientHeight&&e.offsetParent&&"TD"==e.offsetParent.nodeName)&&"http://www.w3.org/2000/svg"!=e.namespaceURI)f+= e.offsetLeft||0,g+=e.offsetTop||0;else if("http://www.w3.org/2000/svg"==e.namespaceURI){var i=I.ng.nd&&I.ng.nd.hf||s;if(-1<navigator.userAgent.indexOf("Opera")&&"svg"!=e.tagName){if(e=z.lang.Nc(e.aa))e=e.ve(),f+=N.$b(e.Ve()).x,g+=N.$b(e.Rf()).y;break}if(i&&(!z.ca.ia||9<=z.ca.ia&&"svg"==e.nodeName.toLowerCase()))f+=parseInt(i.style.left),g+=parseInt(i.style.top)}e=e.offsetParent}c.x=f;c.y=g;I.fa();I.Ea();I.Ea();var k;if(b.Zp==q){if(I.fa()==ae){Cb(a);return}k=I.fa()+2/3}else if(b.Zp==t){if(I.fa()== be){Cb(a);return}k=I.fa()-2/3}I.Qc(k)}Cb(a)}}if(!this.dT){this.dT=q;var i=t,k,m,n=0,o=0,p,v,x=0,y=0,A=t,E=t,C,F,D,I=this.P,R=t,M=0;this.Su=rj(this);this.rI=0;this.QA=t;this.Wn=s;this.og=1;var N=this,fa=t;H()?(z.M(I.qe,"touchstart",function(b){fa=q;for(var c=b.target;c&&c!=I.qe;){if(c===I.Dl){fa=t;break}c=c.offsetParent}N.pH=q;var c=b.touches[0].clientX,e=b.touches[0].clientY,f=I.WA;if(Na())f&&f.QH(b.touches[0].pageX*f.Cd,b.touches[0].pageY*f.Cd)!=s&&(fa=t);else if(!Nb()&&f){var g=Bb(I.qe);-1!=f.KL(c- g.left,e-g.top)&&(fa=t)}a(c,e);(!z.platform.Jm||-1==navigator.userAgent.indexOf("QQBrowser"))&&b.preventDefault();b.stopPropagation()}),z.M(I.qe,"touchmove",function(a){if(!R){b(a.touches[0].clientX-k,a.touches[0].clientY-m);var c=new P("ontouchmove");c.F5=new Q(a.touches[0].pageX,a.touches[0].pageY);I.dispatchEvent(c)}a.preventDefault();a.stopPropagation()}),z.M(document,"touchend",function(a){c(a);fa=t}),I.qe.addEventListener("gesturestart",function(){M=I.fa()},t),I.qe.addEventListener("gesturechange", function(a){N.og=a.scale;E=R=q;a=N.og*M;4<a?a=4:1>a&&(a=1);a=Math.round(100*a)/100;I.Qc(a,{xp:q})},t),I.qe.addEventListener("gestureend",function(){R=t;I.fa()!=M&&I.dispatchEvent(new P("onpinchtozoomend"))},t)):(z.M(I.qe,"mousedown",function(b){a(b.clientX||b.pageX||0,b.clientY||b.pageY||0);D=b.target||b.srcElement;z.ca.ia&&D.setCapture&&D.setCapture();N.$T=I.qe.style.cursor;I.qe.style.cursor=G.Hd;z.ca.ia||Cb(b)}),z.M(document,"mousemove",function(a){b((a.clientX||a.pageX||0)-k,(a.clientY||a.pageY|| 0)-m);na(a)}),z.M(document,"mouseup",function(a){ma(a);c(a);z.ca.ia&&(D&&D.releaseCapture)&&D.releaseCapture();I.qe.style.cursor=N.$T||G.Vb}));I.addEventListener("zoom_changed_inner",function(){N.Su=rj(N)});I.addEventListener("size_changed",function(){N.Su=rj(N)});var pa=new Date;z.M(I.La(),"mousewheel",g);window.addEventListener&&I.La().addEventListener("DOMMouseScroll",g,t)}}; function rj(a){if(!a.ea)return s;var b=a.P,c=ej(b.fh(),b.fa()),e=c.qM,c=c.RK;if("cssRender"===b.er()&&!Na())return 360/(a.ea.tiles.hp(e)*c);b=a.P.fa();return 180/Math.pow(2,b)/a.P.fh().height};function pj(a,b){z.lang.Ca.call(this);this.P=a;this.Aa=b;this.Mh=[];this.Oh={};this.mB=t;this.Gz()}z.lang.ta(pj,z.lang.Ca,"PanoramaOverlayMgr"); z.extend(pj.prototype,{Gz:function(){var a=this,b=this.P;b.addEventListener("add_overlay",function(c){c=c.overlay;c.na(b);a.Mh.push(c);a.Gr()});b.addEventListener("dataload",function(){a.mB=q;a.Gr()});b.addEventListener("remove_overlay",function(b){for(var b=b.overlay,e=0,f=a.Mh.length;e<f;e++)b==a.Mh[e]&&a.Mh.splice(e,1);b.remove();a.Gr()});b.addEventListener("clear",function(){a.mB=t});b.addEventListener("zoom_changed",function(){a.Er()});b.addEventListener("dataload",function(){a.Gr()});b.addEventListener("pov_changed", function(){a.Er()});b.addEventListener("size_changed",function(){a.Er()});b.addEventListener("onoverlay_property_changed",function(){a.Gr()})},Gr:function(){this.gU();if(0!=this.Mh.length&&this.mB){for(var a,b,c,e=this.Mh.length-1;0<=e;e--)if(b=this.Mh[e],ovarlayDom=b.Jd(),c=b.Ea())a=c.heading,b.eP(),b=b.ld,this.Oh[a]||(this.Oh[a]={}),this.Oh[a][b]={dom:ovarlayDom,pov:c},this.P.Dl.appendChild(ovarlayDom);this.Er()}},Er:function(a){this.P.Ea();var a=a||this.Aa.ef,b=this.Aa.Yg,c=function(){function a(c){var e= b[c.top];if(!e)return t;for(var f=e.length-1;0<=f;f--)if(c.left>=e[f].left&&c.left<=e[f].right||c.right>=e[f].left&&c.right<=e[f].right)return q;return t}var b={};return function(c){for(;a(c);)c.top+=-36;b[c.top]?b[c.top].push(c):b[c.top]=[c];return c}}(),e;for(e in this.Oh)for(var f in this.Oh[e]){var g=this.Oh[e][f],i=this.RT(e,g.pov.pitch,a,b),g=g.dom,i=c({top:i[1],left:i[0],right:i[0]+g.offsetWidth});g.style.left=i.left-g.offsetWidth/2+"px";g.style.top=i.top+g.offsetHeight/2+"px"}},RT:function(a, b,c,e){if(Na())return this.ST(a,b);var f=this.P;for(svHeading=f.Ea().heading%360;0>svHeading;)svHeading=(svHeading+360)%360;var g=(a-svHeading)%360,a=f.fh(),c=360/(2*Math.pow(2,c-2)*e);180<g?g-=360:-180>g&&(g+=360);e=Math.round(a.width/2+g/c);b=Math.round(a.height/2-(b-f.Ea().pitch)/c);return[e,b]},ST:function(a,b){var c=500*Math.cos(Pb(b)),c=Math.round(100*c)/100;"string"==typeof a&&(a=parseFloat(a));var e;e=a+this.P.ea.tiles.dirNorth;e%=360;var f=this.P.ng.Bd;e=Z.ug(Math.cos(Pb(e))*c,500*Math.sin(Pb(b)), Math.sin(Pb(e))*c,1);var c=Z.create(),g=f.Nh;sj.multiply(c,f.Jf[0],e);sj.multiply(c,g,c);if(0>c[3])return[-1E3,-1E3];f=this.P.hl;return[(c[0]/c[3]+1)/2*f.width,(1-c[1]/c[3])/2*f.height]},gU:function(){for(var a in this.Oh)for(var b in this.Oh[a]){var c=this.Oh[a][b].dom;c.parentNode.removeChild(c)}this.Oh={}}});z.extend(ee.prototype,{na:function(a){this.P=a;this.ga()||this.sa(this.P.ga());this.Yz();this.Ez()},Jd:w("gd"),hide:function(){this.Qa=t;this.gd&&(this.gd.style.display="none")},show:function(){this.Qa=q;this.gd&&(this.gd.style.display="block")},isVisible:w("Qa"),eP:function(){var a=this.Xn();100<a?(a/=1E3,a=100>a?a.toFixed(2):a.toFixed(0),a+="\\u516c\\u91cc"):(a=100>a?a.toFixed(2):a.toFixed(0),a+="\\u7c73");this.Mq.innerHTML=a},Yz:function(){var a=this.xk(),b=L("div"),c=b.style;c.position="absolute"; c.backgroundColor="rgba(29, 29, 29, 0.8)";c.padding="7px 0";c.height="19px";c.font="16px arial";c.color="white";c.whiteSpace="nowrap";c.borderRadius="4px";c.left="-1000px";c.top="-1000px";c=L("div");c.style["float"]="left";c.style.lineHeight="19px";b.appendChild(c);var e=L("span");e.style.margin="0 14px";e.innerHTML=a;c.appendChild(e);a=L("span");a.style.color="color:rgba(255,255,255,0.3)";a.innerHTML="|";c.appendChild(a);var f=L("span");f.style.margin="0 8px";f.style.color="#60c7fa";f.style.fontSize= "12px";f.innerHTML="0\\u7c73";c.appendChild(f);this.zR===t&&(f.style.display="none",a.style.display="none");this.gd=b;this.Mq=f;this.Zr=e},Ez:function(){var a=this;this.aR(this.gd,function(b){a.dispatchEvent(new P("onclick"),{type:"click",target:a});Cb(b)});z.M(this.gd,"mouseenter",function(){a.dispatchEvent(new P("onmouseover"),{type:"mouseover",target:a})});z.M(this.gd,"mouseleave",function(){a.dispatchEvent(new P("onmouseout"),{type:"mouseout",target:a})})},aR:function(a,b){if(H()){var c=0,e=0, f=0;a.addEventListener("touchstart",function(a){c=(new Date).getTime();e=a.touches[0].pageX;f=a.touches[0].pageY},t);a.addEventListener("touchend",function(a){200<=(new Date).getTime()-c||(5<=Math.abs(a.changedTouches[0].pageX-e)||5<=Math.abs(a.changedTouches[0].pageY-f))||b.call(this,a)},t)}else z.M(this.gd,"click",b)},Nf:function(a,b){this.gd&&("content"===a&&(this.Zr.innerHTML=b),this.P.dispatchEvent(new P("onoverlay_property_changed")))},remove:function(){this.gd=this.Mq=this.Zr=s;this.dispatchEvent(new P("onremove"), {type:"remove",target:this})}});z.extend(he.prototype,{na:function(a){this.P=a;this.Yz();(a=this.$D())&&(a.panoId||a.panoIId)&&this.Ez(a)},Jd:w("gd"),hide:function(){this.Qa=t;this.gd.style.display="none"},show:function(){this.Qa=q;this.gd.style.display="block"},isVisible:w("Qa"),eP:function(){var a=this.P.ga(),b=this.ga(),a=S.$o(a,b);100<a?(a/=1E3,a=100>a?a.toFixed(2):a.toFixed(0),a+="\\u516c\\u91cc"):(a=100>a?a.toFixed(2):a.toFixed(0),a+="\\u7c73");this.Mq.innerHTML=a},Ez:function(a){var b=this;if(a.panoIId){var c=function(c){b.P.EF(a.panoIId); c.stopPropagation();c.preventDefault()};H()?z.M(this.fr,"touchend",function(a){c(a)}):(this.fr.style.cursor="pointer",z.M(this.fr,"click",function(a){c(a)}))}if(a.panoId){var e=function(c){function e(){b.P.Pc(b.Ea());b.P.removeEventListener("dataload",e)}var i=a.panoId;b.P.Xb()!=i&&(b.P.vc(i),b.P.addEventListener("dataload",e));c.stopPropagation();c.preventDefault()};z.M(this.gd,"touchend",function(a){e(a)});this.gd.style.cursor="pointer";z.M(this.gd,"click",function(a){e(a)})}},Yz:function(){var a= this.gp(),b=L("div"),c=b.style;c.position="absolute";c.backgroundColor="rgba(29, 29, 29, 0.8)";c.padding="7px 0";c.height="19px";c.font="16px arial";c.color="white";c.whiteSpace="nowrap";c.borderRadius="4px";c.left="-1000px";c.top="-1000px";var e=L("img");e.src=this.ap();e.style.width="24px";e.style.height="24px";e.style.position="absolute";e.style.left="5px";e.style.top="5px";c.paddingLeft="40px";b.appendChild(e);c=L("div");c.style["float"]="left";c.style.lineHeight="19px";b.appendChild(c);var f= L("span");f.style.margin="0 14px";f.innerHTML=a;c.appendChild(f);a=L("span");a.style.color="color:rgba(255,255,255,0.3)";a.innerHTML="|";c.appendChild(a);a=L("span");a.style.margin="0 8px";a.style.color="#60c7fa";a.style.fontSize="12px";a.innerHTML="0\\u7c73";c.appendChild(a);this.gd=b;this.fr=e;this.Mq=a;this.Zr=f},Nf:function(a,b){switch(a){case "title":this.Zr.innerHTML=b;break;case "altitude":this.P.dispatchEvent(new P("onoverlay_position_changed"));break;case "position":this.P.dispatchEvent(new P("onoverlay_position_changed"))}}, remove:function(){this.Zr=this.Mq=this.fr=this.gd=s;this.dispatchEvent(new P("onremove"),{type:"remove",target:this})}});z.extend(function(a){this.Mi(a)}.prototype,{Mi:function(a){this.P=a;this.xo=this.wo=q;this.Aa();this.ba()},Aa:function(){this.C=L("div");var a=this.C.style;a.position="absolute";a.left="10px";a.top="10px";a.zIndex=1201;a=this.C;a.innerHTML=this.Vq();Ua()?(this.md=z.$(a.children[2]),this.io=[z.$(this.md.children[0]),z.$(this.md.children[1]),z.$(this.md.children[2]),z.$(this.md.children[3])],Nb()?this.yJ=z.$(a.children[1].childNodes[0]):Mb()&&(this.KJ=z.$(a.children[1].childNodes[0])),this.ae=z.$(a.children[3])): H()&&(this.ae=z.$(a.children[0]));this.Ah=z.$(this.ae.children[0]);this.fg=z.$(this.ae.children[1]);z.ca.ia&&(this.md.style.background=\'url("\'+G.qa+\'blank.gif") repeat\');6==z.ca.ia&&this.rU();H()&&(this.ae.style.left=this.ae.style.top="0",this.ae.style.width="36px",this.ae.style.height="78px",this.Ah.style.width=this.fg.style.width="33px",this.Ah.style.height=this.fg.style.height="36px",this.fg.style.top="37.5px");this.P.fa()==ae&&this.oo("zoomIn",t);this.P.fa()==be&&this.oo("zoomOut",t);this.P.La().appendChild(a)}, Vq:function(){var a=[];Ua()&&(a.push(this.lA()),a.push(this.hS()),a.push(this.jS()));a.push(this.mA());return a.join("")},hS:function(){if(Nb())return\'<svg version="1.1" overflow="hidden" width="82px" height="82px" viewBox="0 0 82 82" style="position: absolute; top: 0px; left: 0px;"><g transform="rotate(0, 41, 41)"><rect x="33" y="1" width="14" height="12" rx="4" ry="4" stroke="#868685" stroke-width="1" fill="#f8f8f8"></rect><polyline points="37.5,9.5 37.5,3.5 42.5,9.5 42.5,3.5" stroke-linejoin="bevel" stroke-width="1.5" fill="#f2f4f6" stroke="#868685"></polyline></g></svg>\'; if(Mb())return\'<div style="position:absolute;top:0;left:0;z-index:0"><v:group style="position:absolute;width:82px;height:82px; behavior:url(#default#VML);rotation:0" unselectable="on" coordsize="82,82" coordorigin="-41,-41"><v:roundrect style="width:14px;height:12px;top:-40px; behavior:url(#default#VML);left:-7px;z-index:2" fillcolor="#f8f8f8" arcsize ="19661f" coordsize="21600,21600" strokecolor="#868685" strokeweight="1pt"></v:roundrect><v:polyline style="behavior: url(#default#VML);z-index:3" fillcolor="#f2f4f6" points="-3,-31,-3,-37,3,-31,3,-37" strokeweight="1.2pt"><v:stroke style="behavior:url(#default#VML)" color="#868685" joinstyle="bevel"></v:stroke></v:polyline></v:group></div>\'}, lA:function(){return\'<div style="position:absolute;top:4px;left:4px;width:74px;height:74px;background:url(\'+G.qa+\'st-navictrl.png) no-repeat;-webkit-user-select:none;overflow:hidden"><div></div></div>\'},jS:function(){return\'<div style="position:absolute;top:0px;left:0;width:82px;height:82px;-webkit-user-select:none;cursor:\'+G.Vb+\'"><div style="position:absolute;left:32px;top:13px;cursor:pointer;width:17px;height:17px;" title="\\u5411\\u4e0a\\u5e73\\u79fb"></div><div style="position:absolute;left:14px;top:32px;cursor:pointer;width:17px;height:17px;" title="\\u5411\\u5de6\\u5e73\\u79fb"></div><div style="position:absolute;left:51px;top:32px;cursor:pointer;width:17px;height:17px;" title="\\u5411\\u53f3\\u5e73\\u79fb"></div><div style="position:absolute;left:33px;top:51px;cursor:pointer;width:17px;height:17px;" title="\\u5411\\u4e0b\\u5e73\\u79fb"></div></div>\'}, mA:function(){var a="background-image:url(\'"+G.qa+"st-navictrl.png\');background-repeat:no-repeat;background-position:-74px 0;";H()&&(a="background-image:url(\'"+G.qa+"st-navictrl-hd.png\');background-repeat:no-repeat;background-position:0 0;background-size:180px 78px;");return\'<div style="position:absolute;top:84px;left:30px;width:24px;height:52px;\'+a+\'-webkit-user-select:none;overflow:hidden"><div style="position:absolute;cursor:pointer;width:22px;height:24px;top:0;left:0;-webkit-user-select:none;-webkit-tap-highlight-color:rgba(0,0,0,0)" title="\\u653e\\u5927\\u4e00\\u7ea7"></div><div style="position:absolute;cursor:pointer;width:22px;height:24px;top:25px;left:0;-webkit-user-select:none;-webkit-tap-highlight-color:rgba(0,0,0,0)" title="\\u7f29\\u5c0f\\u4e00\\u7ea7"></div><div></div></div>\'}, ba:function(){var a=this.P,b=this;if(Ua()){z.M(this.io[0],"click",function(){var b=a.Ea();a.My({heading:b.heading,pitch:b.pitch+30})});z.M(this.io[1],"click",function(){var b=a.Ea();a.My({heading:b.heading-45,pitch:b.pitch})});z.M(this.io[2],"click",function(){var b=a.Ea();a.My({heading:b.heading+45,pitch:b.pitch})});z.M(this.io[3],"click",function(){var b=a.Ea();a.My({heading:b.heading,pitch:b.pitch-30})});for(var c=0;c<this.io.length;c++)z.M(this.io[c],"mousedown",ma);b.Gv=t;z.M(this.md,"mousedown", function(c){b.Gv=q;b.md.style.cursor=G.Hd;z.ca.ia&&b.md.setCapture&&b.md.setCapture();b.wJ=b.gH(c);b.uR=a.Ea().heading;z.ca.ia||Cb(c)});z.M(document,"mousemove",function(c){b.Gv&&a.Pc({heading:b.uR+360-(b.gH(c)-b.wJ),pitch:a.Ea().pitch})});z.M(document,"mouseup",function(){b.Gv&&(b.Gv=t,b.md.style.cursor=G.Vb,z.ca.ia&&b.md.releaseCapture&&b.md.releaseCapture(),b.wJ=s)});a.addEventListener("position_changed",function(){var c=360-this.Ea().heading;Nb()?b.yJ.setAttribute("transform","rotate("+c+", 41, 41)"): Mb()&&(b.KJ.style.rotation=c);a.removeEventListener("position_changed",arguments.callee)});a.addEventListener("pov_changed",function(){var a=360-this.Ea().heading;Nb()?b.yJ.setAttribute("transform","rotate("+a+", 41, 41)"):Mb()&&(b.KJ.style.rotation=a)})}z.M(this.C,"mousemove",function(){a.dispatchEvent(new P("hide_ellipse"))});z.M(this.C,"mouseout",function(){a.dispatchEvent(new P("show_ellipse"))});z.M(this.Ah,"click",function(){a.Qc(a.fa()+1)});z.M(this.fg,"click",function(){a.Qc(a.fa()-1)});Ua()? (z.M(this.Ah,"mousedown",function(){b.ck(b.wo,-98)}),z.M(this.fg,"mousedown",function(){b.ck(b.xo,-122)}),z.M(this.Ah,"mouseup",function(){b.ck(b.wo,-74)}),z.M(this.fg,"mouseup",function(){b.ck(b.xo,-74)})):H()&&(z.M(this.Ah,"touchstart",function(){b.ck(b.wo,-98)}),z.M(this.fg,"touchstart",function(){b.ck(b.xo,-122)}),z.M(this.Ah,"touchend",function(){b.ck(b.wo,-74)}),z.M(this.fg,"touchend",function(){b.ck(b.xo,-74)}));this.P.addEventListener("zoom_changed_inner",function(){if(this.fa()==ae)b.oo("zoomIn", t);else if(this.fa()==be)b.oo("zoomOut",t);else{b.oo("zoomIn",q);b.oo("zoomOut",q)}})},ck:function(a,b){a&&(6==z.ca.ia?this.ae.children[2].style.left=b+"px":(H()&&(b=36*((b+74)/24)),this.ae.style.backgroundPosition=b+"px 0"))},rU:function(){var a=this.C.children[0];a.style.background="";a.children[0].style.cssText+="position:absolute;left:0;top:0;width:98px;height:74px;filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src=\'"+G.qa+"st-navictrl.png\')";this.ae.style.background="";this.ae.children[2].style.cssText+= "position:absolute;z-index:-1;left:-74px;top:0;width:98px;height:74px;filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src=\'"+G.qa+"st-navictrl.png\')"},show:function(){this.C.style.display=""},U:function(){this.C.style.display="none"},gH:function(a){var a=window.event||a,b=a.pageX||a.clientX,a=a.pageY||a.clientY,c=Bb(this.P.La()),b=b-c.left-50,a=-(a-c.top-50),b=180*(Math.atan2(a,b)/Math.PI);return 90>b?90-b:450-b},oo:function(a,b){var c=this.ae.children[2];if("zoomIn"==a)if(b)if(this.wo= q,this.Ah.style.cursor="pointer",6==z.ca.ia)c.style.left="-74px";else{var e=-74;H()&&(e=0);this.ae.style.backgroundPosition=e+"px 0"}else this.wo=t,this.Ah.style.cursor="",6==z.ca.ia?c.style.left="-170px":(e=-170,H()&&(e=-144),this.ae.style.backgroundPosition=e+"px 0");"zoomOut"==a&&(b?(this.xo=q,this.fg.style.cursor="pointer",6==z.ca.ia?c.style.left="-74px":(e=-74,H()&&(e=0),this.ae.style.backgroundPosition=e+"px 0")):(this.xo=t,this.fg.style.cursor="",6==z.ca.ia?c.style.left="-146px":(e=-146,H()&& (e=-108),this.ae.style.backgroundPosition=e+"px 0")))}});function tj(a,b){this.KB=a;this.bf=[];this.ub={Jo:s};var b=b||{},c;for(c in b)this.ub[c]=b[c]}tj.prototype.setData=function(a,b){if(0!==this.KB){if(this.bf.length>this.KB){for(var c=Math.round(0.6*this.KB),e=0;e<c;e++){var f=this.bf[e].qI;this.ub.Jo&&this.ub.Jo(this.bf[f]);delete this.bf[f]}this.bf.splice(0,c)}this.bf[a]||this.bf.push(b);this.bf[a]=b;b.qI=a}};tj.prototype.getData=function(a){return this.bf[a]}; tj.prototype.clear=function(){for(var a=0;a<this.bf.length;a++){var b=this.bf[a].qI;this.ub.Jo&&this.ub.Jo(this.bf[b]);delete this.bf[b]}this.bf=[]};function uj(){this.Vz=0;this.Ng={}}uj.prototype.zx=function(){var a=s,b;for(b in this.Ng)if(this.Ng[b]&&this.Ng[b].kA===q)return this.Ng[b].kA=t,this.Ng[b];a=new Image;this.Vz++;b="img_"+this.Vz;return this.Ng[b]=a};uj.prototype.clear=function(){for(var a in this.Ng)this.Ng[a]&&(this.Ng[a].onload=this.Ng[a].onerror=s);this.Ng={};this.Vz=0};function nj(a){this.P=a;return"cvsRender"==a.dk?new vj(a):new wj(a)};function vj(a){this.P=a;this.Sh=a.Sh;this.Jj();this.P.ra=this;this.ba()} z.extend(vj.prototype,{ba:function(){var a=this,b=a.P;b.addEventListener("size_changed",function(c){a.Ce(b.ng.ef,b.ng.bE(),c.size)})},setData:function(a,b,c){a&&(this.ea=a,this.nT(b,c))},Jj:function(){var a=L("canvas");a.width=this.P.La().clientWidth;a.height=this.P.La().clientHeight;var b=a.style;b.position="absolute";b.left=b.top="0";this.Sh.appendChild(a);this.UV=a.getContext("2d");this.ri=[];this.eK=[];this.Xc=new xj(this.UV,this.ri,0,a.width,a.height,this.P);for(a=b=a=0;16>a;a++){this.ri[a]= [];for(b=0;8>b;b++){var c=new yj(this.P);c.$M=a;c.aN=b;c.canvas=this.Xc;this.ri[a][b]=c}}this.P.t2=this.Xc},va:function(a,b){if(a&&b){var c=a.heading%360,e=a.pitch;this.wA({heading:c,pitch:e},b);this.Xc.di({heading:c,pitch:e},b)}},nT:function(a,b){this.Xc.AW();this.kS(this.ea);this.Xc.wY(this.ea);this.dJ(a,b);this.uA(b,q)},Qc:function(a,b){var c=this,e=q;b==this.Xc.ra.zoom&&(e=t);this.dJ(a,b);e&&this.uA(b,q);setTimeout(function(){c.wA()},1E3)},kS:function(a){var b=this,c=a.tiles,e={xe:[]};e.xe[0]= new Image;e.xe[0].src=c.getTilesUrl(a.id,{x:0,y:0},2);e.xe[1]=new Image;e.xe[1].src=c.getTilesUrl(a.id,{x:1,y:0},2);b.pP=0;e.xe[0].onload=function(){b.gy()};e.xe[1].onload=function(){b.gy()};this.eK[0]=e},gy:function(){this.pP++;2==this.pP&&(this.Xc.J_(this.eK[0].xe),this.Xc.gy())},wA:function(a,b){if(this.ea&&this.ea.tiles)for(var c=this.ea.tiles,e=[],b=b||this.Xc.ra.zoom,e=this.ri,f=this.Xc.LD(a),g=f.wu,i=f.Gs,k=f.Ct,f=f.St,f=f>k?f-k:64+f-k,m=0,n=0,o=0;o<f+Math.pow(2,7-b);){for(var p=g;p<i+Math.pow(2, 7-b);){m=o+k;63<m?m%=64:0>m&&(m+=64);m=Math.floor(m*Math.pow(2,b-7));n=Math.floor(p*Math.pow(2,b-7));if(m<Math.pow(2,b-1)&&n<Math.pow(2,b-2)&&0==e[m][n].lm){var v=c.getTilesUrl(this.ea.id,{x:m,y:n},b);e[m][n].yO(v)}p+=Math.pow(2,7-b)}o+=Math.pow(2,7-b)}},uA:function(a,b){var c=t;b&&(c=q);if(!c)if(this.ez)this.ez=t;else return;if(this.ea&&this.ea.tiles){for(var c=this.ea.tiles,e=[],f=Math.pow(2,a-1),g=Math.pow(2,a-2),e=this.ri,i=0;i<f;i++)for(var k=0;k<g;k++)e[i][k].a_();for(var i=this.Xc.LD(),f=i.wu, g=i.Gs,m=i.Ct,i=i.St,n=i>m?i-m:64+i-m,o=0,p=0,i=0;i<n+Math.pow(2,7-a);){for(k=f;k<g+Math.pow(2,7-a);){o=i+m;63<o?o%=64:0>o&&(o+=64);o=Math.floor(o*Math.pow(2,a-7));p=Math.floor(k*Math.pow(2,a-7));if(o<Math.pow(2,a-1)&&p<Math.pow(2,a-2)){var v=c.getTilesUrl(this.ea.id,{x:o,y:p},a);e[o][p].yO(v)}k+=Math.pow(2,7-a)}i+=Math.pow(2,7-a)}}},Ce:function(a,b,c){a!=this.Xc.ra.zoom&&(this.ez=q);this.Xc.Nk(a,b,c);this.uA(a);this.wA();this.Xc.refresh()},dJ:function(a,b){var c=t;b==this.Xc.ra.zoom?this.ez=t:(this.ez= q,b<this.Xc.ra.zoom&&(c=q));this.Xc.b_(a,b);return c},YX:function(a){return this.Xc.$X(a)},OD:function(a,b){return this.Xc.OD(a,b)},M3:function(){return this.Xc.ra.Ba}}); function yj(a){this.xe=new Image;this.src="";this.canvas=s;this.aN=this.$M=this.lm=0;this.P=a;var b=this;this.pM=function(){-1!=b.src.indexOf("pos=0_0&z=1")&&b.P.dispatchEvent(new P("onthumbnail_complete"));b.lm=2;b.canvas.VW(b.$M,b.aN)};this.xe.onload=this.pM;this.yO=function(a){this.src==a?0==this.lm&&b.pM():(this.src=this.xe.src=a,this.lm=1)};this.a_=function(){this.src=this.xe.src="";this.lm=0}} function xj(a,b,c,e,f,g){this.heading=this.pitch=0;this.zoom=2;this.ri=b;this.Mk=[];this.sg=a;this.Xl=t;this.ra={a:e,eb:f,Qf:0,gs:0,Ba:0,m1:512,heading:0,pitch:0,fd:32,zoom:0,Hp:0,Fo:0,$p:0,o2:0,Sm:0,p2:0,Xp:0,Pt:0};this.P=g;this.mt=t;this.lQ()} z.extend(xj.prototype,{lQ:function(){if(z.ca.Qw){var a=navigator.userAgent.substr(navigator.userAgent.indexOf("Chrome"),12);27<Number(/[1-9][0-9]*/.exec(a+"1")[0])?(this.ra.Fo=0.0078125,this.ra.Pt=2):(this.ra.Fo=0.015625,this.ra.Pt=4);this.ra.Sm=0;this.ra.Xp=0}else z.ca.ia?(this.ra.Fo=0.0078125,this.ra.Sm=0,this.ra.Xp=6):(this.ra.Fo=0.0078125,this.ra.Sm=0,this.ra.Xp=4),this.ra.Pt=2},J_:ba("Mk"),xx:function(){var a=this.ra,b=Math.PI/Math.pow(2,this.ra.Hp);b>Math.PI/2+0.1&&(b=2*Math.PI/3);return a.a/ 2/Math.tan(b/2)},Nk:function(a,b,c){var e=this.ra;e.a=c.width;e.eb=c.height;this.sg.canvas.width=c.width;this.sg.canvas.height=c.height;e.zoom=a;e.Hp=b;e.Ba=this.xx();e.fd=16*Math.pow(2,a-2);e.Qt=Math.pow(2,a-1);e.Vm=Math.pow(2,a-2);e.Bp=Math.pow(2,7-a)},b_:function(a,b){var c=this.ra;if(0==this.ra.Ba)c.zoom=b,c.Hp=a,c.Ba=this.xx(),c.fd=16*Math.pow(2,b-2),c.Qt=Math.pow(2,b-1),c.Vm=Math.pow(2,b-2),c.Bp=Math.pow(2,7-b),c.$p=c.Fo*Math.pow(2,-a);else if(!(a==c.Hp&&b==c.zoom)){this.Xl=q;var e=this.ra.Ba; c.fd=16*Math.pow(2,b-2);c.zoom=b;c.Hp=a;c.Qt=Math.pow(2,b-1);c.Vm=Math.pow(2,b-2);c.Bp=Math.pow(2,7-b);e=(this.xx()-e)/32;this.P.Ek();this.QJ(0,e);c.$p=c.Fo*Math.pow(2,-a)}},wY:function(a){this.ra.heading-=this.ra.Qf/180*Math.PI;this.ra.Qf=a.tiles.dirNorth;this.ra.heading+=a.tiles.dirNorth/180*Math.PI;this.ra.gs=a.tiles.pitch||0},QJ:function(a,b){var c=this;if(5>a)this.ra.Ba+=b*Math.pow(2,a),this.RR(),setTimeout(function(){c.QJ(a+1,b)},0);else if(5==a){this.Xl=t;var e=this.ra.zoom;this.ra.Ba=this.xx(); this.ra.fd=16*Math.pow(2,e-2);this.ra.Qt=Math.pow(2,e-1);this.ra.Vm=Math.pow(2,e-2);this.ra.Bp=Math.pow(2,7-e);this.P.Ry();this.refresh()}},S4:function(a,b){this.Xl=q;var c=this.ra;this.aF={data:a,yV:b};this.aF.oc=0;var e=Math.cos((a.dir+c.Qf)/180*Math.PI),c=-Math.sin((a.dir+c.Qf)/180*Math.PI),c=e=0;this.P.Ek();this.LR({index:1,F2:e,G2:c})},VW:function(a,b){if(!this.Xl&&this.mt)if(1<this.ra.Hp)this.refresh();else{var c=this.ra,e=c.fd,f=512/e,g=c.Bp,i=Math.cos(c.pitch),k=Math.cos(c.heading),m=Math.sin(c.heading), n=c.Ba*Math.sin(c.pitch),o=c.Ba*i*m;c.oq=-c.Ba*i*k;c.pq=n;c.qq=o;c.ks=k;c.gu=m;for(i=0;i<f;i++)for(k=0;k<f;k++){m={rb:{x:i*e,y:k*e-1},sb:{x:i*e-1,y:k*e+e+1},tb:{x:i*e+e+1,y:k*e+e}};n={rb:{x:i*e-1,y:k*e},sb:{x:i*e+e+1,y:k*e-1},tb:{x:i*e+e,y:k*e+e+1}};if(0==i)var p=-1;else i==g-1&&(p=1);if(0==k)var v=-1;else k==g-1&&(v=1);this.fD(m,a,b,c.zoom,p,v);this.gD(n,a,b,c.zoom,p,v)}}},LR:function(){var a=this.ra;this.sg.clearRect(0,0,a.a,a.eb);a.heading-=a.Qf/180*Math.PI;a.Qf=this.aF.yV.X4;a.heading+=a.Qf/180* Math.PI;this.Mk=this.aF.data.xe;this.Xl=t;this.P.Ry();this.refresh()},Gn:function(a,b,c,e,f){var c=this.ra,b=2*(0.5*a.x/512+b/2)*Math.PI,g=(0.5-a.y/512)*Math.PI,i=c.Ba,a=Math.cos(g),k=Math.sin(Math.abs(g));0.4>k?k=0.4:0.98<k&&(k=c.Pt);var b=b+e*k,m=-a*Math.cos(b),k=Math.sin(g+f*k),n=a*Math.sin(b),f=c.ks,e=c.gu,b=c.oq,a=c.pq,g=c.qq,o=b*m+k*a+n*g;0>o&&(o=t);o=i*i/o;i=o*m-b;m=o*k-a;n=o*n-g;o=Math.pow(i,2)+Math.pow(m,2)+Math.pow(n,2);k=Math.sqrt(o);0==o?e=f=0:(o=(i*e+n*f)/k,1<o?o=1:-1>o&&(o=-1),o=Math.acos(o), 0>i*f*a+m*e*g-m*f*b-n*a*e&&(o=2*Math.PI-o),f=k*Math.cos(o),e=k*Math.sin(o));return{x:c.a/2+f,y:c.eb/2-e}},ZK:function(a,b,c,e,f,g){var i=this.sg,k=0,m=0,n=0,o=0,p=this.ra.$p;e&&(1==e?m=p:-1==e&&(k=p));f&&(-1==f?n=p:1==f&&(o=p));e=this.ra.Sm;k=this.Gn(a.rb,b,c,-e-k,n,g);if(!k)return t;n=this.Gn(a.sb,b,c,e+m,e+n,g);if(!n)return t;c=this.Gn(a.tb,b,c,m,-e-o,g);if(!c)return t;c={rb:k,sb:n,tb:c};g=c.rb;m=c.sb;o=c.tb;i.save();i.beginPath();i.moveTo(g.x,g.y);i.lineTo(m.x,m.y);i.lineTo(o.x,o.y);i.closePath(); i.clip();a=this.Bk(a,c);i.transform(a[0],a[1],a[2],a[3],a[4],a[5]);i.drawImage(this.Mk[b],0,0);i.restore()},YK:function(a,b,c,e,f,g){var i=this.sg,k=0,m=0,n=0,o=0,p=this.ra.$p;e&&(1==e?m=p:-1==e&&(k=p));f&&(-1==f?n=p:1==f&&(o=p));e=this.ra.Sm;n=this.Gn(a.rb,b,c,-k,e+n,g);if(!n)return t;k=this.Gn(a.sb,b,c,-e-k,-e-o,g);if(!k)return t;c=this.Gn(a.tb,b,c,e+m,-o,g);if(!c)return t;c={rb:n,sb:k,tb:c};g=c.rb;m=c.sb;o=c.tb;i.save();i.beginPath();i.moveTo(g.x,g.y);i.lineTo(m.x,m.y);i.lineTo(o.x,o.y);i.closePath(); i.clip();a=this.Bk(a,c);i.transform(a[0],a[1],a[2],a[3],a[4],a[5]);i.drawImage(this.Mk[b],0,0);i.restore()},AW:function(){this.mt=t;this.P.Ek()},gy:function(){this.mt=q;this.refresh();this.P.Ry()},di:function(a){if(this.mt&&!this.Xl){var b=this.ra;b.pitch=a.pitch/180*Math.PI-b.gs/180*Math.PI;b.heading=(a.heading+b.Qf)/180*Math.PI;b.heading>2*Math.PI?b.heading-=2*Math.PI:0>b.heading&&(b.heading+=2*Math.PI);a=b.fd;this.sg.clearRect(0,0,b.a,b.eb);var c=b.bound,e=c.wu,f=c.Gs,g=c.Ct,c=c.St,c=c>g?c-g:64+ c-g,i=b.Bp,k=b.Xp,m=Math.cos(b.pitch),n=Math.cos(b.heading),o=Math.sin(b.heading),p=b.Ba*Math.sin(b.pitch),v=b.Ba*m*o;b.oq=-b.Ba*m*n;b.pq=p;b.qq=v;b.ks=n;b.gu=o;for(m=0;m<c;m++)for(n=e;n<f;n++){v=m+g;63<v?v%=64:0>v&&(v+=64);var o=Math.floor(v/i),p=Math.floor(n/i),v=v%i,x=n%i,y={rb:{x:v*a,y:x*a-k},sb:{x:v*a-k,y:x*a+a+k},tb:{x:v*a+a+k,y:x*a+a}},A={rb:{x:v*a-k,y:x*a},sb:{x:v*a+a+k,y:x*a-k},tb:{x:v*a+a,y:x*a+a+k}};if(0==v)var E=-1;else v==i-1&&(E=1);if(0==x)var C=-1;else x==i-1&&(C=1);this.fD(y,o,p,b.zoom, E,C);this.gD(A,o,p,b.zoom,E,C)}}},refresh:function(){if(this.mt&&!this.Xl){var a=this.ra,b=a.fd;this.sg.clearRect(0,0,a.a,a.eb);var c=this.LD(),e=c.wu,f=c.Gs,g=c.Ct,c=c.St,c=c>g?c-g:64+c-g,i=a.Bp,k=Math.cos(a.pitch),m=Math.cos(a.heading),n=Math.sin(a.heading),o=a.Ba*Math.sin(a.pitch),p=a.Ba*k*n;a.oq=-a.Ba*k*m;a.pq=o;a.qq=p;a.ks=m;a.gu=n;k=a.Xp;for(m=0;m<c;m++)for(n=e;n<f;n++){var v=m+g;63<v?v%=64:0>v&&(v+=64);var o=Math.floor(v/i),p=Math.floor(n/i),v=v%i,x=n%i,y={rb:{x:v*b,y:x*b-k},sb:{x:v*b-k,y:x* b+b+k},tb:{x:v*b+b+k,y:x*b+b}},A={rb:{x:v*b-k,y:x*b},sb:{x:v*b+b+k,y:x*b-k},tb:{x:v*b+b,y:x*b+b+k}};if(0==v)var E=-1;else v==i-1&&(E=1);if(0==x)var C=-1;else x==i-1&&(C=1);this.fD(y,o,p,a.zoom,E,C);this.gD(A,o,p,a.zoom,E,C)}}},RR:function(){var a=this.ra;this.sg.clearRect(0,0,a.a,a.eb);var b=a.Xp,c=Math.cos(a.pitch),e=Math.cos(a.heading),f=Math.sin(a.heading),g=a.Ba*Math.sin(a.pitch),i=a.Ba*c*f;a.oq=-a.Ba*c*e;a.pq=g;a.qq=i;a.ks=e;a.gu=f;for(a=0;16>a;a++)for(c=0;16>c;c++){e={rb:{x:32*a,y:32*c-b},sb:{x:32* a-b,y:32*c+32+b},tb:{x:32*a+32+b,y:32*c+32}};f={rb:{x:32*a-b,y:32*c},sb:{x:32*a+32+b,y:32*c-b},tb:{x:32*a+32,y:32*c+32+b}};if(0==a)var k=-1;else 15==a&&(k=1);if(0==c)var m=-1;else 15==c&&(m=1);this.YK(e,0,2,k,m);this.ZK(f,0,2,k,m);this.YK(e,1,2,k,m);this.ZK(f,1,2,k,m)}},OD:function(a,b){var c=this.ra,e=a-c.a/2,f=c.eb/2-b,g=c.pitch+c.gs/180*Math.PI,i=c.heading-c.Qf/180*Math.PI,k=-c.Ba*Math.cos(g)*Math.cos(i),m=c.Ba*Math.sin(g),n=c.Ba*Math.cos(g)*Math.sin(i),o=e*Math.sin(i),e=e*Math.cos(i),p=f*Math.sin(g)* Math.cos(i),f=f*Math.cos(g),k=k+o+p,m=m+f,f=n+e+-f*Math.sin(g)*Math.sin(i),g=this.Ea(k,m,f);if(2<m)return t;i=2.08/(2.08-m);k*=i;f*=i;return{PG:k,QG:f,Ba:Math.sqrt(Math.pow(k,2)+Math.pow(f,2)),wF:c.Ba,heading:g.heading}},LD:function(a){var b=this.ra;a&&(b.heading=(a.heading+b.Qf)/180*Math.PI,b.heading>2*Math.PI?b.heading-=2*Math.PI:0>b.heading&&(b.heading+=2*Math.PI),b.pitch=a.pitch/180*Math.PI-b.gs/180*Math.PI);var c=Math.cos(b.pitch),e=Math.sin(b.pitch),f=Math.cos(b.heading),g=Math.sin(b.heading), a=-b.Ba*c*f,i=b.Ba*e,k=b.Ba*c*g,m=b.eb/2*e*f,n=b.eb/2*c,o=-b.eb/2*e*g,p=-b.eb/2*e*f,c=-b.eb/2*c,e=b.eb/2*e*g,v=-b.a/2*g,x=-b.a/2*f,g=b.a/2*g,f=b.a/2*f;if(0<b.pitch)var y=this.Ea(a+m+v,i+n+0,k+o+x),A=this.Ea(a+p,i+c,k+e),E=this.Ea(a+p+g,i+c+0,k+e+f);else var C=a+m,F=i+n,o=k+o,D=this.Ea(C,F,o),F=this.Ea(C+v,F+0,o+x),C=this.Ea(a+p+g,i+c+0,k+e+f);a=Math.atan(b.eb/2/b.Ba);b.pitch+a>Math.PI/2?(D=0,A=A.pitch<E.pitch?A.pitch:E.pitch,A=Math.ceil(32*(Math.PI/2-A)/Math.PI)):b.pitch-a<-Math.PI/2?(A=D.pitch>F.pitch? D.pitch:F.pitch,D=Math.floor(32*(Math.PI/2-A)/Math.PI),A=32):0<b.pitch?(D=Math.floor(32*(Math.PI/2-b.pitch-a)/Math.PI),A=A.pitch>E.pitch?E.pitch:A.pitch,A=Math.ceil(32*(Math.PI/2-A)/Math.PI)):(A=D.pitch>F.pitch?D.pitch:F.pitch,D=Math.floor(32*(Math.PI/2-A)/Math.PI),A=Math.ceil(32*(Math.PI/2-b.pitch+a)/Math.PI));Math.abs(b.pitch)>=Math.PI/2-a?(C=0,y=64):0<b.pitch?(C=Math.floor(32*y.heading/Math.PI),y=b.heading>y.heading?Math.ceil(32*(2*b.heading-y.heading)/Math.PI):Math.ceil(32*(2*b.heading+2*Math.PI- y.heading)/Math.PI)):(y=Math.ceil(32*C.heading/Math.PI),C=b.heading>C.heading?Math.floor(32*(2*b.heading-2*Math.PI-C.heading)/Math.PI):Math.floor(32*(2*b.heading-C.heading)/Math.PI));0>C&&(C+=64);64<y&&(y-=64);32<A&&(A=32);0>D&&(D=0);b.bound={wu:D,Gs:A,Ct:C,St:y};return{wu:D,Gs:A,Ct:C,St:y}},fD:function(a,b,c,e,f,g){if(2==this.ri[b][c].lm){var i=this.sg,k=0,m=0,n=0,o=0,p=this.ra.$p;f&&(1==f?m=p:-1==f&&(k=p));g&&(-1==g?n=p:1==g&&(o=p));f=this.ra.Sm;n=this.mg(a.rb,b,c,e,0,-k,f,n);if(!n)return t;k=this.mg(a.sb, b,c,e,-f,-k,-f,-o);if(!k)return t;e=this.mg(a.tb,b,c,e,f,m,0,-o);if(!e)return t;e={rb:n,sb:k,tb:e};m=e.rb;o=e.sb;k=e.tb;i.save();i.beginPath();i.moveTo(m.x,m.y);i.lineTo(o.x,o.y);i.lineTo(k.x,k.y);i.closePath();i.clip();a=this.Bk(a,e);i.transform(a[0],a[1],a[2],a[3],a[4],a[5]);i.drawImage(this.ri[b][c].xe,0,0)}else{i=this.sg;o=n=m=k=0;p=0.0078125;f&&(1==f?m=p:-1==f&&(k=p));g&&(-1==g?n=p:1==g&&(o=p));f=0.0078125;n=this.mg(a.rb,b,c,e,0,-k,f,n);if(!n)return t;k=this.mg(a.sb,b,c,e,-f,-k,-f,-o);if(!k)return t; e=this.mg(a.tb,b,c,e,f,m,0,-o);if(!e)return t;e={rb:n,sb:k,tb:e};m=e.rb;o=e.sb;k=e.tb;i.save();i.beginPath();i.moveTo(m.x,m.y);i.lineTo(o.x,o.y);i.lineTo(k.x,k.y);i.closePath();i.clip();m=a.rb;o=a.sb;k=a.tb;n=this.ra.Vm;f=512*b;c*=512;b>=n?(a.rb={x:(f+m.x)/n-512,y:(c+m.y)/n},a.sb={x:(f+o.x)/n-512,y:(c+o.y)/n},a.tb={x:(f+k.x)/n-512,y:(c+k.y)/n},a=this.Bk(a,e),i.transform(a[0],a[1],a[2],a[3],a[4],a[5]),i.drawImage(this.Mk[1],0,0)):(a.rb={x:(f+m.x)/n,y:(c+m.y)/n},a.sb={x:(f+o.x)/n,y:(c+o.y)/n},a.tb= {x:(f+k.x)/n,y:(c+k.y)/n},a=this.Bk(a,e),i.transform(a[0],a[1],a[2],a[3],a[4],a[5]),i.drawImage(this.Mk[0],0,0))}i.restore()},gD:function(a,b,c,e,f,g){if(2==this.ri[b][c].lm){var i=this.sg,k=0,m=0,n=0,o=0,p=this.ra.$p;f&&(1==f?m=p:-1==f&&(k=p));g&&(-1==g?n=p:1==g&&(o=p));f=this.ra.Sm;k=this.mg(a.rb,b,c,e,-f,-k,0,n);if(!k)return t;n=this.mg(a.sb,b,c,e,f,m,f,n);if(!n)return t;e=this.mg(a.tb,b,c,e,0,m,-f,-o);if(!e)return t;e={rb:k,sb:n,tb:e};m=e.rb;o=e.sb;n=e.tb;i.save();i.beginPath();i.moveTo(m.x,m.y); i.lineTo(o.x,o.y);i.lineTo(n.x,n.y);i.closePath();i.clip();a=this.Bk(a,e);i.transform(a[0],a[1],a[2],a[3],a[4],a[5]);i.drawImage(this.ri[b][c].xe,0,0)}else{i=this.sg;o=n=m=k=0;p=0.0078125;f&&(1==f?m=p:-1==f&&(k=p));g&&(-1==g?n=p:1==g&&(o=p));f=0.0078125;k=this.mg(a.rb,b,c,e,-f,-k,0,n);if(!k)return t;n=this.mg(a.sb,b,c,e,f,m,f,n);if(!n)return t;e=this.mg(a.tb,b,c,e,0,m,-f,-o);if(!e)return t;e={rb:k,sb:n,tb:e};m=e.rb;o=e.sb;n=e.tb;i.save();i.beginPath();i.moveTo(m.x,m.y);i.lineTo(o.x,o.y);i.lineTo(n.x, n.y);i.closePath();i.clip();m=a.rb;o=a.sb;n=a.tb;k=this.ra.Vm;f=512*b;c*=512;b>=k?(a.rb={x:(f+m.x)/k-512,y:(c+m.y)/k},a.sb={x:(f+o.x)/k-512,y:(c+o.y)/k},a.tb={x:(f+n.x)/k-512,y:(c+n.y)/k},a=this.Bk(a,e),i.transform(a[0],a[1],a[2],a[3],a[4],a[5]),i.drawImage(this.Mk[1],0,0)):(a.rb={x:(f+m.x)/k,y:(c+m.y)/k},a.sb={x:(f+o.x)/k,y:(c+o.y)/k},a.tb={x:(f+n.x)/k,y:(c+n.y)/k},a=this.Bk(a,e),i.transform(a[0],a[1],a[2],a[3],a[4],a[5]),i.drawImage(this.Mk[0],0,0))}i.restore()},mg:function(a,b,c,e,f,g,i,k){e=this.ra; b=2*(1/e.Qt*a.x/512+b/e.Qt)*Math.PI;c=(0.5-1/e.Vm*a.y/512-c/e.Vm)*Math.PI;a=Math.sin(Math.abs(c));0.4>a?a=0.4:0.98<a&&(a=e.Pt);var b=b+(f+g)*a,c=c+(i+k)*a,m=e.Ba,f=Math.cos(c),a=-f*Math.cos(b),c=Math.sin(c),n=f*Math.sin(b),b=e.ks,f=e.gu,g=e.oq,i=e.pq,k=e.qq,o=g*a+c*i+n*k;if(0>o)return t;m=m*m/o;a=m*a-g;c=m*c-i;n=m*n-k;o=Math.pow(a,2)+Math.pow(c,2)+Math.pow(n,2);m=Math.sqrt(o);0==o?f=b=0:(o=(a*f+n*b)/m,1<o?o=1:-1>o&&(o=-1),o=Math.acos(o),0>a*b*i+c*f*k-c*b*g-n*i*f&&(o=2*Math.PI-o),b=m*Math.cos(o),f= m*Math.sin(o));return{x:e.a/2+b,y:e.eb/2-f}},$X:function(a){var b=this.P.ga();if(b){var c=this.ra,e=c.heading,f=c.Ba,g=c.pitch+c.gs/180*Math.PI,e=e-c.Qf/180*Math.PI,i=6378137*-(a.lat-b.lat)/180*Math.PI,k=6378137*(a.lng-b.lng)/180*Math.PI,a=Math.sqrt(Math.pow(i,2)+Math.pow(k,2)),m=Math.cos(g),b=Math.cos(e),e=Math.sin(e),n=-f*m*b,g=f*Math.sin(g),m=f*m*e,o=n*i+-2.08*g+k*m;if(0>o)return t;var o=f*f/o,i=o*i-n,f=-2.08*o-g,o=o*k-m,p=Math.pow(i,2)+Math.pow(f,2)+Math.pow(o,2),k=Math.sqrt(p);0==p?e=b=0:(p= (i*e+o*b)/k,1<p?p=1:-1>p&&(p=-1),p=Math.acos(p),0>i*b*g+f*e*m-f*b*n-o*g*e&&(p=2*Math.PI-p),b=k*Math.cos(p),e=k*Math.sin(p));return{x:c.a/2+b,y:c.eb/2-e,Ba:a}}return t},Ea:function(a,b,c){var e=Math.pow(a,2),f=Math.pow(c,2);return{heading:0<c?Math.PI-Math.acos(a/Math.sqrt(e+f)):Math.PI+Math.acos(a/Math.sqrt(e+f)),pitch:Math.asin(b/Math.sqrt(e+Math.pow(b,2)+f))}},Bk:function(a,b){var c=a.rb.x,e=a.rb.y,f=a.sb.y,g=a.tb.y,i=b.rb.x,k=b.rb.y,m=b.sb.x,n=b.sb.y,o=b.tb.x,p=b.tb.y,v=f-e,x=g-e,y=a.sb.x-c,A=a.tb.x- c,v=f-e,x=g-e,f=o-i,n=n-k,m=m-i,p=p-k,g=A*v-y*x,v=[(f*v-m*x)/g,(p*v-n*x)/g,(f*y-m*A)/-g,(n*A-p*y)/g];v[4]=i-v[0]*c-v[2]*e;v[5]=k-v[3]*e-v[1]*c;return v}});function wj(a){this.P=a;this.Za=this.ea=s;this.Iv=0;this.Sh=a.Sh;this.DA=new uj;this.Xg={};this.po={};this.Lo={Xh:0.0010,width:0,height:0,heading:0,pitch:0};this.P.ra=this;this.uH=0;this.Td={};this.ba()} z.extend(wj.prototype,{ba:function(){var a=this,b=a.P;b.addEventListener("size_changed",function(){a.Td={}});b.addEventListener("zoom_changed_inner",function(){a.Td={}})},setData:function(a){if(!this.ea||!(a&&a.id==this.ea.id))a?(this.qp(),this.Hl=this.ea?this.ea.id:s,this.rB=this.ea?this.ea.tiles.dirNorth:s,this.SI=this.zA||s):this.rB=this.Hl=s,this.BJ=t,this.Td={},this.ea=a,this.zA=this.P.Ea().heading},qp:function(){if(ke()){var a=this.P.fw.style,b=this.P.gw.style;a.kc=a.WebkitTransition="";b.kc= b.WebkitTransition="";a.visibility=b.visibility="hidden";a.opacity=b.opacity="0"}for(var c in this.Xg)this.po[c]=this.Xg[c],this.po[c].style.zIndex="0"},va:function(a,b,c){if(this.ea){var e=a.heading,f=a.pitch,g=this.P,i,k,m,n,o,p;this.Td[this.ea.id]?(g=this.Td[this.ea.id],k=g.Qf,m=g.UO,n=g.WO,o=g.QK,i=g.Xh,p=g.zC,g=g.yC):(i=this.ea.tiles,k=i.dirNorth,p=g.La().clientWidth,g=g.La().clientHeight,m=i.hp(b),n=i.Lx(b),o=360/m,i=360/(m*c),this.Td[this.ea.id]={Qf:k,UO:m,WO:n,QK:o,Xh:i,zC:p,yC:g},this.LQ()); this.SI=this.zA||s;this.zA=e;ke()&&this.MU(e,f,b,c,i,p,g,k,m,n);this.Yg=c;k+=e;m=90-f;var f=Math.floor(k/o),e=Math.floor(m/o),v=p/2;k=Math.round(v-k%o/i);0>f&&(k-=c);m=Math.round(g/2-m%o/i);var x,y;this.rB&&(x=this.rB+this.SI,y=Math.round(v-x%o/i),x=Math.floor(x/o));x&&0>x&&(y-=c);o=e-Math.ceil(m/c);v=e+Math.ceil((g-m-c)/c);o=0>o?0:o;var v=v>n-1?n-1:v,A=f+Math.ceil((p-k-c)/c);n=[];for(var E={},c=f-Math.ceil(k/c);c<=A;c++)for(var C=o;C<=v;C++)n.push([c,C]),E[this.Dq(this.ea.id,c,C,b)]=q;for(F in this.Xg)if(!E[F]&& !this.po[F]&&(c=this.Xg[F]))c.style.display="none";this.Iv+=n.length;n.sort(function(a){return function(b,c){return 0.4*Math.abs(b[0]-a[0])+0.6*Math.abs(b[1]-a[1])-(0.4*Math.abs(c[0]-a[0])+0.6*Math.abs(c[1]-a[1]))}}([f,e]));for(c=0;c<n.length;c++)this.NU(n[c][0],n[c][1],b,f,e,k,m,x,y);this.Lo.Xh=i;this.Lo.width=p;this.Lo.height=g;this.Lo.heading=a.heading;this.Lo.pitch=a.pitch}else{for(var F in this.Xg)(c=this.Xg[F])&&c.parentNode&&c.parentNode.removeChild(c),delete this.Xg[F];ke()&&(a=this.P.fw, b=this.P.gw,a&&(a.src=b.src="",a.style.visibility=b.style.visibility="hidden"))}},MU:function(a,b,c,e,f,g,i,k,m,n){var c=this.P.fw,o=this.P.gw,p=this,e=[m*e,n*e];if(!this.BJ||e!==this.uH)this.BJ=q,c.style.kc=c.style.WebkitTransition=o.style.kc=o.style.WebkitTransition="opacity 300ms ease-out",c.style.width=o.style.width=e[0]+"px",c.style.height=o.style.height=e[1]+"px",c.style.BV=o.style.BV=e[0]+"px, "+e[1]+"px",c.style.left=Math.round((0-k-a)/f+g/2)+"px",o.style.left=parseInt(c.style.left)+e[0]+ "px",c.onload=function(){-1!=this.src.indexOf("pos=0_0&z=1")&&p.P.dispatchEvent(new P("onthumbnail_complete"))},m="http://pcsv0.map.bdimg.com/scape/?qt=pdata&sid="+this.ea.id+"&pos=0_0&z=1",c.src!=m&&(c.src=o.src=m);this.uH=e;a=Math.round((0-k-a)%360/f+g/2);c.style.top=o.style.top=Math.round(-(90-b)/f+i/2)+"px";0<a&&0>a-e[0]?(c.style.left=a+"px",o.style.left=parseInt(c.style.left)-e[0]+"px"):0<=a-e[0]?(o.style.left=a-e[0]+"px",c.style.left=parseInt(o.style.left)-e[0]+"px"):a+e[0]<g&&0<=a+e[0]?(c.style.left= a+"px",o.style.left=parseInt(c.style.left)+e[0]+"px"):0>a+e[0]?(o.style.left=a+e[0]+"px",c.style.left=parseInt(o.style.left)+e[0]+"px"):(c.style.left=a+"px",o.style.left=a+e[0]+"px")},NU:function(a,b,c,e,f,g,i,k,m){var n=this.ea.tiles,o=this.Yg,p=n.hp(c),v=this.po[this.Dq(this.Hl,a,b,c)];v&&(v=v.style,v.top=Math.round((b-f)*o+i)+"px",v.left=Math.round((a-(k||e))*o+(m||g))+"px",v.width=v.height=Math.ceil(o)+"px");var k=this.Dq(this.ea.id,a,b,c),x=this.Xg[k];if(x)n=x.style,n.top=Math.round((b-f)*o+ i)+"px",n.left=Math.round((a-e)*o+g)+"px",n.width=n.height=Math.ceil(o)+"px",n.zIndex="2",n.display="",this.Af(),x.QB.x=a,x.QB.y=b;else{x=this.DA.zx();x.setAttribute("errorCount",0);for(m=a;0>m;)m=p+m;var y=n.getTilesUrl(this.ea.id,new Q((m+p)%p,b),c),A=this;x.onload=function(){A.Af();A.Sh.appendChild(this);x.style.opacity="1"};x.onerror=function(){var a=this,b=a.getAttribute("errorCount");if(b<3){b++;setTimeout(function(){a.src=y},100);a.setAttribute("errorCount",b)}else A.Af()};n=x.style;n.position= "absolute";n.zIndex="2";n.border="none";n.display="";n.WebkitUserSelect="none";n.WebkitUserDrag="none";n.width=n.height=Math.ceil(o)+"px";n.top=Math.round((b-f)*o+i)+"px";n.left=Math.round((a-e)*o+g)+"px";n.opacity="0";n.kc=n.WebkitTransition="opacity 300ms ease-out";x.src=y;0<x.width&&(n.opacity="1");x.QB={id:this.ea.id,zoom:c,x:a,y:b};x.ontouchstart=function(a){a.preventDefault()};this.Xg[k]=x}},US:function(a){var b=this.Xg[a];b&&(delete b.QB,b.onerror=b.onload=b.ontouchstart=s,b.style.display= "none");delete this.Xg[a];delete this.po[a];b&&(b.kA=q,b.src="")},Qc:function(a,b){var c=this.cf;this.cf=b;c!=this.cf&&this.qp()},Nk:function(){this.Td={}},Af:function(){var a=this;this.Iv--;if(0==this.Iv){for(var b in this.po)(function(){var c=b;setTimeout(function(){a.US(c)},300)})();this.P.dispatchEvent(new P("ontilesloaded"))}},Dq:function(a,b,c,e){var f=this.ea.tiles.hp(e),g=(b+f)%f;0==e&&(g=(b+2*f)%(2*f));return a+"_"+g+"_"+c+"_"+e},YX:function(a){var b=this.Lo,c=this.P.ga();if(c){var e=6378137* -(a.lat-c.lat)/180*Math.PI,f=6378137*(a.lng-c.lng)/180*Math.PI,a=Math.sqrt(Math.pow(e,2)+Math.pow(f,2)),c=b.heading%360-b.Xh*b.width/2,g=b.pitch+b.Xh*b.height/2,e=this.Ea(e,-2.08,f);return{x:(180*(e.heading%360/Math.PI)-c)/b.Xh,y:(g-180*(e.pitch/Math.PI))/b.Xh,Ba:a}}return t},Ea:function(a,b,c){var e=Math.pow(a,2),f=Math.pow(c,2);return{heading:0<c?Math.PI-Math.acos(a/Math.sqrt(e+f)):Math.PI+Math.acos(a/Math.sqrt(e+f)),pitch:Math.asin(b/Math.sqrt(e+Math.pow(b,2)+f))}},LQ:function(){var a=this.P,b= a.fh().height/2*this.Td[this.ea.id].Xh;a.H_(-90+b,90-b)}});function oj(a){this.P=a;a.WA=this;this.eg=[];this.fH=[];this.zq=[];this.eH=[];this.pl=[];this.fI=[];this.ew=[];this.$E=this.KI=this.JI=this.fv=this.ev=0;this.Yw={};this.Gt=this.fy=t;Nb()?this.hf=this.Zz():Ob()?(this.Ya=this.Wz(),this.Fh=this.Ya.getContext("2d"),this.vQ(),this.Zn=[]):Mb()&&(this.rw=this.rR());this.Za=s;this.fz="#549eef";this.Td={};this.Un=this.Tj=q;this.ba()} var zj=[[0,-11],[4,-6.5],[2,-6.5],[2,-3],[-2,-3],[-2,-6.5],[-4,-6.5]],Aj=[[5,-2],[5,-12],[-5,-12],[-5,-2]],Bj=[[0,-110],[40,-65],[20,-65],[20,-30],[-20,-30],[-20,-65],[-40,-65]],Cj=[[45,-20],[45,-120],[-45,-120],[-45,-20]],Dj="\\u5317 \\u4e1c\\u5317 \\u4e1c \\u4e1c\\u5357 \\u5357 \\u897f\\u5357 \\u897f \\u897f\\u5317 \\u5317".split(" "); z.extend(oj.prototype,{Zz:function(){var a=L("svg",{version:"1.1",overflow:"hidden"},"http://www.w3.org/2000/svg"),b=this.P,c=b.La().clientWidth,e=b.La().clientHeight;this.hf=a;this.ev=c;this.fv=e;this.JI=b.La().offsetLeft||0;this.KI=b.La().offsetTop||0;this.oJ(c,e);a.style.position="absolute";a.style.left="0";a.style.top="0";a.style.WebkitUserSelect=a.style.OP=a.style.MozUserSelect="none";b.Uj.appendChild(a);Nb()&&"cvsRender"==this.P.dk&&this.A_(a);return a},A_:function(a){var b=this,c=L("ellipse", {fill:"white","fill-rule":"evenodd","fill-opacity":"0.4",cx:"100",cy:"100",rx:"20",ry:"20"},"http://www.w3.org/2000/svg");c.setAttribute("visibility","hidden");this.jD=c;a.appendChild(c);a=this.P.La();z.M(a,"mousedown",function(a){b.$E=(new Date).valueOf();b.j_(a.offsetX||a.layerX||0,a.offsetY||a.layerY||0)});z.M(a,"mouseup",function(){200>(new Date).valueOf()-b.$E&&(b.fy&&b.P.j.visible)&&b.click();b.$E=0});z.M(a,"mousemove",function(a){b.k_(a.offsetX||a.layerX||0,a.offsetY||a.layerY||0);b.v_(a.offsetX|| a.layerX||0,a.offsetY||a.layerY||0)});z.ca.Te?z.M(a,"mouseout",function(a){var c=a.clientX||a.pageX||0,a=a.clientY||a.pageY||0,c=c-b.JI,a=a-b.KI;(40>Math.abs(Math.abs(c-b.ev/2)-b.ev/2)||40>Math.abs(Math.abs(a-b.fv/2)-b.fv/2))&&b.jD.setAttribute("visibility","hidden")}):z.M(a,"mouseout",function(){b.jD.setAttribute("visibility","hidden")});b.P.addEventListener("hide_ellipse",function(){b.Gt=q});b.P.addEventListener("show_ellipse",function(){b.Gt=t})},Wz:function(){var a=L("canvas"),b=this.P,c=b.La().clientWidth, e=b.La().clientHeight;this.Ya=a;a.width=c;a.height=e;var f=a.style;f.position="absolute";f.top=f.left="0";f.width=c+"px";f.height=e+"px";f.q1="rgba(0, 0, 0, 0)";b.Uj.appendChild(a);return a},rR:function(){var a=this.P.La(),b=a.clientWidth,a=a.clientHeight;return Ab(this.P.Uj,[\'<v:group style="behavior:url(#default#VML);z-index:1;width:\',b+"px;height:"+a+\'px;position:absolute;left:0;top:0;"\',\'coordsize="\'+b+","+a+\'" \',\'coordorigin="-\'+b/2+",-"+0.75*a+\'">\',"</v:group>"].join(""))},ba:function(){var a= this,b=a.P;b.addEventListener("size_changed",function(b){b=b.size;a.Ce(b.width,b.height);a.Td={}});b.addEventListener("zoom_changed_inner",function(){a.Td={}})},Ce:function(a,b){if(Nb())this.oJ(a,b);else if(Ob()){var c=this.Ya;c.width=a;c.height=b;c.style.width=a+"px";c.style.height=b+"px"}},oJ:function(a,b){this.hf&&(this.hf.setAttribute("width",a+"px"),this.hf.setAttribute("height",b+"px"),this.hf.setAttribute("viewBox","0 0 "+a+" "+b),this.ev=a,this.fv=b)},setData:ba("ea"),va:function(a,b,c,e){if(this.ea){this.Da= a;this.Mc=b;this.Yg=c;var f=(a.heading+360)%360,a=a.pitch,g=this.P,i=this.ea.tiles,k,m,n;this.Td[this.ea.id]?(c=this.Td[this.ea.id],k=c.zC,m=c.yC,c=c.Xh):(k=g.La().clientWidth,m=g.La().clientHeight,n=i.hp(b),b=i.Lx(b),c=360/(n*c),this.Td[this.ea.id]={zC:k,yC:m,UO:n,WO:b,QK:360/n,Xh:c});g=g.fa()+1;g=0.75*m+1*(a/c)/g;g<m/2&&(g=m/2);m=(90-a)/200;c=10;Nb()?this.UI(this.ea.id,this.ea.links):Ob()?(this.Fh.clearRect(0,0,this.Ya.width,this.Ya.height),this.ea.id!=this.Za&&(this.Zn.length=0)):Mb()&&(c=36,this.ZT(this.ea.id, this.ea.links));for(n=0;n<this.ea.links.length;n++){var b=this.ea.links[n].dir-f,b=(360+b)%360,i=k/2+50*Math.sin(Pb(b)),o=g-60*Math.cos(Pb(b))*m;if(Nb())this.CR(n,[k/2,g],[c,c*m],b),this.OR(n,[i,o]);else if(Ob()){var p="#444";e==n&&(p=this.fz);this.yH([k/2,g+3],[c,c*m],b,"#000",0.4);this.yH([k/2,g],[c,c*m],b,"#fff",0.95,p);this.PR(n,[i,o],p);b=[k/2+60*Math.sin(Pb(b)),g-50*Math.cos(Pb(b))*m];this.Zn[n]=[Math.round(b[0]),Math.round(b[1])]}else Mb()&&(i=1,-30<a&&(i=80>a- -30?i+Math.tan(Pb(a- -30)):i+ 6),i=-Math.sin(Pb(Math.min(a,-30)))/i,this.rw.coordorigin=Math.round(-k/2)+","+Math.round(-g+40),this.DR(n,b,i))}}else Nb()?this.UI(s,[]):(this.Fh.clearRect(0,0,this.Ya.width,this.Ya.height),this.Zn.length=0)},UI:function(a,b){if(a!=this.Za){this.Za=a;linksCount=b.length;if(linksCount>this.eg.length){for(var c=linksCount-this.eg.length,e=this.hf,f=0;f<c;f++){var g=L("path",{fill:"black","fill-rule":"evenodd","fill-opacity":"0.4",stroke:"none"},"http://www.w3.org/2000/svg"),i=L("path",{fill:"white", "fill-rule":"evenodd","fill-opacity":"0.9",stroke:"#444","stroke-width":"0.2","stroke-linecap":"round"},"http://www.w3.org/2000/svg"),k=["M"],m=zj;k.push(m[0][0]);k.push(m[0][1]);for(var n=1;n<m.length;n++)k.push("L"),k.push(m[n][0]),k.push(m[n][1]);k.push(m[0][0]);k.push(m[0][1]);g.setAttribute("d",k.join(" "));this.zq.push(g);e.appendChild(g);i.setAttribute("d",k.join(" "));this.eg.push(i);e.appendChild(i);n=L("text",{fill:"#444","fill-rule":"evenodd",stroke:"none","font-size":"16px","font-family":"arial", "text-anchor":"middle","alignment-baseline":"middle"},"http://www.w3.org/2000/svg");n.style.WebkitUserSelect=n.style.OP=n.style.MozUserSelect="none";this.ew.push(n);e.appendChild(n);var g=L("path",{fill:"white","fill-rule":"evenodd","fill-opacity":"0",stroke:"none",cursor:"pointer"},"http://www.w3.org/2000/svg"),o=this;g.cH=i;g.AJ=n;g.style.MozUserSelect="none";i=["M"];k=Aj;i.push(k[0][0]);i.push(k[0][1]);for(n=1;n<k.length;n++)i.push("L"),i.push(k[n][0]),i.push(k[n][1]);g.setAttribute("d",i.join(" ")); z.M(g,H()?"touchstart":"mousedown",function(a){o.so={heading:o.Da.heading,pitch:o.Da.pitch};o.Jq=this;this.cH.setAttribute("stroke",o.fz);this.AJ.setAttribute("fill",o.fz);Cb(a)});this.pl.push(g);e.appendChild(g);z.M(g,"mousemove",function(){o.Gt=q});z.M(g,"mouseout",function(){o.Gt=t})}z.M(this.P.C,H()?"touchend":"mouseup",function(a){if(o.Jq){if(o.Da.heading==o.so.heading&&o.Da.pitch==o.so.pitch){var b=o.Jq.getAttribute("sid");b&&(o.P.dispatchEvent(new P("onlinkclick")),o.P.vc(b));o.c2=o.d2=s}o.Jq.cH.setAttribute("stroke", "#444");o.Jq.AJ.setAttribute("fill","#444");o.so=s;o.Jq=s;a.stopPropagation()}})}for(f=0;f<this.eg.length;f++)c="",f<linksCount?(e=this.ew[f],0<e.childNodes.length&&e.removeChild(e.childNodes[0]),n=this.OH(b[f].dir),e.appendChild(document.createTextNode(n)),this.pl[f].setAttribute("sid",b[f].id)):c="none",this.eg[f].style.display=c,this.zq[f].style.display=c,this.ew[f].style.display=c,this.pl[f].style.display=c}},vQ:function(){var a=this,b=a.P;z.M(this.Ya,"touchstart",function(b){a.so={heading:a.Da.heading, pitch:a.Da.pitch};var e=Bb(a.Ya);a.xv=a.KL(b.touches[0].clientX-e.left,b.touches[0].clientY-e.top);a.va(a.Da,a.Mc,a.Yg,a.xv)});z.M(this.Ya,"touchend",function(){a.Da.heading==a.so.heading&&a.Da.pitch==a.so.pitch&&a.ea.links[a.xv]&&(b.dispatchEvent(new P("onlinkclick")),b.vc(a.ea.links[a.xv].id));a.xv=s})},KL:function(a,b){for(var c=0;c<this.Zn.length;c++)if(30>Math.abs(a-this.Zn[c][0])&&30>Math.abs(b-this.Zn[c][1]))return c;return-1},ZT:function(a,b){if(a!=this.Za){this.Za=a;linksCount=b.length;if(linksCount> this.eg.length)for(var c=linksCount-this.eg.length,e=0;e<c;e++){for(var f=[],g=Bj,i=0;i<g.length;i++)f.push(g[i][0]),f.push(g[i][1]);f.push(g[0][0]);f.push(g[0][1]);i=Ab(this.rw,["<v:polyline ",\'points="\'+f.join(" ")+\'"\',\' style="behavior:url(#default#VML);z-index:1;left:0;top:0;" stroked="false"><v:fill style="behavior:url(#default#VML)" color="black" opacity="0.5"></v:fill><v:skew style="behavior:url(#default#VML)" on="true" origin="0,1.5" matrix="1,0,0,1,0,0" offset="0,0.05"></v:skew></v:polyline>\'].join("")); this.zq.push(i);this.eH.push(i.getElementsByTagName("skew")[0]);i=Ab(this.rw,["<v:polyline ",\'points="\'+f.join(" ")+\'"\',\'fillcolor="white" stroked="true" strokecolor="#444" strokeweight="1"  style="behavior:url(#default#VML);z-index:2;left:0;top:0;"><v:fill style="behavior:url(#default#VML)" color="white" opacity="1"></v:fill><v:skew style="behavior:url(#default#VML)" on="true" origin="0,1.5" matrix="1,0,0,1,0,0" offset="0,0"></v:skew></v:polyline>\'].join(""));this.eg.push(i);this.fH.push(i.getElementsByTagName("skew")[0]); f=[];g=Cj;for(i=0;i<g.length;i++)f.push(g[i][0]),f.push(g[i][1]);var i=Ab(this.rw,[\'<v:polyline style="cursor:pointer;z-index:3;behavior:url(#default#VML);"\',\' points="\'+f.join(" ")+\'" stroked="false">\',\'<v:fill style="behavior:url(#default#VML);" color="white" opacity="0"></v:fill><v:skew style="behavior:url(#default#VML);" on="true" origin="0,1.2" matrix="1,0,0,1,0,0"></v:skew></v:polyline>\'].join("")),k=this;z.M(i,"click",function(a){(a=a.srcElement.sid)&&k.P.vc(a)});this.pl.push(i);this.fI.push(i.getElementsByTagName("skew")[0])}for(e= 0;e<this.eg.length;e++)c="",e<linksCount?this.pl[e].sid=b[e].id:c="none",this.eg[e].style.display=c,this.zq[e].style.display=c,this.pl[e].style.display=c}},CR:function(a,b,c,e){this.eg[a]&&(this.eg[a].setAttribute("transform","translate("+b[0]+" "+b[1]+")scale("+c[0]+" "+c[1]+")rotate("+e+")"),this.zq[a].setAttribute("transform","translate("+b[0]+" "+(b[1]+4)+")scale("+c[0]+" "+c[1]+")rotate("+e+")"),this.pl[a].setAttribute("transform","translate("+b[0]+" "+b[1]+")scale("+c[0]+" "+c[1]+")rotate("+ e+")"))},OR:function(a,b){this.ew[a].setAttribute("transform","translate("+b[0]+" "+b[1]+")")},DR:function(a,b,c){this.Az(this.fH[a],b,0,c);this.Az(this.eH[a],b,0.6,c);this.Az(this.fI[a],b,0,c)},Az:function(a,b,c,e){var f=Pb(b),b=Math.cos(f),f=Math.sin(f);a.matrix=[Number(b).toFixed(4),Number(-f).toFixed(4),Number(f*e).toFixed(4),Number(b*e).toFixed(4),0,0].join();a.offset="0,"+Math.round(50*c)/1E3},yH:function(a,b,c,e,f,g){var i=this.Fh;i.save();i.fillStyle=e;i.globalAlpha=f;i.translate(a[0],a[1]); i.scale(b[0],b[1]);i.rotate(Pb(c));i.beginPath();i.moveTo(zj[0][0],zj[0][1]);for(a=1;a<zj.length;a++)i.lineTo(zj[a][0],zj[a][1]);i.closePath();i.fill();g&&(i.lineWidth="0.2",i.lineCap="round",i.lineJoin="round",i.strokeStyle=g,i.stroke());i.restore()},PR:function(a,b,c){var e=this.Fh;e.save();a=this.OH(this.ea.links[a].dir);e.font="16px sans-serif";e.textAlign="center";e.textBaseline="middle";e.fillStyle=c;e.fillText(a,b[0],b[1]);e.restore()},OH:function(a){return Dj[Math.ceil(Math.floor((a+360)% 360/22.5)/2)]},dM:ca(0),click:function(){if(!(2<Math.abs(this.SW-this.rZ)||2<Math.abs(this.WK-this.WK))){var a=this.Yw;if(a){for(var b=this.ea.links,c=this.JX(a.PG,a.QG),e=30,f=0,g=0;g<b.length;g++)f=b[g].dir-c,f=180<Math.abs(f)?b[g].dir>c?360+c-b[g].dir:360-c+b[g].dir:Math.abs(f),f<e&&(e=f);29>e&&(b=this.P.ga(),this.P.sa(new J(b.lng+180*a.QG/Math.PI/6378137,b.lat-180*a.PG/Math.PI/6378137)))}}},j_:function(a,b){this.SW=a;this.WK=b},k_:ba("rZ"),v_:function(a,b){this.Yw=coord=this.P.ra.OD(a,b);var c= this.jD;if(coord&&!this.Gt&&this.P.j.visible){c.setAttribute("cx",a);c.setAttribute("cy",b);var e=Math.abs(2*coord.wF/(coord.Ba-2)-2*coord.wF/(coord.Ba+2))/2,f=2*coord.wF/coord.Ba;if(4<coord.Ba){if(100<f){var g=f/100,f=100,e=e/g;e>f&&(e=f)}c.setAttribute("rx",f);c.setAttribute("ry",e);c.setAttribute("visibility","visible");this.fy=q}else this.fy=t,c.setAttribute("visibility","hidden")}else this.fy=t,c.setAttribute("visibility","hidden")},JX:function(a,b){var c=0;0<a?c=180*(Math.atan(b/a)/Math.PI): 0>a?c=180*(Math.atan(b/a)/Math.PI)+180:0==a&&(c=0<b?90:-90);heading=180-c;360<heading?heading-=360:0>heading&&(heading+=360);return heading},U:function(){this.Tj=t;this.P.Uj&&(this.P.Uj.style.visibility="hidden")},show:function(){this.Tj=q;this.P.Uj&&(this.P.Uj.style.visibility="visible")},kD:function(){this.Un=q},SC:function(){this.Un=t}});var Ej={get:function(a,b){var c="inst_"+b;Ej[c]||(Ej[c]=new Fj(a));return Ej[c]}};function Fj(a){this.jb=a;this.Tu=new Uint8Array(16);this.Sq=new Uint8Array(16)}z.extend(Fj.prototype,{rE:function(){for(var a=0,b=this.Tu.length;a<b;a++)this.Tu[a]=0},So:function(a){this.Tu[a]=1;0===this.Sq[a]&&(this.jb.enableVertexAttribArray(a),this.Sq[a]=1)},VC:function(){for(var a=0,b=this.Sq.length;a<b;a++)this.Sq[a]!==this.Tu[a]&&(this.jb.disableVertexAttribArray(a),this.Sq[a]=0)}});if(!Gj)var Gj=1.0E-6;if(!Hj)var Hj="undefined"!==typeof Float32Array?Float32Array:Array;if(!Ij)var Ij=Math.random;var Jj={q5:function(a){Hj=a}};"undefined"!==typeof exports&&(exports.R3=Jj);var Kj=Math.PI/180;Jj.C5=function(a){return a*Kj}; var Lj={create:function(){var a=new Hj(2);a[0]=0;a[1]=0;return a},$h:function(a){var b=new Hj(2);b[0]=a[0];b[1]=a[1];return b},ug:function(a,b){var c=new Hj(2);c[0]=a;c[1]=b;return c},copy:function(a,b){a[0]=b[0];a[1]=b[1];return a},set:function(a,b,c){a[0]=b;a[1]=c;return a},add:function(a,b,c){a[0]=b[0]+c[0];a[1]=b[1]+c[1];return a},ou:function(a,b,c){a[0]=b[0]-c[0];a[1]=b[1]-c[1];return a}};Lj.sub=Lj.ou;Lj.multiply=function(a,b,c){a[0]=b[0]*c[0];a[1]=b[1]*c[1];return a};Lj.Rm=Lj.multiply; Lj.Cs=function(a,b,c){a[0]=b[0]/c[0];a[1]=b[1]/c[1];return a};Lj.UK=Lj.Cs;Lj.min=function(a,b,c){a[0]=Math.min(b[0],c[0]);a[1]=Math.min(b[1],c[1]);return a};Lj.max=function(a,b,c){a[0]=Math.max(b[0],c[0]);a[1]=Math.max(b[1],c[1]);return a};Lj.scale=function(a,b,c){a[0]=b[0]*c;a[1]=b[1]*c;return a};Lj.iO=function(a,b,c,e){a[0]=b[0]+c[0]*e;a[1]=b[1]+c[1]*e;return a};Lj.oc=function(a,b){var c=b[0]-a[0],e=b[1]-a[1];return Math.sqrt(c*c+e*e)};Lj.Ba=Lj.oc; Lj.ku=function(a,b){var c=b[0]-a[0],e=b[1]-a[1];return c*c+e*e};Lj.EO=Lj.ku;Lj.length=function(a){var b=a[0],a=a[1];return Math.sqrt(b*b+a*a)};Lj.NE=Lj.length;Lj.Vk=function(a){var b=a[0],a=a[1];return b*b+a*a};Lj.RF=Lj.Vk;Lj.pN=function(a,b){a[0]=-b[0];a[1]=-b[1];return a};Lj.normalize=function(a,b){var c=b[0],e=b[1],c=c*c+e*e;if(c>0){c=1/Math.sqrt(c);a[0]=b[0]*c;a[1]=b[1]*c}return a};Lj.Fs=function(a,b){return a[0]*b[0]+a[1]*b[1]};Lj.ax=function(a,b,c){b=b[0]*c[1]-b[1]*c[0];a[0]=a[1]=0;a[2]=b;return a}; Lj.dy=function(a,b,c,e){var f=b[0],b=b[1];a[0]=f+e*(c[0]-f);a[1]=b+e*(c[1]-b);return a};Lj.random=function(a,b){var b=b||1,c=Ij()*2*Math.PI;a[0]=Math.cos(c)*b;a[1]=Math.sin(c)*b;return a};Lj.I5=function(a,b,c){var e=b[0],b=b[1];a[0]=c[0]*e+c[2]*b;a[1]=c[1]*e+c[3]*b;return a};Lj.J5=function(a,b,c){var e=b[0],b=b[1];a[0]=c[0]*e+c[2]*b+c[4];a[1]=c[1]*e+c[3]*b+c[5];return a};Lj.y0=function(a,b,c){var e=b[0],b=b[1];a[0]=c[0]*e+c[3]*b+c[6];a[1]=c[1]*e+c[4]*b+c[7];return a}; Lj.YO=function(a,b,c){var e=b[0],b=b[1];a[0]=c[0]*e+c[4]*b+c[12];a[1]=c[1]*e+c[5]*b+c[13];return a};Lj.forEach=function(){var a=Lj.create();return function(b,c,e,f,g,i){c||(c=2);e||(e=0);for(f=f?Math.min(f*c+e,b.length):b.length;e<f;e=e+c){a[0]=b[e];a[1]=b[e+1];g(a,a,i);b[e]=a[0];b[e+1]=a[1]}return b}}();Lj.kn=function(a){return"vec2("+a[0]+", "+a[1]+")"};"undefined"!==typeof exports&&(exports.O5=Lj); var Mj={create:function(){var a=new Hj(3);a[0]=0;a[1]=0;a[2]=0;return a},$h:function(a){var b=new Hj(3);b[0]=a[0];b[1]=a[1];b[2]=a[2];return b},ug:function(a,b,c){var e=new Hj(3);e[0]=a;e[1]=b;e[2]=c;return e},copy:function(a,b){a[0]=b[0];a[1]=b[1];a[2]=b[2];return a},set:function(a,b,c,e){a[0]=b;a[1]=c;a[2]=e;return a},add:function(a,b,c){a[0]=b[0]+c[0];a[1]=b[1]+c[1];a[2]=b[2]+c[2];return a},ou:function(a,b,c){a[0]=b[0]-c[0];a[1]=b[1]-c[1];a[2]=b[2]-c[2];return a}};Mj.sub=Mj.ou; Mj.multiply=function(a,b,c){a[0]=b[0]*c[0];a[1]=b[1]*c[1];a[2]=b[2]*c[2];return a};Mj.Rm=Mj.multiply;Mj.Cs=function(a,b,c){a[0]=b[0]/c[0];a[1]=b[1]/c[1];a[2]=b[2]/c[2];return a};Mj.UK=Mj.Cs;Mj.min=function(a,b,c){a[0]=Math.min(b[0],c[0]);a[1]=Math.min(b[1],c[1]);a[2]=Math.min(b[2],c[2]);return a};Mj.max=function(a,b,c){a[0]=Math.max(b[0],c[0]);a[1]=Math.max(b[1],c[1]);a[2]=Math.max(b[2],c[2]);return a};Mj.scale=function(a,b,c){a[0]=b[0]*c;a[1]=b[1]*c;a[2]=b[2]*c;return a}; Mj.iO=function(a,b,c,e){a[0]=b[0]+c[0]*e;a[1]=b[1]+c[1]*e;a[2]=b[2]+c[2]*e;return a};Mj.oc=function(a,b){var c=b[0]-a[0],e=b[1]-a[1],f=b[2]-a[2];return Math.sqrt(c*c+e*e+f*f)};Mj.Ba=Mj.oc;Mj.ku=function(a,b){var c=b[0]-a[0],e=b[1]-a[1],f=b[2]-a[2];return c*c+e*e+f*f};Mj.EO=Mj.ku;Mj.length=function(a){var b=a[0],c=a[1],a=a[2];return Math.sqrt(b*b+c*c+a*a)};Mj.NE=Mj.length;Mj.Vk=function(a){var b=a[0],c=a[1],a=a[2];return b*b+c*c+a*a};Mj.RF=Mj.Vk; Mj.pN=function(a,b){a[0]=-b[0];a[1]=-b[1];a[2]=-b[2];return a};Mj.normalize=function(a,b){var c=b[0],e=b[1],f=b[2],c=c*c+e*e+f*f;if(c>0){c=1/Math.sqrt(c);a[0]=b[0]*c;a[1]=b[1]*c;a[2]=b[2]*c}return a};Mj.Fs=function(a,b){return a[0]*b[0]+a[1]*b[1]+a[2]*b[2]};Mj.ax=function(a,b,c){var e=b[0],f=b[1],b=b[2],g=c[0],i=c[1],c=c[2];a[0]=f*c-b*i;a[1]=b*g-e*c;a[2]=e*i-f*g;return a};Mj.dy=function(a,b,c,e){var f=b[0],g=b[1],b=b[2];a[0]=f+e*(c[0]-f);a[1]=g+e*(c[1]-g);a[2]=b+e*(c[2]-b);return a}; Mj.random=function(a,b){var b=b||1,c=Ij()*2*Math.PI,e=Ij()*2-1,f=Math.sqrt(1-e*e)*b;a[0]=Math.cos(c)*f;a[1]=Math.sin(c)*f;a[2]=e*b;return a};Mj.YO=function(a,b,c){var e=b[0],f=b[1],b=b[2];a[0]=c[0]*e+c[4]*f+c[8]*b+c[12];a[1]=c[1]*e+c[5]*f+c[9]*b+c[13];a[2]=c[2]*e+c[6]*f+c[10]*b+c[14];return a};Mj.y0=function(a,b,c){var e=b[0],f=b[1],b=b[2];a[0]=e*c[0]+f*c[3]+b*c[6];a[1]=e*c[1]+f*c[4]+b*c[7];a[2]=e*c[2]+f*c[5]+b*c[8];return a}; Mj.A0=function(a,b,c){var e=b[0],f=b[1],g=b[2],b=c[0],i=c[1],k=c[2],c=c[3],m=c*e+i*g-k*f,n=c*f+k*e-b*g,o=c*g+b*f-i*e,e=-b*e-i*f-k*g;a[0]=m*c+e*-b+n*-k-o*-i;a[1]=n*c+e*-i+o*-b-m*-k;a[2]=o*c+e*-k+m*-i-n*-b;return a};Mj.cO=function(a,b,c,e){var f=[],g=[];f[0]=b[0]-c[0];f[1]=b[1]-c[1];f[2]=b[2]-c[2];g[0]=f[0];g[1]=f[1]*Math.cos(e)-f[2]*Math.sin(e);g[2]=f[1]*Math.sin(e)+f[2]*Math.cos(e);a[0]=g[0]+c[0];a[1]=g[1]+c[1];a[2]=g[2]+c[2];return a}; Mj.dO=function(a,b,c,e){var f=[],g=[];f[0]=b[0]-c[0];f[1]=b[1]-c[1];f[2]=b[2]-c[2];g[0]=f[2]*Math.sin(e)+f[0]*Math.cos(e);g[1]=f[1];g[2]=f[2]*Math.cos(e)-f[0]*Math.sin(e);a[0]=g[0]+c[0];a[1]=g[1]+c[1];a[2]=g[2]+c[2];return a};Mj.eO=function(a,b,c,e){var f=[],g=[];f[0]=b[0]-c[0];f[1]=b[1]-c[1];f[2]=b[2]-c[2];g[0]=f[0]*Math.cos(e)-f[1]*Math.sin(e);g[1]=f[0]*Math.sin(e)+f[1]*Math.cos(e);g[2]=f[2];a[0]=g[0]+c[0];a[1]=g[1]+c[1];a[2]=g[2]+c[2];return a}; Mj.forEach=function(){var a=Mj.create();return function(b,c,e,f,g,i){c||(c=3);e||(e=0);for(f=f?Math.min(f*c+e,b.length):b.length;e<f;e=e+c){a[0]=b[e];a[1]=b[e+1];a[2]=b[e+2];g(a,a,i);b[e]=a[0];b[e+1]=a[1];b[e+2]=a[2]}return b}}();Mj.kn=function(a){return"vec3("+a[0]+", "+a[1]+", "+a[2]+")"};"undefined"!==typeof exports&&(exports.P5=Mj); var Z={create:function(){var a=new Hj(4);a[0]=0;a[1]=0;a[2]=0;a[3]=0;return a},$h:function(a){var b=new Hj(4);b[0]=a[0];b[1]=a[1];b[2]=a[2];b[3]=a[3];return b},ug:function(a,b,c,e){var f=new Hj(4);f[0]=a;f[1]=b;f[2]=c;f[3]=e;return f},copy:function(a,b){a[0]=b[0];a[1]=b[1];a[2]=b[2];a[3]=b[3];return a},set:function(a,b,c,e,f){a[0]=b;a[1]=c;a[2]=e;a[3]=f;return a},add:function(a,b,c){a[0]=b[0]+c[0];a[1]=b[1]+c[1];a[2]=b[2]+c[2];a[3]=b[3]+c[3];return a},ou:function(a,b,c){a[0]=b[0]-c[0];a[1]=b[1]-c[1]; a[2]=b[2]-c[2];a[3]=b[3]-c[3];return a}};Z.sub=Z.ou;Z.multiply=function(a,b,c){a[0]=b[0]*c[0];a[1]=b[1]*c[1];a[2]=b[2]*c[2];a[3]=b[3]*c[3];return a};Z.Rm=Z.multiply;Z.Cs=function(a,b,c){a[0]=b[0]/c[0];a[1]=b[1]/c[1];a[2]=b[2]/c[2];a[3]=b[3]/c[3];return a};Z.UK=Z.Cs;Z.min=function(a,b,c){a[0]=Math.min(b[0],c[0]);a[1]=Math.min(b[1],c[1]);a[2]=Math.min(b[2],c[2]);a[3]=Math.min(b[3],c[3]);return a}; Z.max=function(a,b,c){a[0]=Math.max(b[0],c[0]);a[1]=Math.max(b[1],c[1]);a[2]=Math.max(b[2],c[2]);a[3]=Math.max(b[3],c[3]);return a};Z.scale=function(a,b,c){a[0]=b[0]*c;a[1]=b[1]*c;a[2]=b[2]*c;a[3]=b[3]*c;return a};Z.iO=function(a,b,c,e){a[0]=b[0]+c[0]*e;a[1]=b[1]+c[1]*e;a[2]=b[2]+c[2]*e;a[3]=b[3]+c[3]*e;return a};Z.oc=function(a,b){var c=b[0]-a[0],e=b[1]-a[1],f=b[2]-a[2],g=b[3]-a[3];return Math.sqrt(c*c+e*e+f*f+g*g)};Z.Ba=Z.oc; Z.ku=function(a,b){var c=b[0]-a[0],e=b[1]-a[1],f=b[2]-a[2],g=b[3]-a[3];return c*c+e*e+f*f+g*g};Z.EO=Z.ku;Z.length=function(a){var b=a[0],c=a[1],e=a[2],a=a[3];return Math.sqrt(b*b+c*c+e*e+a*a)};Z.NE=Z.length;Z.Vk=function(a){var b=a[0],c=a[1],e=a[2],a=a[3];return b*b+c*c+e*e+a*a};Z.RF=Z.Vk;Z.pN=function(a,b){a[0]=-b[0];a[1]=-b[1];a[2]=-b[2];a[3]=-b[3];return a}; Z.normalize=function(a,b){var c=b[0],e=b[1],f=b[2],g=b[3],c=c*c+e*e+f*f+g*g;if(c>0){c=1/Math.sqrt(c);a[0]=b[0]*c;a[1]=b[1]*c;a[2]=b[2]*c;a[3]=b[3]*c}return a};Z.Fs=function(a,b){return a[0]*b[0]+a[1]*b[1]+a[2]*b[2]+a[3]*b[3]};Z.dy=function(a,b,c,e){var f=b[0],g=b[1],i=b[2],b=b[3];a[0]=f+e*(c[0]-f);a[1]=g+e*(c[1]-g);a[2]=i+e*(c[2]-i);a[3]=b+e*(c[3]-b);return a};Z.random=function(a,b){b=b||1;a[0]=Ij();a[1]=Ij();a[2]=Ij();a[3]=Ij();Z.normalize(a,a);Z.scale(a,a,b);return a}; Z.YO=function(a,b,c){var e=b[0],f=b[1],g=b[2],b=b[3];a[0]=c[0]*e+c[4]*f+c[8]*g+c[12]*b;a[1]=c[1]*e+c[5]*f+c[9]*g+c[13]*b;a[2]=c[2]*e+c[6]*f+c[10]*g+c[14]*b;a[3]=c[3]*e+c[7]*f+c[11]*g+c[15]*b;return a};Z.A0=function(a,b,c){var e=b[0],f=b[1],g=b[2],b=c[0],i=c[1],k=c[2],c=c[3],m=c*e+i*g-k*f,n=c*f+k*e-b*g,o=c*g+b*f-i*e,e=-b*e-i*f-k*g;a[0]=m*c+e*-b+n*-k-o*-i;a[1]=n*c+e*-i+o*-b-m*-k;a[2]=o*c+e*-k+m*-i-n*-b;return a}; Z.forEach=function(){var a=Z.create();return function(b,c,e,f,g,i){c||(c=4);e||(e=0);for(f=f?Math.min(f*c+e,b.length):b.length;e<f;e=e+c){a[0]=b[e];a[1]=b[e+1];a[2]=b[e+2];a[3]=b[e+3];g(a,a,i);b[e]=a[0];b[e+1]=a[1];b[e+2]=a[2];b[e+3]=a[3]}return b}}();Z.kn=function(a){return"vec4("+a[0]+", "+a[1]+", "+a[2]+", "+a[3]+")"};"undefined"!==typeof exports&&(exports.Q5=Z); var Nj={create:function(){var a=new Hj(4);a[0]=1;a[1]=0;a[2]=0;a[3]=1;return a},$h:function(a){var b=new Hj(4);b[0]=a[0];b[1]=a[1];b[2]=a[2];b[3]=a[3];return b},copy:function(a,b){a[0]=b[0];a[1]=b[1];a[2]=b[2];a[3]=b[3];return a},Em:function(a){a[0]=1;a[1]=0;a[2]=0;a[3]=1;return a},aP:function(a,b){if(a===b){var c=b[1];a[1]=b[2];a[2]=c}else{a[0]=b[0];a[1]=b[2];a[2]=b[1];a[3]=b[3]}return a},np:function(a,b){var c=b[0],e=b[1],f=b[2],g=b[3],i=c*g-f*e;if(!i)return s;i=1/i;a[0]=g*i;a[1]=-e*i;a[2]=-f*i; a[3]=c*i;return a},bK:function(a,b){var c=b[0];a[0]=b[3];a[1]=-b[1];a[2]=-b[2];a[3]=c;return a},PC:function(a){return a[0]*a[3]-a[2]*a[1]},multiply:function(a,b,c){var e=b[0],f=b[1],g=b[2],b=b[3],i=c[0],k=c[1],m=c[2],c=c[3];a[0]=e*i+g*k;a[1]=f*i+b*k;a[2]=e*m+g*c;a[3]=f*m+b*c;return a}};Nj.Rm=Nj.multiply;Nj.rotate=function(a,b,c){var e=b[0],f=b[1],g=b[2],b=b[3],i=Math.sin(c),c=Math.cos(c);a[0]=e*c+g*i;a[1]=f*c+b*i;a[2]=e*-i+g*c;a[3]=f*-i+b*c;return a}; Nj.scale=function(a,b,c){var e=b[1],f=b[2],g=b[3],i=c[0],c=c[1];a[0]=b[0]*i;a[1]=e*i;a[2]=f*c;a[3]=g*c;return a};Nj.kn=function(a){return"mat2("+a[0]+", "+a[1]+", "+a[2]+", "+a[3]+")"};Nj.CD=function(a){return Math.sqrt(Math.pow(a[0],2)+Math.pow(a[1],2)+Math.pow(a[2],2)+Math.pow(a[3],2))};Nj.j1=function(a,b,c,e){a[2]=e[2]/e[0];c[0]=e[0];c[1]=e[1];c[3]=e[3]-a[2]*c[1];return[a,b,c]};"undefined"!==typeof exports&&(exports.t4=Nj); var Oj={create:function(){var a=new Hj(6);a[0]=1;a[1]=0;a[2]=0;a[3]=1;a[4]=0;a[5]=0;return a},$h:function(a){var b=new Hj(6);b[0]=a[0];b[1]=a[1];b[2]=a[2];b[3]=a[3];b[4]=a[4];b[5]=a[5];return b},copy:function(a,b){a[0]=b[0];a[1]=b[1];a[2]=b[2];a[3]=b[3];a[4]=b[4];a[5]=b[5];return a},Em:function(a){a[0]=1;a[1]=0;a[2]=0;a[3]=1;a[4]=0;a[5]=0;return a},np:function(a,b){var c=b[0],e=b[1],f=b[2],g=b[3],i=b[4],k=b[5],m=c*g-e*f;if(!m)return s;m=1/m;a[0]=g*m;a[1]=-e*m;a[2]=-f*m;a[3]=c*m;a[4]=(f*k-g*i)*m;a[5]= (e*i-c*k)*m;return a},PC:function(a){return a[0]*a[3]-a[1]*a[2]},multiply:function(a,b,c){var e=b[0],f=b[1],g=b[2],i=b[3],k=b[4],b=b[5],m=c[0],n=c[1],o=c[2],p=c[3],v=c[4],c=c[5];a[0]=e*m+g*n;a[1]=f*m+i*n;a[2]=e*o+g*p;a[3]=f*o+i*p;a[4]=e*v+g*c+k;a[5]=f*v+i*c+b;return a}};Oj.Rm=Oj.multiply;Oj.rotate=function(a,b,c){var e=b[0],f=b[1],g=b[2],i=b[3],k=b[4],b=b[5],m=Math.sin(c),c=Math.cos(c);a[0]=e*c+g*m;a[1]=f*c+i*m;a[2]=e*-m+g*c;a[3]=f*-m+i*c;a[4]=k;a[5]=b;return a}; Oj.scale=function(a,b,c){var e=b[1],f=b[2],g=b[3],i=b[4],k=b[5],m=c[0],c=c[1];a[0]=b[0]*m;a[1]=e*m;a[2]=f*c;a[3]=g*c;a[4]=i;a[5]=k;return a};Oj.translate=function(a,b,c){var e=b[0],f=b[1],g=b[2],i=b[3],k=b[4],b=b[5],m=c[0],c=c[1];a[0]=e;a[1]=f;a[2]=g;a[3]=i;a[4]=e*m+g*c+k;a[5]=f*m+i*c+b;return a};Oj.kn=function(a){return"mat2d("+a[0]+", "+a[1]+", "+a[2]+", "+a[3]+", "+a[4]+", "+a[5]+")"}; Oj.CD=function(a){return Math.sqrt(Math.pow(a[0],2)+Math.pow(a[1],2)+Math.pow(a[2],2)+Math.pow(a[3],2)+Math.pow(a[4],2)+Math.pow(a[5],2)+1)};"undefined"!==typeof exports&&(exports.u4=Oj); var Pj={create:function(){var a=new Hj(9);a[0]=1;a[1]=0;a[2]=0;a[3]=0;a[4]=1;a[5]=0;a[6]=0;a[7]=0;a[8]=1;return a},b3:function(a,b){a[0]=b[0];a[1]=b[1];a[2]=b[2];a[3]=b[4];a[4]=b[5];a[5]=b[6];a[6]=b[8];a[7]=b[9];a[8]=b[10];return a},$h:function(a){var b=new Hj(9);b[0]=a[0];b[1]=a[1];b[2]=a[2];b[3]=a[3];b[4]=a[4];b[5]=a[5];b[6]=a[6];b[7]=a[7];b[8]=a[8];return b},copy:function(a,b){a[0]=b[0];a[1]=b[1];a[2]=b[2];a[3]=b[3];a[4]=b[4];a[5]=b[5];a[6]=b[6];a[7]=b[7];a[8]=b[8];return a},Em:function(a){a[0]= 1;a[1]=0;a[2]=0;a[3]=0;a[4]=1;a[5]=0;a[6]=0;a[7]=0;a[8]=1;return a},aP:function(a,b){if(a===b){var c=b[1],e=b[2],f=b[5];a[1]=b[3];a[2]=b[6];a[3]=c;a[5]=b[7];a[6]=e;a[7]=f}else{a[0]=b[0];a[1]=b[3];a[2]=b[6];a[3]=b[1];a[4]=b[4];a[5]=b[7];a[6]=b[2];a[7]=b[5];a[8]=b[8]}return a},np:function(a,b){var c=b[0],e=b[1],f=b[2],g=b[3],i=b[4],k=b[5],m=b[6],n=b[7],o=b[8],p=o*i-k*n,v=-o*g+k*m,x=n*g-i*m,y=c*p+e*v+f*x;if(!y)return s;y=1/y;a[0]=p*y;a[1]=(-o*e+f*n)*y;a[2]=(k*e-f*i)*y;a[3]=v*y;a[4]=(o*c-f*m)*y;a[5]= (-k*c+f*g)*y;a[6]=x*y;a[7]=(-n*c+e*m)*y;a[8]=(i*c-e*g)*y;return a},bK:function(a,b){var c=b[0],e=b[1],f=b[2],g=b[3],i=b[4],k=b[5],m=b[6],n=b[7],o=b[8];a[0]=i*o-k*n;a[1]=f*n-e*o;a[2]=e*k-f*i;a[3]=k*m-g*o;a[4]=c*o-f*m;a[5]=f*g-c*k;a[6]=g*n-i*m;a[7]=e*m-c*n;a[8]=c*i-e*g;return a},PC:function(a){var b=a[3],c=a[4],e=a[5],f=a[6],g=a[7],i=a[8];return a[0]*(i*c-e*g)+a[1]*(-i*b+e*f)+a[2]*(g*b-c*f)},multiply:function(a,b,c){var e=b[0],f=b[1],g=b[2],i=b[3],k=b[4],m=b[5],n=b[6],o=b[7],b=b[8],p=c[0],v=c[1],x= c[2],y=c[3],A=c[4],E=c[5],C=c[6],F=c[7],c=c[8];a[0]=p*e+v*i+x*n;a[1]=p*f+v*k+x*o;a[2]=p*g+v*m+x*b;a[3]=y*e+A*i+E*n;a[4]=y*f+A*k+E*o;a[5]=y*g+A*m+E*b;a[6]=C*e+F*i+c*n;a[7]=C*f+F*k+c*o;a[8]=C*g+F*m+c*b;return a}};Pj.Rm=Pj.multiply;Pj.translate=function(a,b,c){var e=b[0],f=b[1],g=b[2],i=b[3],k=b[4],m=b[5],n=b[6],o=b[7],b=b[8],p=c[0],c=c[1];a[0]=e;a[1]=f;a[2]=g;a[3]=i;a[4]=k;a[5]=m;a[6]=p*e+c*i+n;a[7]=p*f+c*k+o;a[8]=p*g+c*m+b;return a}; Pj.rotate=function(a,b,c){var e=b[0],f=b[1],g=b[2],i=b[3],k=b[4],m=b[5],n=b[6],o=b[7],b=b[8],p=Math.sin(c),c=Math.cos(c);a[0]=c*e+p*i;a[1]=c*f+p*k;a[2]=c*g+p*m;a[3]=c*i-p*e;a[4]=c*k-p*f;a[5]=c*m-p*g;a[6]=n;a[7]=o;a[8]=b;return a};Pj.scale=function(a,b,c){var e=c[0],c=c[1];a[0]=e*b[0];a[1]=e*b[1];a[2]=e*b[2];a[3]=c*b[3];a[4]=c*b[4];a[5]=c*b[5];a[6]=b[6];a[7]=b[7];a[8]=b[8];return a};Pj.a3=function(a,b){a[0]=b[0];a[1]=b[1];a[2]=0;a[3]=b[2];a[4]=b[3];a[5]=0;a[6]=b[4];a[7]=b[5];a[8]=1;return a}; Pj.pX=function(a,b){var c=b[0],e=b[1],f=b[2],g=b[3],i=c+c,k=e+e,m=f+f,c=c*i,n=e*i,e=e*k,o=f*i,p=f*k,f=f*m,i=g*i,k=g*k,g=g*m;a[0]=1-e-f;a[3]=n-g;a[6]=o+k;a[1]=n+g;a[4]=1-c-f;a[7]=p-i;a[2]=o-k;a[5]=p+i;a[8]=1-c-e;return a}; Pj.A4=function(a,b){var c=b[0],e=b[1],f=b[2],g=b[3],i=b[4],k=b[5],m=b[6],n=b[7],o=b[8],p=b[9],v=b[10],x=b[11],y=b[12],A=b[13],E=b[14],C=b[15],F=c*k-e*i,D=c*m-f*i,I=c*n-g*i,R=e*m-f*k,M=e*n-g*k,N=f*n-g*m,fa=o*A-p*y,pa=o*E-v*y,o=o*C-x*y,wa=p*E-v*A,p=p*C-x*A,v=v*C-x*E,x=F*v-D*p+I*wa+R*o-M*pa+N*fa;if(!x)return s;x=1/x;a[0]=(k*v-m*p+n*wa)*x;a[1]=(m*o-i*v-n*pa)*x;a[2]=(i*p-k*o+n*fa)*x;a[3]=(f*p-e*v-g*wa)*x;a[4]=(c*v-f*o+g*pa)*x;a[5]=(e*o-c*p-g*fa)*x;a[6]=(A*N-E*M+C*R)*x;a[7]=(E*I-y*N-C*D)*x;a[8]=(y*M-A* I+C*F)*x;return a};Pj.kn=function(a){return"mat3("+a[0]+", "+a[1]+", "+a[2]+", "+a[3]+", "+a[4]+", "+a[5]+", "+a[6]+", "+a[7]+", "+a[8]+")"};Pj.CD=function(a){return Math.sqrt(Math.pow(a[0],2)+Math.pow(a[1],2)+Math.pow(a[2],2)+Math.pow(a[3],2)+Math.pow(a[4],2)+Math.pow(a[5],2)+Math.pow(a[6],2)+Math.pow(a[7],2)+Math.pow(a[8],2))};"undefined"!==typeof exports&&(exports.v4=Pj); var sj={create:function(){var a=new Hj(16);a[0]=1;a[1]=0;a[2]=0;a[3]=0;a[4]=0;a[5]=1;a[6]=0;a[7]=0;a[8]=0;a[9]=0;a[10]=1;a[11]=0;a[12]=0;a[13]=0;a[14]=0;a[15]=1;return a},$h:function(a){var b=new Hj(16);b[0]=a[0];b[1]=a[1];b[2]=a[2];b[3]=a[3];b[4]=a[4];b[5]=a[5];b[6]=a[6];b[7]=a[7];b[8]=a[8];b[9]=a[9];b[10]=a[10];b[11]=a[11];b[12]=a[12];b[13]=a[13];b[14]=a[14];b[15]=a[15];return b},copy:function(a,b){a[0]=b[0];a[1]=b[1];a[2]=b[2];a[3]=b[3];a[4]=b[4];a[5]=b[5];a[6]=b[6];a[7]=b[7];a[8]=b[8];a[9]=b[9]; a[10]=b[10];a[11]=b[11];a[12]=b[12];a[13]=b[13];a[14]=b[14];a[15]=b[15];return a},Em:function(a){a[0]=1;a[1]=0;a[2]=0;a[3]=0;a[4]=0;a[5]=1;a[6]=0;a[7]=0;a[8]=0;a[9]=0;a[10]=1;a[11]=0;a[12]=0;a[13]=0;a[14]=0;a[15]=1;return a},aP:function(a,b){if(a===b){var c=b[1],e=b[2],f=b[3],g=b[6],i=b[7],k=b[11];a[1]=b[4];a[2]=b[8];a[3]=b[12];a[4]=c;a[6]=b[9];a[7]=b[13];a[8]=e;a[9]=g;a[11]=b[14];a[12]=f;a[13]=i;a[14]=k}else{a[0]=b[0];a[1]=b[4];a[2]=b[8];a[3]=b[12];a[4]=b[1];a[5]=b[5];a[6]=b[9];a[7]=b[13];a[8]=b[2]; a[9]=b[6];a[10]=b[10];a[11]=b[14];a[12]=b[3];a[13]=b[7];a[14]=b[11];a[15]=b[15]}return a},np:function(a,b){var c=b[0],e=b[1],f=b[2],g=b[3],i=b[4],k=b[5],m=b[6],n=b[7],o=b[8],p=b[9],v=b[10],x=b[11],y=b[12],A=b[13],E=b[14],C=b[15],F=c*k-e*i,D=c*m-f*i,I=c*n-g*i,R=e*m-f*k,M=e*n-g*k,N=f*n-g*m,fa=o*A-p*y,pa=o*E-v*y,wa=o*C-x*y,Ma=p*E-v*A,sb=p*C-x*A,Za=v*C-x*E,Pa=F*Za-D*sb+I*Ma+R*wa-M*pa+N*fa;if(!Pa)return s;Pa=1/Pa;a[0]=(k*Za-m*sb+n*Ma)*Pa;a[1]=(f*sb-e*Za-g*Ma)*Pa;a[2]=(A*N-E*M+C*R)*Pa;a[3]=(v*M-p*N-x*R)* Pa;a[4]=(m*wa-i*Za-n*pa)*Pa;a[5]=(c*Za-f*wa+g*pa)*Pa;a[6]=(E*I-y*N-C*D)*Pa;a[7]=(o*N-v*I+x*D)*Pa;a[8]=(i*sb-k*wa+n*fa)*Pa;a[9]=(e*wa-c*sb-g*fa)*Pa;a[10]=(y*M-A*I+C*F)*Pa;a[11]=(p*I-o*M-x*F)*Pa;a[12]=(k*pa-i*Ma-m*fa)*Pa;a[13]=(c*Ma-e*pa+f*fa)*Pa;a[14]=(A*D-y*R-E*F)*Pa;a[15]=(o*R-p*D+v*F)*Pa;return a},bK:function(a,b){var c=b[0],e=b[1],f=b[2],g=b[3],i=b[4],k=b[5],m=b[6],n=b[7],o=b[8],p=b[9],v=b[10],x=b[11],y=b[12],A=b[13],E=b[14],C=b[15];a[0]=k*(v*C-x*E)-p*(m*C-n*E)+A*(m*x-n*v);a[1]=-(e*(v*C-x*E)-p* (f*C-g*E)+A*(f*x-g*v));a[2]=e*(m*C-n*E)-k*(f*C-g*E)+A*(f*n-g*m);a[3]=-(e*(m*x-n*v)-k*(f*x-g*v)+p*(f*n-g*m));a[4]=-(i*(v*C-x*E)-o*(m*C-n*E)+y*(m*x-n*v));a[5]=c*(v*C-x*E)-o*(f*C-g*E)+y*(f*x-g*v);a[6]=-(c*(m*C-n*E)-i*(f*C-g*E)+y*(f*n-g*m));a[7]=c*(m*x-n*v)-i*(f*x-g*v)+o*(f*n-g*m);a[8]=i*(p*C-x*A)-o*(k*C-n*A)+y*(k*x-n*p);a[9]=-(c*(p*C-x*A)-o*(e*C-g*A)+y*(e*x-g*p));a[10]=c*(k*C-n*A)-i*(e*C-g*A)+y*(e*n-g*k);a[11]=-(c*(k*x-n*p)-i*(e*x-g*p)+o*(e*n-g*k));a[12]=-(i*(p*E-v*A)-o*(k*E-m*A)+y*(k*v-m*p));a[13]= c*(p*E-v*A)-o*(e*E-f*A)+y*(e*v-f*p);a[14]=-(c*(k*E-m*A)-i*(e*E-f*A)+y*(e*m-f*k));a[15]=c*(k*v-m*p)-i*(e*v-f*p)+o*(e*m-f*k);return a},PC:function(a){var b=a[0],c=a[1],e=a[2],f=a[3],g=a[4],i=a[5],k=a[6],m=a[7],n=a[8],o=a[9],p=a[10],v=a[11],x=a[12],y=a[13],A=a[14],a=a[15];return(b*i-c*g)*(p*a-v*A)-(b*k-e*g)*(o*a-v*y)+(b*m-f*g)*(o*A-p*y)+(c*k-e*i)*(n*a-v*x)-(c*m-f*i)*(n*A-p*x)+(e*m-f*k)*(n*y-o*x)},multiply:function(a,b,c){var e=b[0],f=b[1],g=b[2],i=b[3],k=b[4],m=b[5],n=b[6],o=b[7],p=b[8],v=b[9],x=b[10], y=b[11],A=b[12],E=b[13],C=b[14],b=b[15],F=c[0],D=c[1],I=c[2],R=c[3];a[0]=F*e+D*k+I*p+R*A;a[1]=F*f+D*m+I*v+R*E;a[2]=F*g+D*n+I*x+R*C;a[3]=F*i+D*o+I*y+R*b;F=c[4];D=c[5];I=c[6];R=c[7];a[4]=F*e+D*k+I*p+R*A;a[5]=F*f+D*m+I*v+R*E;a[6]=F*g+D*n+I*x+R*C;a[7]=F*i+D*o+I*y+R*b;F=c[8];D=c[9];I=c[10];R=c[11];a[8]=F*e+D*k+I*p+R*A;a[9]=F*f+D*m+I*v+R*E;a[10]=F*g+D*n+I*x+R*C;a[11]=F*i+D*o+I*y+R*b;F=c[12];D=c[13];I=c[14];R=c[15];a[12]=F*e+D*k+I*p+R*A;a[13]=F*f+D*m+I*v+R*E;a[14]=F*g+D*n+I*x+R*C;a[15]=F*i+D*o+I*y+R*b;return a}}; sj.Rm=sj.multiply; sj.translate=function(a,b,c){var e=c[0],f=c[1],c=c[2],g,i,k,m,n,o,p,v,x,y,A,E;if(b===a){a[12]=b[0]*e+b[4]*f+b[8]*c+b[12];a[13]=b[1]*e+b[5]*f+b[9]*c+b[13];a[14]=b[2]*e+b[6]*f+b[10]*c+b[14];a[15]=b[3]*e+b[7]*f+b[11]*c+b[15]}else{g=b[0];i=b[1];k=b[2];m=b[3];n=b[4];o=b[5];p=b[6];v=b[7];x=b[8];y=b[9];A=b[10];E=b[11];a[0]=g;a[1]=i;a[2]=k;a[3]=m;a[4]=n;a[5]=o;a[6]=p;a[7]=v;a[8]=x;a[9]=y;a[10]=A;a[11]=E;a[12]=g*e+n*f+x*c+b[12];a[13]=i*e+o*f+y*c+b[13];a[14]=k*e+p*f+A*c+b[14];a[15]=m*e+v*f+E*c+b[15]}return a}; sj.scale=function(a,b,c){var e=c[0],f=c[1],c=c[2];a[0]=b[0]*e;a[1]=b[1]*e;a[2]=b[2]*e;a[3]=b[3]*e;a[4]=b[4]*f;a[5]=b[5]*f;a[6]=b[6]*f;a[7]=b[7]*f;a[8]=b[8]*c;a[9]=b[9]*c;a[10]=b[10]*c;a[11]=b[11]*c;a[12]=b[12];a[13]=b[13];a[14]=b[14];a[15]=b[15];return a}; sj.rotate=function(a,b,c,e){var f=e[0],g=e[1],e=e[2],i=Math.sqrt(f*f+g*g+e*e),k,m,n,o,p,v,x,y,A,E,C,F,D,I,R,M,N,fa,pa,wa;if(Math.abs(i)<Gj)return s;i=1/i;f=f*i;g=g*i;e=e*i;k=Math.sin(c);m=Math.cos(c);n=1-m;c=b[0];i=b[1];o=b[2];p=b[3];v=b[4];x=b[5];y=b[6];A=b[7];E=b[8];C=b[9];F=b[10];D=b[11];I=f*f*n+m;R=g*f*n+e*k;M=e*f*n-g*k;N=f*g*n-e*k;fa=g*g*n+m;pa=e*g*n+f*k;wa=f*e*n+g*k;f=g*e*n-f*k;g=e*e*n+m;a[0]=c*I+v*R+E*M;a[1]=i*I+x*R+C*M;a[2]=o*I+y*R+F*M;a[3]=p*I+A*R+D*M;a[4]=c*N+v*fa+E*pa;a[5]=i*N+x*fa+C*pa; a[6]=o*N+y*fa+F*pa;a[7]=p*N+A*fa+D*pa;a[8]=c*wa+v*f+E*g;a[9]=i*wa+x*f+C*g;a[10]=o*wa+y*f+F*g;a[11]=p*wa+A*f+D*g;if(b!==a){a[12]=b[12];a[13]=b[13];a[14]=b[14];a[15]=b[15]}return a};sj.cO=function(a,b,c){var e=Math.sin(c),c=Math.cos(c),f=b[4],g=b[5],i=b[6],k=b[7],m=b[8],n=b[9],o=b[10],p=b[11];if(b!==a){a[0]=b[0];a[1]=b[1];a[2]=b[2];a[3]=b[3];a[12]=b[12];a[13]=b[13];a[14]=b[14];a[15]=b[15]}a[4]=f*c+m*e;a[5]=g*c+n*e;a[6]=i*c+o*e;a[7]=k*c+p*e;a[8]=m*c-f*e;a[9]=n*c-g*e;a[10]=o*c-i*e;a[11]=p*c-k*e;return a}; sj.dO=function(a,b,c){var e=Math.sin(c),c=Math.cos(c),f=b[0],g=b[1],i=b[2],k=b[3],m=b[8],n=b[9],o=b[10],p=b[11];if(b!==a){a[4]=b[4];a[5]=b[5];a[6]=b[6];a[7]=b[7];a[12]=b[12];a[13]=b[13];a[14]=b[14];a[15]=b[15]}a[0]=f*c-m*e;a[1]=g*c-n*e;a[2]=i*c-o*e;a[3]=k*c-p*e;a[8]=f*e+m*c;a[9]=g*e+n*c;a[10]=i*e+o*c;a[11]=k*e+p*c;return a}; sj.eO=function(a,b,c){var e=Math.sin(c),c=Math.cos(c),f=b[0],g=b[1],i=b[2],k=b[3],m=b[4],n=b[5],o=b[6],p=b[7];if(b!==a){a[8]=b[8];a[9]=b[9];a[10]=b[10];a[11]=b[11];a[12]=b[12];a[13]=b[13];a[14]=b[14];a[15]=b[15]}a[0]=f*c+m*e;a[1]=g*c+n*e;a[2]=i*c+o*e;a[3]=k*c+p*e;a[4]=m*c-f*e;a[5]=n*c-g*e;a[6]=o*c-i*e;a[7]=p*c-k*e;return a}; sj.c3=function(a,b,c){var e=b[0],f=b[1],g=b[2],i=b[3],k=e+e,m=f+f,n=g+g,b=e*k,o=e*m,e=e*n,p=f*m,f=f*n,g=g*n,k=i*k,m=i*m,i=i*n;a[0]=1-(p+g);a[1]=o+i;a[2]=e-m;a[3]=0;a[4]=o-i;a[5]=1-(b+g);a[6]=f+k;a[7]=0;a[8]=e+m;a[9]=f-k;a[10]=1-(b+p);a[11]=0;a[12]=c[0];a[13]=c[1];a[14]=c[2];a[15]=1;return a}; sj.pX=function(a,b){var c=b[0],e=b[1],f=b[2],g=b[3],i=c+c,k=e+e,m=f+f,c=c*i,n=e*i,e=e*k,o=f*i,p=f*k,f=f*m,i=g*i,k=g*k,g=g*m;a[0]=1-e-f;a[1]=n+g;a[2]=o-k;a[3]=0;a[4]=n-g;a[5]=1-c-f;a[6]=p+i;a[7]=0;a[8]=o+k;a[9]=p-i;a[10]=1-c-e;a[11]=0;a[12]=0;a[13]=0;a[14]=0;a[15]=1;return a};sj.e3=function(a,b,c,e,f,g,i){var k=1/(c-b),m=1/(f-e),n=1/(g-i);a[0]=g*2*k;a[1]=0;a[2]=0;a[3]=0;a[4]=0;a[5]=g*2*m;a[6]=0;a[7]=0;a[8]=(c+b)*k;a[9]=(f+e)*m;a[10]=(i+g)*n;a[11]=-1;a[12]=0;a[13]=0;a[14]=i*g*2*n;a[15]=0;return a}; sj.NZ=function(a,b,c){var b=1/Math.tan(b/2),e=1/-999.99;a[0]=b/c;a[1]=0;a[2]=0;a[3]=0;a[4]=0;a[5]=b;a[6]=0;a[7]=0;a[8]=0;a[9]=0;a[10]=1000.01*e;a[11]=-1;a[12]=0;a[13]=0;a[14]=20*e;a[15]=0};sj.K4=function(a,b,c,e,f,g,i){var k=1/(b-c),m=1/(e-f),n=1/(g-i);a[0]=-2*k;a[1]=0;a[2]=0;a[3]=0;a[4]=0;a[5]=-2*m;a[6]=0;a[7]=0;a[8]=0;a[9]=0;a[10]=2*n;a[11]=0;a[12]=(b+c)*k;a[13]=(f+e)*m;a[14]=(i+g)*n;a[15]=1;return a}; sj.p4=function(a,b,c,e){var f,g,i,k,m,n,o,p,v=b[0],x=b[1],b=b[2];i=e[0];k=e[1];g=e[2];o=c[0];e=c[1];f=c[2];if(Math.abs(v-o)<Gj&&Math.abs(x-e)<Gj&&Math.abs(b-f)<Gj)return sj.Em(a);c=v-o;e=x-e;o=b-f;p=1/Math.sqrt(c*c+e*e+o*o);c=c*p;e=e*p;o=o*p;f=k*o-g*e;g=g*c-i*o;i=i*e-k*c;if(p=Math.sqrt(f*f+g*g+i*i)){p=1/p;f=f*p;g=g*p;i=i*p}else i=g=f=0;k=e*i-o*g;m=o*f-c*i;n=c*g-e*f;if(p=Math.sqrt(k*k+m*m+n*n)){p=1/p;k=k*p;m=m*p;n=n*p}else n=m=k=0;a[0]=f;a[1]=k;a[2]=c;a[3]=0;a[4]=g;a[5]=m;a[6]=e;a[7]=0;a[8]=i;a[9]= n;a[10]=o;a[11]=0;a[12]=-(f*v+g*x+i*b);a[13]=-(k*v+m*x+n*b);a[14]=-(c*v+e*x+o*b);a[15]=1;return a};sj.kn=function(a){return"mat4("+a[0]+", "+a[1]+", "+a[2]+", "+a[3]+", "+a[4]+", "+a[5]+", "+a[6]+", "+a[7]+", "+a[8]+", "+a[9]+", "+a[10]+", "+a[11]+", "+a[12]+", "+a[13]+", "+a[14]+", "+a[15]+")"}; sj.CD=function(a){return Math.sqrt(Math.pow(a[0],2)+Math.pow(a[1],2)+Math.pow(a[2],2)+Math.pow(a[3],2)+Math.pow(a[4],2)+Math.pow(a[5],2)+Math.pow(a[6],2)+Math.pow(a[6],2)+Math.pow(a[7],2)+Math.pow(a[8],2)+Math.pow(a[9],2)+Math.pow(a[10],2)+Math.pow(a[11],2)+Math.pow(a[12],2)+Math.pow(a[13],2)+Math.pow(a[14],2)+Math.pow(a[15],2))};"undefined"!==typeof exports&&(exports.w4=sj);var Qj={create:function(){var a=new Hj(4);a[0]=0;a[1]=0;a[2]=0;a[3]=1;return a}}; Qj.g5=function(){var a=Mj.create(),b=Mj.ug(1,0,0),c=Mj.ug(0,1,0);return function(e,f,g){var i=Mj.Fs(f,g);if(i<-0.999999){Mj.ax(a,b,f);Mj.length(a)<1.0E-6&&Mj.ax(a,c,f);Mj.normalize(a,a);Qj.r_(e,a);return e}if(i>0.999999){e[0]=0;e[1]=0;e[2]=0;e[3]=1;return e}Mj.ax(a,f,g);e[0]=a[0];e[1]=a[1];e[2]=a[2];e[3]=1+i;return Qj.normalize(e,e)}}(); Qj.k5=function(){var a=Pj.create();return function(b,c,e,f){a[0]=e[0];a[3]=e[1];a[6]=e[2];a[1]=f[0];a[4]=f[1];a[7]=f[2];a[2]=-c[0];a[5]=-c[1];a[8]=-c[2];return Qj.normalize(b,Qj.oX(b,a))}}();Qj.$h=Z.$h;Qj.ug=Z.ug;Qj.copy=Z.copy;Qj.set=Z.set;Qj.Em=function(a){a[0]=0;a[1]=0;a[2]=0;a[3]=1;return a};Qj.r_=function(a,b){var c=Math.PI,c=c*0.5,e=Math.sin(c);a[0]=e*b[0];a[1]=e*b[1];a[2]=e*b[2];a[3]=Math.cos(c)};Qj.add=Z.add; Qj.multiply=function(a,b,c){var e=b[0],f=b[1],g=b[2],b=b[3],i=c[0],k=c[1],m=c[2],c=c[3];a[0]=e*c+b*i+f*m-g*k;a[1]=f*c+b*k+g*i-e*m;a[2]=g*c+b*m+e*k-f*i;a[3]=b*c-e*i-f*k-g*m;return a};Qj.Rm=Qj.multiply;Qj.scale=Z.scale;Qj.cO=function(a,b,c){var c=c*0.5,e=b[0],f=b[1],g=b[2],b=b[3],i=Math.sin(c),c=Math.cos(c);a[0]=e*c+b*i;a[1]=f*c+g*i;a[2]=g*c-f*i;a[3]=b*c-e*i;return a}; Qj.dO=function(a,b,c){var c=c*0.5,e=b[0],f=b[1],g=b[2],b=b[3],i=Math.sin(c),c=Math.cos(c);a[0]=e*c-g*i;a[1]=f*c+b*i;a[2]=g*c+e*i;a[3]=b*c-f*i;return a};Qj.eO=function(a,b,c){var c=c*0.5,e=b[0],f=b[1],g=b[2],b=b[3],i=Math.sin(c),c=Math.cos(c);a[0]=e*c+f*i;a[1]=f*c-e*i;a[2]=g*c+b*i;a[3]=b*c-g*i;return a};Qj.s2=function(a,b){var c=b[0],e=b[1],f=b[2];a[0]=c;a[1]=e;a[2]=f;a[3]=-Math.sqrt(Math.abs(1-c*c-e*e-f*f));return a};Qj.Fs=Z.Fs;Qj.dy=Z.dy; Qj.t5=function(a,b,c,e){var f=b[0],g=b[1],i=b[2],b=b[3],k=c[0],m=c[1],n=c[2],c=c[3],o,p,v;p=f*k+g*m+i*n+b*c;if(p<0){p=-p;k=-k;m=-m;n=-n;c=-c}if(1-p>1.0E-6){o=Math.acos(p);v=Math.sin(o);p=Math.sin((1-e)*o)/v;e=Math.sin(e*o)/v}else p=1-e;a[0]=p*f+e*k;a[1]=p*g+e*m;a[2]=p*i+e*n;a[3]=p*b+e*c;return a};Qj.np=function(a,b){var c=b[0],e=b[1],f=b[2],g=b[3],i=c*c+e*e+f*f+g*g,i=i?1/i:0;a[0]=-c*i;a[1]=-e*i;a[2]=-f*i;a[3]=g*i;return a};Qj.y2=function(a,b){a[0]=-b[0];a[1]=-b[1];a[2]=-b[2];a[3]=b[3];return a}; Qj.length=Z.length;Qj.NE=Qj.length;Qj.Vk=Z.Vk;Qj.RF=Qj.Vk;Qj.normalize=Z.normalize;Qj.oX=function(a,b){var c=b[0]+b[4]+b[8];if(c>0){c=Math.sqrt(c+1);a[3]=0.5*c;c=0.5/c;a[0]=(b[7]-b[5])*c;a[1]=(b[2]-b[6])*c;a[2]=(b[3]-b[1])*c}else{var e=0;b[4]>b[0]&&(e=1);b[8]>b[e*3+e]&&(e=2);var f=(e+1)%3,g=(e+2)%3,c=Math.sqrt(b[e*3+e]-b[f*3+f]-b[g*3+g]+1);a[e]=0.5*c;c=0.5/c;a[3]=(b[g*3+f]-b[f*3+g])*c;a[f]=(b[f*3+e]+b[e*3+f])*c;a[g]=(b[g*3+e]+b[e*3+g])*c}return a}; Qj.kn=function(a){return"quat("+a[0]+", "+a[1]+", "+a[2]+", "+a[3]+")"};"undefined"!==typeof exports&&(exports.Z4=Qj);function Rj(a,b){this.Lf=a;this.Aq=b||2;this.MA=a-this.Aq;this.yc=[];this.Kg=[];this.uE()}z.extend(Rj.prototype,{uE:function(){var a,b;this.yc.push(0,-20,0);for(pg=0;360>pg;pg+=6)a=Math.cos(Pb(pg))*this.Lf,b=Math.sin(Pb(pg))*this.Lf,this.yc.push(a,-20,b)},sE:function(){for(var a=(this.yc.length-3)/3,b=1;b<a;b++)this.Kg.push(0,b,b+1);this.Kg.push(0,b,1)},RD:function(){0==this.Kg.length&&this.sE();return this.Kg}});function Sj(a,b){this.Lf=a;this.Aq=b||2;this.MA=a-this.Aq;this.yc=[];this.Kg=[];this.uE()} z.extend(Sj.prototype,{uE:function(){for(var a,b,c=0;360>c;c+=6)a=Math.cos(Pb(c))*this.MA,b=Math.sin(Pb(c))*this.MA,this.yc.push(a,-20,b);for(c=0;360>c;c+=6)a=Math.cos(Pb(c))*this.Lf,b=Math.sin(Pb(c))*this.Lf,this.yc.push(a,-20,b)},sE:function(){var a=this.yc.length/3/2;for(pg=0;pg<a-1;pg++)this.Kg.push(pg,pg+a+1,pg+a),this.Kg.push(pg,pg+1,pg+a+1);this.Kg.push(pg,a,pg+a);this.Kg.push(pg,0,a)},RD:function(){0==this.Kg.length&&this.sE();return this.Kg}});function Tj(a){this.Lf=a;this.Ud=Math.pow(2,4);this.Ir=this.Ud/2;this.yc=[];this.iA=[];a=360/this.Ud;this.NJ=Math.cos(Pb(3*a))*this.Lf;this.OJ=Math.cos(Pb(2*a))*this.Lf;this.PJ=Math.cos(Pb(1*a))*this.Lf;this.RY()} z.extend(Tj.prototype,{RY:function(){if(!(0<this.yc.length))for(var a=this.Lf,b=[],c=64/this.Ud,e=64/c,f=[],g=0,i=0,k,m=0;33>m;m++)for(var n=Math.cos(Pb(5.625*m-90))*a,n=Math.round(100*n)/100,o=Math.sin(Pb(5.625*m-90))*a,o=Math.round(100*o)/100,g=Math.floor(m/c),p=0;64>p;p++){var v=Math.cos(Pb(5.625*p))*n,x=Math.sin(Pb(5.625*p))*n,v=Math.round(100*v)/100,x=Math.round(100*x)/100;b.push(v,o,x);i=Math.floor(p/c);k=i+g*e;g<this.Ir&&(this.yc[k]||(this.yc[k]=[]),this.yc[k].push(v,o,x));if(0<g&&0==m%c){var y= i+(g-1)*e;this.yc[y].push(v,o,x);0<p&&0==p%c&&this.yc[y-1]&&this.yc[y-1].push(v,o,x);63==p&&this.yc[y].push(f[0],f[1],f[2])}0==i&&0==p&&(f=[v,o,x]);0<p&&0==p%c&&this.yc[k-1]&&this.yc[k-1].push(v,o,x);g<this.Ir&&63==p&&this.yc[k].push(f[0],f[1],f[2])}},QY:function(){for(var a=64/this.Ud,b=a+1,c=0;c<a;c++)for(var e=0;e<a;e++){var f=c*b,g=f+e+1;this.iA.push(f+e,g,f+e+1+b);g=f+e+1+b;this.iA.push(f+e,g,f+b+e)}return this.iA},tX:function(a,b,c){var e=this.yc[Math.round(this.yc.length/2)];if(e){for(var f= [],g=Math.pow(2,5-a),b=b%g,c=c%g,i=64/this.Ud,k=1/i,m=1/(32/this.Ir),n=0;n<e.length/3;n++){var o=n%(i+1)*k,p=Math.floor(n/(i+1))*m,o=o/g+1*b/g,p=p/g+1*c/g;1===a&&(p*=2);f.push(o,p)}return f}},FX:function(a){var b=0,c=0,c=a[0],b=a[1],e=a[2],a=Math.round(180*Math.atan(Math.abs(e)/Math.abs(c))/Math.PI);0<c?0>e&&(a=360-a):a=0<e?180-a:180+a;c=b>this.PJ?7:b>this.OJ?6:b>this.NJ?5:0<b?4:b>-this.NJ?3:b>-this.OJ?2:b>-this.PJ?1:0;b=Math.floor(a/22.5);return[b,c,b+c*(64/(64/this.Ud))]}});function lj(a){this.P=a;this.ne=this.jb=this.Ya=this.Ui=s;var b=this;this.Vr=new tj(6,{Jo:function(a){for(var e=0;e<a.length;e++)if(a[e])for(var f=0;f<a[e].length;f++)a[e][f]&&(b.jb.deleteTexture(a[e][f].Zy),a[e][f].Es=t)}});this.CJ=new tj(32,{Jo:function(a){a&&(a.kA=q,a.src="")}});this.DA=new uj;this.Ud=16;this.Ir=8;this.Lf=500;this.Qj=this.Vi=0;this.Jf=[sj.create(),sj.create()];this.Nh=sj.create();this.fB=[sj.create(),sj.create()];this.sr=sj.create();this.dB=[];this.Sl=this.to=this.kw=s;this.Qr= {};this.dS();this.RB=0;this.nv=[s,s];this.EA=[s,s];this.ng=[];this.Cd=H()?2:1;this.Li()} z.extend(lj.prototype,{dS:function(){this.ql=[];for(var a=2;5>=a;a++)this.ql[a]={cols:Math.pow(2,a-1),rows:Math.pow(2,a-1)/2};this.ql[1]={cols:1,rows:1}},Li:function(){this.Wz();this.jb=this.Ya.getContext("webgl",{alpha:t});this.tw=Ej.get(this.jb,this.P.aa);this.JA();this.GU();this.nA();this.oA();this.XT();this.tw.rE()},Wz:function(){var a=this.Ya=L("canvas"),b=this.Ya.style,c=this.P.C,e=c.clientWidth,c=c.clientHeight;b.width=e+"px";b.height=c+"px";this.Vi=a.width=e*this.Cd;this.Qj=a.height=c*this.Cd; this.P.Sh.appendChild(this.Ya)},GU:function(){var a=this.jb,b=this.Ya;a.clearColor(0,0,0,1);a.clear(a.COLOR_BUFFER_BIT|a.DEPTH_BUFFER_BIT);a.disable(a.DEPTH_TEST);a.enable(a.BLEND);a.depthMask(q);a.viewport(0,0,b.width,b.height)},Nk:function(){var a=this.P.C,b=a.clientWidth,a=a.clientHeight;this.Ya.style.width=b+"px";this.Ya.style.height=a+"px";this.Vi=this.Ya.width=b*this.Cd;this.Qj=this.Ya.height=a*this.Cd;this.Tq=s;b=this.Ya;this.jb.viewport(0,0,b.width,b.height)},JA:function(){var a=this.jb,b= this.ne=a.createProgram(),c=this.wl("attribute vec3 aVertexPosition;\\nattribute vec2 aVertexTextureCoordBack;\\nattribute vec2 aVertexTextureCoord;\\nvarying mediump vec2 vTextureCoordBack;\\nvarying mediump vec2 vTextureCoord;\\nuniform mat4 uMVMatrix;\\nuniform mat4 uPMatrix;\\nvoid main(void) {\\n    gl_Position = uPMatrix * uMVMatrix * vec4(aVertexPosition, 1.0);\\n    vTextureCoordBack = aVertexTextureCoordBack;\\n    vTextureCoord = aVertexTextureCoord;\\n}",a.VERTEX_SHADER),e=this.wl("precision mediump float;\\nvarying vec2 vTextureCoordBack;\\nvarying vec2 vTextureCoord;\\nuniform sampler2D uSamplerBack;\\nuniform sampler2D uSampler;\\nuniform bool uDrawThumb;\\nuniform bool uDrawBack;\\nuniform float uAlpha;\\nvoid main(void) {\\n    if (uDrawThumb) {\\n        gl_FragColor = texture2D(uSamplerBack, vTextureCoordBack);\\n        return;\\n    }\\n    if (uAlpha == 1.0) {\\n        gl_FragColor = texture2D(uSampler, vTextureCoord);\\n        return;\\n    }\\n    if (uDrawBack) {\\n        gl_FragColor = texture2D(uSampler, vTextureCoord) * uAlpha + texture2D(uSamplerBack, vTextureCoordBack) * (1.0 - uAlpha);\\n    } else {\\n        vec4 textureColor = texture2D(uSampler, vec2(vTextureCoord.s, vTextureCoord.t));\\n        gl_FragColor =  vec4(textureColor.rgb, textureColor.a * uAlpha);\\n    }\\n}", a.FRAGMENT_SHADER);a.attachShader(b,c);a.attachShader(b,e);a.bindAttribLocation(b,0,"aVertexPosition");a.linkProgram(b)},wl:function(a,b){var c=this.jb,e=c.createShader(b);c.shaderSource(e,a);c.compileShader(e);return e},XT:function(){var a=new Tj(this.Lf),b=a.QY();this.Qr={DO:a,jX:b,iX:a.yc}},setData:function(a){a!==s&&(this.Hl=this.ea?this.ea.id:s,this.qB=this.ea||s);(this.ea=a)&&this.Vr.setData(a.id,[])},va:function(a,b){if(this.ea){this.Da=a;this.cf=b;var c=this.Ya;this.Ui||(this.Ui=this.P.fa()); this.Tq||(this.Tq=180/Math.pow(2,this.Ui),this.fV=c.width/c.height,sj.NZ(this.Nh,Pb(this.Tq),this.fV),sj.np(this.sr,this.Nh));this.Jz(0);c=this.pA(0);this.nv[0]=c[0];this.EA[0]=c[1];this.pT();this.Rr()}},Jz:function(a){sj.Em(this.Jf[a]);sj.rotate(this.Jf[a],this.Jf[a],Pb(this.Da.pitch),[-1,0,0]);var b=this.ea.tiles.dirNorth;1==a&&this.qB&&(b=this.qB.tiles.dirNorth);sj.rotate(this.Jf[a],this.Jf[a],Pb(b+this.Da.heading+90),[0,1,0]);sj.np(this.fB[a],this.Jf[a])},Pj:function(a,b,c){var e=this.Vi,f=this.Qj, g=Z.create(),i=this.sr,c=this.fB[c],a=Z.ug(2*a/e-1,1-2*b/f,-1,1);sj.multiply(g,i,a);sj.multiply(g,c,g);b=this.Lf;i=200*g[0];a=200*g[1];g=200*g[2];b=Math.sqrt(-(-b*b)/(i*i+a*a+g*g));return this.Qr.DO.FX([i*b,a*b,g*b])},hI:function(a,b,c,e){var f=this.Qj,b=Z.ug(a,b,c,1),a=Z.create();sj.multiply(a,this.Jf[e],b);b=Z.ug(a[0],a[1],a[2],a[3]);sj.multiply(a,this.Nh,b);if(0!==a[3])return e=(1-Mj.ug(a[0]/a[3],a[1]/a[3],a[2]/a[3])[1])*f/2,0<a[3]&&0<e&&e<f?q:t},Qc:function(a,b,c){var c=c||{},e=c.xp||t,f=c.XZ|| s;this.ko=this.cf;this.cf=b;this.Tq=s;var g=this,i=this.Ui,k=a-i;g.gr&&(g.gr.stop(),g.gr=s);e?(g.Ui=a,g.hV=t,g.va(g.Da,b)):g.gr=new tb({duration:600,Ic:60,kc:ub.eL,va:function(a){if(a!=0){g.Tq=s;g.Ui=i+k*a;a<0.5?g.va(g.Da,g.ko):g.va(g.Da,b);f&&f(g.Ui)}},finish:function(){g.gr=s;g.Ui=a;g.hV=t;g.P.dispatchEvent(new P("onzoom_changed"))},Kt:function(){g.P.dispatchEvent(new P("onzoom_changed"));g.gr=s}})},pT:function(){var a=this;this.VI(0,0,1,function(){a.Rg||a.vI()})},vI:function(){for(var a=this.EA[0], b=a[0],c=a[1],e=a[3],a=a[2];a<=e;a++){var f=b,g=c;"number"===typeof this.FA&&(a>=this.FA&&a<=this.iI)&&(f=0,g=this.ql[this.cf].cols-1);for(;f<=g;f++)this.VI(f,a)}},VI:function(a,b,c,e){var c=c||this.cf,f=this.ql[c].cols,g=this.ql[c].rows,i=this,a=i.tB(a,c);0>b&&(a=i.tB(f-1-a,c),b=0);b>g-1&&(a=i.tB(f-1-a,c),b=g-1);var k=a+b*f,m=i.Vr.getData(i.ea.id);m[c]||(m[c]=[]);m[c][k]||(m[c][k]={});var n=m[c][k];n.loaded?(i.rJ(c)&&i.xJ(),e&&e()):(n.index=k,n.loaded=t,n.HZ=i.ea.id,i.oT(a,b,c,function(a){if(n.HZ== i.ea.id){n.loaded=q;var b=m[c][k];if(!b.Es){i.FU(b,a,c);if(c>1)b.Wl={duration:300,startTime:Date.now(),DC:0,Oo:1};b.Es=q}i.rJ(c)&&i.xJ();e&&e();i.Rr()}}))},rJ:function(a){return 1===a&&this.Hl&&this.P.IJ?q:t},rO:ba("AT"),tB:function(a,b){for(var c=a,e=this.ql[b||this.cf].cols;0>c;)c+=e;return c%e},Af:function(){this.RB--;0===this.RB&&this.P.dispatchEvent(new P("ontilesloaded"))},Rr:function(){this.Zu&&(clearTimeout(this.Zu),this.Zu=s);var a=this;this.Zu=setTimeout(function(){cancelAnimationFrame(a.Rv); a.Rv=s;a.Zu=s},300);this.Rv||this.Qv()},Qv:function(){var a=this;a.Rv=requestAnimationFrame(function(){a.Qv()});var b=this.ne;this.jb.useProgram(b);var c=this.tw;c.So(b.yn);c.So(b.oP);c.So(b.Au);c.VC();b=t;this.Rg?this.bU():b=this.yB(0);if(b&&!this.Rg)for(b=0;b<this.ng.length;b++)this.ng[b].Qv(this.Nh,this.Jf[0],this.sr,this.fB[0])},yB:function(a,b){if(!this.ea)return t;var c=this.nv[a];if(!c)return t;var e=0==a?this.ea.id:this.Hl;if(!e)return t;b&&sj.translate(this.Jf[a],this.dB[a],b);var f=c[0], g=c[1],i=c[2],c=c[3];this.Rg&&0==a&&(f-=2,g+=2,i-=2,c+=2,i=0>i?0:i);e=this.Vr.getData(e);if(!e)return t;for(;i<=c;i++){var k=f,m=g;"number"===typeof this.jk&&(i>=this.jk&&i<=this.Tl)&&(k=0,m=this.Ud-1);for(;k<=m;k++){for(var n=k;0>n;)n+=this.Ud;var n=n%this.Ud,o=this.nl(n,i)[2],p=this.nl(n,i,this.ko)[2],v=this.nl(n,i,1)[2],x=s,y=s,A=1;e[this.cf]&&(e[this.cf][o]&&e[this.cf][o].Es)&&(x=o);e[this.ko]&&e[this.ko][p]&&e[this.ko][p].Es?(y=p,A=this.ko):e[1][v]&&e[1][v].Es&&(y=v);if(x===s&&y===s)return t; o=this.jb;p=this.ne;v=this.cf;this.Rg&&1==a?(o.uniform1i(p.aL,t),x===s&&(x=y,v=1),y=s):o.uniform1i(p.aL,q);this.hT(x,v,y,A,a);this.EU(n,i,v,A);this.EB(a);this.MR()}}return q},nl:function(a,b,c){var c=c||this.cf,e=Math.pow(2,5-c),a=Math.floor(a/e),b=Math.floor(b/e),b=this.ql[c].rows-b-1;return[a,b,a+b*this.Ud/e]},FU:function(a,b,c){if(a){var e=this.jb;1===c?e.activeTexture(e.TEXTURE0):e.activeTexture(e.TEXTURE1);a.Zy=e.createTexture();e.bindTexture(e.TEXTURE_2D,a.Zy);e.pixelStorei(e.UNPACK_FLIP_Y_WEBGL, q);e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,b);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.LINEAR);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.LINEAR)}},hT:function(a,b,c,e,f){var g=0==f?this.ea.id:this.Hl;if(g){var i=this.Vr.getData(g),g=this.jb,k=this.ne;a===s?g.uniform1i(k.bL,q):g.uniform1i(k.bL,t);var m=s;i[e]&&i[e][c]&&(g.activeTexture(g.TEXTURE0), m=i[e][c].Zy,g.bindTexture(g.TEXTURE_2D,m),g.uniform1i(k.h_,0));if("number"===typeof a){g.activeTexture(g.TEXTURE1);g.bindTexture(g.TEXTURE_2D,i[b][a].Zy);g.uniform1i(k.i_,1);a=i[b][a];"number"!=typeof a.Oo&&(a.Oo=1);if(1==f&&(!a.Wl||0!=a.Wl.Oo))a.Wl={duration:400,startTime:this.bw,DC:1,Oo:0},a.Oo=0;a.Wl?(f=a.Wl,b=(Date.now()-f.startTime)/f.duration,0==f.DC?(f=b,1<f&&(f=1,a.Wl=s)):(f=f.DC*(1-b),0>f&&(f=0,a.Wl=s)),g.uniform1f(k.alpha,f)):g.uniform1f(k.alpha,a.Oo)}}},EU:function(a,b,c,e){var f=this.QS(a, b),g=this.Qr.jX,c=this.aI(a,b,c),a=this.aI(a,b,e),b=this.jb;this.kw||(this.kw=b.createBuffer());b.bindBuffer(b.ARRAY_BUFFER,this.kw);b.bufferData(b.ARRAY_BUFFER,new Float32Array(f),b.STATIC_DRAW);this.to||(this.to=b.createBuffer(),this.to.hG=g.length,b.bindBuffer(b.ELEMENT_ARRAY_BUFFER,this.to),b.bufferData(b.ELEMENT_ARRAY_BUFFER,new Uint16Array(g),b.STATIC_DRAW));this.VB||(this.VB=b.createBuffer());b.bindBuffer(b.ARRAY_BUFFER,this.VB);b.bufferData(b.ARRAY_BUFFER,new Float32Array(a),b.STATIC_DRAW); c&&(this.Sl||(this.Sl=b.createBuffer()),b.bindBuffer(b.ARRAY_BUFFER,this.Sl),b.bufferData(b.ARRAY_BUFFER,new Float32Array(c),b.STATIC_DRAW))},MR:function(){var a=this.jb,b=this.ne;a.bindBuffer(a.ARRAY_BUFFER,this.kw);a.bindBuffer(a.ELEMENT_ARRAY_BUFFER,this.to);a.vertexAttribPointer(b.yn,3,a.FLOAT,t,0,0);a.bindBuffer(a.ARRAY_BUFFER,this.VB);a.vertexAttribPointer(b.oP,2,a.FLOAT,t,0,0);a.bindBuffer(a.ARRAY_BUFFER,this.Sl);a.vertexAttribPointer(b.Au,2,a.FLOAT,t,0,0);a.drawElements(a.TRIANGLES,this.to.hG, a.UNSIGNED_SHORT,0)},QS:function(a,b){return this.Qr.iX[a+b*(64/(64/this.Ud))]},aI:function(a,b,c){return this.Qr.DO.tX(c||this.cf,a,b)},nA:function(){var a=this.ne,b=this.jb;a.Lt=b.getUniformLocation(a,"uPMatrix");a.It=b.getUniformLocation(a,"uMVMatrix");a.bL=b.getUniformLocation(a,"uDrawThumb");a.aL=b.getUniformLocation(a,"uDrawBack");a.alpha=b.getUniformLocation(a,"uAlpha");a.h_=b.getUniformLocation(a,"uSamplerBack");a.i_=b.getUniformLocation(a,"uSampler")},oA:function(){var a=this.ne,b=this.jb; a.yn=b.getAttribLocation(a,"aVertexPosition");a.oP=b.getAttribLocation(a,"aVertexTextureCoordBack");a.Au=b.getAttribLocation(a,"aVertexTextureCoord")},EB:function(a){var b=this.ne,c=this.jb;c.uniformMatrix4fv(b.Lt,t,this.Nh);c.uniformMatrix4fv(b.It,t,this.Jf[a])},Dq:function(a,b,c,e){return a+"_"+b+"_"+c+"_"+e},oT:function(a,b,c,e){var f=this.Dq(this.ea.id,a,b,c),g=this.CJ.getData(f);this.RB++;if(g)g.nr&&(e(g),this.Af());else{g=this.DA.zx();g.crossOrigin="anonymous";g.nr=t;var i=this;g.onload=function(){-1!= this.src.indexOf("pos=0_0&z=1")&&i.P.dispatchEvent(new P("onthumbnail_complete"));e(this);i.Af();this.nr=q};g.src=this.ea.tiles.getTilesUrl(this.ea.id,new Q(a,b),c);this.CJ.setData(f,g)}},pA:function(a){var b=this.Vi,c=this.Qj,e,f,g,i,k,m=this.Pj(0,0,a);e=m[0];i=m[1];m=this.Pj(b/2,0,a);m[1]>i&&(i=m[1]);m=this.Pj(b,0,a);f=m[0];f<e&&(f+=this.Ud);k=m[1];m=this.Pj(0,c/2,a);m[0]>f&&(m[0]-=this.Ud);m[0]<e&&(e=m[0]);m=this.Pj(b,c/2,a);m[0]<e&&(m[0]+=this.Ud);m[0]>f&&(f=m[0]);m=this.Pj(0,c,a);m[0]>f&&(m[0]-= this.Ud);m[0]<e&&(e=m[0]);g=m[1];m=this.Pj(b/2,c,a);m[1]<g&&(g=m[1]);m=this.Pj(b,c,a);m[0]<e&&(m[0]+=this.Ud);m[0]>f&&(f=m[0]);b=m[1];this.hI(0,100,0,a)?(this.Tl=this.Ir-1,this.jk=k):this.hI(0,-100,0,a)?(this.jk=0,this.Tl=b):this.iI=this.FA=this.Tl=this.jk=s;this.Tl!=s&&i<this.Tl&&(i=this.Tl);this.jk!=s&&g>this.jk&&(g=this.jk);b=this.nl(e,i);c=this.nl(f,g);a=b[0];k=c[0];b=b[1];c=c[1];this.FA=this.nl(0,this.jk)[1];this.iI=this.nl(0,this.Tl)[1];return[[e,f,g,i],[a,k,b,c]]},lV:function(a){this.ng.push(a)}, xJ:function(){if(!this.Rg&&(this.Jz(1),this.dB[0]=sj.$h(this.Jf[0]),this.dB[1]=sj.$h(this.Jf[1]),this.nv[1]=this.pA(1)[0],this.Rg=q,this.cA=160,this.dA=120,"number"==typeof this.P.wH)){var a=Math.pow(this.P.wH,0.25);this.cA*=a;this.dA*=a}},NI:function(){this.Rg=t;this.bw=this.Hl=s;this.Jz(0);var a=this.pA(0);this.nv[0]=a[0];this.EA[0]=a[1];this.vI();this.Rr()},bU:function(){this.bw||(this.bw=Date.now());this.Rr();this.sR=Date.now();var a=this.sR-this.bw;if(400<a)this.NI();else{var a=a/400,a=-a*(a- 2),b=this.qB.links[this.AT];if(b){var c=b.dir+this.ea.tiles.dirNorth;360<c&&(c%=360);var e=this.cA*(1-a),f=Math.cos(Pb(c))*e,c=Math.sin(Pb(c))*e;try{this.yB(0,[f,0,c])}catch(g){}e=this.dA*a;f=Math.cos(Pb(b.vh))*e;c=Math.sin(Pb(b.vh))*e;try{this.yB(1,[-f,0,-c])}catch(i){}}else this.NI()}},NC:function(){cancelAnimationFrame(this.Rv);this.Vr.clear()}});function mj(a,b){this.P=a;this.Ya=b;this.Vi=b.width;this.Qj=b.height;this.jb=b.getContext("webgl",{alpha:t});this.tw=Ej.get(this.jb,this.P.aa);this.ea=this.ne=s;this.Qa=t;this.Li();this.Un=this.Tj=q;this.On=[0,-20,0];this.Cd=H()?2:1;this.Jn=this.Kn=this.In=s;this.Td={};a.WA=this;this.qo=a.ng.Bd;this.Wd=new S}var Uj=[10,-20,30,10,-20,10,-10,-20,10,-10,-20,30],Vj=[0,0,0,1,1,1,1,0],Wj=[2.2,-20,22.3,2.2,-20,13.7,-2.2,-20,13.7,-2.2,-20,22.3],Xj=[0,0,0,0.5,0.25,0.5,0.25,0]; z.extend(mj.prototype,{Li:function(){this.lH=new Rj(8.5);this.eJ=new Sj(10,1.5);this.rT();this.JA();this.cT();this.ba();this.nA();this.oA()},rT:function(){var a=new Image,b=this;a.crossOrigin="anonymous";a.onload=function(){this.nr=q;b.In=b.jb.createTexture();b.KA(b.In,this)};a.src="http://map.baidu.com/res_mobile2/images/link-arrow-shadow.png";a=new Image;a.crossOrigin="anonymous";a.onload=function(){this.nr=q;b.Kn=b.jb.createTexture();b.KA(b.Kn,this)};a.src="http://map.baidu.com/res_mobile2/images/link-arrow.png"; a=new Image;a.crossOrigin="anonymous";a.onload=function(){this.nr=q;b.Jn=b.jb.createTexture();b.KA(b.Jn,this)};a.src="http://map.baidu.com/res_mobile2/images/dir-texture.png"},JA:function(){var a=this.jb,b=this.ne=a.createProgram(),c=this.wl("attribute vec3 aVertexPosition;\\nuniform mat4 uMVMatrix;\\nuniform mat4 uPMatrix;\\nvoid main(void) {\\n    gl_Position = uPMatrix * uMVMatrix * vec4(aVertexPosition, 1.0);\\n}",a.VERTEX_SHADER),e=this.wl("precision mediump float;\\nuniform float uColor;\\nuniform float uAlpha;\\nvoid main(void) {\\n    gl_FragColor = vec4(uColor, uColor, uColor, 1.0) * uAlpha;\\n}", a.FRAGMENT_SHADER);a.attachShader(b,c);a.attachShader(b,e);a.linkProgram(b);a.useProgram(b)},cT:function(){var a=this.jb,b=this.uv=a.createProgram(),c=this.wl("attribute vec3 aVertexPosition;\\nattribute vec2 aVertexTextureCoord;\\nvarying highp vec2 vTextureCoord;\\nuniform mat4 uMVMatrix;\\nuniform mat4 uPMatrix;\\nvoid main(void) {\\n    gl_Position = uPMatrix * uMVMatrix * vec4(aVertexPosition, 1.0);\\n    vTextureCoord = aVertexTextureCoord;\\n}",a.VERTEX_SHADER),e=this.wl("precision mediump float;\\nuniform sampler2D uSampler;\\nvarying highp vec2 vTextureCoord;\\nvoid main(void) {\\n    gl_FragColor = texture2D(uSampler, vTextureCoord);\\n}", a.FRAGMENT_SHADER);a.attachShader(b,c);a.attachShader(b,e);a.linkProgram(b);a.useProgram(b)},wl:function(a,b){var c=this.jb,e=c.createShader(b);c.shaderSource(e,a);c.compileShader(e);return e},KA:function(a,b){var c=this.jb;c.activeTexture(c.TEXTURE2);c.bindTexture(c.TEXTURE_2D,a);c.pixelStorei(c.UNPACK_FLIP_Y_WEBGL,t);c.pixelStorei(c.UNPACK_PREMULTIPLY_ALPHA_WEBGL,q);c.texImage2D(c.TEXTURE_2D,0,c.RGBA,c.RGBA,c.UNSIGNED_BYTE,b);c.texParameteri(c.TEXTURE_2D,c.TEXTURE_WRAP_S,c.CLAMP_TO_EDGE);c.texParameteri(c.TEXTURE_2D, c.TEXTURE_WRAP_T,c.CLAMP_TO_EDGE);c.texParameteri(c.TEXTURE_2D,c.TEXTURE_MAG_FILTER,c.LINEAR);c.texParameteri(c.TEXTURE_2D,c.TEXTURE_MIN_FILTER,c.LINEAR);c.bindTexture(c.TEXTURE_2D,s)},ba:function(){var a=this;z.M(this.Ya,"touchstart",function(b){if(!a.qo.Rg){a.EI=[a.P.Ea().heading,a.P.Ea().pitch];var c=b.changedTouches[0].pageX*a.Cd,e=b.changedTouches[0].pageY*a.Cd,f=s;a.Tj&&(f=a.QH(c,e));a.b2=[c,e];a.eI=f;f!==s&&(a.P.dispatchEvent(new P("onlinktouchstart")),a.JU=setTimeout(function(){for(var e= a.ea.links,i=0;i<e.length;i++)if(e[i].id===f){a.tR=e[i].dir;a.mb=i;break}a.dI=q;a.Nl=q;a.P.Nl=q;e=[c,(b.changedTouches[0].pageY-60)*a.Cd];a.On=a.Yq(e);a.rH=a.Iz(e);a.P.dispatchEvent(new P("onrefresh"));a.P.dispatchEvent(new P("onlongjumpstart"))},300))}});z.M(this.Ya,"touchend",function(){clearTimeout(a.JU);if(!a.qo.Rg)if(a.qo.rO(a.mb),a.Nl){a.Nl=t;a.P.Nl=t;a.dI=t;var b=a.tR,c=a.Iz();if(5>c)a.P.dispatchEvent(new P("onrefresh")),a.P.dispatchEvent(new P("onlongjumpcancel"));else{var e=a.ea.rh+Math.sin(Pb(b))* c,b=a.ea.th+Math.cos(Pb(b))*c;a.P.dispatchEvent(new P("onrefresh"));a.P.dispatchEvent(new P("onlongjumpend"));e=new J(e,b);a.P.sa(a.Wd.nh(e),{kk:q})}}else e=a.P.Ea(),a.bV=1<Math.abs(Math.round(a.EI[0])-Math.round(e.heading))||1<Math.abs(Math.round(a.EI[1])-Math.round(e.pitch))?t:q,a.bV&&a.ea&&a.eI&&(a.P.dispatchEvent(new P("onlinkclick")),a.P.vc(a.eI,{kk:q}),a.P.dispatchEvent(new P("onlinktouchend")))});z.M(this.Ya,"touchmove",function(b){!a.qo.Rg&&a.Nl&&(a.rH=a.Iz([b.changedTouches[0].pageX*a.Cd, (b.changedTouches[0].pageY-60)*a.Cd]),a.P.dispatchEvent(new P("onrefresh")))})},VS:function(a,b){var c=a[2],e=[(b[0]+b[3]+b[6]+b[9])/4,(b[2]+b[5]+b[8]+b[11])/4];return 12>Math.abs(a[0]-e[0])&&12>Math.abs(c-e[1])?q:t},QH:function(a,b){if(!this.Tj)return s;var c=this.lT[1],e=this.Yq([a,b],c[1]);if(!e)return s;for(var f=0,g=this.ea.links.length;f<g;f++){var i=c.slice(12*f,12*f+12);if(this.VS(e,i)){this.mb=f;break}}return this.ea.links[f]?this.ea.links[f].id:s},dM:function(a,b){if(0===this.ea.links.length|| this.Un===t)return 0;var c=this.Yq([a,b]),e=t;if(!c){e=this.P.fa();c=this.Yq([a,b+50*Math.pow(2,e-2)]);if(!c)return 0;e=q}for(var f=this.P.Ea().heading;0>f;)f+=360;var f=this.YR(f%360),g=this.ea.links[f].vh,i=Math.sin(Pb(g)),g=Math.cos(Pb(g)),k=-i*c[0]+g*c[2];if(100<k||-100>k)return 0;c=(g*c[0]+i*c[2])/9;if(3.6>c)return 0;i=this.ea.links[f];g=this.MQ([this.ea.rh,0,this.ea.th],[i.x,0,i.y]);this.qo.rO(f);if(c<g)return this.P.vc(i.id,{kk:q}),this.P.dispatchEvent(new P("onclickonroad")),c;300<c&&(c=300); e&&(c=300);f=this.ea.links[f].dir;e=this.ea.rh+Math.sin(Pb(f))*c;f=this.ea.th+Math.cos(Pb(f))*c;this.P.dispatchEvent(new P("onclickonroad"));this.P.sa(this.Wd.nh(new J(e,f)),{kk:q});return c},Iz:function(a){var b=this.On;a&&(b=this.Yq(a));if(!b)return 300;var c=this.ea.links[this.mb].vh,e=Math.sin(Pb(c)),f=Math.cos(Pb(c)),a=Math.sin(Pb(-c)),c=Math.cos(Pb(-c)),g=f*b[0]+e*b[2],e=-e*b[0]+f*b[2];70<e&&(e=70);-70>e&&(e=-70);b[0]=c*g+a*e;b[2]=-a*g+c*e;this.On[0]=b[0];this.On[2]=b[2];b=g/9;return 300<b? 300:b},YR:function(a){for(var b=360,c=-1,e=0;e<this.ea.links.length;e++){var f=Math.abs(a-this.ea.links[e].dir);f<b&&(b=f,c=e)}e=c==this.ea.links.length-1?0:c+1;f=this.ea.links[e].dir;if(f<this.ea.links[c].dir&&(f=Math.abs(a-(f+360)),f<b))return e;e=0==c?this.ea.links.length-1:c-1;f=this.ea.links[e].dir;return f>this.ea.links[c].dir&&(f=Math.abs(360+a-f),f<b)?e:c},MQ:function(a,b){return Math.sqrt(Math.pow(a[0]-b[0],2)+Math.pow(a[2]-b[2],2))},nA:function(){var a=this.ne,b=this.jb;a.Lt=b.getUniformLocation(a, "uPMatrix");a.It=b.getUniformLocation(a,"uMVMatrix");a.alpha=b.getUniformLocation(a,"uAlpha");a.color=b.getUniformLocation(a,"uColor");a=this.uv;a.Lt=b.getUniformLocation(a,"uPMatrix");a.It=b.getUniformLocation(a,"uMVMatrix");a.g_=b.getUniformLocation(a,"uSampler")},oA:function(){var a=this.ne,b=this.jb;a.yn=b.getAttribLocation(a,"aVertexPosition");a=this.uv;a.yn=b.getAttribLocation(a,"aVertexPosition");a.Au=b.getAttribLocation(a,"aVertexTextureCoord")},EB:function(){var a=this.ne,b=this.jb;b.uniformMatrix4fv(a.Lt, t,this.Nh);b.uniformMatrix4fv(a.It,t,this.eB)},va:u(),setData:function(a){if(this.ea=a)this.Td={},this.yR=this.ea.tiles.dirNorth,this.Qa=q},Qv:function(a,b,c,e){this.jb&&(this.Qa&&this.ea)&&(this.jb.useProgram(this.ne),this.Nh=a,this.eB=b,this.sr=c,this.DT=e,this.EB(),this.ib())},ib:function(){if(this.Nh&&this.eB){var a=this.YT(),b=this.jb,c=this.ne,e=this.uv;b.depthMask(t);b.blendFunc(b.SRC_ALPHA,b.ONE_MINUS_SRC_ALPHA);var f=this.tw;f.rE();f.So(c.yn);f.VC();this.Nl&&(c=0.6,5>this.rH&&(c=0.2),this.Qq(this.lH.yc, this.lH.RD(),this.On,1,c,t),this.Qq(this.eJ.yc,this.eJ.RD(),this.On,1,c+0.3,t));if(!(this.dI||this.Tj===t)){var c=a[0],g=a[1],i=a[2],k=a[3],m=a[4],n=a[5],a=a[6];b.blendFuncSeparate(b.SRC_ALPHA,b.ONE_MINUS_SRC_ALPHA,b.ONE,b.ONE_MINUS_SRC_ALPHA);b.activeTexture(b.TEXTURE2);b.useProgram(e);f.rE();f.So(e.yn);f.So(e.Au);f.VC();b.uniformMatrix4fv(e.Lt,t,this.Nh);b.uniformMatrix4fv(e.It,t,this.eB);b.uniform1i(e.g_,2);this.In&&(b.bindTexture(b.TEXTURE_2D,this.In),this.Qq(c,k,s,1,1,q,m));this.Kn&&(b.bindTexture(b.TEXTURE_2D, this.Kn),this.Qq(g,k,s,1,1,q,m));this.Jn&&(b.bindTexture(b.TEXTURE_2D,this.Jn),this.Qq(i,n,s,1,1,q,a));b.depthMask(q)}}},YT:function(){for(var a=[],b=[],c=[],e=[],f=[],g=Uj.length/3,i=this.P.Ea().heading+this.yR;0>i;)i+=360;var k=this.P.Ea().pitch,i=20,m=-30,n=this.qo.Ui||this.P.fa(),i=i+20*n;0>k&&(k=Math.abs(k),i*=(90-k)/90,m-=20*k/90);for(k=0;k<this.ea.links.length;k++){var o=this.ea.links[k],p=Math.cos(Pb(o.vh))*i,v=Math.sin(Pb(o.vh))*i,n=sj.create();sj.Em(n);sj.rotate(n,n,Pb(o.vh-90),[0,1,0]); for(var o=[p,m,v],p=Uj,v=Wj,x=0;x<g;x++){var y=this.hH(n,p,3*x);a.push(y[0]+o[0],o[1]-1,y[2]+o[2]);b.push(y[0]+o[0],o[1],y[2]+o[2]);y=this.hH(n,v,3*x);c.push(y[0]+o[0],o[1],y[2]+o[2])}}g=this.KQ(e,[],f,[]);return this.lT=a=[a,b,c,e,g[0],f,g[1]]},KQ:function(a,b,c,e){for(var f=0;f<this.ea.links.length;f++){var g=4*f;a.push(g,g+1,g+2);a.push(g,g+2,g+3);c.push(g,g+1,g+3);c.push(g+2,g+3,g+1);b=b.concat(Vj);e=e.concat(this.qS(this.ea.links[f].dir))}return[b,e]},hH:function(a,b,c){return[a[0]*b[c]+a[1]* b[c+1]+a[2]*b[c+2],0,a[8]*b[c]+a[9]*b[c+1]+a[10]*b[c+2]]},Qq:function(a,b,c,e,f,g,i){var k=this.jb,m=this.ne;g&&(m=this.uv);var n=[0,0,0],o=[];if(c){n[0]=c[0];n[2]=c[2];for(var p=0;p<a.length/3;p++){var v=3*p;o.push(a[v]+n[0],c[1],a[v+2]+n[2])}}else o=a;g?(this.Sl||(this.Sl=k.createBuffer()),k.bindBuffer(k.ARRAY_BUFFER,this.Sl),k.bufferData(k.ARRAY_BUFFER,new Float32Array(i),k.STATIC_DRAW),k.vertexAttribPointer(m.Au,2,k.FLOAT,t,0,0)):(k.uniform1f(m.color,e),k.uniform1f(m.alpha,f));this.tI||(this.tI= k.createBuffer());k.bindBuffer(k.ARRAY_BUFFER,this.tI);k.bufferData(k.ARRAY_BUFFER,new Float32Array(o),k.STATIC_DRAW);this.Bv||(this.Bv=k.createBuffer());k.bindBuffer(k.ELEMENT_ARRAY_BUFFER,this.Bv);k.bufferData(k.ELEMENT_ARRAY_BUFFER,new Uint16Array(b),k.STATIC_DRAW);this.Bv.hG=b.length;k.vertexAttribPointer(m.yn,3,k.FLOAT,t,0,0);k.drawElements(k.TRIANGLES,this.Bv.hG,k.UNSIGNED_SHORT,0)},Yq:function(a,b){var c=this.sr,e=this.DT;if(c){var f=a[0],g=a[1],i=this.Vi,k=this.Qj,m=Z.create(),f=Z.ug(2*f/ i-1,1-2*g/k,-1,1);sj.multiply(m,c,f);sj.multiply(m,e,m);c=200*m[0];e=200*m[1];m=200*m[2];f=(b||-20)/e;if(!(0>f))return[c*f,e*f,m*f]}},Nk:function(){this.Vi=this.Ya.width;this.Qj=this.Ya.height},qS:function(a){var b=Math.ceil(Math.floor((a+360)%360/22.5)/2);8===b&&(b=0);for(var a=Xj.slice(0),c=Math.floor(b/2),b=b%2,e=0;e<a.length;e++)a[e]=0==e%2?a[e]+0.25*c:a[e]+0.5*b;return a},bW:function(){this.Td={}},show:function(){this.Tj=q},U:function(){this.Tj=t},kD:function(){this.Un=q},SC:function(){this.Un= t},NC:function(){this.bW();var a=this.jb;a.deleteTexture(this.In);a.deleteTexture(this.Kn);a.deleteTexture(this.Jn);this.Jn=this.Kn=this.In=s}}); ');