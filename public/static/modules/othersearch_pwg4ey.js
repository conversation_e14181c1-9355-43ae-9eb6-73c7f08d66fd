_jsload2&&_jsload2('othersearch', 'z.extend(Ud.prototype,{Sd:function(){for(var a=0,b=this.Pa.length;a<b;a++){var c=this.Pa[a];this[c.method].apply(this,c.arguments)}delete this.Pa},vm:function(a,b,c){c=c||"\\u5317\\u4eac\\u5e02";/^[ \\s]*$/.test(a)||rd.ab(function(a){if(a&&a.result&&35==a.result.type){var c=a.result,a=a.content,g=s,i=s;if(c&&0==c.error&&a)var c=a.cn,i=a.sc,k=a.wd,m=a.prc,n=a.fuzzy_score,o=a.catalog,g=new J(a.coord.x,a.coord.y),g=S.Tb(g),i={city:c,citycode:i,address:k,precise:m,confidence:n,level:o};b&&b(g,i)}},{qt:"gc", wd:a,cn:c})},rm:function(a,b,c){if(!(a instanceof J)&&b)b(s);else{var a=S.Eb(a),e=a.lng,a=a.lat,c=c||{};rd.ab(function(a){var c=s;if(a&&a.result&&44==a.result.type){var e=a.content,k={},m=[];if(0==a.result.error&&e){c=e.address_detail;a=c.city;c&&(k.streetNumber=c.street_number,k.street=c.street,k.district=c.district,k.city=a,k.province=c.province);var c=e.point,c=new J(c.x,c.y),c=S.Tb(c),n=e.surround_poi;if(n&&n.length)for(var o=0,p=n.length;o<p;o++){var v={},x=n[o];v.title=x.name;v.uid=x.uid;var y= new J(x.point.x,x.point.y),y=S.Tb(y);v.point=y;v.city=a;v.Pi=x.poiType;v.type=0;v.address=x.addr;v.postcode=x.zip||s;v.phoneNumber=x.tel||s;x=v.Pi;0!==x.length&&(v.pu=x.split(","));m.push(v)}c={point:c,address:e.address,addressComponents:k,surroundingPois:m,business:e.business}}}b&&b(c)},{qt:"rgc",x:e,y:a,dis_poi:c.poiRadius||100,poi_num:c.numPois||10,latest_admin:"1"})}}});T(Vd,{getPoint:Vd.vm,getLocation:Vd.rm});function Ph(a){var b=document.createElement("script");b.onload=function(){b.parentNode.removeChild(b)};b.src=a;document.getElementsByTagName("head")[0].appendChild(b)} z.extend(Geolocation.prototype,{getCurrentPosition:function(a,b){function c(b){k||(m&&(clearTimeout(m),m=s),i.xL=b,a.apply(i,arguments),k=q)}function e(b){if(!k&&(m&&(clearTimeout(m),m=s),i.xL=b,B!==s))a.apply(i,arguments),k=q,Ra(8E3,{longitude:b.longitude,latitude:b.latitude,accuracy:b.accuracy})}function f(a){navigator.geolocation&&(new Qh({timeout:i.K.timeout,maximumAge:i.K.maximumAge,enableHighAccuracy:i.K.enableHighAccuracy})).getCurrentPosition(function(a){e(a)},function(b){switch(b.errorCode){case b.kz:i.Vp= td;c(s);break;case b.Iu:i.Vp=sd;a();break;case b.CG:i.Vp=ud,a()}})}function g(a){(new Rh).getCurrentPosition(function(a){e(a)},function(){a()})}var i=this,k=t,b=b||{};i.K={timeout:b.timeout||i.K.timeout,maximumAge:b.maximumAge||i.K.maximumAge,enableHighAccuracy:b.enableHighAccuracy||i.K.enableHighAccuracy,zi:b.SDKLocation||i.K.zi};var m=setTimeout(function(){k||(i.Vp=ud,c(s))},i.K.timeout);(function(a){(new Sh({zi:i.K.zi})).getCurrentPosition(function(a){e(a)},function(){a()})})(function(){f(function(){g(function(){i.Vp= sd;c(s)})})})},xm:function(){this.xL&&(this.Vp=0);return this.Vp}});Geolocation.prototype.getCurrentPosition=Geolocation.prototype.getCurrentPosition;Geolocation.prototype.getStatus=Geolocation.prototype.xm;function Qh(a){this.K={timeout:a.timeout||5E3,maximumAge:a.maximumAge||6E5,enableHighAccuracy:a.enableHighAccuracy||t}} z.extend(Qh.prototype,{getCurrentPosition:function(a,b){function c(a){var c=new Th;a.code===a.PERMISSION_DENIED&&-1===a.message.indexOf("secure")&&(c.errorCode=c.kz,c.uk="\\u7528\\u6237\\u51b3\\u7edd\\u5b9a\\u4f4d\\u8bf7\\u6c42");a.code===a.POSITION_UNAVAILABLE&&(c.errorCode=c.Iu,c.uk="\\u5b9a\\u4f4d\\u4e0d\\u53ef\\u7528");-1<a.message.indexOf("secure")&&(confirm(location.hostname+"\\u60f3\\u8981\\u83b7\\u53d6\\u60a8\\u5f53\\u524d\\u7684\\u4f4d\\u7f6e\\u3002\\u5141\\u8bb8\\u8bf7\\u70b9\\u51fb\\u786e\\u5b9a\\uff0c\\u7981\\u6b62\\u8bf7\\u70b9\\u51fb\\u53d6\\u6d88")? (c.errorCode=c.Iu,c.uk="\\u5b9a\\u4f4d\\u4e0d\\u53ef\\u7528"):(c.errorCode=c.kz,c.uk="\\u7528\\u6237\\u51b3\\u7edd\\u5b9a\\u4f4d\\u8bf7\\u6c42"));a.code===a.TIMEOUT&&(c.errorCode=c.CG,c.uk="\\u5b9a\\u4f4d\\u8d85\\u65f6");b(c)}function e(b){if(/BIDUBrowser/i.test(navigator.userAgent))(new Ud).rm(new J(b.coords.longitude,b.coords.latitude),function(c){var e=new Uh;e.accuracy=1999<b.coords.accuracy?1999:b.coords.accuracy;e.longitude=b.coords.longitude;e.latitude=b.coords.latitude;e.point=new J(e.longitude,e.latitude); c.addressComponents&&(c.addressComponents.province&&(e.address.province=c.addressComponents.province),c.addressComponents.city&&(e.address.city=c.addressComponents.city),c.addressComponents.district&&(e.address.district=c.addressComponents.district),c.addressComponents.street&&(e.address.street=c.addressComponents.street),c.addressComponents.streetNumber&&(e.address.street_number=c.addressComponents.streetNumber));a(e)});else{var c="_cbk"+Math.floor(1E5*Math.random());window.baidu=window.baidu||{}; window.baidu[c]=function(c){var c=new J(c.x,c.y),e=new Uh;e.accuracy=1999<b.coords.accuracy?1999:b.coords.accuracy;e.longitude=c.lng;e.latitude=c.lat;e.point=new J(c.lng,c.lat);(new Ud).rm(c,function(b){if(b=b.addressComponents)b.province&&(e.address.province=b.province),b.city&&(e.address.city=b.city),b.district&&(e.address.district=b.district),b.street&&(e.address.street=b.street),b.streetNumber&&(e.address.street_number=b.streetNumber);a(e)})};Ph(B.Wc+"?"+["qt=coordtrans","x="+b.coords.longitude, "y="+b.coords.latitude,"from=0&to=4","callback=baidu."+c].join("&"))}}var f=navigator.userAgent.toLowerCase(),g;0<f.indexOf("like mac os x")&&(g=(f.match(/os [\\d._]*/gi)+"").replace(/[^0-9|_.]/ig,"").replace(/_/ig,"."));f=g+"";"undefined"!=f&&0<f.length&&10<=parseInt(g)||0<z.ca.Iy?(g=document.createElement("iframe"),g.height=0,g.width=0,g.style.borderWidth=0,g.src="https://api.map.baidu.com/res/staticPages/location.html?timeout="+this.K.timeout+"&maximumAge="+this.K.maximumAge+"&enableHighAccuracy="+ this.K.enableHighAccuracy+"&type=3",document.body.appendChild(g),window.addEventListener("message",function(a){if(a.data)try{var b=JSON.parse(a.data);b.code?c(b):e(b)}catch(f){}},t)):navigator.geolocation.getCurrentPosition(e,c,{enableHighAccuracy:this.K.enableHighAccuracy,maximumAge:this.K.maximumAge})}});function Rh(){this.K={hX:1,Yw:"bd09ll",fW:"utf-8",SV:"jsonp"}} z.extend(Rh.prototype,{getCurrentPosition:function(a,b){var c="_cbk"+Math.floor(1E5*Math.random());window[c]=function(c){if(c.result){if(161===c.result.error){if(c.content&&c.content.location){var e=new Uh;e.accuracy=c.content.radius;e.longitude=c.content.location.lng;e.latitude=c.content.location.lat;e.point=new J(c.content.location.lng,c.content.location.lat)}c.content&&c.content.address_component&&(c.content.address_component.country&&(e.address.country=c.content.address_component.country),c.content.address_component.province&& (e.address.province=c.content.address_component.province),c.content.address_component.city&&(e.address.city=c.content.address_component.city),c.content.address_component.district&&(e.address.district=c.content.address_component.district),c.content.address_component.street&&(e.address.street=c.content.address_component.street),c.content.address_component.street_number&&(e.address.street_number=c.content.address_component.street_number));a(e);return}if(0===c.result.error){c.content&&c.content.location&& (e=new Uh,e.longitude=c.content.location.lng,e.latitude=c.content.location.lat,e.point=new J(c.content.location.lng,c.content.location.lat));c.content&&c.content.address_component&&(c.content.address_component.country&&(e.address.country=c.content.address_component.country),c.content.address_component.province&&(e.address.province=c.content.address_component.province),c.content.address_component.city&&(e.address.city=c.content.address_component.city),c.content.address_component.district&&(e.address.district= c.content.address_component.district),c.content.address_component.street&&(e.address.street=c.content.address_component.street),c.content.address_component.street_number&&(e.address.street_number=c.content.address_component.street_number));a(e);return}}c=Th();c.errorCode=c.Iu;c.uk="\\u8bf7\\u6c42IP\\u5730\\u7406\\u5b9a\\u4f4d\\u4fe1\\u606f\\u5931\\u8d25";b(c)};var e="pc";H()&&(e="mb");rd.ab(function(a){window[c](a)},{qterm:e,coding:this.K.fW,coord:this.K.Yw,extensions:this.K.hX,callback_type:this.K.SV,ak:qa, qt:"hip",v:"3.0"})}});function Sh(a){this.K={zi:a.zi||t}} z.extend(Sh.prototype,{getCurrentPosition:function(a,b){var c=Math.floor(1E5*Math.random()),e="_cbk"+c;window.baidu=window.baidu||{};window.baidu[e]=function(f){(f=JSON.parse(f))&&1===f.errorcode?(c=Math.floor(1E5*Math.random()),e="_cbk"+c,window.baidu=window.baidu||{},window.baidu[e]=function(b){var b=new J(b.x,b.y),c=new Uh;c.longitude=b.lng;c.latitude=b.lat;c.point=new J(b.lng,b.lat);(new Ud).rm(b,function(b){if(b=b.addressComponents)b.province&&(c.address.province=b.province),b.city&&(c.address.city= b.city),b.district&&(c.address.district=b.district),b.street&&(c.address.street=b.street),b.streetNumber&&(c.address.street_number=b.streetNumber);a(c)})},Ph(B.Wc+"?"+["qt=coordtrans","x="+f.longitude,"y="+f.latitude,"from=0&to=4","callback=baidu."+e].join("&"))):b()};this.K.zi&&window.BaiduLocAssistant&&window.BaiduLocAssistant.sendMessage?window.BaiduLocAssistant.sendMessage(JSON.stringify({action:"requestLoc",callback:"baidu."+e})):b()}}); function Uh(){this.point=this.timestamp=this.speed=this.longitude=this.latitude=this.heading=this.altitudeAccuracy=this.altitude=this.accuracy=s;this.address={country:"",city:"",city_code:0,district:"",province:"",street:"",street_number:""}}function Th(){this.uk=l;this.kz=this.errorCode=1;this.Iu=2;this.CG=3};z.extend(Wd.prototype,{Sd:function(){for(var a=0,b=this.Pa.length;a<b;a++){var c=this.Pa[a];this[c.method].apply(this,c.arguments)}delete this.Pa},get:function(a){var b=this;rd.ab(function(c){if(c&&c.result&&2==c.result.type){var e=c.result,c=c.content;if(e&&0==e.error){var e=b.j.la.map,f=c.level,g=c.cname,i=c.code,k=ab.Be(c.geo,q).point,f=ab.tx(f,e);e&&e.Fd(k,f)}a&&a({center:k,level:f,name:g,code:i})}},{qt:"dec"})}});z.extend(Xd.prototype,{Sd:function(){for(var a=0,b=this.Pa.length;a<b;a++){var c=this.Pa[a];this[c.method].apply(this,c.arguments)}delete this.Pa},get:function(a,b){var c={boundaries:[]},e="119.590757,23.808251;119.268804,23.41408;119.422881,23.163397;119.758632,23.201668;119.726437,23.588002;119.70804,23.763808;119.645949,23.797671 121.642055,25.312114;121.752439,25.169879;121.945611,25.186621;121.991604,25.052618;122.055995,24.985561;121.872022,24.834547;121.88122,24.624496;121.899617,24.498294;121.798432,24.270807;121.697247,24.135805;121.642055,23.958396;121.504076,23.288798;121.439685,23.178281;121.310904,22.991042;121.255712,22.812068;121.053342,22.624316;120.933759,22.290847;121.016547,21.990891;120.86017,21.887901;120.648601,21.947988;120.584211,22.376429;120.289854,22.51325;120.11508,22.939931;120.013895,23.118734;119.9771,23.543483;120.372642,24.11892;120.694595,24.641313;121.126931,25.119638;121.559268,25.337197;121.623658,25.345557 120.38414,22.372151;120.423234,22.355038;120.372642,22.307967;120.347346,22.329365;120.351945,22.355038;120.356544,22.363595 121.506375,22.696888;121.552369,22.660607;121.517874,22.611506;121.469581,22.64353;121.464981,22.690486;121.492577,22.701155 121.589163,22.100236;121.600661,22.050934;121.623658,22.012338;121.637456,21.939406;121.596062,21.93726;121.550069,22.014483;121.499476,22.044502;121.501776,22.102379;121.54087,22.102379 122.08819,25.668005;122.110037,25.488627;121.911116,25.435387;122.027249,25.658625;122.057144,25.669047 123.686455,25.955273;123.730149,25.909524;123.58527,25.699264;123.454189,25.743012;123.557674,25.817972;123.594468,25.84711 124.589072,25.924082;124.60172,25.894964;124.564926,25.902244;124.567226,25.929282;124.571825,25.930321 115.973388,21.36321;116.152762,21.298566;116.152762,21.164878;116.148163,21.039704;116.056176,21.000836;115.863004,21.035386;115.835409,21.143304;115.830809,21.26839;115.844607,21.358902;115.922796,21.36321 118.474702,24.527161;118.4862,24.509276;118.491949,24.487706;118.498848,24.46771;118.501148,24.448764;118.501148,24.435079;118.492524,24.422972;118.481026,24.411916;118.467228,24.406125;118.443082,24.39928;118.41721,24.386643;118.378116,24.379797;118.314301,24.369264;118.28728,24.371898;118.247035,24.378217;118.230363,24.377164;118.202192,24.376111;118.182645,24.375584;118.167122,24.375584;118.159073,24.378744;118.156199,24.389802;118.160223,24.397174;118.167697,24.407178;118.17862,24.424024;118.194718,24.434026;118.210241,24.444027;118.232088,24.457185;118.254509,24.472972;118.275206,24.488232;118.289579,24.497702;118.316025,24.506646;118.35052,24.521375;118.368918,24.529791;118.386165,24.537154;118.405137,24.540836;118.425834,24.543465;118.449981,24.541888;118.460904,24.53768".split(" "); if("\\u53f0\\u6e7e\\u7701"===a||"\\u53f0\\u6e7e"===a){for(var f=0;6>f;f++)c.boundaries[f]=e[f];c.boundaries[6]=e[9];b&&b(c)}if("\\u9493\\u9c7c\\u5c9b"===a){for(f=0;1>f;f++)c.boundaries[f]=e[6];b&&b(c)}if("\\u8d64\\u5c3e\\u5c7f"===a){for(f=0;1>f;f++)c.boundaries[f]=e[7];b&&b(c)}rd.ab(function(f){f&&f.content&&f.content.uid?rd.ab(function(f){if(f&&f.content&&f.content.geo){var g=ab.Be(f.content.geo,t);if(g.Id&&g.Id.length&&0<g.Id.length)for(var m=g.Id.length,f=0;f<m;f++){var n=g.Id[f];if(n&&n.length&&0<n.length){for(var o= n.length-1,p=[],v=t,x=0,y=0,A=0;A<o;A+=2){var E=new J(n[A],n[A+1]),E=S.Tb(E);if(A<o-3)var C=new J(n[A+2],n[A+3]),C=S.Tb(C);var F=function(a,b){if(a.lng>109&&a.lng<113&&a.lat>15&&a.lat<19){if(a.lng-b.lng>1&&a.lat-b.lat<-1){v=q;x=A/2}if(a.lng-b.lng<-1&&a.lat-b.lat>1){v=q;y=A/2}}};if(-1<a.indexOf("\\u5168\\u56fd")||-1<a.indexOf("\\u4e2d\\u56fd")||-1<a.indexOf("\\u4e2d\\u534e\\u4eba\\u6c11\\u5171\\u548c\\u56fd")){if(117.546263<=E.lng&&118.711042>=E.lng&&38.697981<=E.lat&&38.91302>=E.lat||113.576516<=E.lng&&113.638822>= E.lng&&22.146067<=E.lat&&22.156645>=E.lat){p.length=0;break}F(E,C)}-1<a.indexOf("\\u6d77\\u5357")&&F(E,C);p.push(E.lng+", "+E.lat)}v===q?(x>y&&(n=x,x=y,y=n),n=p,p=p.splice(x+1),o=p.splice(y-x),n=n.concat(o),c.boundaries.push(n.join(";")),c.boundaries.push(p.join(";")),v=t):0!==p.length&&c.boundaries.push(p.join(";"))}}}C=c.boundaries.length;if(("\\u4e2d\\u56fd"===a||"\\u5168\\u56fd"===a||"\\u4e2d\\u534e\\u4eba\\u6c11\\u5171\\u548c\\u56fd"===a)&&0<c.boundaries.length)for(f=0;f<e.length-1;f++)c.boundaries[C+f]= e[f];b&&b(c)},{qt:"ext",num:1E3,l:10,uid:f.content.uid}):b&&b(c)},{qt:"s",wd:a})},S1:function(a){var b=[];if("string"!=typeof a)return b;for(var a=a.split("|")[2].split(";"),c=0;c<a.length-1;c++){for(var e=[],f=a[c].split(","),g=0;g<f.length;g+=2){var i=new J(f[g],f[g+1]),i=S.Tb(i);e.push(i.lng+", "+i.lat)}b.push(e.join(";"))}return b}});T(Ff,{get:Ff.get}); ');