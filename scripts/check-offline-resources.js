#!/usr/bin/env node

/**
 * 检查打包后的文件是否包含线上资源引用
 * 用于确保离线部署的完整性
 */

const fs = require('fs')
const path = require('path')
const glob = require('glob')

// 需要检查的线上资源模式
const ONLINE_PATTERNS = [
  /https?:\/\/[^"'\s)]+/g,  // HTTP/HTTPS URLs
  /\/\/[^"'\s)]+\.com[^"'\s)]*/g,  // 协议相对URLs
  /fonts\.googleapis\.com/g,  // Google Fonts
  /fonts\.gstatic\.com/g,     // Google Fonts Static
  /cdn\.jsdelivr\.net/g,      // jsDelivr CDN
  /unpkg\.com/g,              // unpkg CDN
  /cdnjs\.cloudflare\.com/g,  // Cloudflare CDN
  /api\.map\.baidu\.com/g,    // 百度地图API
  /webapi\.amap\.com/g,       // 高德地图API
]

// 排除的文件模式
const EXCLUDE_PATTERNS = [
  '**/node_modules/**',
  '**/scripts/**',
  '**/*.md',
  '**/.git/**',
  '**/dist/static/bmap_offline_api_v3.0_min.js', // 离线百度地图文件
]

function checkFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    const issues = []
    
    ONLINE_PATTERNS.forEach((pattern, index) => {
      const matches = content.match(pattern)
      if (matches) {
        matches.forEach(match => {
          // 排除一些已知的安全引用
          if (
            match.includes('localhost') ||
            match.includes('127.0.0.1') ||
            match.includes('10.8.') ||  // 内网IP
            match.includes('192.168.') ||
            match.includes('172.16.') ||
            match.includes('process.env.VUE_APP_') ||
            match.includes('${') ||
            match.includes('{{') ||
            match.includes('local(') // CSS local() 字体引用
          ) {
            return
          }
          
          issues.push({
            file: filePath,
            pattern: pattern.toString(),
            match: match,
            line: getLineNumber(content, match)
          })
        })
      }
    })
    
    return issues
  } catch (error) {
    console.warn(`⚠️  无法读取文件: ${filePath}`)
    return []
  }
}

function getLineNumber(content, searchText) {
  const lines = content.split('\n')
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].includes(searchText)) {
      return i + 1
    }
  }
  return 0
}

function checkDirectory(dir) {
  console.log(`🔍 检查目录: ${dir}`)
  
  const files = glob.sync('**/*', {
    cwd: dir,
    ignore: EXCLUDE_PATTERNS,
    nodir: true
  })
  
  let totalIssues = []
  let checkedFiles = 0
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const ext = path.extname(file).toLowerCase()
    
    // 只检查文本文件
    if (['.js', '.css', '.html', '.vue', '.json', '.xml', '.svg'].includes(ext)) {
      const issues = checkFile(filePath)
      totalIssues = totalIssues.concat(issues)
      checkedFiles++
    }
  })
  
  return { issues: totalIssues, checkedFiles }
}

function main() {
  console.log('🚀 开始检查离线资源引用...\n')
  
  const distDir = path.join(process.cwd(), 'dist')
  const srcDir = path.join(process.cwd(), 'src')
  const publicDir = path.join(process.cwd(), 'public')
  
  let allIssues = []
  let totalCheckedFiles = 0
  
  // 检查源码目录
  if (fs.existsSync(srcDir)) {
    const result = checkDirectory(srcDir)
    allIssues = allIssues.concat(result.issues)
    totalCheckedFiles += result.checkedFiles
  }
  
  // 检查public目录
  if (fs.existsSync(publicDir)) {
    const result = checkDirectory(publicDir)
    allIssues = allIssues.concat(result.issues)
    totalCheckedFiles += result.checkedFiles
  }
  
  // 检查打包目录（如果存在）
  if (fs.existsSync(distDir)) {
    const result = checkDirectory(distDir)
    allIssues = allIssues.concat(result.issues)
    totalCheckedFiles += result.checkedFiles
  }
  
  console.log(`\n📊 检查完成！`)
  console.log(`📁 检查文件数: ${totalCheckedFiles}`)
  console.log(`⚠️  发现问题数: ${allIssues.length}`)
  
  if (allIssues.length > 0) {
    console.log('\n❌ 发现以下线上资源引用:')
    console.log('=' .repeat(80))
    
    allIssues.forEach((issue, index) => {
      console.log(`\n${index + 1}. 文件: ${issue.file}`)
      console.log(`   行号: ${issue.line}`)
      console.log(`   匹配: ${issue.match}`)
      console.log(`   模式: ${issue.pattern}`)
    })
    
    console.log('\n💡 建议:')
    console.log('1. 将在线字体替换为本地字体或系统字体')
    console.log('2. 下载CDN资源到本地')
    console.log('3. 确保所有API调用都指向内网地址')
    console.log('4. 检查配置文件中的环境变量设置')
    
    process.exit(1)
  } else {
    console.log('\n✅ 太棒了！没有发现线上资源引用，项目可以完全离线部署。')
    process.exit(0)
  }
}

if (require.main === module) {
  main()
}

module.exports = { checkDirectory, checkFile }
