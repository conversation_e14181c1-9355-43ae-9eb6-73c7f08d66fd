<!DOCTYPE html>
<!-- saved from url=(0230)file:///Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application%20Support/com.tencent.xinWeChat/2.0b4.0.9/60189fed5198b065b7043fa431c0ac8a/Message/MessageTemp/c8b4d644cfbd817b2c0ef79798eade01/File/2/page_1.html -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Page 1</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <link href="./axure_rp_page.css" type="text/css" rel="stylesheet">
    <link href="./styles.css" type="text/css" rel="stylesheet">
    <link href="./styles(1).css" type="text/css" rel="stylesheet">
    <script src="./jquery-3.2.1.min.js"></script>
    <script src="./axQuery.js"></script>
    <script src="./globals.js"></script>
    <script src="./axutils.js"></script>
    <script src="./annotation.js"></script>
    <script src="./axQuery.std.js"></script>
    <script src="./doc.js"></script>
    <script src="./messagecenter.js"></script>
    <script src="./events.js"></script>
    <script src="./recording.js"></script>
    <script src="./action.js"></script>
    <script src="./expr.js"></script>
    <script src="./geometry.js"></script>
    <script src="./flyout.js"></script>
    <script src="./model.js"></script>
    <script src="./repeater.js"></script>
    <script src="./sto.js"></script>
    <script src="./utils.temp.js"></script>
    <script src="./variables.js"></script>
    <script src="./drag.js"></script>
    <script src="./move.js"></script>
    <script src="./visibility.js"></script>
    <script src="./style.js"></script>
    <script src="./adaptive.js"></script>
    <script src="./tree.js"></script>
    <script src="./init.temp.js"></script>
    <script src="./legacy.js"></script>
    <script src="./viewer.js"></script>
    <script src="./math.js"></script>
    <script src="./jquery.nicescroll.min.js"></script>
    <script src="./document.js"></script>
    <script src="./data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (图片 ) -->
      <div id="u0" class="ax_default _图片_">
        <img id="u0_img" class="img " src="./u0.png">
        <div id="u0_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u1" class="ax_default _图片_">
        <img id="u1_img" class="img " src="./u1.png">
        <div id="u1_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2" class="ax_default box_1" selectiongroup="标签页选项组1" style="cursor: pointer;">
        <div id="u2_div" class="" tabindex="0"></div>
        <div id="u2_text" class="text ">
          <p id="cache0" style=""><span id="cache1" style="">电子屏</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3" class="ax_default box_1" selectiongroup="标签页选项组1" style="cursor: pointer;">
        <div id="u3_div" class="" tabindex="0"></div>
        <div id="u3_text" class="text ">
          <p id="cache2" style=""><span id="cache3" style="">等保备案</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4" class="ax_default box_1" selectiongroup="标签页选项组1" style="cursor: pointer;">
        <div id="u4_div" class="" tabindex="0"></div>
        <div id="u4_text" class="text ">
          <p id="cache4" style=""><span id="cache5" style="">网站备案</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5" class="ax_default box_1" selectiongroup="标签页选项组1" style="cursor: pointer;">
        <div id="u5_div" class="" tabindex="0"></div>
        <div id="u5_text" class="text ">
          <p id="cache6" style=""><span id="cache7" style="">运营商</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6" class="ax_default box_1" selectiongroup="标签页选项组1" style="cursor: pointer;">
        <div id="u6_div" class="" tabindex="0"></div>
        <div id="u6_text" class="text ">
          <p id="cache8" style=""><span id="cache9" style="">网吧</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u7" class="ax_default box_1" selectiongroup="标签页选项组1" style="cursor: pointer;">
        <div id="u7_div" class="" tabindex="0"></div>
        <div id="u7_text" class="text ">
          <p id="cache10" style=""><span id="cache11" style="">非经营</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u8" class="ax_default box_1" selectiongroup="标签页选项组1" style="cursor: pointer;">
        <div id="u8_div" class="" tabindex="0"></div>
        <div id="u8_text" class="text ">
          <p id="cache12" style=""><span id="cache13" style="">APP</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u9" class="ax_default box_1 selected" selectiongroup="标签页选项组1" style="cursor: pointer;">
        <div id="u9_div" class="selected" tabindex="0"></div>
        <div id="u9_text" class="text ">
          <p id="cache14"><span id="cache15" style="color: rgb(255, 255, 255);">小程序</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u10" class="ax_default" data-left="87" data-top="260" data-width="28" data-height="39">

        <!-- Unnamed (形状) -->
        <div id="u11" class="ax_default _形状">
          <img id="u11_img" class="img " src="./u11.svg">
          <div id="u11_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u12" class="ax_default" data-left="92" data-top="267" data-width="17" data-height="14">

          <!-- Unnamed (形状) -->
          <div id="u13" class="ax_default _形状">
            <img id="u13_img" class="img " src="./u13.svg">
            <div id="u13_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u14" class="ax_default _形状">
            <img id="u14_img" class="img " src="./u14.svg">
            <div id="u14_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u15" class="ax_default _形状">
            <img id="u15_img" class="img " src="./u15.svg">
            <div id="u15_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u16" class="ax_default" data-left="758" data-top="260" data-width="29" data-height="40">

        <!-- Unnamed (形状) -->
        <div id="u17" class="ax_default _形状">
          <img id="u17_img" class="img " src="./u17.svg">
          <div id="u17_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u18" class="ax_default _形状">
          <img id="u18_img" class="img " src="./u18.svg">
          <div id="u18_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u19" class="ax_default" data-left="646" data-top="260" data-width="29" data-height="40">

        <!-- Unnamed (形状) -->
        <div id="u20" class="ax_default _形状">
          <img id="u20_img" class="img " src="./u20.svg">
          <div id="u20_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u21" class="ax_default _形状">
          <img id="u21_img" class="img " src="./u21.svg">
          <div id="u21_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u22" class="ax_default" data-left="534" data-top="260" data-width="29" data-height="40">

        <!-- Unnamed (形状) -->
        <div id="u23" class="ax_default _形状">
          <img id="u23_img" class="img " src="./u23.svg">
          <div id="u23_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u24" class="ax_default" data-left="541" data-top="267" data-width="16" data-height="16">

          <!-- Unnamed (形状) -->
          <div id="u25" class="ax_default _形状">
            <img id="u25_img" class="img " src="./u25.svg">
            <div id="u25_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u26" class="ax_default _形状">
            <img id="u26_img" class="img " src="./u26.svg">
            <div id="u26_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u27" class="ax_default _形状">
            <img id="u27_img" class="img " src="./u27.svg">
            <div id="u27_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u28" class="ax_default" data-left="422" data-top="260" data-width="29" data-height="40">

        <!-- Unnamed (形状) -->
        <div id="u29" class="ax_default _形状">
          <img id="u29_img" class="img " src="./u29.svg">
          <div id="u29_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u30" class="ax_default _形状">
          <img id="u30_img" class="img " src="./u30.svg">
          <div id="u30_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u31" class="ax_default" data-left="310" data-top="260" data-width="29" data-height="40">

        <!-- Unnamed (形状) -->
        <div id="u32" class="ax_default _形状">
          <img id="u32_img" class="img " src="./u32.svg">
          <div id="u32_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u33" class="ax_default _形状">
          <img id="u33_img" class="img " src="./u33.svg">
          <div id="u33_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u34" class="ax_default" data-left="198" data-top="260" data-width="29" data-height="40">

        <!-- Unnamed (形状) -->
        <div id="u35" class="ax_default _形状">
          <img id="u35_img" class="img " src="./u35.svg">
          <div id="u35_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u36" class="ax_default" data-left="206" data-top="268" data-width="13" data-height="15">

          <!-- Unnamed (形状) -->
          <div id="u37" class="ax_default _形状">
            <img id="u37_img" class="img " src="./u37.svg">
            <div id="u37_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u38" class="ax_default _形状">
            <img id="u38_img" class="img " src="./u38.svg">
            <div id="u38_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u39" class="ax_default" data-left="870" data-top="260" data-width="29" data-height="40">

        <!-- Unnamed (形状) -->
        <div id="u40" class="ax_default _形状">
          <img id="u40_img" class="img " src="./u40.svg">
          <div id="u40_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u41" class="ax_default _形状">
          <img id="u41_img" class="img " src="./u41.svg">
          <div id="u41_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u42" class="ax_default" data-left="87" data-top="355" data-width="28" data-height="39">

        <!-- Unnamed (形状) -->
        <div id="u43" class="ax_default _形状">
          <img id="u43_img" class="img " src="./u11.svg">
          <div id="u43_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u44" class="ax_default" data-left="92" data-top="362" data-width="17" data-height="14">

          <!-- Unnamed (形状) -->
          <div id="u45" class="ax_default _形状">
            <img id="u45_img" class="img " src="./u13.svg">
            <div id="u45_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u46" class="ax_default _形状">
            <img id="u46_img" class="img " src="./u14.svg">
            <div id="u46_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u47" class="ax_default _形状">
            <img id="u47_img" class="img " src="./u15.svg">
            <div id="u47_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u48" class="ax_default" data-left="758" data-top="355" data-width="29" data-height="40">

        <!-- Unnamed (形状) -->
        <div id="u49" class="ax_default _形状">
          <img id="u49_img" class="img " src="./u17.svg">
          <div id="u49_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u50" class="ax_default _形状">
          <img id="u50_img" class="img " src="./u18.svg">
          <div id="u50_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u51" class="ax_default" data-left="646" data-top="355" data-width="29" data-height="40">

        <!-- Unnamed (形状) -->
        <div id="u52" class="ax_default _形状">
          <img id="u52_img" class="img " src="./u20.svg">
          <div id="u52_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u53" class="ax_default _形状">
          <img id="u53_img" class="img " src="./u21.svg">
          <div id="u53_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u54" class="ax_default" data-left="534" data-top="355" data-width="29" data-height="40">

        <!-- Unnamed (形状) -->
        <div id="u55" class="ax_default _形状">
          <img id="u55_img" class="img " src="./u23.svg">
          <div id="u55_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u56" class="ax_default" data-left="541" data-top="362" data-width="16" data-height="16">

          <!-- Unnamed (形状) -->
          <div id="u57" class="ax_default _形状">
            <img id="u57_img" class="img " src="./u25.svg">
            <div id="u57_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u58" class="ax_default _形状">
            <img id="u58_img" class="img " src="./u26.svg">
            <div id="u58_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u59" class="ax_default _形状">
            <img id="u59_img" class="img " src="./u27.svg">
            <div id="u59_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u60" class="ax_default" data-left="422" data-top="355" data-width="29" data-height="40">

        <!-- Unnamed (形状) -->
        <div id="u61" class="ax_default _形状">
          <img id="u61_img" class="img " src="./u29.svg">
          <div id="u61_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u62" class="ax_default _形状">
          <img id="u62_img" class="img " src="./u30.svg">
          <div id="u62_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u63" class="ax_default" data-left="310" data-top="355" data-width="29" data-height="40">

        <!-- Unnamed (形状) -->
        <div id="u64" class="ax_default _形状">
          <img id="u64_img" class="img " src="./u32.svg">
          <div id="u64_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u65" class="ax_default _形状">
          <img id="u65_img" class="img " src="./u33.svg">
          <div id="u65_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u66" class="ax_default" data-left="198" data-top="355" data-width="29" data-height="40">

        <!-- Unnamed (形状) -->
        <div id="u67" class="ax_default _形状">
          <img id="u67_img" class="img " src="./u35.svg">
          <div id="u67_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u68" class="ax_default" data-left="206" data-top="363" data-width="13" data-height="15">

          <!-- Unnamed (形状) -->
          <div id="u69" class="ax_default _形状">
            <img id="u69_img" class="img " src="./u37.svg">
            <div id="u69_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u70" class="ax_default _形状">
            <img id="u70_img" class="img " src="./u38.svg">
            <div id="u70_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u71" class="ax_default" data-left="870" data-top="355" data-width="29" data-height="40">

        <!-- Unnamed (形状) -->
        <div id="u72" class="ax_default _形状">
          <img id="u72_img" class="img " src="./u40.svg">
          <div id="u72_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u73" class="ax_default _形状">
          <img id="u73_img" class="img " src="./u41.svg">
          <div id="u73_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u74" class="ax_default _图片_">
        <img id="u74_img" class="img " src="./u74.png">
        <div id="u74_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u75" class="ax_default _图片_">
        <img id="u75_img" class="img " src="./u75.png">
        <div id="u75_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u76" class="ax_default _图片_">
        <img id="u76_img" class="img " src="./u76.png">
        <div id="u76_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u77" class="ax_default _图片_">
        <img id="u77_img" class="img " src="./u77.png">
        <div id="u77_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u78" class="ax_default _图片_">
        <img id="u78_img" class="img " src="./u78.png">
        <div id="u78_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u79" class="ax_default _图片_">
        <img id="u79_img" class="img " src="./u79.png">
        <div id="u79_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u80" class="ax_default _图片_">
        <img id="u80_img" class="img " src="./u0.png">
        <div id="u80_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u81" class="ax_default _图片_">
        <img id="u81_img" class="img " src="./u1.png">
        <div id="u81_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
    </div>
    <script src="./ios.js"></script>
  

<div id="axureEventReceiverDiv" style="display:none">{"message":"setContentScale","data":{"scaleN":1,"prevScaleN":1,"contentOriginOffset":0,"clipToView":"","viewportHeight":796,"viewportWidth":1392,"panelWidthOffset":220,"scale":"0"}}</div><div id="axureEventSenderDiv" style="display:none">{"message":"setContentScale","data":{"scaleN":1,"prevScaleN":1,"contentOriginOffset":0,"clipToView":"","viewportHeight":796,"viewportWidth":1392,"panelWidthOffset":220,"scale":"0"}}</div></body></html>