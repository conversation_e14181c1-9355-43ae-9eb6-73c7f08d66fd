﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-63px;
  width:887px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u0_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:40px;
}
#u0 {
  border-width:0px;
  position:absolute;
  left:808px;
  top:411px;
  width:29px;
  height:40px;
  display:flex;
}
#u0 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u0_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:40px;
}
#u1 {
  border-width:0px;
  position:absolute;
  left:921px;
  top:411px;
  width:29px;
  height:40px;
  display:flex;
}
#u1 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:111px;
  background:inherit;
  background-color:rgba(69, 132, 255, 1);
  border:none;
  border-radius:21px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u2 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:121px;
  width:98px;
  height:111px;
  display:flex;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u2 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:111px;
  background:inherit;
  background-color:rgba(96, 184, 255, 1);
  border:none;
  border-radius:21px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u2.selected {
}
#u2_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:111px;
  background:inherit;
  background-color:rgba(17, 221, 99, 1);
  border:none;
  border-radius:21px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u3 {
  border-width:0px;
  position:absolute;
  left:173px;
  top:121px;
  width:98px;
  height:111px;
  display:flex;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u3 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:111px;
  background:inherit;
  background-color:rgba(68, 201, 145, 1);
  border:none;
  border-radius:21px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u3.selected {
}
#u3_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:111px;
  background:inherit;
  background-color:rgba(230, 196, 17, 1);
  border:none;
  border-radius:21px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u4 {
  border-width:0px;
  position:absolute;
  left:284px;
  top:121px;
  width:98px;
  height:111px;
  display:flex;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u4 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:111px;
  background:inherit;
  background-color:rgba(245, 188, 108, 1);
  border:none;
  border-radius:21px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u4.selected {
}
#u4_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:111px;
  background:inherit;
  background-color:rgba(53, 183, 200, 1);
  border:none;
  border-radius:21px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u5 {
  border-width:0px;
  position:absolute;
  left:616px;
  top:121px;
  width:98px;
  height:111px;
  display:flex;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u5 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:111px;
  background:inherit;
  background-color:rgba(25, 168, 187, 1);
  border:none;
  border-radius:21px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u5.selected {
}
#u5_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:111px;
  background:inherit;
  background-color:rgba(255, 130, 60, 1);
  border:none;
  border-radius:21px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u6 {
  border-width:0px;
  position:absolute;
  left:726px;
  top:121px;
  width:98px;
  height:111px;
  display:flex;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u6 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:111px;
  background:inherit;
  background-color:rgba(255, 107, 42, 1);
  border:none;
  border-radius:21px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u6.selected {
}
#u6_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:111px;
  background:inherit;
  background-color:rgba(214, 156, 242, 1);
  border:none;
  border-radius:21px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u7 {
  border-width:0px;
  position:absolute;
  left:838px;
  top:121px;
  width:98px;
  height:111px;
  display:flex;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u7 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:111px;
  background:inherit;
  background-color:rgba(215, 137, 212, 1);
  border:none;
  border-radius:21px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u7.selected {
}
#u7_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u8_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:111px;
  background:inherit;
  background-color:rgba(88, 204, 255, 1);
  border:none;
  border-radius:21px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u8 {
  border-width:0px;
  position:absolute;
  left:398px;
  top:121px;
  width:98px;
  height:111px;
  display:flex;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u8 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:111px;
  background:inherit;
  background-color:rgba(245, 188, 108, 1);
  border:none;
  border-radius:21px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u8.selected {
}
#u8_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u9_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:111px;
  background:inherit;
  background-color:rgba(255, 117, 117, 1);
  border:none;
  border-radius:21px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u9 {
  border-width:0px;
  position:absolute;
  left:502px;
  top:121px;
  width:98px;
  height:111px;
  display:flex;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u9 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u9_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:111px;
  background:inherit;
  background-color:rgba(245, 188, 108, 1);
  border:none;
  border-radius:21px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftYaHei', '微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
  line-height:29px;
}
#u9.selected {
}
#u9_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u10 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u11_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:39px;
}
#u11 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:260px;
  width:28px;
  height:39px;
  display:flex;
  color:#4584FF;
}
#u11 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u11_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u13_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:11px;
}
#u13 {
  border-width:0px;
  position:absolute;
  left:96px;
  top:267px;
  width:13px;
  height:11px;
  display:flex;
}
#u13 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u14_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:10px;
}
#u14 {
  border-width:0px;
  position:absolute;
  left:92px;
  top:269px;
  width:15px;
  height:10px;
  display:flex;
}
#u14 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u14_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u15_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:9px;
}
#u15 {
  border-width:0px;
  position:absolute;
  left:94px;
  top:272px;
  width:11px;
  height:9px;
  display:flex;
}
#u15 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u15_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u16 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u17_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:40px;
}
#u17 {
  border-width:0px;
  position:absolute;
  left:758px;
  top:260px;
  width:29px;
  height:40px;
  display:flex;
  color:#4584FF;
}
#u17 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u17_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u18_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:13px;
}
#u18 {
  border-width:0px;
  position:absolute;
  left:764px;
  top:268px;
  width:16px;
  height:13px;
  display:flex;
}
#u18 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u18_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u19 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u20_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:40px;
}
#u20 {
  border-width:0px;
  position:absolute;
  left:646px;
  top:260px;
  width:29px;
  height:40px;
  display:flex;
  color:#4584FF;
}
#u20 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u20_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u21_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u21 {
  border-width:0px;
  position:absolute;
  left:654px;
  top:266px;
  width:14px;
  height:14px;
  display:flex;
}
#u21 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u21_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u22 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u23_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:40px;
}
#u23 {
  border-width:0px;
  position:absolute;
  left:534px;
  top:260px;
  width:29px;
  height:40px;
  display:flex;
  color:#4584FF;
}
#u23 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u23_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u24 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u25_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
}
#u25 {
  border-width:0px;
  position:absolute;
  left:541px;
  top:267px;
  width:16px;
  height:16px;
  display:flex;
}
#u25 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u25_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u26_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:8px;
}
#u26 {
  border-width:0px;
  position:absolute;
  left:545px;
  top:271px;
  width:8px;
  height:8px;
  display:flex;
}
#u26 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u26_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u27_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:4px;
}
#u27 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:273px;
  width:2px;
  height:4px;
  display:flex;
}
#u27 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u27_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u28 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u29_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:40px;
}
#u29 {
  border-width:0px;
  position:absolute;
  left:422px;
  top:260px;
  width:29px;
  height:40px;
  display:flex;
  color:#4584FF;
}
#u29 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u29_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u30_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u30 {
  border-width:0px;
  position:absolute;
  left:431px;
  top:268px;
  width:12px;
  height:12px;
  display:flex;
}
#u30 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u30_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u32_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:40px;
}
#u32 {
  border-width:0px;
  position:absolute;
  left:310px;
  top:260px;
  width:29px;
  height:40px;
  display:flex;
  color:#4584FF;
}
#u32 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u32_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u33_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u33 {
  border-width:0px;
  position:absolute;
  left:318px;
  top:267px;
  width:14px;
  height:14px;
  display:flex;
}
#u33 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u33_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u34 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u35_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:40px;
}
#u35 {
  border-width:0px;
  position:absolute;
  left:198px;
  top:260px;
  width:29px;
  height:40px;
  display:flex;
  color:#4584FF;
}
#u35 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u35_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u36 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u37_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:15px;
}
#u37 {
  border-width:0px;
  position:absolute;
  left:206px;
  top:268px;
  width:13px;
  height:15px;
  display:flex;
}
#u37 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u37_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u38_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:6px;
}
#u38 {
  border-width:0px;
  position:absolute;
  left:209px;
  top:272px;
  width:8px;
  height:6px;
  display:flex;
}
#u38 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u38_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u39 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u40_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:40px;
}
#u40 {
  border-width:0px;
  position:absolute;
  left:870px;
  top:260px;
  width:29px;
  height:40px;
  display:flex;
  color:#4584FF;
}
#u40 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u40_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u41_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u41 {
  border-width:0px;
  position:absolute;
  left:878px;
  top:268px;
  width:14px;
  height:14px;
  display:flex;
}
#u41 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u41_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u42 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u43_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:39px;
}
#u43 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:355px;
  width:28px;
  height:39px;
  display:flex;
  color:#4584FF;
}
#u43 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u43_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u44 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u45_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:11px;
}
#u45 {
  border-width:0px;
  position:absolute;
  left:96px;
  top:362px;
  width:13px;
  height:11px;
  display:flex;
}
#u45 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u45_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u46_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:10px;
}
#u46 {
  border-width:0px;
  position:absolute;
  left:92px;
  top:364px;
  width:15px;
  height:10px;
  display:flex;
}
#u46 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u46_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u47_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:9px;
}
#u47 {
  border-width:0px;
  position:absolute;
  left:94px;
  top:367px;
  width:11px;
  height:9px;
  display:flex;
}
#u47 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u47_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u48 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u49_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:40px;
}
#u49 {
  border-width:0px;
  position:absolute;
  left:758px;
  top:355px;
  width:29px;
  height:40px;
  display:flex;
  color:#4584FF;
}
#u49 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u49_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u50_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:13px;
}
#u50 {
  border-width:0px;
  position:absolute;
  left:764px;
  top:363px;
  width:16px;
  height:13px;
  display:flex;
}
#u50 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u50_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u51 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u52_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:40px;
}
#u52 {
  border-width:0px;
  position:absolute;
  left:646px;
  top:355px;
  width:29px;
  height:40px;
  display:flex;
  color:#4584FF;
}
#u52 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u52_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u53_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u53 {
  border-width:0px;
  position:absolute;
  left:654px;
  top:361px;
  width:14px;
  height:14px;
  display:flex;
}
#u53 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u53_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u54 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u55_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:40px;
}
#u55 {
  border-width:0px;
  position:absolute;
  left:534px;
  top:355px;
  width:29px;
  height:40px;
  display:flex;
  color:#4584FF;
}
#u55 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u55_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u56 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u57_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
}
#u57 {
  border-width:0px;
  position:absolute;
  left:541px;
  top:362px;
  width:16px;
  height:16px;
  display:flex;
}
#u57 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u57_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u58_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:8px;
}
#u58 {
  border-width:0px;
  position:absolute;
  left:545px;
  top:366px;
  width:8px;
  height:8px;
  display:flex;
}
#u58 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u58_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u59_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:4px;
}
#u59 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:368px;
  width:2px;
  height:4px;
  display:flex;
}
#u59 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u59_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u60 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u61_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:40px;
}
#u61 {
  border-width:0px;
  position:absolute;
  left:422px;
  top:355px;
  width:29px;
  height:40px;
  display:flex;
  color:#4584FF;
}
#u61 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u61_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u62_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u62 {
  border-width:0px;
  position:absolute;
  left:431px;
  top:363px;
  width:12px;
  height:12px;
  display:flex;
}
#u62 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u62_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u63 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u64_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:40px;
}
#u64 {
  border-width:0px;
  position:absolute;
  left:310px;
  top:355px;
  width:29px;
  height:40px;
  display:flex;
  color:#4584FF;
}
#u64 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u64_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u65_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u65 {
  border-width:0px;
  position:absolute;
  left:318px;
  top:362px;
  width:14px;
  height:14px;
  display:flex;
}
#u65 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u65_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u66 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u67_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:40px;
}
#u67 {
  border-width:0px;
  position:absolute;
  left:198px;
  top:355px;
  width:29px;
  height:40px;
  display:flex;
  color:#4584FF;
}
#u67 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u67_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u68 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u69_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:15px;
}
#u69 {
  border-width:0px;
  position:absolute;
  left:206px;
  top:363px;
  width:13px;
  height:15px;
  display:flex;
}
#u69 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u69_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u70_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:6px;
}
#u70 {
  border-width:0px;
  position:absolute;
  left:209px;
  top:367px;
  width:8px;
  height:6px;
  display:flex;
}
#u70 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u70_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u71 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u72_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:40px;
}
#u72 {
  border-width:0px;
  position:absolute;
  left:870px;
  top:355px;
  width:29px;
  height:40px;
  display:flex;
  color:#4584FF;
}
#u72 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u72_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u73_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u73 {
  border-width:0px;
  position:absolute;
  left:878px;
  top:363px;
  width:14px;
  height:14px;
  display:flex;
}
#u73 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u73_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u74_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:39px;
}
#u74 {
  border-width:0px;
  position:absolute;
  left:81px;
  top:446px;
  width:28px;
  height:39px;
  display:flex;
}
#u74 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u74_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u75_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:40px;
}
#u75 {
  border-width:0px;
  position:absolute;
  left:193px;
  top:446px;
  width:29px;
  height:40px;
  display:flex;
}
#u75 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u75_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u76_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:40px;
}
#u76 {
  border-width:0px;
  position:absolute;
  left:306px;
  top:446px;
  width:29px;
  height:40px;
  display:flex;
}
#u76 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u76_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u77_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:40px;
}
#u77 {
  border-width:0px;
  position:absolute;
  left:419px;
  top:446px;
  width:29px;
  height:40px;
  display:flex;
}
#u77 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u77_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u78_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:40px;
}
#u78 {
  border-width:0px;
  position:absolute;
  left:532px;
  top:446px;
  width:29px;
  height:40px;
  display:flex;
}
#u78 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u78_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u79_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:40px;
}
#u79 {
  border-width:0px;
  position:absolute;
  left:645px;
  top:446px;
  width:29px;
  height:40px;
  display:flex;
}
#u79 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u79_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u80_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:40px;
}
#u80 {
  border-width:0px;
  position:absolute;
  left:758px;
  top:446px;
  width:29px;
  height:40px;
  display:flex;
}
#u80 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u80_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u81_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:40px;
}
#u81 {
  border-width:0px;
  position:absolute;
  left:871px;
  top:446px;
  width:29px;
  height:40px;
  display:flex;
}
#u81 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u81_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
