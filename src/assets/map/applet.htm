<!DOCTYPE html>
<!-- saved from url=(0252)file:///Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application%20Support/com.tencent.xinWeChat/2.0b4.0.9/60189fed5198b065b7043fa431c0ac8a/Message/MessageTemp/c8b4d644cfbd817b2c0ef79798eade01/File/2/start.html#g=1&id=msvgqr&p=page_1 -->
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Page 1</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <link type="text/css" href="./applet_files/reset.css" rel="Stylesheet">
    <link type="text/css" href="./applet_files/default.css" rel="Stylesheet">
    <link rel="shortcut icon" href="file:///Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application%20Support/com.tencent.xinWeChat/2.0b4.0.9/60189fed5198b065b7043fa431c0ac8a/Message/MessageTemp/c8b4d644cfbd817b2c0ef79798eade01/File/2/resources/images/favicon_play.ico">
    <link href="./applet_files/css" rel="stylesheet">

    <script type="text/javascript">
        if (location.href.toString().indexOf('file://localhost/') == 0) {
            location.href = location.href.toString().replace('file://localhost/', 'file:///');
        }
    </script>

    <!--<link type="text/css" rel="Stylesheet" href="plugins/sitemap/styles/sitemap.css" />
    <link type="text/css" rel="Stylesheet" href="plugins/page_notes/styles/page_notes.css" />
    <link type="text/css" rel="Stylesheet" href="plugins/debug/styles/debug.css" />
    <link type="text/css" rel="Stylesheet" href="plugins/handoff/styles/handoff.css" />
    <link type="text/css" rel="Stylesheet" href="plugins/handoff/styles/codemirror.css" />-->
<script type="text/javascript" async="" src="./applet_files/document.js"></script><script type="text/javascript" src="./applet_files/sitemap.js"></script><script type="text/javascript" src="./applet_files/page_notes.js"></script><script type="text/javascript" src="./applet_files/debug.js"></script><link type="text/css" rel="stylesheet" href="./applet_files/sitemap.css"><link type="text/css" rel="stylesheet" href="./applet_files/page_notes.css"><link type="text/css" rel="stylesheet" href="./applet_files/debug.css"></head>
<body scroll="no" class="hashover" style="overflow: hidden;" plugindetected="true">
    <div id="topPanel">
        <div id="interfaceControlFrame" style="opacity: 1;">
            <div id="interfaceControlFrameLeft">
                <div id="interfaceControlFrameMinimizeContainer">
                    <a title="Collapse" id="interfaceControlFrameMinimizeButton">
                        <div id="minimizeArrow" class="minimizeButtonHover minimizeIcon"></div>
                        <div id="minimizeX" class="minimizeButton minimizeIcon"></div>
                    </a>
                </div>
                <div id="interfaceControlFrameCloseContainer">
                    <a title="Close" id="interfaceControlFrameCloseButton">CLOSE</a>
                </div>

                <div id="sitemapControlFrameContainer" title="Project Pages" class="selected">
                    <div id="projectControlFrameHeaderContainer">
                        <ul id="projectControlFrameHeader"><li id="sitemapHostBtn"><a pluginid="sitemapHost" title="Project Pages" class="selected">&nbsp;</a></li></ul>
                    </div>
                    
                    <div id="interfacePageNameContainer">
                        <div id="interfacePageNameButton" class="pageNameHeader">Page 1</div>
                    </div>
                </div>


                <div id="interfaceAdaptiveViewsContainer" title="Adaptive Views" style="display: none;">
                    <div id="interfaceAdaptiveViewsIconButton">&nbsp;</div>
                    <div id="interfaceAdaptiveViewsButton" class="adaptiveViewHeader"> (1392 x any)</div>
                    <div id="adaptiveViewsDropdown" class="caret"></div>
                </div>
            </div>
            
            <div id="interfaceControlFrameContainer">
                <div id="handoffControlFrameHeaderContainer">
                    <ul id="handoffControlFrameHeader"></ul>
                </div>
                <div id="interfaceControlFrameLogoContainer">
                    <div id="previewNotice">
                        Local Preview
                    </div>
                </div>
            </div>

            <div id="interfaceControlFrameRight">
                <div id="publishContainer">
                    <a id="publishButton">Share Prototype</a>
                </div>
                
                <div id="inspectControlFrameHeaderContainer">
                    <ul id="inspectControlFrameHeader">
                        <li id="overflowBtn">
                            <a id="overflowMenuButton" title="View Options"></a>
                        </li>
                    <li id="pageNotesHostBtn"><a pluginid="pageNotesHost" title="Documentation"><span></span></a></li></ul>
                </div>

                <div id="separatorContainer" class="hasLeft">
                    <div class="separator"></div>
                </div>

                <div id="overflowMadeWith"><a href="https://www.axure.com/" id="axureLogo" target="_blank"></a></div>

            </div>

        </div>
    </div>
    <div id="popupContainer">
        <div id="interfaceAdaptiveViewsListContainer" style="display: none;"><div class="adaptiveViewOption" val="auto"><div class="adapViewRadioButton selectedRadioButton"><div class="selectedRadioButtonFill" style="display: block;"></div></div>Adaptive</div><div class="adaptiveViewOption currentAdaptiveView" val="default" data-dim="0x0"><div class="adapViewRadioButton"><div class="selectedRadioButtonFill" style="display: none;"></div></div> (any x any)</div></div>

        

        <div id="accountLoginContainer">
        </div>

        <div id="overflowMenuContainer"><div id="showNotesOption" class="showOption" style="order: 3"><div class="overflowOptionCheckbox selected"></div>Show Note Markers</div>
        <div id="showHotspotsOption" class="showOption" style="order: 1"><div class="overflowOptionCheckbox"></div>Show Hotspots</div><div id="interfaceScaleListContainer">
        <div class="vpScaleOption" val="0"><div class="scaleRadioButton selectedRadioButton"><div class="selectedRadioButtonFill" style="display: block;"></div></div>Default Scale</div><div class="vpScaleOption" val="1"><div class="scaleRadioButton"><div class="selectedRadioButtonFill" style="display: none;"></div></div>Scale to Width</div><div class="vpScaleOption" val="2"><div class="scaleRadioButton"><div class="selectedRadioButtonFill" style="display: none;"></div></div>Scale to Fit</div></div></div>
    </div>
    <div id="outerContainer" style="height: 796px; width: 1612px;"><div id="sitemapHost" class="leftPanel" style="width: 220px;"><div id="sitemapHeader" '="" class="sitemapHeader"><div id="sitemapToolbar" class="sitemapToolbar"><div id="searchDiv"><span id="searchIcon" class="sitemapToolbarButton"></span><input id="searchBox" type="text" class="searchBoxHint"></div><div class="leftArrow sitemapToolbarButton"></div><div class="rightArrow sitemapToolbarButton"></div></div></div><div class="sitemapPluginNameHeader pluginNameHeader">Pages</div><div id="sitemapTreeContainer"><ul class="sitemapTree" style="clear:both;"><li class="sitemapNode sitemapLeafNode"><div class="sitemapHighlight"><div class="sitemapPageLinkContainer" style="margin-left:19px"><a class="sitemapPageLink" nodeurl="page_1.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">Page 1</span></a></div></div></li></ul></div><div id="changePageInstructions" class="pageSwapInstructions">Use  <span class="backKeys"></span>  and  <span class="forwardKeys"></span>  keys<br>to move between pages</div></div>
        <div id="clippingBounds" style="height: 796px; left: 220px; width: 1392px; opacity: 1;"><div id="notesOverlay">&nbsp;</div>
            <div id="clippingBoundsScrollContainer"></div>
        </div>

        <div id="mHideSidebar"></div>
        <div id="lsplitbar" class="splitbar" style="left: 216px; display: block;"></div>

        <div id="mainPanel" style="height: 796px; opacity: 1;">
            <div id="mainPanelContainer" style="position: relative; width: 100%; height: 796px;" data-page-dimensions-type="auto" data-scale-n="0">
                <div id="clipFrameScroll" style="position: relative; width: 100%; height: 796px; background-color: rgb(255, 255, 255);">
                    <iframe id="mainFrame" name="mainFrame" width="100%" height="100%" src="./applet_files/saved_resource.html" frameborder="0" style="display: block; position: absolute; width: 100%; height: 796px;" webkitallowfullscreen="" mozallowfullscreen="" allowfullscreen=""></iframe>
                </div>
            </div>
        </div>

        <div id="rsplitbar" class="splitbar"></div>

    <div id="pageNotesHost" class="rightPanel" style="display: none;"><div id="pageNotesHeader"><div id="pageNotesToolbar" style="height: 12px;"></div></div><div id="pageNotesScrollContainer"><div id="pageNotesContainer"><div id="pageNotesEmptyState" class="emptyStateContainer"><div class="emptyStateTitle">No notes for this page.</div><div class="emptyStateContent">Notes added in Axure RP will appear here.</div><div class="dottedDivider"></div></div><span id="pageNotesContent"></span></div></div></div></div>

    <div id="maximizePanelContainer" class="maximizePanelContainer" style="display: none;">
        <div id="maximizePanelOver">
            <div id="maximizePanel" title="Expand" class="maximizePanelOver">
                <div id="maximizeButton" class="maximizeButton maximizeIcon"></div>
            </div>
        </div>
    </div>

    <div id="mobileControlFrameContainer"></div>

<!-- 9.0.0.3679 -->
<script src="./applet_files/jquery-3.2.1.min.js"></script>
<script src="./applet_files/jquery.nicescroll.min.js"></script>
<script src="./applet_files/axutils.js"></script>
<script src="./applet_files/messagecenter.js"></script>
<script src="./applet_files/axplayer.js"></script>
<script src="./applet_files/init.js"></script>




<div id="axureEventReceiverDiv" style="display:none">{"message":"setContentScale","data":{"scaleN":1,"prevScaleN":1,"contentOriginOffset":0,"clipToView":"","viewportHeight":796,"viewportWidth":1392,"panelWidthOffset":220,"scale":"0"}}</div><div id="axureEventSenderDiv" style="display:none">{"message":"cloud_ScaleValueChanged","data":{"scale":"0"}}</div></body></html>