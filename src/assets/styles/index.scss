@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}



.el-message {
  top: 160px !important; 
}

.el-tooltip__popper {
  max-width: 680px;
  word-break: break-all;
  white-space: normal;
  line-height: 1.5;
}
.text-ellipsis {
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 隐藏溢出的内容 */
  text-overflow: ellipsis; /* 显示省略符号来代表被修剪的文本 */
  // width: 200px; /* 指定一个宽度，超出这个宽度的文本会被修剪 */
}
.multiline-ellipsis-2{
    display: -webkit-box;
    -webkit-line-clamp: 1; /* 限制行数为2 */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.multiline-ellipsis-3{
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 限制行数为3 */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}
.multiline-ellipsis-4 {
    display: -webkit-box;
    -webkit-line-clamp: 3; /* 限制行数为4 */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

// 表格名称文字省略样式 - 可复用

.el-table .cell .table-name-text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80%;
  display: inline-block;
  vertical-align: top;
}

// 更新状态徽章样式 - 气泡边框样式
.el-table .cell .site-name-container {
  max-width: 100%;
  position: relative;
  display: inline-block;
  padding: 12px 12px 0 0;
  z-index: 10;
}
.el-table .cell .update-badge {
   position: absolute;
    top: 0px;
    right: 0px;
    z-index: 20;
  // display: inline-block;
  padding: 0px 4px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  white-space: nowrap;
  border: 1px solid;
  background-color: transparent;

  // 小三角样式
  &::before {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 8px;
    width: 6px;
    height: 6px;
    border: 1px solid;
    border-top: none;
    border-right: none;
    transform: rotate(-45deg);
  }

  &.pending {
    color: #FE9400;
    border-color: #FE9400;
    background-color: #F6EEE5;

    &::before {
      border-color: #FE9400;
      background-color: #F6EEE5;
    }
  }

  &.overdue {
    color: #FF2A2A;
    border-color: #FF2A2A;
    background-color: rgba(255, 42, 42, 0.1);

    &::before {
      border-color: #FF2A2A;
      background-color: #F6E4E7;
    }
  }
}

