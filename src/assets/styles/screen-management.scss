// 电子屏管理样式 - 列表页面样式
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .screen-management-layout {
    display: flex;
    gap: 20px;
    height: 100%;

    .content-area {
      flex: 1;
      min-width: 0;
    }
  }

  .search-container {
    background: white;
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 16px 20px;
  }

  .action-bar {
    background: white;
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .el-row {
      width: 100% !important;
    }
    .action-left {
      display: flex;
      gap: 12px;
    }

    .action-right {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  .table-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
  }

  .toolbar-right {
    float: right;
    display: flex;
    align-items: center;
    gap: 12px;

    .display-fields-btn {
      color: #ACB0B3;
      padding: 0;
      border: none;
      background: none;

      &:hover {
        color: #4584FF;
      }
    }
  }
}

// 表格样式
::v-deep .el-table {
  .text-nowrap {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .keyarea-tag {
    border-color: #4584FF;
    color: #4584FF;
    background-color: #E9F1FF;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }

  .special-tag {
    border-color: #19BB77;
    color: #19BB77;
    background-color: #EAFEF6;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }

  // 表格头部样式
  .el-table__header-wrapper {
    .el-table__header {
      th {
        background-color: #EBF2FF;
        color: #333;
        font-weight: 500;
      }
    }
  }
  .el-table .el-table__fixed-header-wrapper th, .el-table .el-table__header-wrapper th {
     background-color: #EBF2FF !important;
  }

  // 表格边框
  &.el-table--border {
    border: 1px solid #EBEEF5;
    
    &::after {
      background-color: #EBEEF5;
    }
    
    th, td {
      border-right: 1px solid #EBEEF5;
    }
    
    th {
      border-bottom: 1px solid #EBEEF5;
    }
  }
}

// 弹窗样式
::v-deep .el-message-box {
  width: 620px !important;
}

// 按钮样式
// ::v-deep .el-button {
  
//   &.el-button--mini {
//     padding: 7px 15px;
//   }
// }

// 分页样式
::v-deep .el-pagination {
  margin-top: 20px;
  text-align: right;
}

// 搜索表单样式
::v-deep .el-form--inline {
  .el-form-item {
    margin-bottom: 16px;
    margin-right: 16px;
  }
  
  .el-form-item__label {
    color: #606266;
    font-weight: 500;
  }
}

// 选择器样式
::v-deep .el-select {
  width: 100%;
}

// 输入框样式
::v-deep .el-input__inner {
  border-radius: 4px;
}

// 日期选择器样式
::v-deep .el-date-editor {
  width: 100%;
}

// 标签样式
::v-deep .el-tag {
  
  &.el-tag--small {
    height: 24px;
    line-height: 22px;
    padding: 0 8px;
  }
}

// 工具栏样式
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  .toolbar-left {
    display: flex;
    gap: 12px;
  }
  
  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }
}

// 加载状态
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

// 空状态
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #909399;
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  .empty-text {
    font-size: 14px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
    
    .screen-management-layout {
      flex-direction: column;
      gap: 10px;
    }
    
    .action-bar {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
      
      .action-left,
      .action-right {
        justify-content: center;
      }
    }
    
    .table-container {
      padding: 10px;
      overflow-x: auto;
    }
  }
}

// 卡片样式
.data-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
    
    .card-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .card-content {
    color: #606266;
    line-height: 1.6;
  }
}


::v-deep .el-table.el-table--border th {
    border-bottom: 1px solid #ebeef5;
    background-color: #EBF2FF !important;
}