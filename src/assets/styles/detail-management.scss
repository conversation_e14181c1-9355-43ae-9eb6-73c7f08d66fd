// 详情管理样式 - 详情页面样式
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .detail-header {
    width: 94%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .detail-title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      margin: 0;
    }

    .detail-actions {
      display: flex;
      gap: 12px;
    }
  }

  .detail-content {
    width: 94%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
  }

  .detail-section {
    margin-bottom: 24px;

    .section-header {
      background-color: #f6f7fb;
      padding: 12px 16px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 4px;
      margin-bottom: 16px;
      border-left: 4px solid #4584ff;

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        font-weight: 600;
        color: #4584FF;
      }

      i {
        font-size: 14px;
        transition: transform 0.3s;
        color: #4584FF;
      }

      &:hover {
        background-color: #f0f2f5;
      }
    }

    .section-content {
      padding: 0 16px;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 描述列表样式
::v-deep .el-descriptions {
  .el-descriptions__header {
    margin-bottom: 20px;
    
    .el-descriptions__title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .el-descriptions__body {
    .el-descriptions__table {
      border-collapse: collapse;
      width: 100%;
      
      .el-descriptions__cell {
        border: 1px solid #EBEEF5;
        padding: 12px 15px;
        
        &.is-bordered-label {
          background-color: #FAFAFA;
          font-weight: 500;
          color: #606266;
          width: 150px;
          text-align: right;
        }
        
        &.is-bordered-content {
          color: #303133;
          background-color: #FFFFFF;
        }
      }
    }
  }

  &.el-descriptions--border {
    .el-descriptions__cell {
      border: 1px solid #EBEEF5;
    }
  }
}

// 表格样式（用于详情页中的表格）
::v-deep .el-table {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  
  .el-table__header-wrapper {
    .el-table__header {
      th {
        background-color: #EBF2FF;
        color: #333;
        font-weight: 500;
        border-bottom: 1px solid #EBEEF5;
      }
    }
  }
  
  .el-table__body-wrapper {
    .el-table__body {
      td {
        border-bottom: 1px solid #EBEEF5;
        color: #606266;
      }
    }
  }
  
  &.el-table--border {
    &::after {
      background-color: #EBEEF5;
    }
    
    th, td {
      border-right: 1px solid #EBEEF5;
    }
  }
}

// 标签样式
::v-deep .el-tag {
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
  font-size: 12px;
  
  &.el-tag--primary {
    background-color: #E9F1FF;
    border-color: #4584FF;
    color: #4584FF;
  }
  
  &.el-tag--success {
    background-color: #EAFEF6;
    border-color: #19BB77;
    color: #19BB77;
  }
  
  &.el-tag--warning {
    background-color: #FFF7E6;
    border-color: #FF9900;
    color: #FF9900;
  }
  
  &.el-tag--danger {
    background-color: #FFEBEB;
    border-color: #FF4444;
    color: #FF4444;
  }
  
  &.el-tag--info {
    background-color: #F4F4F5;
    border-color: #909399;
    color: #909399;
  }
}

// 按钮样式
::v-deep .el-button {
  padding: 10px 20px;
  font-weight: 500;

  &.el-button--primary {
    background-color: #4584FF;
    border-color: #4584FF;

    &:hover {
      background-color: #6699FF;
      border-color: #6699FF;
    }
  }

  &.el-button--text {
    color: #4584FF;
    padding: 0;
    
    &:hover {
      color: #6699FF;
    }
  }
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    color: #C0C4CC;
  }
  
  .empty-text {
    font-size: 14px;
    color: #909399;
  }
}

// 加载状态样式
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  
  .el-loading-spinner {
    .circular {
      stroke: #4584FF;
    }
  }
}

// 信息展示样式
.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  
  .info-label {
    font-weight: 500;
    color: #606266;
    margin-right: 12px;
    min-width: 100px;
    text-align: right;
  }
  
  .info-value {
    color: #303133;
    flex: 1;
  }
}

// 状态指示器
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  
  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    
    &.status-active {
      background-color: #19BB77;
    }
    
    &.status-inactive {
      background-color: #909399;
    }
    
    &.status-warning {
      background-color: #FF9900;
    }
    
    &.status-error {
      background-color: #FF4444;
    }
  }
  
  .status-text {
    font-size: 14px;
    color: #606266;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
    
    .detail-header,
    .detail-content {
      width: 100%;
      margin-left: 0;
      margin-right: 0;
    }
    
    .detail-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
      text-align: center;
    }
    
    .detail-section .section-content {
      padding: 0 8px;
    }
  }
  
  ::v-deep .el-descriptions {
    .el-descriptions__body {
      .el-descriptions__table {
        .el-descriptions__cell {
          &.is-bordered-label {
            width: auto;
            text-align: left;
          }
        }
      }
    }
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    
    .info-label {
      text-align: left;
      margin-bottom: 4px;
      min-width: auto;
    }
  }
}
