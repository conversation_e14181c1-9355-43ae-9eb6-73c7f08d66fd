// 表单管理样式 - 新增/编辑页面样式
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .form-header {
    width: 94%;
    background: white;
    border-radius: 8px;
    padding: 0px;
    margin: 0 auto 20px auto;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .form-title {
      padding-left: 20px;
      font-size: 18px;
      font-weight: 400;
      color: #303133;
      margin: 0;
    }

    .form-actions {
      display: flex;
      gap: 12px;
    }
  }

  .page-header {
    width: 94%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      justify-content: space-between;
      
      .page-title {
        margin: 0;
        font-size: 18px;
        color: #303133;
        font-weight: 600;
      }
    }
  }

  .form-container {
    width: 94%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
  }

 .form-section {
    margin-bottom: 24px;
    .section-header {
      background-color: #f6f7fb;
      padding: 12px 16px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 4px;
      margin-bottom: 16px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-line {
          width: 3px;
          height: 16px;
          background-color: #4584FF;
        }

        .title-text {
          font-size: 16px;
          font-weight: 600;
          color: #4584FF;
        }
      }

      .section-toggle {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #4584FF;
        font-size: 14px;

        .toggle-text {
          font-size: 14px;
        }

        i {
          font-size: 12px;
        }
      }

      &:hover {
        background-color: #f0f2f5;
      }
    }

    .section-content {
      padding: 0 16px;
    }
  }

  .form-actions {
    background: white;
    padding: 20px;
    border-radius: 8px;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

// 表单项样式
::v-deep .el-form {
  .el-form-item {
    margin-bottom: 20px;

    .el-form-item__label {
      color: #606266;
      font-weight: 400;
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
    }

    .el-form-item__content {
      .el-input,
      .el-select,
      .el-date-editor,
      .el-textarea {
        width: 100%;
      }

      .el-input__inner,
      .el-textarea__inner {
        border-radius: 4px;
        border: 1px solid #DCDFE6;
        
        &:focus {
          border-color: #4584FF;
        }
      }

      .el-select .el-input__inner {
        cursor: pointer;
      }
    }

    &.is-error {
      .el-input__inner,
      .el-textarea__inner,
      .el-select .el-input__inner {
        border-color: #F56C6C;
      }
    }

    &.is-required {
      .el-form-item__label::before {
        content: '*';
        color: #F56C6C;
        margin-right: 4px;
      }
    }
  }

  // 行内表单样式
  &.el-form--inline {
    .el-form-item {
      margin-right: 16px;
      margin-bottom: 16px;
    }
  }
}

// 按钮样式
::v-deep .el-button {
  padding: 10px 20px;
  font-weight: 500;

  &.el-button--primary {
    background-color: #4584FF;
    border-color: #4584FF;

    &:hover {
      background-color: #6699FF;
      border-color: #6699FF;
    }
  }

  &.el-button--success {
    background-color: #19BB77;
    border-color: #19BB77;

    &:hover {
      background-color: #33CC88;
      border-color: #33CC88;
    }
  }

  &.el-button--warning {
    background-color: #FF9900;
    border-color: #FF9900;

    &:hover {
      background-color: #FFAA33;
      border-color: #FFAA33;
    }
  }

  &.el-button--danger {
    background-color: #FF4444;
    border-color: #FF4444;

    &:hover {
      background-color: #FF6666;
      border-color: #FF6666;
    }
  }

  &.el-button--mini {
    padding: 7px 15px;
    font-size: 12px;
  }

  &.el-button--small {
    padding: 9px 15px;
    font-size: 13px;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// 开关样式
::v-deep .el-switch {
  .el-switch__core {
    
    &::after {
      border-radius: 50%;
    }
  }

  &.is-checked .el-switch__core {
    background-color: #4584FF;
    border-color: #4584FF;
  }
}

// 单选框样式
::v-deep .el-radio-group {
  .el-radio {
    margin-right: 20px;
    
    .el-radio__input.is-checked .el-radio__inner {
      background-color: #4584FF;
      border-color: #4584FF;
    }
  }
}

// 复选框样式
::v-deep .el-checkbox-group {
  .el-checkbox {
    margin-right: 20px;
    
    .el-checkbox__input.is-checked .el-checkbox__inner {
      background-color: #4584FF;
      border-color: #4584FF;
    }
  }
}

// 上传组件样式
::v-deep .el-upload {
  .el-upload-dragger {
    border-radius: 8px;
    border: 2px dashed #DCDFE6;
    
    &:hover {
      border-color: #4584FF;
    }
  }
}

// 步骤条样式
::v-deep .el-steps {
  .el-step__icon.is-text {
    background-color: #4584FF;
    border-color: #4584FF;
  }
  
  .el-step.is-process .el-step__icon {
    background-color: #4584FF;
    border-color: #4584FF;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
    
    .form-header,
    .page-header,
    .form-container {
      width: 100%;
      margin-left: 0;
      margin-right: 0;
    }
    
    .form-header,
    .page-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
      text-align: center;
    }
    
    .form-section .section-content {
      padding: 0 8px;
    }
    
    .form-actions {
      flex-direction: column;
      gap: 8px;
    }
  }
  
  ::v-deep .el-form {
    .el-form-item__label {
      text-align: left !important;
      float: none !important;
      display: block !important;
      width: auto !important;
      margin-bottom: 8px;
    }
    
    .el-form-item__content {
      margin-left: 0 !important;
    }
}

}


