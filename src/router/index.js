import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true
  },

  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  {
    path: '',
    component: Layout,
    redirect: 'index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/index'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true }
      }
    ]
  },
  {
    path: '/deepseek',
    component: Layout,
    children: [
      {
        path: 'chat',
        component: () => import('@/views/deepseek/chat'),
        name: 'DeepSeekChat',
        meta: { title: 'DeepSeek问答', icon: 'message' }
      }
    ]
  },
  {
    path: '/ai',
    component: Layout,
    // redirect: '/ai/demo',
    name: 'AI',
    meta: { title: 'AI助手', icon: 'chat-dot-round' },
    children: [
      {
        path: 'demo',
        component: () => import('@/views/ai/demoAI'),
        name: 'DemoAI',
        meta: { title: 'AI问答', icon: 'chat-line-round' }
      }
    ]
  },

  {
    path: '/enterprise',
    component: Layout,
    redirect: '/enterprise/index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/enterprise/index'),
        name: 'EnterpriseArchive',
        meta: { title: '一企一档', icon: 'company' }
      },
      {
        path: 'detail/:id',
        component: () => import('@/views/enterprise/detailReport'),
        name: 'EnterpriseDetail',
        hidden: true,
        meta: { title: '企业详情报告', activeMenu: '/enterprise/index' }
      },
      {
        path: 'chat',
        component: () => import('@/views/enterprise/chat'),
        name: 'EnterpriseChat',
        hidden: true,
        meta: { title: '一企一档智能助手', activeMenu: '/enterprise/index' }
      },
      {
        path: 'enterpriseAdd',
        component: () => import('@/views/enterprise/enterpriseAdd'),
        name: 'EnterpriseAdd',
        hidden: true,
        meta: { title: '新增/编辑企业', activeMenu: '/enterprise/index' }
      }
    ]
  },
  {
    path: '/data-management',
    component: Layout,
    redirect: '/data-management/index',
    name: 'DataManagement',
    meta: { title: '基础数据管理', icon: 'data-board' },
    children: [
      {
        path: 'index',
        component: () => import('@/views/data-management/index'),
        name: 'DataManagementHome',
        meta: { title: '基础数据管理首页', icon: 'data-board' }
      },
      {
        path: 'searchDetail',
        component: () => import('@/views/data-management/searchDetail'),
        name: 'DataManagementSearchDetail',
        hidden: true,
        meta: { title: '搜索详情', activeMenu: '/data-management/index' }
      },
      {
        path: 'screenmap',
        component: () => import('@/views/data-management/screenmap/index'),
        name: 'DataManagementScreenMap',
        hidden: true,
        meta: { title: '地图模式', activeMenu: '/data-management/index' }
      },
      {
        path: 'screen',
        component: () => import('@/views/data-management/screen/index'),
        name: 'ScreenManagement',
        meta: { title: '电子屏管理', icon: 'monitor' }
      },
      {
        path: 'screen/add',
        component: () => import('@/views/data-management/screen/add'),
        name: 'ScreenAdd',
        hidden: true,
        meta: { title: '新增电子屏', activeMenu: '/data-management/screen' }
      },
      {
        path: 'screen/edit/:screenId',
        component: () => import('@/views/data-management/screen/add'),
        name: 'ScreenEdit',
        hidden: true,
        meta: { title: '编辑电子屏', activeMenu: '/data-management/screen' }
      },
      {
        path: 'screen/detail/:id',
        component: () => import('@/views/data-management/screen/detail'),
        name: 'ScreenDetail',
        hidden: true,
        meta: { title: '电子屏详情', activeMenu: '/data-management/screen' }
      },
      // 归属场所路由
      {
        path: 'screen/site',
        component: () => import('@/views/data-management/screen/site/index'),
        name: 'SiteManagement',
        hidden: true,
        meta: { title: '归属场所', activeMenu: '/data-management/screen' }
      },
      {
        path: 'screen/site/detail/:id',
        component: () => import('@/views/data-management/screen/site/detail'),
        name: 'SiteDetail',
        hidden: true,
        meta: { title: '归属场所详情', activeMenu: '/data-management/screen' }
      },
      // 责任单位路由
      {
        path: 'screen/enterprise',
        component: () => import('@/views/data-management/screen/enterprise/index'),
        name: 'EnterpriseManagement',
        hidden: true,
        meta: { title: '责任单位', activeMenu: '/data-management/screen' }
      },
      {
        path: 'screen/enterprise/detail/:id',
        component: () => import('@/views/data-management/screen/enterprise/detail'),
        name: 'EnterpriseDetail',
        hidden: true,
        meta: { title: '责任单位详情', activeMenu: '/data-management/screen' }
      },
      // 联网系统路由
      {
        path: 'screen/netsystem',
        component: () => import('@/views/data-management/screen/netsystem/index'),
        name: 'NetsystemManagement',
        hidden: true,
        meta: { title: '联网系统', activeMenu: '/data-management/screen' }
      },
      {
        path: 'screen/netsystem/detail/:id',
        component: () => import('@/views/data-management/screen/netsystem/detail'),
        name: 'NetsystemDetail',
        hidden: true,
        meta: { title: '联网系统详情', activeMenu: '/data-management/screen' }
      },
      // 重点区域路由
      {
        path: 'screen/keyarea',
        component: () => import('@/views/data-management/screen/keyarea/index'),
        name: 'KeyareaManagement',
        hidden: true,
        meta: { title: '重点区域', activeMenu: '/data-management/screen' }
      },
      {
        path: 'screen/keyarea/detail/:id',
        component: () => import('@/views/data-management/screen/keyarea/detail'),
        name: 'KeyareaDetail',
        hidden: true,
        meta: { title: '重点区域详情', activeMenu: '/data-management/screen' }
      },
      // 专项管理路由
      {
        path: 'screen/special',
        component: () => import('@/views/data-management/screen/special/index'),
        name: 'SpecialManagement',
        hidden: true,
        meta: { title: '专项管理', activeMenu: '/data-management/screen' }
      },
      {
        path: 'screen/special/detail/:id',
        component: () => import('@/views/data-management/screen/special/detail'),
        name: 'SpecialDetail',
        hidden: true,
        meta: { title: '专项管理详情', activeMenu: '/data-management/screen' }
      },
      // 等保备案业务管理路由
      {
        path: 'levelprotect',
        component: () => import('@/views/data-management/levelprotect/index'),
        name: 'LevelprotectManagement',
        meta: { title: '等保备案业务管理', icon: 'lock' }
      },
      {
        path: 'levelprotect/add',
        component: () => import('@/views/data-management/levelprotect/add'),
        name: 'LevelprotectAdd',
        hidden: true,
        meta: { title: '新增等保备案', activeMenu: '/data-management/levelprotect' }
      },
      {
        path: 'levelprotect/edit/:id',
        component: () => import('@/views/data-management/levelprotect/edit'),
        name: 'LevelprotectEdit',
        hidden: true,
        meta: { title: '编辑等保备案', activeMenu: '/data-management/levelprotect' }
      },
      {
        path: 'levelprotect/detail/:id',
        component: () => import('@/views/data-management/levelprotect/detail'),
        name: 'LevelprotectDetail',
        hidden: true,
        meta: { title: '等保备案详情', activeMenu: '/data-management/levelprotect' }
      },
      // 网站信息管理路由
      {
        path: 'website',
        component: () => import('@/views/data-management/website/index'),
        name: 'WebsiteManagement',
        meta: { title: '网站信息管理', icon: 'globe' }
      },
      {
        path: 'website/add',
        component: () => import('@/views/data-management/website/add'),
        name: 'WebsiteAdd',
        hidden: true,
        meta: { title: '新增网站信息', activeMenu: '/data-management/website' }
      },
      {
        path: 'website/edit/:id',
        component: () => import('@/views/data-management/website/edit'),
        name: 'WebsiteEdit',
        hidden: true,
        meta: { title: '编辑网站信息', activeMenu: '/data-management/website' }
      },
      {
        path: 'website/detail/:id',
        component: () => import('@/views/data-management/website/detail'),
        name: 'WebsiteDetail',
        hidden: true,
        meta: { title: '网站信息详情', activeMenu: '/data-management/website' }
      },
      {
        path: 'website/app',
        component: () => import('@/views/data-management/website/app/index'),
        name: 'WebsiteAppManagement',
        meta: { title: 'APP信息管理', icon: 'mobile-phone' }
      },
      {
        path: 'website/app/add',
        component: () => import('@/views/data-management/website/app/add'),
        name: 'WebsiteAppAdd',
        hidden: true,
        meta: { title: '新增APP信息', activeMenu: '/data-management/website/app' }
      },
      {
        path: 'website/app/edit/:id',
        component: () => import('@/views/data-management/website/app/edit'),
        name: 'WebsiteAppEdit',
        hidden: true,
        meta: { title: '编辑APP信息', activeMenu: '/data-management/website/app' }
      },
      {
        path: 'website/app/detail/:id',
        component: () => import('@/views/data-management/website/app/detail'),
        name: 'WebsiteAppDetail',
        hidden: true,
        meta: { title: 'APP信息详情', activeMenu: '/data-management/website/app' }
      },
      {
        path: 'website/applet',
        component: () => import('@/views/data-management/website/applet/index'),
        name: 'WebsiteAppletManagement',
        meta: { title: '小程序信息审核', icon: 's-grid' }
      },
      {
        path: 'website/applet/add',
        component: () => import('@/views/data-management/website/applet/add'),
        name: 'WebsiteAppletAdd',
        hidden: true,
        meta: { title: '新增小程序信息', activeMenu: '/data-management/website/applet' }
      },
      {
        path: 'website/applet/edit/:id',
        component: () => import('@/views/data-management/website/applet/edit'),
        name: 'WebsiteAppletEdit',
        hidden: true,
        meta: { title: '编辑小程序信息', activeMenu: '/data-management/website/applet' }
      },
      {
        path: 'website/applet/detail/:id',
        component: () => import('@/views/data-management/website/applet/detail'),
        name: 'WebsiteAppletDetail',
        hidden: true,
        meta: { title: '小程序信息详情', activeMenu: '/data-management/website/applet' }
      },
      // 网吧管理路由
      {
        path: 'netbar',
        component: () => import('@/views/data-management/netbar/index'),
        name: 'NetbarManagement',
        meta: { title: '网吧管理', icon: 'coffee-cup' }
      },
      {
        path: 'netbar/add',
        component: () => import('@/views/data-management/netbar/add'),
        name: 'NetbarAdd',
        hidden: true,
        meta: { title: '新增网吧', activeMenu: '/data-management/netbar' }
      },
      {
        path: 'netbar/edit/:id',
        component: () => import('@/views/data-management/netbar/add'),
        name: 'NetbarEdit',
        hidden: true,
        meta: { title: '编辑网吧', activeMenu: '/data-management/netbar' }
      },
      {
        path: 'netbar/detail/:id',
        component: () => import('@/views/data-management/netbar/detail'),
        name: 'NetbarDetail',
        hidden: true,
        meta: { title: '网吧详情', activeMenu: '/data-management/netbar' }
      },
      // 运营商基础信息管理路由
      {
        path: 'operator/baseinfo',
        component: () => import('@/views/data-management/operator/baseinfo/index'),
        name: 'OperatorBaseinfoManagement',
        meta: { title: '运营商基础信息', icon: 'phone' }
      },
      {
        path: 'operator/baseinfo/add',
        component: () => import('@/views/data-management/operator/baseinfo/add'),
        name: 'OperatorBaseinfoAdd',
        hidden: true,
        meta: { title: '新增运营商', activeMenu: '/data-management/operator/baseinfo' }
      },
      {
        path: 'operator/baseinfo/edit/:id',
        component: () => import('@/views/data-management/operator/baseinfo/add'),
        name: 'OperatorBaseinfoEdit',
        hidden: true,
        meta: { title: '编辑运营商', activeMenu: '/data-management/operator/baseinfo' }
      },
      {
        path: 'operator/baseinfo/detail/:id',
        component: () => import('@/views/data-management/operator/baseinfo/detail'),
        name: 'OperatorBaseinfoDetail',
        hidden: true,
        meta: { title: '运营商详情', activeMenu: '/data-management/operator/baseinfo' }
      },
      // 非经营场所管理路由
      {
        path: 'wifi/site',
        component: () => import('@/views/data-management/wifi/site/index'),
        name: 'WifiSiteManagement',
        meta: { title: '非经营场所管理', icon: 'connection' }
      },
      {
        path: 'wifi/site/add',
        component: () => import('@/views/data-management/wifi/site/add'),
        name: 'WifiSiteAdd',
        hidden: true,
        meta: { title: '新增非经营场所', activeMenu: '/data-management/wifi/site' }
      },
      {
        path: 'wifi/site/edit/:id',
        component: () => import('@/views/data-management/wifi/site/add'),
        name: 'WifiSiteEdit',
        hidden: true,
        meta: { title: '编辑非经营场所', activeMenu: '/data-management/wifi/site' }
      },
      {
        path: 'wifi/site/detail/:id',
        component: () => import('@/views/data-management/wifi/site/detail'),
        name: 'WifiSiteDetail',
        hidden: true,
        meta: { title: '非经营场所详情', activeMenu: '/data-management/wifi/site' }
      },
      // WiFi设备管理路由
      {
        path: 'wifi/equipment',
        component: () => import('@/views/data-management/wifi/equipment/index'),
        name: 'WifiEquipmentManagement',
        meta: { title: 'WiFi设备管理', icon: 'cpu' }
      },
      {
        path: 'wifi/equipment/add',
        component: () => import('@/views/data-management/wifi/equipment/add'),
        name: 'WifiEquipmentAdd',
        hidden: true,
        meta: { title: '新增WiFi设备', activeMenu: '/data-management/wifi/equipment' }
      },
      {
        path: 'wifi/equipment/edit/:equipmentNum',
        component: () => import('@/views/data-management/wifi/equipment/add'),
        name: 'WifiEquipmentEdit',
        hidden: true,
        meta: { title: '编辑WiFi设备', activeMenu: '/data-management/wifi/equipment' }
      },
      {
        path: 'wifi/equipment/detail/:equipmentNum',
        component: () => import('@/views/data-management/wifi/equipment/detail'),
        name: 'WifiEquipmentDetail',
        hidden: true,
        meta: { title: 'WiFi设备详情', activeMenu: '/data-management/wifi/equipment' }
      },
      // 行政管理路由
      {
        path: 'scene',
        component: () => import('@/views/data-management/scene/index'),
        name: 'SceneManagement',
        meta: { title: '行政监管', icon: 'warning' }
      },
      // 行政处罚路由
      {
        path: 'scene/punish/add',
        component: () => import('@/views/data-management/scene/punish/add'),
        name: 'ScenePunishAdd',
        meta: { title: '新增行政处罚', activeMenu: '/data-management/scene' },
        hidden: true
      },
      {
        path: 'scene/punish/edit/:id',
        component: () => import('@/views/data-management/scene/punish/add'),
        name: 'ScenePunishEdit',
        meta: { title: '编辑行政处罚', activeMenu: '/data-management/scene' },
        hidden: true
      },
      {
        path: 'scene/punish/detail/:id',
        component: () => import('@/views/data-management/scene/punish/detail'),
        name: 'ScenePunishDetail',
        meta: { title: '行政处罚详情', activeMenu: '/data-management/scene' },
        hidden: true
      },
      // 行政处置路由
      {
        path: 'scene/disposal/add',
        component: () => import('@/views/data-management/scene/disposal/add'),
        name: 'SceneDisposalAdd',
        meta: { title: '新增行政处置', activeMenu: '/data-management/scene' },
        hidden: true
      },
      {
        path: 'scene/disposal/edit/:id',
        component: () => import('@/views/data-management/scene/disposal/add'),
        name: 'SceneDisposalEdit',
        meta: { title: '编辑行政处置', activeMenu: '/data-management/scene' },
        hidden: true
      },
      {
        path: 'scene/disposal/detail/:id',
        component: () => import('@/views/data-management/scene/disposal/detail'),
        name: 'SceneDisposalDetail',
        meta: { title: '行政处置详情', activeMenu: '/data-management/scene' },
        hidden: true
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  }
  
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: () => import('@/views/system/user/authRole'),
        name: 'AuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' }
      }
    ]
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    permissions: ['system:dict:list'],
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/system/dict/data'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict' }
      }
    ]
  },
  {
    path: '/monitor/job-log',
    component: Layout,
    hidden: true,
    permissions: ['monitor:job:list'],
    children: [
      {
        path: 'index/:jobId(\\d+)',
        component: () => import('@/views/monitor/job/log'),
        name: 'JobLog',
        meta: { title: '调度日志', activeMenu: '/monitor/job' }
      }
    ]
  },
  {
    path: '/tool/gen-edit',
    component: Layout,
    hidden: true,
    permissions: ['tool:gen:edit'],
    children: [
      {
        path: 'index/:tableId(\\d+)',
        component: () => import('@/views/tool/gen/editTable'),
        name: 'GenEdit',
        meta: { title: '修改生成配置', activeMenu: '/tool/gen' }
      }
    ]
  }
]

// 防止连续点击多次路由报错
let routerPush = Router.prototype.push
let routerReplace = Router.prototype.replace
// push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch(err => err)
}
// replace
Router.prototype.replace = function push(location) {
  return routerReplace.call(this, location).catch(err => err)
}

export default new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior(to, from, savedPosition) {
    // 如果有保存的位置（比如浏览器前进后退），使用保存的位置
    if (savedPosition) {
      return savedPosition
    }
    // 否则滚动到顶部
    return { x: 0, y: 0 }
  },
  routes: constantRoutes
})
