import Mock from 'mockjs'

// 基础数据台账统计
const dataStatistics = {
  code: 200,
  msg: '操作成功',
  data: [
    {
      key: 'screenNum',
      name: '电子屏',
      totalCount: 156,
      pendingCount: 8,
      overdueCount: 3,
      trendPercent: 10,
      trendType: 'up'
    },
    {
      key: 'levelprotectNum',
      name: '等保备案',
      totalCount: 89,
      pendingCount: 5,
      overdueCount: 2,
      trendPercent: -5,
      trendType: 'down'
    },
    {
      key: 'websiteNum',
      name: '网站信息备案',
      totalCount: 234,
      pendingCount: 12,
      overdueCount: 7,
      trendPercent: 15,
      trendType: 'up'
    },
    {
      key: 'appNum',
      name: 'APP',
      totalCount: 67,
      pendingCount: 3,
      overdueCount: 1,
      trendPercent: -8,
      trendType: 'down'
    },
    {
      key: 'appletNum',
      name: '小程序',
      totalCount: 45,
      pendingCount: 2,
      overdueCount: 0,
      trendPercent: 20,
      trendType: 'up'
    },
    {
      key: 'netbarNum',
      name: '网吧',
      totalCount: 123,
      pendingCount: 6,
      overdueCount: 4,
      trendPercent: 5,
      trendType: 'up'
    },
    {
      key: 'operatorNum',
      name: '运营商',
      totalCount: 78,
      pendingCount: 4,
      overdueCount: 2,
      trendPercent: -12,
      trendType: 'down'
    },
    {
      key: 'wifiNum',
      name: '非经营',
      totalCount: 198,
      pendingCount: 9,
      overdueCount: 5,
      trendPercent: 8,
      trendType: 'up'
    }
  ]
}

// 全量数据变化情况
const dataChangeHistory = {
  code: 200,
  msg: '操作成功',
  data: {
    months: ['2023.9', '2023.10', '2023.11', '2023.12', '2024.1', '2024.2', '2024.3'],
    change: [120, 132, 101, 134, 90, 230, 210],
    increase: [220, 182, 191, 234, 290, 330, 310]
  }
}

// 数据分布情况
const dataDistribution = {
  code: 200,
  msg: '操作成功',
  data: [
    { name: '电子屏', value: 156, itemStyle: { color: '#0081ff' } },
    { name: '等保备案', value: 89, itemStyle: { color: '#1abb78' } },
    { name: '网站信息备案', value: 234, itemStyle: { color: '#ffc53d' } },
    { name: 'APP', value: 67, itemStyle: { color: '#ff7a45' } },
    { name: '小程序', value: 45, itemStyle: { color: '#722ed1' } },
    { name: '网吧', value: 123, itemStyle: { color: '#eb2f96' } },
    { name: '运营商', value: 78, itemStyle: { color: '#52c41a' } },
    { name: '非经营', value: 198, itemStyle: { color: '#13c2c2' } }
  ]
}

// 各派出所数据贡献情况
const policeStationContribution = {
  total: {
    code: 200,
    msg: '操作成功',
    data: [
      { name: '九棵树派出所', value: 1072 },
      { name: '北苑派出所', value: 1678 },
      { name: '来广营派出所', value: 893 },
      { name: '焦王庄派出所', value: 555 },
      { name: '永顺派出所', value: 742 }
    ]
  },
  update: {
    code: 200,
    msg: '操作成功',
    data: [
      { name: '九棵树派出所', value: 532 },
      { name: '北苑派出所', value: 678 },
      { name: '来广营派出所', value: 393 },
      { name: '焦王庄派出所', value: 255 },
      { name: '永顺派出所', value: 442 }
    ]
  },
  insert: {
    code: 200,
    msg: '操作成功',
    data: [
      { name: '九棵树派出所', value: 340 },
      { name: '北苑派出所', value: 500 },
      { name: '来广营派出所', value: 300 },
      { name: '焦王庄派出所', value: 200 },
      { name: '永顺派出所', value: 200 }
    ]
  },
  delete: {
    code: 200,
    msg: '操作成功',
    data: [
      { name: '九棵树派出所', value: 200 },
      { name: '北苑派出所', value: 500 },
      { name: '来广营派出所', value: 200 },
      { name: '焦王庄派出所', value: 100 },
      { name: '永顺派出所', value: 100 }
    ]
  }
}

// Mock API
Mock.mock('/dev-api/data-management/statistics', 'get', () => {
  return dataStatistics
})

Mock.mock('/dev-api/data-management/change-history', 'get', () => {
  return dataChangeHistory
})

Mock.mock('/dev-api/data-management/distribution', 'get', () => {
  return dataDistribution
})

Mock.mock(/\/dev-api\/data-management\/police-contribution/, 'get', (options) => {
  const url = new URL(options.url, 'http://localhost')
  const type = url.searchParams.get('type') || 'total'
  return policeStationContribution[type] || policeStationContribution.total
})

Mock.mock(/\/dev-api\/data-management\/search/, 'get', (options) => {
  const url = new URL(options.url, 'http://localhost')
  const searchType = url.searchParams.get('searchType') || 'totalNum'
  const keyword = url.searchParams.get('keyword') || ''
  
  return {
    code: 200,
    msg: '操作成功',
    data: {
      searchType,
      keyword,
      results: Mock.mock({
        'list|10-20': [{
          'id|+1': 1,
          'name': '@cname',
          'type': searchType,
          'value|1-1000': 100,
          'updateTime': '@datetime'
        }]
      }).list
    }
  }
})

export default {
  dataStatistics,
  dataChangeHistory,
  dataDistribution,
  policeStationContribution
}
