<template>
  <div class="screen-management">
    <!-- 搜索组件 -->
    <common-search
      :search-config="searchConfig"
      :advanced-fields="advancedFields"
      :initial-form="searchForm"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增电子屏</el-button>
      <el-button 
        type="danger" 
        icon="el-icon-delete" 
        :disabled="selectedRows.length === 0"
        @click="handleBatchDelete"
      >
        批量删除
      </el-button>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        ref="dataTable"
        v-loading="loading"
        :data="tableData"
        :header-cell-style="{ backgroundColor: '#EBF2FF' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="screenName" label="电子屏名称" min-width="150" />
        <el-table-column prop="location" label="安装位置" min-width="120" />
        <el-table-column prop="screenType" label="屏幕类型" width="100">
          <template slot-scope="scope">
            <el-tag :type="getScreenTypeTag(scope.row.screenType)">
              {{ getScreenTypeText(scope.row.screenType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="resolution" label="分辨率" width="120" />
        <el-table-column prop="status" label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'">
              {{ scope.row.status === '1' ? '正常' : '异常' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="responsiblePerson" label="负责人" width="100" />
        <el-table-column prop="contactPhone" label="联系电话" width="120" />
        <el-table-column prop="installDate" label="安装日期" width="120" />
        <el-table-column prop="lastUpdateTime" label="最后更新时间" width="160" />
        <el-table-column label="操作" width="180" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="text" size="small" style="color: #F56C6C;" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <screen-form-dialog
      :visible.sync="dialogVisible"
      :form-data="currentRow"
      :is-edit="isEdit"
      @success="handleFormSuccess"
    />

    <!-- 详情弹窗 -->
    <screen-detail-dialog
      :visible.sync="detailVisible"
      :detail-data="currentRow"
      @edit="handleEditFromDetail"
    />
  </div>
</template>

<script>
import CommonSearch from '@/components/CommonSearch'
import ScreenFormDialog from './ScreenFormDialog'
import ScreenDetailDialog from './ScreenDetailDialog'

export default {
  name: 'ScreenManagement',
  components: {
    CommonSearch,
    ScreenFormDialog,
    ScreenDetailDialog
  },
  data() {
    return {
      loading: false,
      tableData: [],
      selectedRows: [],
      dialogVisible: false,
      detailVisible: false,
      isEdit: false,
      currentRow: {},
      
      // 搜索配置
      searchConfig: {
        label: '电子屏名称或关键字',
        key: 'keyword',
        placeholder: '请输入电子屏名称或关键字'
      },
      
      // 高级搜索字段
      advancedFields: [
        {
          label: '屏幕类型',
          key: 'screenType',
          type: 'select',
          placeholder: '请选择屏幕类型',
          span: 8,
          options: [
            { label: 'LED屏', value: '1' },
            { label: 'LCD屏', value: '2' },
            { label: '投影屏', value: '3' },
            { label: '其他', value: '4' }
          ]
        },
        {
          label: '状态',
          key: 'status',
          type: 'select',
          placeholder: '请选择状态',
          span: 8,
          options: [
            { label: '正常', value: '1' },
            { label: '异常', value: '0' }
          ]
        },
        {
          label: '安装位置',
          key: 'location',
          type: 'input',
          placeholder: '请输入安装位置',
          span: 8
        },
        {
          label: '负责人',
          key: 'responsiblePerson',
          type: 'input',
          placeholder: '请输入负责人',
          span: 8
        },
        {
          label: '安装日期',
          key: 'installDateRange',
          type: 'daterange',
          placeholder: '请选择安装日期范围',
          span: 16
        }
      ],
      
      // 搜索表单
      searchForm: {},
      
      // 分页配置
      pagination: {
        pageNum: 1,
        pageSize: 20,
        total: 0
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    // 加载数据
    async loadData() {
      this.loading = true
      try {
        // 构建查询参数
        const params = {
          ...this.searchForm,
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize
        }
        
        // 这里调用实际的API
        // const response = await getScreenList(params)
        
        // 模拟数据
        const mockData = this.generateMockData()
        this.tableData = mockData.list
        this.pagination.total = mockData.total
        
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    
    // 生成模拟数据
    generateMockData() {
      const list = []
      for (let i = 1; i <= 20; i++) {
        list.push({
          id: i,
          screenName: `电子屏${i.toString().padStart(3, '0')}`,
          location: `位置${i}`,
          screenType: String((i % 4) + 1),
          resolution: '1920x1080',
          status: i % 3 === 0 ? '0' : '1',
          responsiblePerson: `负责人${i}`,
          contactPhone: `138${i.toString().padStart(8, '0')}`,
          installDate: '2024-01-01',
          lastUpdateTime: '2024-01-01 10:00:00'
        })
      }
      return {
        list,
        total: 100
      }
    },
    
    // 处理搜索
    handleSearch(searchForm) {
      this.searchForm = searchForm
      this.pagination.pageNum = 1
      this.loadData()
    },

    // 处理重置
    handleReset(searchForm) {
      this.searchForm = searchForm
      this.pagination.pageNum = 1
      this.loadData()
    },
    
    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    
    // 处理新增
    handleAdd() {
      this.currentRow = {}
      this.isEdit = false
      this.dialogVisible = true
    },
    
    // 处理编辑
    handleEdit(row) {
      this.currentRow = { ...row }
      this.isEdit = true
      this.dialogVisible = true
    },
    
    // 处理查看
    handleView(row) {
      this.currentRow = { ...row }
      this.detailVisible = true
    },

    // 从详情页编辑
    handleEditFromDetail(row) {
      this.currentRow = { ...row }
      this.isEdit = true
      this.dialogVisible = true
    },
    
    // 处理删除
    handleDelete(row) {
      this.$confirm('确认删除该电子屏吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里调用删除API
        this.$message.success('删除成功')
        this.loadData()
      }).catch(() => {})
    },
    
    // 处理批量删除
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      
      this.$confirm(`确认删除选中的${this.selectedRows.length}条数据吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里调用批量删除API
        this.$message.success('批量删除成功')
        this.loadData()
      }).catch(() => {})
    },
    
    // 处理表单提交成功
    handleFormSuccess() {
      this.dialogVisible = false
      this.loadData()
    },
    
    // 处理页码变化
    handleCurrentChange(page) {
      this.pagination.pageNum = page
      this.loadData()
    },
    
    // 处理页大小变化
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.loadData()
    },
    
    // 获取屏幕类型标签
    getScreenTypeTag(type) {
      const tagMap = {
        '1': 'primary',
        '2': 'success',
        '3': 'warning',
        '4': 'info'
      }
      return tagMap[type] || 'info'
    },
    
    // 获取屏幕类型文本
    getScreenTypeText(type) {
      const textMap = {
        '1': 'LED屏',
        '2': 'LCD屏',
        '3': '投影屏',
        '4': '其他'
      }
      return textMap[type] || '未知'
    }
  }
}
</script>

<style lang="scss" scoped>
.screen-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .action-bar {
    background: white;
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    gap: 12px;
  }

  .table-container {
    background: white;
    border-radius: 8px;
    padding: 20px;

    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }
}
</style>
