<template>
  <div class="enterprise-edit-drawer" v-show="visible">
    <!-- 遮罩层 -->
    <div class="drawer-overlay" @click="handleClose" v-show="visible"></div>

    <!-- 抽屉容器 -->
    <div
      class="drawer-container"
      :style="{ width: drawerWidth + 'px' }"
      v-show="visible"
    >
      <!-- 拖拽调整宽度的手柄 -->
      <div
        class="resize-handle"
        @mousedown="startResize"
      ></div>

      <!-- 抽屉头部 -->
      <div class="drawer-header">
        <div class="header-left">
          <h3 class="drawer-title">编辑企业信息</h3>
          <span class="enterprise-name">{{ enterpriseData.name || '未知企业' }}</span>
        </div>
        <div class="header-right">
          <el-button
            type="primary"
            size="small"
            @click="handleSave"
            :loading="saving"
          >
            保存
          </el-button>
          <el-button
            size="small"
            @click="handleClose"
          >
            取消
          </el-button>
          <i class="el-icon-close close-icon" @click="handleClose"></i>
        </div>
      </div>

      <!-- 抽屉内容 -->
      <div class="drawer-content">
        <el-form
          ref="enterpriseForm"
          :model="formData"
          :rules="formRules"
          label-width="120px"
          size="small"
        >
          <!-- 企业主体信息 -->
          <div class="form-section">
            <div class="section-title">
              <i class="el-icon-office-building"></i>
              企业主体信息
            </div>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="企业名称" prop="name">
                  <el-input v-model="formData.name" placeholder="请输入企业名称"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="统一社会信用代码" prop="creditCode">
                  <el-input v-model="formData.creditCode" placeholder="请输入统一社会信用代码"></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="企业类型" prop="type">
                  <el-select v-model="formData.type" placeholder="请选择企业类型" style="width: 100%">
                    <el-option label="有限责任公司" value="有限责任公司"></el-option>
                    <el-option label="股份有限公司" value="股份有限公司"></el-option>
                    <el-option label="个人独资企业" value="个人独资企业"></el-option>
                    <el-option label="合伙企业" value="合伙企业"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="注册资本" prop="registeredCapital">
                  <el-input v-model="formData.registeredCapital" placeholder="请输入注册资本">
                    <template slot="append">万元</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="成立日期" prop="establishDate">
                  <el-date-picker
                    v-model="formData.establishDate"
                    type="date"
                    placeholder="请选择成立日期"
                    style="width: 100%"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="经营状态" prop="status">
                  <el-select v-model="formData.status" placeholder="请选择经营状态" style="width: 100%">
                    <el-option label="存续" value="存续"></el-option>
                    <el-option label="在业" value="在业"></el-option>
                    <el-option label="吊销" value="吊销"></el-option>
                    <el-option label="注销" value="注销"></el-option>
                    <el-option label="迁出" value="迁出"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="注册地址" prop="address">
              <el-input
                v-model="formData.address"
                type="textarea"
                :rows="2"
                placeholder="请输入注册地址"
              ></el-input>
            </el-form-item>

            <!-- <el-form-item label="经营范围" prop="businessScope">
              <el-input
                v-model="formData.businessScope"
                type="textarea"
                :rows="3"
                placeholder="请输入经营范围"
              ></el-input>
            </el-form-item> -->
          </div>

          <!-- 企业基础信息 -->
          <div class="form-section">
            <div class="section-title">
              <i class="el-icon-user"></i>
              企业基础信息
            </div>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="企业性质" prop="nature">
                  <el-select v-model="formData.nature" placeholder="请选择企业性质" style="width: 100%">
                    <el-option label="国有企业" value="国有企业"></el-option>
                    <el-option label="民营企业" value="民营企业"></el-option>
                    <el-option label="外资企业" value="外资企业"></el-option>
                    <el-option label="合资企业" value="合资企业"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="警务备案号" prop="policeFilingNumber">
                  <el-input v-model="formData.policeFilingNumber" placeholder="请输入警务备案号"></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="关联其他" prop="relatedOthers">
              <el-input
                v-model="formData.relatedOthers"
                type="textarea"
                :rows="2"
                placeholder="请输入关联其他信息"
              ></el-input>
            </el-form-item>
          </div>

          <!-- 企业法人信息 -->
          <div class="form-section">
            <div class="section-title">
              <i class="el-icon-user-solid"></i>
              企业法人信息
            </div>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="法人姓名" prop="legalPerson.name">
                  <el-input v-model="formData.legalPerson.name" placeholder="请输入法人姓名"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="法人联系电话" prop="legalPerson.phone">
                  <el-input v-model="formData.legalPerson.phone" placeholder="请输入法人联系电话"></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="证件类型" prop="legalPerson.idType">
                  <el-select v-model="formData.legalPerson.idType" placeholder="请选择证件类型" style="width: 100%">
                    <el-option label="身份证" value="身份证"></el-option>
                    <el-option label="护照" value="护照"></el-option>
                    <el-option label="港澳通行证" value="港澳通行证"></el-option>
                    <el-option label="台胞证" value="台胞证"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="证件号码" prop="legalPerson.idNumber">
                  <el-input v-model="formData.legalPerson.idNumber" placeholder="请输入证件号码"></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="证件有效期" prop="legalPerson.expiryDate">
                  <el-date-picker
                    v-model="formData.legalPerson.expiryDate"
                    type="date"
                    placeholder="请选择证件有效期"
                    style="width: 100%"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 法人证件照片上传 -->
            <div class="photo-upload-section">
              <h5 class="photo-section-title">法人证件照片</h5>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="证件(人像)">
                    <el-upload
                      class="photo-uploader"
                      action="#"
                      :show-file-list="false"
                      :before-upload="(file) => beforeUpload(file, 'legalPerson', 'portraitPhoto')"
                    >
                      <img v-if="formData.legalPerson.portraitPhoto" :src="formData.legalPerson.portraitPhoto" class="photo-preview">
                      <i v-else class="el-icon-plus photo-uploader-icon"></i>
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="证件(国徽)">
                    <el-upload
                      class="photo-uploader"
                      action="#"
                      :show-file-list="false"
                      :before-upload="(file) => beforeUpload(file, 'legalPerson', 'nationalEmblemPhoto')"
                    >
                      <img v-if="formData.legalPerson.nationalEmblemPhoto" :src="formData.legalPerson.nationalEmblemPhoto" class="photo-preview">
                      <i v-else class="el-icon-plus photo-uploader-icon"></i>
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="证件(手持)">
                    <el-upload
                      class="photo-uploader"
                      action="#"
                      :show-file-list="false"
                      :before-upload="(file) => beforeUpload(file, 'legalPerson', 'holdingPhoto')"
                    >
                      <img v-if="formData.legalPerson.holdingPhoto" :src="formData.legalPerson.holdingPhoto" class="photo-preview">
                      <i v-else class="el-icon-plus photo-uploader-icon"></i>
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>

          <!-- 企业负责人信息 -->
          <div class="form-section">
            <div class="section-title">
              <i class="el-icon-s-custom"></i>
              企业负责人信息
            </div>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="负责人姓名" prop="responsiblePerson.name">
                  <el-input v-model="formData.responsiblePerson.name" placeholder="请输入负责人姓名"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="负责人联系电话" prop="responsiblePerson.phone">
                  <el-input v-model="formData.responsiblePerson.phone" placeholder="请输入负责人联系电话"></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="证件类型" prop="responsiblePerson.idType">
                  <el-select v-model="formData.responsiblePerson.idType" placeholder="请选择证件类型" style="width: 100%">
                    <el-option label="身份证" value="身份证"></el-option>
                    <el-option label="护照" value="护照"></el-option>
                    <el-option label="港澳通行证" value="港澳通行证"></el-option>
                    <el-option label="台胞证" value="台胞证"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="证件号码" prop="responsiblePerson.idNumber">
                  <el-input v-model="formData.responsiblePerson.idNumber" placeholder="请输入证件号码"></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="证件有效期" prop="responsiblePerson.expiryDate">
                  <el-date-picker
                    v-model="formData.responsiblePerson.expiryDate"
                    type="date"
                    placeholder="请选择证件有效期"
                    style="width: 100%"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="办公室电话" prop="responsiblePerson.officePhone">
                  <el-input v-model="formData.responsiblePerson.officePhone" placeholder="请输入办公室电话"></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="电子邮件" prop="responsiblePerson.email">
                  <el-input v-model="formData.responsiblePerson.email" placeholder="请输入电子邮件"></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="常驻地址" prop="responsiblePerson.address">
              <el-input
                v-model="formData.responsiblePerson.address"
                type="textarea"
                :rows="2"
                placeholder="请输入常驻地址"
              ></el-input>
            </el-form-item>

            <!-- 责任人证件照片上传 -->
            <div class="photo-upload-section">
              <h5 class="photo-section-title">责任人证件照片</h5>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="证件(人像)">
                    <el-upload
                      class="photo-uploader"
                      action="#"
                      :show-file-list="false"
                      :before-upload="(file) => beforeUpload(file, 'responsiblePerson', 'portraitPhoto')"
                    >
                      <img v-if="formData.responsiblePerson.portraitPhoto" :src="formData.responsiblePerson.portraitPhoto" class="photo-preview">
                      <i v-else class="el-icon-plus photo-uploader-icon"></i>
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="证件(国徽)">
                    <el-upload
                      class="photo-uploader"
                      action="#"
                      :show-file-list="false"
                      :before-upload="(file) => beforeUpload(file, 'responsiblePerson', 'nationalEmblemPhoto')"
                    >
                      <img v-if="formData.responsiblePerson.nationalEmblemPhoto" :src="formData.responsiblePerson.nationalEmblemPhoto" class="photo-preview">
                      <i v-else class="el-icon-plus photo-uploader-icon"></i>
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="证件(手持)">
                    <el-upload
                      class="photo-uploader"
                      action="#"
                      :show-file-list="false"
                      :before-upload="(file) => beforeUpload(file, 'responsiblePerson', 'holdingPhoto')"
                    >
                      <img v-if="formData.responsiblePerson.holdingPhoto" :src="formData.responsiblePerson.holdingPhoto" class="photo-preview">
                      <i v-else class="el-icon-plus photo-uploader-icon"></i>
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { updateEnterprise } from '@/api/enterprise'

export default {
  name: 'EnterpriseEditDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    enterpriseData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      drawerWidth: 800, // 抽屉默认宽度
      minWidth: 600,    // 最小宽度
      maxWidth: 1200,   // 最大宽度
      isResizing: false,
      saving: false,
      formData: {
        id: null,
        name: '',
        creditCode: '',
        type: '',
        registeredCapital: '',
        establishDate: '',
        status: '',
        address: '',
        businessScope: '',
        nature: '',
        policeFilingNumber: '',
        relatedOthers: '',
        // 法人信息
        legalPerson: {
          name: '',
          phone: '',
          idType: '身份证',
          idNumber: '',
          expiryDate: '',
          portraitPhoto: '',
          nationalEmblemPhoto: '',
          holdingPhoto: ''
        },
        // 责任人信息
        responsiblePerson: {
          name: '',
          phone: '',
          idType: '身份证',
          idNumber: '',
          expiryDate: '',
          officePhone: '',
          email: '',
          address: '',
          portraitPhoto: '',
          nationalEmblemPhoto: '',
          holdingPhoto: ''
        }
      },
      formRules: {
        name: [
          { required: true, message: '请输入企业名称', trigger: 'blur' }
        ],
        creditCode: [
          { required: true, message: '请输入统一社会信用代码', trigger: 'blur' },
          { pattern: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/, message: '统一社会信用代码格式不正确', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择企业类型', trigger: 'change' }
        ],
        'legalPerson.name': [
          { required: true, message: '请输入法人姓名', trigger: 'blur' }
        ],
        'legalPerson.phone': [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        'legalPerson.idNumber': [
          { required: true, message: '请输入证件号码', trigger: 'blur' }
        ],
        'responsiblePerson.name': [
          { required: true, message: '请输入负责人姓名', trigger: 'blur' }
        ],
        'responsiblePerson.phone': [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        'responsiblePerson.email': [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initFormData()
        document.addEventListener('mousemove', this.handleMouseMove)
        document.addEventListener('mouseup', this.stopResize)
      } else {
        document.removeEventListener('mousemove', this.handleMouseMove)
        document.removeEventListener('mouseup', this.stopResize)
      }
    },
    enterpriseData: {
      handler() {
        if (this.visible) {
          this.initFormData()
        }
      },
      deep: true
    }
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      this.formData = {
        id: this.enterpriseData.id || null,
        name: this.enterpriseData.name || '',
        creditCode: this.enterpriseData.creditCode || '',
        type: this.enterpriseData.type || '',
        registeredCapital: this.enterpriseData.registeredCapital || '',
        establishDate: this.enterpriseData.establishDate || '',
        status: this.enterpriseData.status || '',
        address: this.enterpriseData.address || '',
        businessScope: this.enterpriseData.businessScope || '',
        nature: this.enterpriseData.nature || '',
        policeFilingNumber: this.enterpriseData.policeFilingNumber || '',
        relatedOthers: this.enterpriseData.relatedOthers || '',
        // 法人信息
        legalPerson: {
          name: this.enterpriseData.legalPerson?.name || this.enterpriseData.legalPerson || '',
          phone: this.enterpriseData.legalPerson?.phone || this.enterpriseData.legalPersonPhone || '',
          idType: this.enterpriseData.legalPerson?.idType || '身份证',
          idNumber: this.enterpriseData.legalPerson?.idNumber || '',
          expiryDate: this.enterpriseData.legalPerson?.expiryDate || '',
          portraitPhoto: this.enterpriseData.legalPerson?.portraitPhoto || '',
          nationalEmblemPhoto: this.enterpriseData.legalPerson?.nationalEmblemPhoto || '',
          holdingPhoto: this.enterpriseData.legalPerson?.holdingPhoto || ''
        },
        // 责任人信息
        responsiblePerson: {
          name: this.enterpriseData.responsiblePerson?.name || this.enterpriseData.responsiblePerson || '',
          phone: this.enterpriseData.responsiblePerson?.phone || this.enterpriseData.responsiblePersonPhone || '',
          idType: this.enterpriseData.responsiblePerson?.idType || '身份证',
          idNumber: this.enterpriseData.responsiblePerson?.idNumber || '',
          expiryDate: this.enterpriseData.responsiblePerson?.expiryDate || '',
          officePhone: this.enterpriseData.responsiblePerson?.officePhone || '',
          email: this.enterpriseData.responsiblePerson?.email || '',
          address: this.enterpriseData.responsiblePerson?.address || '',
          portraitPhoto: this.enterpriseData.responsiblePerson?.portraitPhoto || '',
          nationalEmblemPhoto: this.enterpriseData.responsiblePerson?.nationalEmblemPhoto || '',
          holdingPhoto: this.enterpriseData.responsiblePerson?.holdingPhoto || ''
        }
      }
    },

    // 开始拖拽调整宽度
    startResize(e) {
      this.isResizing = true
      e.preventDefault()
    },

    // 处理鼠标移动
    handleMouseMove(e) {
      if (!this.isResizing) return

      const newWidth = window.innerWidth - e.clientX
      if (newWidth >= this.minWidth && newWidth <= this.maxWidth) {
        this.drawerWidth = newWidth
      }
    },

    // 停止拖拽
    stopResize() {
      this.isResizing = false
    },

    // 保存企业信息
    async handleSave() {
      try {
        await this.$refs.enterpriseForm.validate()
        this.saving = true

        await updateEnterprise(this.formData)

        this.$message.success('企业信息更新成功')
        this.$emit('save-success', this.formData)
        this.handleClose()
      } catch (error) {
        if (error !== false) { // 不是表单验证错误
          this.$message.error('保存失败：' + (error.message || '未知错误'))
        }
      } finally {
        this.saving = false
      }
    },

    // 关闭抽屉
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
      // 重置表单
      this.$nextTick(() => {
        this.$refs.enterpriseForm && this.$refs.enterpriseForm.resetFields()
      })
    },

    // 照片上传前的验证
    beforeUpload(file, personType, fieldName) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('上传照片只能是 JPG/PNG 格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传照片大小不能超过 2MB!')
        return false
      }

      // 模拟上传成功，实际项目中这里应该调用真实的上传接口
      const reader = new FileReader()
      reader.onload = (e) => {
        // 这里模拟上传成功的回调
        this.handlePhotoSuccess({ url: e.target.result }, file, personType, fieldName)
      }
      reader.readAsDataURL(file)

      return false // 阻止默认上传行为
    },

    // 照片上传成功处理
    handlePhotoSuccess(response, file, personType, fieldName) {
      this.formData[personType][fieldName] = response.url || URL.createObjectURL(file)
      this.$message.success('照片上传成功')
    }
  },

  beforeDestroy() {
    document.removeEventListener('mousemove', this.handleMouseMove)
    document.removeEventListener('mouseup', this.stopResize)
  }
}
</script>

<style lang="scss" scoped>
.enterprise-edit-drawer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2000;

  .drawer-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    transition: opacity 0.3s ease;
  }

  .drawer-container {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    background-color: #fff;
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
    transform: translateX(0);
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;

    .resize-handle {
      position: absolute;
      left: 0;
      top: 0;
      width: 4px;
      height: 100%;
      background-color: transparent;
      cursor: ew-resize;
      z-index: 10;

      &:hover {
        background-color: #409EFF;
      }

      &:active {
        background-color: #337ecc;
      }
    }

    .drawer-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 24px;
      border-bottom: 1px solid #ebeef5;
      background-color: #fafafa;

      .header-left {
        display: flex;
        align-items: center;

        .drawer-title {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #303133;
          margin-right: 12px;
        }

        .enterprise-name {
          color: #909399;
          font-size: 14px;
        }
      }

      .header-right {
        display: flex;
        align-items: center;
        gap: 8px;

        .close-icon {
          font-size: 18px;
          color: #909399;
          cursor: pointer;
          padding: 4px;

          &:hover {
            color: #606266;
          }
        }
      }
    }

    .drawer-content {
      flex: 1;
      padding: 24px;
      overflow-y: auto;

      .form-section {
        margin-bottom: 32px;

        &:last-child {
          margin-bottom: 0;
        }

        .section-title {
          display: flex;
          align-items: center;
          margin-bottom: 20px;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          padding-bottom: 8px;
          border-bottom: 1px solid #ebeef5;

          i {
            margin-right: 8px;
            color: #409EFF;
          }
        }
      }
    }
  }
}

// 照片上传样式
.photo-upload-section {
  margin-top: 20px;

  .photo-section-title {
    margin: 0 0 16px 0;
    font-size: 14px;
    font-weight: 600;
    color: #606266;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 8px;
  }

  .photo-uploader {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      border-color: #409EFF;
    }

    .photo-uploader-icon {
      font-size: 28px;
      color: #8c939d;
    }

    .photo-preview {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

// 抽屉进入动画
.enterprise-edit-drawer.drawer-enter-active,
.enterprise-edit-drawer.drawer-leave-active {
  .drawer-overlay {
    transition: opacity 0.3s ease;
  }

  .drawer-container {
    transition: transform 0.3s ease;
  }
}

.enterprise-edit-drawer.drawer-enter,
.enterprise-edit-drawer.drawer-leave-to {
  .drawer-overlay {
    opacity: 0;
  }

  .drawer-container {
    transform: translateX(100%);
  }
}
</style>
