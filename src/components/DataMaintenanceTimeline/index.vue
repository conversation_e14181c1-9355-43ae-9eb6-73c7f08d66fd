<template>
  <div class="data-maintenance-timeline">
    <div class="timeline-header">
      <h3 class="timeline-title">数据维护记录</h3>
    </div>
    
    <div class="timeline-content" v-loading="loading">
      <el-timeline v-if="timelineData.length > 0">
        <el-timeline-item
          v-for="(item, index) in timelineData"
          :key="index"
          :icon="'el-icon-time'"
          color="#409EFF"
          size="large"
        >
          <div class="timeline-item-content">
            <!-- 第一行：时间和变更类型 -->
            <div class="timeline-row timeline-header-row">
              <span class="timeline-time">{{ item.createTime }}</span>
              <el-tag
                :style="getTagStyle(item.changeType)"
                size="small"
              >
                数据{{ getChangeTypeLabel(item.changeType) }}
              </el-tag>
            </div>

            <!-- 第二行：部门-职务-姓名 -->
            <div class="timeline-row timeline-info-row">
              <span class="timeline-info">
                {{ item.deptName }} - {{ item.postName || '未知职务' }} - {{ item.createBy }}
              </span>
            </div>

            <!-- 循环显示所有变更字段 -->
            <template v-if="item.changeFields && item.changeFields.length > 0">
              <div
                v-for="(field, fieldIndex) in item.changeFields"
                :key="fieldIndex"
                class="field-change-group"
              >
                <!-- 第三行：字段名称 -->
                <div class="timeline-row timeline-field-row">
                  <span class="timeline-field-name">{{ field.fieldName }}：</span>
                </div>

                <!-- 第四行：变更内容 -->
                <div class="timeline-row timeline-change-row">
                  <!-- 变更前内容 -->
                  <span v-if="!isImageField(field.changeBefore)" class="timeline-change-before">{{ field.changeBefore }}</span>
                  <div v-else class="timeline-image-container">
                    <el-image
                      :src="processImageUrl(field.changeBefore)"
                      :preview-src-list="[processImageUrl(field.changeBefore)]"
                      class="timeline-image"
                      fit="cover"
                    >
                      <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                      </div>
                    </el-image>
                  </div>

                  <span class="timeline-change-action">{{ getChangeAction(item.changeType) }}为：</span>

                  <!-- 变更后内容 -->
                  <span v-if="!isImageField(field.changeAfter)" class="timeline-change-after">{{ field.changeAfter }}</span>
                  <div v-else class="timeline-image-container">
                    <el-image
                      :src="processImageUrl(field.changeAfter)"
                      :preview-src-list="[processImageUrl(field.changeAfter)]"
                      class="timeline-image"
                      fit="cover"
                    >
                      <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                      </div>
                    </el-image>
                  </div>
                </div>
              </div>
            </template>

            <!-- 如果是新增操作且没有changeFields，显示新增提示 -->
            <template v-else-if="item.changeType === 'INSERT'">
              <div class="timeline-row timeline-field-row">
                <span class="timeline-field-name">新增数据记录</span>
              </div>
            </template>
          </div>
        </el-timeline-item>
      </el-timeline>
      
      <!-- 空状态 -->
      <div v-else class="timeline-empty">
        <el-empty description="暂无数据维护记录" ></el-empty>
      </div>
      
      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        layout="total, prev, pager, next"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getTimelineData"
        size="mini"
        style="padding:0 15px 10px 0;"
      />

      
    </div>
  </div>
</template>

<script>
import { getChangeData } from '@/api/data-management'

export default {
  name: 'DataMaintenanceTimeline',
  props: {
    // 业务类型
    bizType: {
      type: String,
      required: true
    },
    // 业务ID
    bizId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      loading: false,
      timelineData: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        bizType: '',
        bizId: ''
      }
    }
  },
  watch: {
    bizType: {
      handler(newVal) {
        this.queryParams.bizType = newVal
        this.getTimelineData()
      },
      immediate: true
    },
    bizId: {
      handler(newVal) {
        this.queryParams.bizId = newVal
        this.getTimelineData()
      },
      immediate: true
    }
  },
  methods: {
    // 获取时间线数据
    async getTimelineData() {
      if (!this.queryParams.bizType || !this.queryParams.bizId) {
        return
      }
      
      this.loading = true
      try {
        let response = await getChangeData(this.queryParams)
       
        this.timelineData = response.rows || []
        console.log('----timelineData----', this.timelineData)
        this.total = response.total || 0
      } catch (error) {
        console.error('获取数据维护记录失败:', error)
        this.$message.error('获取数据维护记录失败')
      } finally {
        this.loading = false
      }
    },
    
    // 获取标签样式
    getTagStyle(changeType) {
      if (changeType === 'UPDATE') {
        return {
          backgroundColor: '#F9F0FF',
          color: '#722ed1',
          border: '1px solid #722ed1'
        }
      } else if (changeType === 'AUDIT') {
        return {
          backgroundColor: '#F6FEED',
          color: '#52c41a',
          border: '1px solid #52c41a'
        }
      } else if (changeType === 'INSERT') {
        return {
          backgroundColor: '#E6F7FF',
          color: '#1890ff',
          border: '1px solid #1890ff'
        }
      }
      return {}
    },

    // 获取变更类型标签文字
    getChangeTypeLabel(changeType) {
      if (changeType === 'UPDATE') {
        return '更新'
      } else if (changeType === 'AUDIT') {
        return '审核'
      } else if (changeType === 'INSERT') {
        return '新增'
      }
      return '变更'
    },

    // 获取变更动作文字
    getChangeAction(changeType) {
      if (changeType === 'UPDATE') {
        return '更新'
      } else if (changeType === 'AUDIT') {
        return '审核'
      } else if (changeType === 'INSERT') {
        return '新增'
      }
      return '变更'
    },

    // 判断是否为图片字段
    isImageField(value) {
      if (!value || typeof value !== 'string') return false;

      // 检查是否包含图片相关的关键词或文件扩展名
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
      const lowerValue = value.toLowerCase();

      // 检查是否包含文件下载路径或图片扩展名
      return lowerValue.includes('/file/download/') ||
             lowerValue.includes('upload') ||
             imageExtensions.some(ext => lowerValue.includes(ext));
    },

    // 处理图片URL，确保包含正确的前缀
    processImageUrl(url) {
      if (!url) return '';

      const baseUrl = process.env.VUE_APP_BASE_API + "/file/download/";

      // 如果已经包含完整的URL前缀，直接返回
      if (url.includes(baseUrl)) {
        return url;
      }

      // 如果包含其他域名，直接返回
      if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
      }

      // 否则拼接前缀
      return baseUrl + url;
    }
  }
}
</script>

<style lang="scss" scoped>
.data-maintenance-timeline {
  background:#fff;
  margin-top: 22px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 200px);
  overflow: hidden;

  .timeline-header {
    margin-bottom: 20px;
    padding-top:20px;
    background: #fff;
    position: sticky;
    top: 0;
    z-index: 10;

    .timeline-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      padding-left:30px;
      margin: 0;
    }
  }
  
  .timeline-content {
    flex: 1;
    overflow-y: auto;
    padding-top: 3px;
    .timeline-item-content {
      .timeline-row {
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .timeline-header-row {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .timeline-time {
          font-size: 14px;
          color: #606266;
          font-weight: 500;
        }
      }

      .timeline-info-row {
        .timeline-info {
          font-size: 13px;
          color: #606266;
        }
      }

      .field-change-group {
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px dashed #e4e7ed;

        &:last-child {
          margin-bottom: 0;
          border-bottom: none;
          padding-bottom: 0;
        }
      }

      .timeline-field-row {
        text-align: center;

        .timeline-field-name {
          font-size: 14px;
          color: #303133;
          font-weight: 500;
        }
      }

      .timeline-change-row {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 8px;

        .timeline-change-before {
          font-size: 13px;
          color: #606266;
        }

        .timeline-change-action {
          font-size: 13px;
          color: #4584FF;
          margin: 0 8px;
          font-weight: 500;
        }

        .timeline-change-after {
          font-size: 13px;
          color: #606266;
        }

        .timeline-image-container {
          display: inline-block;

          .timeline-image {
            width: 30px;
            height: auto;
            border-radius: 4px;
            cursor: pointer;
            border: 1px solid #dcdfe6;

            &:hover {
              border-color: #4584FF;
            }
          }

          .image-slot {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 30px;
            height: 30px;
            background: #f5f7fa;
            color: #909399;
            font-size: 14px;
            border-radius: 4px;
          }
        }
      }
    }
  }
  
  .timeline-empty {
    text-align: center;
    padding: 40px 0;
    ::v-deep .el-empty__image {
      width:80px;
       height:80px;
    }
  }
}

// 自定义时间线样式
::v-deep .el-timeline {
  padding-left: 24px;
  padding-right: 24px;
  .el-timeline-item__icon {
    width: 12px;
    height: 12px;
    border: 2px solid #409EFF;
    background-color: #fff;
    border-radius: 50%;
  }
  
  .el-timeline-item__wrapper {
    padding-left: 28px;
  }
  
  .el-timeline-item__content {
    padding-bottom: 20px;
  }
}
</style>
