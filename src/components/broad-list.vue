<template>
  <div class="broad-list-container" :class="{'horizontal': horizontal}">
    <div class="broad-list" :class="{'horizontal': horizontal}">
      <div
        v-for="(item, index) in items"
        :key="index"
        class="broad-list-item"
        @click="handleItemClick(item, index)"
        :class="{'active': activeIndex === index}"
        :style="{
          background: activeIndex === index
            ? item.backgroundColor_active
            : `linear-gradient(135deg, ${item.backgroundColor} 0%, ${getLightColor(item.backgroundColor_active)} 100%)`
        }"
      >
        <!-- 使用插槽允许自定义内容 -->
        <slot :item="item" :index="index">
          <div class="item-content">
            <div class="item-badge" v-if="item.badge" :class="{'active': activeIndex === index}">
              {{ item.badge }}
            </div>
            <div class="item-title" :class="{'active': activeIndex === index}">
              {{ item.title }}
            </div>
          </div>
        </slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BroadList',
  props: {
    items: {
      type: Array,
      required: true,
      default: () => []
    },
    horizontal: {
      type: Boolean,
      default: false
    },
    // 允许外部控制选中项
    selectedIndex: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      activeIndex: null
    }
  },
  mounted() {
    setTimeout(() => {
      console.log('*********',this.items)
    }, 3000);
  },
  watch: {
    // 监听外部传入的选中项变化
    selectedIndex(newVal) {
      this.activeIndex = newVal;
    }
  },
  methods: {
    handleItemClick(item, index) {
      this.$emit('item-click', item, index);
    },

    // 获取浅色版本的颜色
    getLightColor(color) {
      // 如果颜色是十六进制格式
      if (color.startsWith('#')) {
        // 移除#号
        const hex = color.slice(1);

        // 转换为RGB
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);

        // 计算浅色版本（增加亮度）
        const lightR = Math.min(255, Math.floor(r + (255 - r) * 0.7));
        const lightG = Math.min(255, Math.floor(g + (255 - g) * 0.7));
        const lightB = Math.min(255, Math.floor(b + (255 - b) * 0.7));

        // 转换回十六进制
        return `#${lightR.toString(16).padStart(2, '0')}${lightG.toString(16).padStart(2, '0')}${lightB.toString(16).padStart(2, '0')}`;
      }

      // 如果不是十六进制格式，返回默认浅色
      return '#f0f0f0';
    }
  }
}
</script>

<style lang="scss" scoped>
.broad-list-container {
  width: 100%;
  background-color: transparent;
  border-radius: 4px;

  .broad-list {
    display: flex;
    flex-direction: column;
    padding: 0;
    margin: 0;

    // 横向布局
    &.horizontal {
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: space-between;

      &::after {
        content: "";
        flex: 0 0 calc(12.5% - 10px); // 8个卡片时的占位，保证最后一行左对齐
      }

      .broad-list-item {
        flex: 0 0 calc(12.5% - 10px); // 8个卡片，每个占12.5%的宽度，减去间距
        margin: 5px;
        border: none;
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }

    .broad-list-item {
      position: relative;
      padding: 16px 10px;
      cursor: pointer;
      transition: background-color 0.3s ease;

      &.active {
        .item-title {
          color: #ffffff;
        }
      }

      .item-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;

        .item-badge {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 8px;
          color: inherit;
          transition: color 0.3s ease;

          &.active {
            color: #ffffff;
          }
        }

        .item-title {
          font-size: 14px;
          font-weight: 500;
          color: #303133;
          transition: color 0.3s ease;

          &.active {
            color: #ffffff;
          }
        }
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  .broad-list-container {
    .broad-list {
      &.horizontal {
        .broad-list-item {
          flex: 0 0 calc(25% - 10px); // 移动端4个卡片一行
        }

        &::after {
          content: "";
          flex: 0 0 calc(25% - 10px); // 移动端占位
        }
      }

      .broad-list-item {
        padding: 12px 8px;

        .item-content {
          .item-badge {
            font-size: 20px;
          }

          .item-title {
            font-size: 12px;
          }
        }
      }
    }
  }
}
</style>
