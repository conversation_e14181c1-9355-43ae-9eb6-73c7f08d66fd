<template>
  <el-drawer
    title="设置到期提醒"
    :visible.sync="visible"
    direction="rtl"
    :size="drawerWidth + 'px'"
    :before-close="handleClose"
    class="data-update-reminder-drawer"
  >
    <!-- 拖拽调整宽度的分隔线 -->
    <div 
      class="resize-handle" 
      @mousedown="startResize"
    ></div>

    <div class="reminder-content">
      <!-- 提醒规则列表 -->
      <div class="reminder-rules">
        <div
          v-for="(rule, index) in reminderRuleList"
          :key="index"
          class="reminder-rule-item"
        >
          <!-- 规则标题 -->
          <div class="rule-title">
            <div class="title-indicator"></div>
            <span class="title-text">到期提醒{{ index + 1 }}</span>
          </div>

          <el-form :model="rule" label-width="132px">
            <!-- 第一行：选择项目 -->
            <el-row :gutter="20" class="rule-row">
              <el-col :span="12">
                <el-form-item label="选择项目" required>
                  <el-select
                    v-model="rule.dataTable"
                    placeholder="请选择项目"
                    @change="handleTableChange(index)"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="table in tableList"
                      :key="table.tableName"
                      :label="table.name"
                      :value="table.tableName"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="4" class="delete-col">
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  @click="removeRule(index)"
                  class="delete-btn"
                >
                  删除
                </el-button>
              </el-col>
            </el-row>

            <!-- 第二行：设置更新字段 + 更新周期 -->
            <el-row :gutter="20" class="rule-row">
              <el-col :span="12">
                <el-form-item label="设置更新字段" required>
                  <el-select
                    v-model="rule.dataField"
                    placeholder="请选择更新字段"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="field in rule.fieldList || []"
                      :key="field.columnName"
                      :label="field.columnComment"
                      :value="field.columnName"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="更新周期" required>
                  <el-select
                    v-model="rule.expireRule"
                    placeholder="请选择更新周期"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="cycle in expireRuleOptions"
                      :key="cycle.value"
                      :label="cycle.label"
                      :value="cycle.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 第三行：设置智能提醒时间 + 设置智能提醒内容 -->
            <el-row :gutter="20" class="rule-row">
              <el-col :span="12">
                <el-form-item label="设置智能提醒时间">
                  <el-select
                    v-model="rule.earlyHour"
                    placeholder="请选择提醒时间"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="time in earlyHourOptions"
                      :key="time.value"
                      :label="time.label"
                      :value="time.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="设置智能提醒内容">
                  <el-input
                    v-model="rule.reminderContent"
                    placeholder="请输入提醒内容"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>

          <!-- 分隔线 -->
          <!-- <el-divider v-if="index < reminderRuleList.length - 1"></el-divider> -->
        </div>
      </div>

      <!-- 添加提醒按钮 -->
      <div class="add-reminder-btn">
        <div class="add-btn-custom" @click="addRule">
          <i class="el-icon-plus"></i>
          <span>添加提醒</span>
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="footer-actions">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { getTableListByBiz,getFieldMetadataByTable,batchAddReminder,getReminderRuleListByBiz } from '@/api/dataUpdateReminder'

export default {
  name: 'DataUpdateReminder',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    bizType: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      drawerWidth: 640,
      minWidth: 400,
      maxWidth: 1000,
      isResizing: false,
      startX: 0,
      startWidth: 0,
      
      saving: false,
      tableList: [],
      reminderRuleList: [],
      
      // 更新周期选项
      expireRuleOptions: [
        { label: '每天', value: '每天' },
        { label: '每三天', value: '每三天' },
        { label: '每周', value: '每周' },
        { label: '每月', value: '每月' },
        { label: '每半年', value: '每半年' },
        { label: '每一年', value: '每一年' },
        { label: '自定义', value: '自定义' }
      ],
      
      // 智能提醒时间选项
      earlyHourOptions: [
        { label: '提前1小时', value: 1 },
        { label: '提前2小时', value: 2 },
        { label: '提前3小时', value: 3 },
        { label: '提前5小时', value: 5 },
        { label: '提前1天', value: 24 },
        { label: '提前2天', value: 48 },
        { label: '提前3天', value: 72 },
        { label: '提前5天', value: 120 },
        { label: '提前10天', value: 240 },
        { label: '提前15天', value: 360 },
        { label: '提前30天', value: 720 }
      ]
    }
  },
  watch: {
    visible: {
      handler(newVal) {
        if (newVal) {
          this.initData()
        }
      },
      immediate: false
    }
  },
  methods: {
    // 初始化数据
    async initData() {
      try {
        await this.loadTableList()
        await this.loadExistingRules()
      } catch (error) {
        console.error('初始化数据失败:', error)
        this.$message.error('初始化数据失败: ' + (error.message || '未知错误'))
      }
    },

    // 加载已存在的提醒规则
    async loadExistingRules() {
      try {
        const response = await getReminderRuleListByBiz({biz:this.bizType})
        // const response = {
        //   "code": 0,
        //   "msg": null,
        //   "data": [
        //     {
        //       "id": "1",
        //       "buzType": "WIFI_EQUIPMENT",
        //       "database": "lan_wifi_manager",
        //       "dataTable": "wifi_equipment",
        //       "dataField": "equipment_name",
        //       "expireRule": "每天",
        //       "earlyHour": 1,
        //       "periodStart": null,}]}

              
        const existingRules = response.data || []

        if (existingRules.length > 0) {
          // 有已存在的规则，进行数据反显
          this.reminderRuleList = []

          for (const rule of existingRules) {
            const newRule = this.createEmptyRule()

            // 填充基本信息
            newRule.id = rule.id
            newRule.dataTable = rule.dataTable
            newRule.database = rule.database
            newRule.dataField = rule.dataField
            newRule.expireRule = rule.expireRule
            newRule.earlyHour = rule.earlyHour
            newRule.periodStart = rule.periodStart
            newRule.periodEnd = rule.periodEnd
            newRule.remindTime = rule.remindTime
            newRule.reminderContent = rule.reminderContent

            // 加载对应表的字段列表
            if (rule.dataTable && rule.database) {
              try {
                const fieldResponse = await getFieldMetadataByTable({
                  database: rule.database,
                  tableName: rule.dataTable
                })
                newRule.fieldList = fieldResponse.data || []
              } catch (error) {
                console.error('加载字段列表失败:', error)
                newRule.fieldList = []
              }
            }

            this.reminderRuleList.push(newRule)
          }
        } else {
          // 没有已存在的规则，初始化空规则
          this.initReminderRules()
        }
      } catch (error) {
        console.error('加载已存在规则失败:', error)
        // 如果加载失败，初始化空规则
        this.initReminderRules()
      }
    },

    // 加载表格列表
    async loadTableList() {
      try {
        const response = await getTableListByBiz(this.bizType)
        this.tableList = response.data || []
      } catch (error) {
        console.error('加载表格列表失败:', error)
        this.tableList = []
        // 如果API调用失败，仍然初始化一个空的规则
      }
    },

    // 初始化提醒规则
    initReminderRules() {
      this.reminderRuleList = [this.createEmptyRule()]
      if (this.tableList.length > 0) {
        this.reminderRuleList[0].dataTable = this.tableList[0].tableName
        this.reminderRuleList[0].database = this.tableList[0].dataBase
        this.handleTableChange(0)
      }
    },

    // 创建空的规则对象
    createEmptyRule() {
      return {
        id: null, // 添加id字段，用于更新已存在的规则
        database: '',
        dataTable: '',
        dataField: '',
        expireRule: '',
        earlyHour: null,
        periodStart: null,
        periodEnd: null,
        remindTime: null,
        reminderContent: '',
        fieldList: []
      }
    },

    // 处理表格变化
    async handleTableChange(index) {
      const rule = this.reminderRuleList[index]
      const selectedTable = this.tableList.find(table => table.tableName === rule.dataTable)

      if (selectedTable) {
        rule.database = selectedTable.dataBase
        rule.dataField = '' // 重置字段选择

        try {
          // 加载字段列表
          const response = await getFieldMetadataByTable({
            database: selectedTable.dataBase,
            tableName: selectedTable.tableName
          })
          rule.fieldList = response.data || []
        } catch (error) {
          console.error('加载字段列表失败:', error)
          this.$message.error('加载字段列表失败')
        }
      }
    },

    // 添加规则
    addRule() {
      this.reminderRuleList.push(this.createEmptyRule())
    },

    // 删除规则
    removeRule(index) {
      this.reminderRuleList.splice(index, 1)
    },

    // 开始拖拽调整宽度
    startResize(e) {
      this.isResizing = true
      this.startX = e.clientX
      this.startWidth = this.drawerWidth

      document.addEventListener('mousemove', this.doResize)
      document.addEventListener('mouseup', this.stopResize)
      document.body.style.cursor = 'ew-resize'
      document.body.style.userSelect = 'none'
      document.body.classList.add('resizing')
    },

    // 执行拖拽调整
    doResize(e) {
      if (!this.isResizing) return

      const deltaX = this.startX - e.clientX // 向左拖拽为正值
      const newWidth = Math.max(this.minWidth, Math.min(this.maxWidth, this.startWidth + deltaX))

      this.drawerWidth = newWidth
    },

    // 停止拖拽调整
    stopResize() {
      this.isResizing = false
      document.removeEventListener('mousemove', this.doResize)
      document.removeEventListener('mouseup', this.stopResize)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
      document.body.classList.remove('resizing')
    },

    // 验证表单
    validateForm() {
      // 允许空的规则列表
      if (this.reminderRuleList.length === 0) {
        return true
      }

      for (let i = 0; i < this.reminderRuleList.length; i++) {
        const rule = this.reminderRuleList[i]

        if (!rule.dataTable) {
          this.$message.error(`第${i + 1}个规则：请选择项目`)
          return false
        }

        if (!rule.dataField) {
          this.$message.error(`第${i + 1}个规则：请选择更新字段`)
          return false
        }

        if (!rule.expireRule) {
          this.$message.error(`第${i + 1}个规则：请选择更新周期`)
          return false
        }
      }

      return true
    },

    // 保存
    async handleSave() {
      if (!this.validateForm()) {
        return
      }

      this.saving = true

      try {
        let params = {
          buzType: this.bizType,
          reminderRuleList: this.reminderRuleList.map(rule => {
            let ruleData = {
              database: rule.database,
              dataTable: rule.dataTable,
              dataField: rule.dataField,
              expireRule: rule.expireRule,
              earlyHour: rule.earlyHour,
              reminderContent: rule.reminderContent
            }

            // 如果有id，说明是更新已存在的规则
            if (rule.id) {
              ruleData.id = rule.id
            }

            // 添加其他可能的字段
            if (rule.periodStart) ruleData.periodStart = rule.periodStart
            if (rule.periodEnd) ruleData.periodEnd = rule.periodEnd
            if (rule.remindTime) ruleData.remindTime = rule.remindTime

            return ruleData
          })
        }
        if (params.reminderRuleList.length === 0) {
          params.reminderRuleList = null
        }

        await batchAddReminder(params)

        this.$message.success('保存成功')
        this.handleClose()

      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败')
      } finally {
        this.saving = false
      }
    },

    // 关闭抽屉
    handleClose() {
      this.reminderRuleList = []
      this.tableList = []
      this.$emit('update:visible', false)
    }
  },

  beforeDestroy() {
    // 清理事件监听器
    document.removeEventListener('mousemove', this.doResize)
    document.removeEventListener('mouseup', this.stopResize)
  }
}
</script>

<style lang="scss" scoped>
.data-update-reminder-drawer {
  ::v-deep .el-drawer__body {
    padding: 0;
    position: relative;
  }
}

.resize-handle {
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  cursor: ew-resize;
  background: transparent;
  z-index: 10;

  &:hover {
    background: rgba(69, 132, 255, 0.3);
  }

  &:active {
    background: rgba(69, 132, 255, 0.5);
  }
}

.reminder-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.reminder-rules {
  flex: 1;
  padding-right: 10px;
  overflow-y: auto;
}

.reminder-rule-item {
  overflow-x: hidden;
  .rule-title {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    margin-top: 10px;

    .title-indicator {
      width: 3px;
      height: 16px;
      background-color: #4584FF;
      margin-right: 8px;
    }

    .title-text {
      font-size: 16px;
      color: #333;
    }
  }

  .rule-row {
    margin-bottom: 16px;
  }

  .delete-col {
    display: flex;
    align-items: center;
    justify-content: center;
    float:right;
  }

  .delete-btn {
    color: #f56c6c;

    &:hover {
      color: #f56c6c;
    }
  }
}

.add-reminder-btn {
  margin: 20px 0;

  .add-btn-custom {
    width: 90%;
    margin: 0 auto;
    background-color: #F8FBFF;
    color: #4584FF;
    border: 1px  dashed #4584FF;
    border-radius: 4px;
    padding: 12px 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 14px;
    transition: all 0.3s ease;

    &:hover {
      background-color: #EBF2FF;
      border-color: #3a73e6;
      color: #3a73e6;
    }

    i {
      font-size: 16px;
    }
  }
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
  margin-top: auto;
}

// 表单项样式优化
::v-deep  .el-form {
border: 1px solid #e6ebf5;
    border-radius: 10px;
    padding: 20px 12px;
}
::v-deep .el-form-item {
  margin-bottom: 16px;

  .el-form-item__label {
    font-weight: 500;
    color: #333;
  }
}

// 分隔线样式
::v-deep .el-divider {
  margin: 20px 0;
}

// 拖拽时的全局样式
body.resizing {
  cursor: ew-resize !important;
  user-select: none !important;
}
</style>
