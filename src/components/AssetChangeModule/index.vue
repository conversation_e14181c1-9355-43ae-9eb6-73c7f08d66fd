<template>
  <div class="asset-change-module">
    <el-row :gutter="20">
      <!-- 左侧：资产变更情况折线图 -->
      <el-col :span="8">
        <div class="chart-card">
          <div class="chart-title">资产变更情况</div>
          <div id="assetChangeChart" class="chart-container"></div>
        </div>
      </el-col>
      
      <!-- 中间：数字资产分布情况环状饼图 -->
      <el-col :span="8">
        <div class="chart-card">
          <div class="chart-title">数字资产分布情况</div>
          <div class="distribution-container">
            <div class="chart-section">
              <div id="assetDistributionChart" class="chart-container"></div>
            </div>
            <div class="legend-section">
              <div class="custom-legend">
                <div
                  v-for="(item, index) in assetDistributionData"
                  :key="index"
                  class="legend-item"
                >
                  <span class="legend-icon" :style="{ backgroundColor: item.itemStyle.color }"></span>
                  <span class="legend-name">{{ item.name }}</span>
                  <span class="legend-value">{{ item.value }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      
      <!-- 右侧：数字资产同比增长情况双柱状图 -->
      <el-col :span="8">
        <div class="chart-card">
          <div class="chart-title">数字资产同比增长情况</div>
          <div id="assetGrowthChart" class="chart-container"></div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getDataAssetMonthlyTrend, getDataAssetStat, getDataAssetGrowthTrend } from '@/api/enterprise'

export default {
  name: 'AssetChangeModule',
  props: {
    enterpriseData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 图表实例
      assetChangeChart: null,
      assetDistributionChart: null,
      assetGrowthChart: null,
      isLoading: false,
      
      // 资产变更数据
      assetChangeData: {
        months: ['2024.6', '2024.7', '2024.8', '2024.9', '2024.10'],
        change: [120, 132, 101, 134, 90],
        increase: [220, 182, 191, 234, 290]
      },
      
      // 数字资产分布数据
      assetDistributionData: [
        // { name: '宽带用户总数', value: 0, itemStyle: { color: '#3481FF' } },
        // { name: 'IDC托管用户', value: 0, itemStyle: { color: '#1ABB78' } },
        // { name: 'IP地址段', value: 0, itemStyle: { color: '#FFC53D' } },
        // { name: '接入网站', value: 0, itemStyle: { color: '#FF7A45' } },
        // { name: '虚拟运营', value: 0, itemStyle: { color: '#722ED1' } }
      ],
      
      // 数字资产同比增长数据
      assetGrowthData: {
        categories: ['宽带用户', 'IP地址段', '云服务', '专线用户', '域名注册'],
        currentMonth: [30, 52, 70, 34, 90],
        lastYear: [40, 30, 50, 60, 70]
      }
    }
  },
  mounted() {
    console.log('AssetChangeModule - mounted时的enterpriseData:', this.enterpriseData)
    console.log('AssetChangeModule - mounted被调用')

    // 延迟初始化，确保DOM完全渲染
    this.$nextTick(() => {
      setTimeout(() => {
        this.initializeComponent()
      }, 100)
    })
  },
  watch: {
    // 监听enterpriseData变化
    enterpriseData: {
      handler(newVal, oldVal) {
        console.log('AssetChangeModule - enterpriseData变化:', { newVal, oldVal })
        if (newVal && newVal.creditCode && (!oldVal || !oldVal.creditCode || newVal.creditCode !== oldVal.creditCode)) {
          console.log('AssetChangeModule - 检测到新的creditCode，重新初始化组件:', newVal.creditCode)
          // 延迟重新初始化，确保DOM已更新
          this.$nextTick(() => {
            setTimeout(() => {
              this.initializeComponent()
            }, 100)
          })
        }
      },
      deep: true,
      immediate: false
    }
  },
  beforeDestroy() {
    // 销毁图表实例，避免内存泄漏
    this.assetChangeChart && this.assetChangeChart.dispose()
    this.assetDistributionChart && this.assetDistributionChart.dispose()
    this.assetGrowthChart && this.assetGrowthChart.dispose()
    
    // 移除事件监听
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    // 初始化组件
    initializeComponent() {
      console.log('AssetChangeModule - initializeComponent被调用')
      console.log('AssetChangeModule - 当前enterpriseData:', this.enterpriseData)

      // 检查DOM元素是否存在
      const assetChangeChart = document.getElementById('assetChangeChart')
      const assetDistributionChart = document.getElementById('assetDistributionChart')
      const assetGrowthChart = document.getElementById('assetGrowthChart')

      console.log('AssetChangeModule - DOM检查结果:', {
        assetChangeChart: !!assetChangeChart,
        assetDistributionChart: !!assetDistributionChart,
        assetGrowthChart: !!assetGrowthChart
      })

      if (!assetChangeChart || !assetDistributionChart || !assetGrowthChart) {
        console.error('AssetChangeModule - 部分DOM元素不存在，延迟重试')
        setTimeout(() => {
          this.initializeComponent()
        }, 200)
        return
      }

      // 先初始化图表（使用默认数据）
      this.initAssetChangeChart()
      this.initAssetDistributionChart()
      this.initAssetGrowthChart()

      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize)

      // 如果有enterpriseData，加载真实数据
      if (this.enterpriseData?.creditCode) {
        console.log('AssetChangeModule - 有creditCode，加载真实数据:', this.enterpriseData.creditCode)
        this.loadAllData()
      } else {
        console.warn('AssetChangeModule - 缺少creditCode，使用默认数据')
      }
    },

    // 加载所有数据
    async loadAllData() {
      console.log('AssetChangeModule - loadAllData被调用')
      console.log('AssetChangeModule - 当前enterpriseData:', this.enterpriseData)
      console.log('AssetChangeModule - enterpriseData类型:', typeof this.enterpriseData)

      if (this.enterpriseData) {
        console.log('AssetChangeModule - enterpriseData的所有键:', Object.keys(this.enterpriseData))
        console.log('AssetChangeModule - creditCode值:', this.enterpriseData.creditCode)
        console.log('AssetChangeModule - creditCode类型:', typeof this.enterpriseData.creditCode)
      }

      // 使用真实的creditCode
      if (!this.enterpriseData?.creditCode) {
        console.warn('AssetChangeModule - 缺少creditCode，无法加载数据')
        // this.loadDefaultData()
        return
      }
      const creditCode = this.enterpriseData.creditCode
      console.log('AssetChangeModule - 使用的creditCode:', creditCode)

      this.isLoading = true

      try {
        console.log('AssetChangeModule - 开始并发加载三个接口数据，creditCode:', creditCode)

        // 并发请求三个接口
        const [trendResponse, distributionResponse, growthResponse] = await Promise.all([
          getDataAssetMonthlyTrend(creditCode),
          getDataAssetStat(creditCode),
          getDataAssetGrowthTrend(creditCode)
        ])

        console.log('AssetChangeModule - 月度趋势响应:', trendResponse)
        console.log('AssetChangeModule - 分布情况响应:', distributionResponse)
        console.log('AssetChangeModule - 增长趋势响应:', growthResponse)

        // 处理资产变更趋势数据
        if (trendResponse.code === 200 && trendResponse.data) {
          this.processAssetChangeData(trendResponse.data)
        } else {
          console.error('AssetChangeModule - 获取月度趋势数据失败:', trendResponse.msg)
        }

        // 处理资产分布数据
        if (distributionResponse.code === 200 && distributionResponse.data) {
          this.processAssetDistributionData(distributionResponse.data)
        } else {
          console.error('AssetChangeModule - 获取分布数据失败:', distributionResponse.msg)
        }

        // 处理资产增长数据
        if (growthResponse.code === 200 && growthResponse.data) {
          this.processAssetGrowthData(growthResponse.data)
        } else {
          console.error('AssetChangeModule - 获取增长数据失败:', growthResponse.msg)
        }

        // 重新初始化图表
        this.$nextTick(() => {
          this.initAssetChangeChart()
          this.initAssetDistributionChart()
          this.initAssetGrowthChart()
        })

      } catch (error) {
        console.error('AssetChangeModule - 加载数据出错:', error)
        // this.loadDefaultData()
      } finally {
        this.isLoading = false
      }
    },

    // 处理资产变更趋势数据
    processAssetChangeData(data) {
      console.log('AssetChangeModule - 处理资产变更趋势数据:', data)

      if (Array.isArray(data)) {
        // 根据API文档，data是数组格式：[{time, insertNum, updateNum}, ...]
        const months = data.map(item => {
          const time = item.time || ''
          // 将202412格式转换为2024.12格式
          if (time.length === 6) {
            return `${time.substring(0, 4)}.${time.substring(4)}`
          }
          return time
        })
        const insertData = data.map(item => item.insertNum || 0)
        const updateData = data.map(item => item.updateNum || 0)

        this.assetChangeData = {
          months: months.length > 0 ? months : ['2024.6', '2024.7', '2024.8', '2024.9', '2024.10'],
          change: updateData.length > 0 ? updateData : [120, 132, 101, 134, 90],
          increase: insertData.length > 0 ? insertData : [220, 182, 191, 234, 290]
        }
      } else {
        // 使用默认数据
        this.assetChangeData = {
          months: ['2024.6', '2024.7', '2024.8', '2024.9', '2024.10'],
          change: [120, 132, 101, 134, 90],
          increase: [220, 182, 191, 234, 290]
        }
      }
    },

    // 处理资产分布数据
    processAssetDistributionData(data) {
      console.log('AssetChangeModule - 处理资产分布数据:', data)

      if (Array.isArray(data)) {
        // 根据API文档，data是数组格式：[{code, name, num}, ...]
        const colors = ['#3481FF', '#1ABB78', '#FFC53D', '#b6b705','#FF7A45', '#722ED1', '#9C27B0', '#FF5722', '#607D8B']

        this.assetDistributionData = data.map((item, index) => ({
          name: item.name || '未知',
          value: item.num || 0,
          itemStyle: { color: colors[index % colors.length] }
        }))
      } else {
        // 使用默认数据
        // this.assetDistributionData = [
        //   { name: '宽带用户总数', value: 0, itemStyle: { color: '#3481FF' } },
        //   { name: 'IDC托管用户', value: 0, itemStyle: { color: '#1ABB78' } },
        //   { name: 'IP地址段', value: 0, itemStyle: { color: '#FFC53D' } },
        //   { name: '接入网站', value: 0, itemStyle: { color: '#FF7A45' } },
        //   { name: '虚拟运营', value: 0, itemStyle: { color: '#722ED1' } }
        // ]
      }
    },

    // 处理资产增长数据
    processAssetGrowthData(data) {
      console.log('AssetChangeModule - 处理资产增长数据:', data)

      if (Array.isArray(data)) {
        // 根据API文档，data是数组格式：[{name, num, lastNum}, ...]
        const categories = data.map(item => item.name || '未知')
        const currentMonth = data.map(item => item.num || 0)
        const lastYear = data.map(item => item.lastNum || 0)

        this.assetGrowthData = {
          categories: categories.length > 0 ? categories : ['宽带用户', 'IP地址段', '云服务', '专线用户', '域名注册'],
          currentMonth: currentMonth.length > 0 ? currentMonth : [0, 0, 0, 4, 0],
          lastYear: lastYear.length > 0 ? lastYear : [0, 0, 0, 0, 0]
        }
      } else {
        // 使用默认数据
        // this.assetGrowthData = {
        //   categories: ['宽带用户', 'IP地址段', '云服务', '专线用户', '域名注册'],
        //   currentMonth: [30, 52, 70, 34, 90],
        //   lastYear: [40, 30, 50, 60, 70]
        // }
      }
    },

    // 加载默认数据
    // loadDefaultData() {
    //   // 保持原有的默认数据
    //   this.assetChangeData = {
    //     months: ['2024.6', '2024.7', '2024.8', '2024.9', '2024.10'],
    //     change: [120, 132, 101, 134, 90],
    //     increase: [220, 182, 191, 234, 290]
    //   }

    //   // this.assetDistributionData = [
    //   //   { name: '宽带用户总数', value: 0, itemStyle: { color: '#3481FF' } },
    //   //   { name: 'IDC托管用户', value: 0, itemStyle: { color: '#1ABB78' } },
    //   //   { name: 'IP地址段', value: 0, itemStyle: { color: '#FFC53D' } },
    //   //   { name: '接入网站', value: 0, itemStyle: { color: '#FF7A45' } },
    //   //   { name: '虚拟运营', value: 0, itemStyle: { color: '#722ED1' } }
    //   // ]

    //   this.assetGrowthData = {
    //     categories: ['宽带用户', 'IP地址段', '云服务', '专线用户', '域名注册'],
    //     currentMonth: [30, 52, 70, 34, 90],
    //     lastYear: [40, 30, 50, 60, 70]
    //   }

    //   // 重新初始化图表
    //   this.$nextTick(() => {
    //     this.initAssetChangeChart()
    //     this.initAssetDistributionChart()
    //     this.initAssetGrowthChart()
    //   })
    // },

    // 初始化资产变更情况折线图
    initAssetChangeChart() {
      console.log('AssetChangeModule - 初始化资产变更图表')
      const chartDom = document.getElementById('assetChangeChart')
      if (!chartDom) {
        console.error('AssetChangeModule - 找不到assetChangeChart DOM元素')
        return
      }

      console.log('AssetChangeModule - assetChangeChart DOM元素尺寸:', {
        width: chartDom.offsetWidth,
        height: chartDom.offsetHeight,
        clientWidth: chartDom.clientWidth,
        clientHeight: chartDom.clientHeight
      })

      // 如果已存在图表实例，先销毁
      if (this.assetChangeChart) {
        this.assetChangeChart.dispose()
      }

      this.assetChangeChart = echarts.init(chartDom)
      console.log('AssetChangeModule - assetChangeChart实例创建成功:', !!this.assetChangeChart)
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          data: ['变更', '新增'],
          bottom: 0
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.assetChangeData.months
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 500
        },
        series: [
          {
            name: '变更',
            type: 'line',
            smooth: true,
            lineStyle: {
              width: 3,
              color: '#3481FF'
            },
            itemStyle: {
              color: '#3481FF'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(52, 129, 255, 0.3)' },
                { offset: 1, color: 'rgba(52, 129, 255, 0.1)' }
              ])
            },
            data: this.assetChangeData.change
          },
          {
            name: '新增',
            type: 'line',
            smooth: true,
            lineStyle: {
              width: 3,
              color: '#1ABB78'
            },
            itemStyle: {
              color: '#1ABB78'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(26, 187, 120, 0.3)' },
                { offset: 1, color: 'rgba(26, 187, 120, 0.1)' }
              ])
            },
            data: this.assetChangeData.increase
          }
        ]
      }

      try {
        this.assetChangeChart.setOption(option)
        console.log('AssetChangeModule - assetChangeChart配置设置成功')
      } catch (error) {
        console.error('AssetChangeModule - assetChangeChart配置设置失败:', error)
      }
    },
    
    // 初始化数字资产分布情况环状饼图
    initAssetDistributionChart() {
      console.log('AssetChangeModule - 初始化资产分布图表')
      const chartDom = document.getElementById('assetDistributionChart')
      if (!chartDom) {
        console.error('AssetChangeModule - 找不到assetDistributionChart DOM元素')
        return
      }

      console.log('AssetChangeModule - assetDistributionChart DOM元素尺寸:', {
        width: chartDom.offsetWidth,
        height: chartDom.offsetHeight
      })

      // 如果已存在图表实例，先销毁
      if (this.assetDistributionChart) {
        this.assetDistributionChart.dispose()
      }

      this.assetDistributionChart = echarts.init(chartDom)
      console.log('AssetChangeModule - assetDistributionChart实例创建成功:', !!this.assetDistributionChart)

      const totalValue = this.assetDistributionData.reduce((sum, item) => sum + item.value, 0)
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        // 移除图例，使用自定义图例
        legend: {
          show: false
        },
        series: [
          {
            name: '数字资产分布',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '50%'], // 居中显示
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 2,
              borderColor: '#fff',
              borderWidth: 0.4
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: false
              }
            },
            labelLine: {
              show: false
            },
            data: this.assetDistributionData
          }
        ],
        graphic: {
          type: 'text',
          left: '50%',
          top: '50%',
          style: {
            text: `${totalValue}\n总量`,
            textAlign: 'center',
            fill: '#333',
            fontSize: 16,
            fontWeight: 'bold'
          }
        }
      }

      try {
        this.assetDistributionChart.setOption(option)
        console.log('AssetChangeModule - assetDistributionChart配置设置成功')
      } catch (error) {
        console.error('AssetChangeModule - assetDistributionChart配置设置失败:', error)
      }
    },
    
    // 初始化数字资产同比增长情况双柱状图
    initAssetGrowthChart() {
      console.log('AssetChangeModule - 初始化资产增长图表')
      const chartDom = document.getElementById('assetGrowthChart')
      if (!chartDom) {
        console.error('AssetChangeModule - 找不到assetGrowthChart DOM元素')
        return
      }

      console.log('AssetChangeModule - assetGrowthChart DOM元素尺寸:', {
        width: chartDom.offsetWidth,
        height: chartDom.offsetHeight
      })

      // 如果已存在图表实例，先销毁
      if (this.assetGrowthChart) {
        this.assetGrowthChart.dispose()
      }

      this.assetGrowthChart = echarts.init(chartDom)
      console.log('AssetChangeModule - assetGrowthChart实例创建成功:', !!this.assetGrowthChart)
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['本月增长', '同比去年'],
          bottom: 0
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.assetGrowthData.categories
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 100
        },
        series: [
          {
            name: '本月增长',
            type: 'bar',
            barWidth: '30%',
            itemStyle: {
              color: '#50B5FF'
            },
            data: this.assetGrowthData.currentMonth
          },
          {
            name: '同比去年',
            type: 'bar',
            barWidth: '30%',
            itemStyle: {
              color: '#FC7A8C'
            },
            data: this.assetGrowthData.lastYear
          }
        ]
      }

      try {
        this.assetGrowthChart.setOption(option)
        console.log('AssetChangeModule - assetGrowthChart配置设置成功')
      } catch (error) {
        console.error('AssetChangeModule - assetGrowthChart配置设置失败:', error)
      }
    },
    
    // 处理窗口大小变化
    handleResize() {
      this.assetChangeChart && this.assetChangeChart.resize()
      this.assetDistributionChart && this.assetDistributionChart.resize()
      this.assetGrowthChart && this.assetGrowthChart.resize()
    }
  }
}
</script>

<style lang="scss" scoped>
.asset-change-module {
  margin-top: 20px;
  margin-bottom: 30px;
  
  .chart-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    height: 350px;
    
    .chart-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #EBEEF5;
    }
    
    .chart-container {
      height: calc(100% - 40px);
      width: 100%;
    }

    // 数字资产分布情况特殊布局
    .distribution-container {
      display: flex;
      height: calc(100% - 40px);

      .chart-section {
        flex: 0 0 60%;

        .chart-container {
          width: 100%;
          height: 100%;
        }
      }

      .legend-section {
        flex: 0 0 40%;
        padding-left: 10px;
        display: flex;
        align-items: center;

        .custom-legend {
          width: 100%;
          max-height: 280px;
          overflow-y: auto;
          padding-right: 5px;

          // 自定义滚动条样式
          &::-webkit-scrollbar {
            width: 4px;
          }

          &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
          }

          &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 2px;

            &:hover {
              background: #a8a8a8;
            }
          }

          .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            font-size: 12px;
            color: #666;

            .legend-icon {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              margin-right: 8px;
              flex-shrink: 0;
            }

            .legend-name {
              flex: 1;
              margin-right: 8px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .legend-value {
              font-weight: 500;
              color: #333;
              flex-shrink: 0;
            }
          }
        }
      }
    }
  }
}
</style>
