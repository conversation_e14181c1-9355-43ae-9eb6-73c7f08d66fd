<template>
  <div
    class="collapsible-detail-right"
    :class="{ 'collapsed': isCollapsed }"
    :style="containerStyle"
  >
    <!-- 拖拽分隔线 -->
    <div
      class="resize-handle"
      @mousedown="startResize"
      v-show="!isCollapsed"
    ></div>

    <!-- 收起展开按钮 -->
    <div class="toggle-button" @click="toggleCollapse">
      <i :class="isCollapsed ? 'el-icon-d-arrow-left' : 'el-icon-d-arrow-right'"></i>
    </div>

    <!-- 右侧内容区域 -->
    <div class="detail-right-content" v-show="!isCollapsed" :style="contentStyle">
      <div
        ref="anchorContainer"
        class="anchor-container"
        :style="anchorStyle"
      >
        <slot name="anchor"></slot>
      </div>
      <div
        ref="timelineContainer"
        class="timeline-container"
        :style="timelineStyle"
      >
        <slot name="timeline"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CollapsibleDetailRight',
  data() {
    return {
      isCollapsed: false,
      currentWidth: 360,
      minWidth: 280,
      maxWidth: 600,
      isResizing: false,
      startX: 0,
      startWidth: 0,
      containerHeight: 0,
      anchorHeight: 0,
      timelineHeight: 0,
      anchorNaturalHeight: 0,
      timelineNaturalHeight: 0
    }
  },
  computed: {
    // 容器样式
    containerStyle() {
      return {
        width: this.currentWidth + 'px',
        height: this.containerHeight + 'px',
        position: 'fixed',
        top: '123px',
        right: '0',
        zIndex: 1000
      }
    },
    // 内容区域样式
    contentStyle() {
      return {
        height: this.containerHeight + 'px'
      }
    },
    // 锚点导航样式
    anchorStyle() {
      return {
        height: this.anchorHeight ? this.anchorHeight + 'px' : 'auto',
        overflowY: this.anchorHeight ? 'auto' : 'visible'
      }
    },
    // 时间线样式
    timelineStyle() {
      return {
        height: this.timelineHeight ? this.timelineHeight + 'px' : 'auto',
        overflowY: this.timelineHeight ? 'auto' : 'visible'
      }
    }
  },
  mounted() {
    this.calculateContainerHeight();
    this.observeContentChanges();
    window.addEventListener('resize', this.calculateContainerHeight);
  },
  beforeDestroy() {
    // 清理事件监听器
    document.removeEventListener('mousemove', this.doResize);
    document.removeEventListener('mouseup', this.stopResize);
    window.removeEventListener('resize', this.calculateContainerHeight);
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  },
  methods: {
    // 计算容器高度
    calculateContainerHeight() {
      this.containerHeight = window.innerHeight - 106;
      this.$nextTick(() => {
        this.calculateChildrenHeights();
      });
    },

    // 观察内容变化
    observeContentChanges() {
      if (window.ResizeObserver) {
        this.resizeObserver = new ResizeObserver(() => {
          this.calculateChildrenHeights();
        });

        this.$nextTick(() => {
          if (this.$refs.anchorContainer) {
            this.resizeObserver.observe(this.$refs.anchorContainer);
          }
          if (this.$refs.timelineContainer) {
            this.resizeObserver.observe(this.$refs.timelineContainer);
          }
        });
      }
    },

    // 计算子组件高度
    calculateChildrenHeights() {
      if (!this.$refs.anchorContainer || !this.$refs.timelineContainer) {
        return;
      }

      // 获取子组件的自然高度
      this.anchorNaturalHeight = this.getNaturalHeight(this.$refs.anchorContainer);
      this.timelineNaturalHeight = this.getNaturalHeight(this.$refs.timelineContainer);

      const totalNaturalHeight = this.anchorNaturalHeight + this.timelineNaturalHeight;
      const availableHeight = this.containerHeight - 40; // 减去padding

      console.log('高度计算:', {
        containerHeight: this.containerHeight,
        availableHeight,
        anchorNaturalHeight: this.anchorNaturalHeight,
        timelineNaturalHeight: this.timelineNaturalHeight,
        totalNaturalHeight
      });

      // 应用高度管理规则
      if (totalNaturalHeight <= availableHeight) {
        // 规则1: 两个组件内容都不多，保持自由高度
        this.anchorHeight = 0;
        this.timelineHeight = 0;
      } else {
        const halfHeight = availableHeight / 2.05;

        if (this.anchorNaturalHeight > halfHeight && this.timelineNaturalHeight > halfHeight) {
          // 规则3: 两个组件都内容较多，平分高度
          this.anchorHeight = halfHeight;
          this.timelineHeight = halfHeight;
          console.log(11111111)
        } else if (this.anchorNaturalHeight <= halfHeight) {
          // 规则2: anchor内容较少，timeline内容较多
          this.anchorHeight = 0;
          this.timelineHeight = availableHeight - this.anchorNaturalHeight -10 ;
          console.log(2222222)
        } else {
          // 规则2: timeline内容较少，anchor内容较多
          this.timelineHeight = 0;
          this.anchorHeight = availableHeight - this.timelineNaturalHeight;
          console.log(33333)
        }
      }
    },

    // 获取元素的自然高度
    getNaturalHeight(element) {
      if (!element) return 0;

      // 临时移除高度限制来获取自然高度
      const originalHeight = element.style.height;
      const originalOverflow = element.style.overflow;

      element.style.height = 'auto';
      element.style.overflow = 'visible';

      const naturalHeight = element.scrollHeight;

      // 恢复原始样式
      element.style.height = originalHeight;
      element.style.overflow = originalOverflow;

      return naturalHeight;
    },

    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed;
      // 通知父组件状态变化
      this.$emit('collapse-change', this.isCollapsed);
      this.$emit('width-change', this.isCollapsed ? 40 : this.currentWidth);

      if (!this.isCollapsed) {
        this.$nextTick(() => {
          this.calculateChildrenHeights();
        });
      }
    },

    startResize(e) {
      this.isResizing = true;
      this.startX = e.clientX;
      this.startWidth = this.currentWidth;

      document.addEventListener('mousemove', this.doResize);
      document.addEventListener('mouseup', this.stopResize);
      document.body.style.cursor = 'ew-resize';
      document.body.style.userSelect = 'none';
    },

    doResize(e) {
      if (!this.isResizing) return;

      const deltaX = this.startX - e.clientX; // 向左拖拽为正值
      const newWidth = Math.max(this.minWidth, Math.min(this.maxWidth, this.startWidth + deltaX));

      this.currentWidth = newWidth;
      this.$emit('width-change', newWidth);
    },

    stopResize() {
      this.isResizing = false;
      document.removeEventListener('mousemove', this.doResize);
      document.removeEventListener('mouseup', this.stopResize);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    }
  }
}
</script>

<style lang="scss" scoped>
.collapsible-detail-right {
  position: fixed;
  top: 126px;
  right: 0;
  background-color: #f5f7fa;
  transition: width 0.3s ease;
  z-index: 1000;
  // box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);

  &.collapsed {
    width: 40px !important;
  }

  .resize-handle {
    position: absolute;
    left: 0;
    top: 0;
    width: 4px;
    height: 100%;
    cursor: ew-resize;
    background: transparent;
    z-index: 5;

    &:hover {
      background: rgba(69, 132, 255, 0.3);
    }

    &:active {
      background: rgba(69, 132, 255, 0.5);
    }
  }

  .toggle-button {
    position: absolute;
    left: -20px;
    top: 0px;
    transform: translateY(-50%);
    width: 30px;
    height: 30px;
    // background: #4584FF;
    background: #2987D2;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: white;
    font-size: 16px;
    // box-shadow: 0 2px 8px rgba(69, 132, 255, 0.3);
    z-index: 10;
    transition: all 0.3s ease;

    &:hover {
      background: #2987D2;
      transform: translateY(-50%) scale(1.1);
      box-shadow: 0 4px 12px rgba(69, 132, 255, 0.4);
    }
  }

  .detail-right-content {
    padding:0 20px 0 0;
    display: flex;
    flex-direction: column;
    gap: 20px;
    overflow: hidden;

    .anchor-container {
      flex-shrink: 0;
      transition: height 0.3s ease;
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a8a8a8;
        }
      }
    }

    .timeline-container {
      // flex: 1;
      transition: height 0.3s ease;
      overflow-y: hidden;
      overflow-x: hidden;
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a8a8a8;
        }
      }
    }
  }
}

// 拖拽时的全局样式
body.resizing {
  cursor: ew-resize !important;
  user-select: none !important;
}
</style>
