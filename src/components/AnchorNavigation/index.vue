<template>
  <div class="anchor-navigation">
    <div class="anchor-header">
      <h3 class="anchor-title">页面导航</h3>
    </div>
    
    <div class="anchor-content">
      <el-timeline>
        <el-timeline-item
          v-for="(item, index) in anchorItems"
          :key="index"
          :icon="'el-icon-location'"
          :color="activeAnchor === item.id ? '#409EFF' : '#F6F7FB'"
          size="normal"
          @click.native="scrollToAnchor(item.id)"
        >
          <div 
            class="anchor-item"
            :class="{ 'active': activeAnchor === item.id }"
            @click="scrollToAnchor(item.id)"
          >
            <span class="anchor-text">{{ item.label }}</span>
            <span v-if="item.count !== undefined" class="anchor-count">({{ item.count }})</span>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AnchorNavigation',
  props: {
    // 锚点配置项
    anchorItems: {
      type: Array,
      default: () => []
      // 格式: [{ id: 'basic-info', label: '基本信息' }, { id: 'tasks', label: '检查任务列表', count: 0 }]
    },
    // 滚动偏移量
    offset: {
      type: Number,
      default: 80
    },
    // 滚动容器选择器
    scrollContainer: {
      type: String,
      default: '.app-main'
    }
  },
  data() {
    return {
      activeAnchor: '',
      scrollElement: null
    }
  },
  mounted() {
    this.initScrollContainer()
    // 初始化激活状态
    this.updateActiveAnchor()
  },
  beforeDestroy() {
    if (this.scrollElement) {
      this.scrollElement.removeEventListener('scroll', this.handleScroll)
    } else {
      window.removeEventListener('scroll', this.handleScroll)
    }
  },
  methods: {
    // 初始化滚动容器
    initScrollContainer() {
      // console.log( '----scrollContainer---', this.scrollContainer)
      if (this.scrollContainer) {
        this.scrollElement = document.querySelector(this.scrollContainer)
        //  console.log( '----scrollContainer---', document.querySelector(this.scrollContainer))
      }
      
      // 如果没有找到指定容器，尝试自动检测
      if (!this.scrollElement) {
        // 检测常见的滚动容器
        const containers = [
          '.app-container',
          '.detail-content',
          '.main-content',
          'main',
          'body'
        ]

        for (const selector of containers) {
          const element = document.querySelector(selector)
          if (element && this.hasScrollableContent(element)) {
            this.scrollElement = element
            break
          }
        }
      }

      // 如果还是没有找到，使用window
      if (!this.scrollElement) {
        this.scrollElement = window
      }

      // 监听滚动事件
      if (this.scrollElement === window) {
        window.addEventListener('scroll', this.handleScroll)
      } else {
        this.scrollElement.addEventListener('scroll', this.handleScroll)
      }
    },

    // 检查元素是否有滚动内容
    hasScrollableContent(element) {
      const style = window.getComputedStyle(element)
      const overflowY = style.overflowY
      const scrollHeight = element.scrollHeight
      const clientHeight = element.clientHeight

      return (overflowY === 'auto' || overflowY === 'scroll') && scrollHeight > clientHeight
    },
    // 滚动到指定锚点
    scrollToAnchor(anchorId) {
      const element = document.getElementById(anchorId)
      if (element) {
        if (this.scrollElement === window) {
          // 使用window滚动
          const offsetTop = element.offsetTop - this.offset
          window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
          })
        } else {
          // 使用容器滚动
          const containerRect = this.scrollElement.getBoundingClientRect()
          const elementRect = element.getBoundingClientRect()
          const scrollTop = this.scrollElement.scrollTop
          const targetScrollTop = scrollTop + elementRect.top - containerRect.top - this.offset
          this.scrollElement.scrollTo({
            top: targetScrollTop-40,
            behavior: 'smooth'
          })
        }
        this.activeAnchor = anchorId
        this.$emit('anchor-change', anchorId)
      }
    },
    
    // 处理滚动事件
    handleScroll() {
      this.updateActiveAnchor()
    },
    
    // 更新激活的锚点
    updateActiveAnchor() {
      let scrollTop = 0

      if (this.scrollElement === window) {
        scrollTop = window.pageYOffset || document.documentElement.scrollTop
      } else {
        scrollTop = this.scrollElement.scrollTop
      }

      for (let i = this.anchorItems.length - 1; i >= 0; i--) {
        const item = this.anchorItems[i]
        const element = document.getElementById(item.id)

        if (element) {
          let offsetTop = 0

          if (this.scrollElement === window) {
            offsetTop = element.offsetTop - this.offset - 10
          } else {
            // 计算相对于滚动容器的位置
            const containerRect = this.scrollElement.getBoundingClientRect()
            const elementRect = element.getBoundingClientRect()
            offsetTop = this.scrollElement.scrollTop + elementRect.top - containerRect.top - this.offset - 10
          }

          if (scrollTop >= offsetTop) {
            this.activeAnchor = item.id
            this.$emit('anchor-change', item.id)
            break
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.anchor-navigation {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 10px;
  bottom: 10px;
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 140px);
  overflow: hidden;

  ::v-deep .el-icon-location:before {
    content: " " !important;
  }

  .anchor-header {
    padding: 16px 20px 0;
    border-bottom: 1px solid #ebeef5;
    margin-bottom: 16px;
    background: #fff;
    position: sticky;
    top: 0;
    z-index: 10;

    .anchor-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 16px 0;
    }
  }
  
  .anchor-content {
    padding: 0 20px 20px;
    flex: 1;
    overflow-y: auto;
    
    .anchor-item {
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.3s;
      display: flex;
      align-items: center;
      
      &:hover {
        background-color: #f5f7fa;
        color: #409EFF;
      }
      
      &.active {
        background-color: #ecf5ff;
        color: #409EFF;
        font-weight: 500;
      }
      
      .anchor-text {
        font-size: 14px;
        line-height: 1.5;
      }
      
      .anchor-count {
        margin-left: 4px;
        font-size: 12px;
        color: #909399;
        
        .active & {
          color: #409EFF;
        }
      }
    }
  }
}

// 自定义时间线样式
::v-deep .el-timeline {
  .el-timeline-item {
    padding-bottom: 5px !important;

    .el-timeline-item__node {
      position: absolute;
      background-color: #fff;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      z-index: 2;

      // 重置默认样式
      &.el-timeline-item__node--normal {
        left: -6px;
        width: 12px;
        height: 12px;
        // border: 2px solid #C0C4CC;
        transition: all 0.3s ease;

        // 内部圆点
        &::before {
          content: '';
          width: 4px;
          height: 4px;
          background-color:  #E9E9E9;
          border-radius: 50%;
          transition: all 0.3s ease;
        }

        &:hover {
          transform: scale(1.2);
          border-color: #409EFF;
          box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);

          &::before {
            background-color: #409EFF;
            width: 5px;
            height: 5px;
          }
        }
      }

      // 激活状态
      &[style*="#409EFF"] {
        border-color: #409EFF !important;
        border-width: 3px !important;
        box-shadow: 0 0 12px rgba(64, 158, 255, 0.4);

        &::before {
          background-color: #409EFF !important;
          width: 5px !important;
          height: 5px !important;
        }
      }
    }

    .el-timeline-item__wrapper {
      position: relative;
      padding-left: 28px;
      top: -6px;
      cursor: pointer;
    }

    .el-timeline-item__content {
      padding-bottom: 0;
    }

    // 连接线
    .el-timeline-item__tail {
      position: absolute;
      left: 0;
      height: 100%;
      border-left: 2px solid  #E9E9E9;
    }
  }

  // 最后一个项目不显示连接线
  .el-timeline-item:last-child {
    .el-timeline-item__tail {
      display: none;
    }
  }
}
</style>
