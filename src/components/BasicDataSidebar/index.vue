<template>
  <div class="basic-data-sidebar" :class="{ 'collapsed': isCollapsed }">
    <!-- 展开收起按钮 -->
    <div class="toggle-button" @click="toggleCollapse">
      <i :class="isCollapsed ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
    </div>

    <el-menu
      :default-active="activeMenu"
      mode="vertical"
      @select="handleMenuSelect"
      :unique-opened="false"
      :collapse="isCollapsed"
    >
      <!-- 电子屏管理 -->
      <el-tooltip :content="'电子屏管理'" placement="right" :disabled="!isCollapsed">
        <el-menu-item index="/data-management/screen" class="flat-menu">
          <i class="el-icon-monitor"></i>
          <span slot="title">电子屏管理</span>
        </el-menu-item>
      </el-tooltip>
      <el-tooltip :content="'归属场所'" placement="right" :disabled="!isCollapsed">
        <el-menu-item index="/data-management/screen/site" class="flat-menu">
          <i class="el-icon-office-building"></i>
          <span slot="title">归属场所</span>
        </el-menu-item>
      </el-tooltip>
      <el-tooltip :content="'责任单位'" placement="right" :disabled="!isCollapsed">
        <el-menu-item index="/data-management/screen/enterprise" class="flat-menu">
          <i class="el-icon-s-shop"></i>
          <span slot="title">责任单位</span>
        </el-menu-item>
      </el-tooltip>
      <el-tooltip :content="'联网系统'" placement="right" :disabled="!isCollapsed">
        <el-menu-item index="/data-management/screen/netsystem" class="flat-menu">
          <i class="el-icon-connection"></i>
          <span slot="title">联网系统</span>
        </el-menu-item>
      </el-tooltip>
      <el-tooltip :content="'重点区域'" placement="right" :disabled="!isCollapsed">
        <el-menu-item index="/data-management/screen/keyarea" class="flat-menu">
          <i class="el-icon-star-off"></i>
          <span slot="title">重点区域</span>
        </el-menu-item>
      </el-tooltip>
      <el-tooltip :content="'专项管理'" placement="right" :disabled="!isCollapsed">
        <el-menu-item index="/data-management/screen/special" class="flat-menu">
          <i class="el-icon-s-flag"></i>
          <span slot="title">专项管理</span>
        </el-menu-item>
      </el-tooltip>

      <!-- 等保备案业务管理 -->
      <!-- <el-menu-item index="/data-management/levelprotect" class="flat-menu">
        <i class="el-icon-lock"></i>
        <span slot="title">等保备案业务管理</span>
      </el-menu-item> -->

      <!-- 网站信息管理 -->
      <!-- <el-menu-item index="/data-management/website" class="flat-menu">
        <i class="el-icon-globe"></i>
        <span slot="title">网站信息管理</span>
      </el-menu-item>
      <el-menu-item index="/data-management/website/app" class="flat-menu">
        <i class="el-icon-mobile-phone"></i>
        <span slot="title">APP信息管理</span>
      </el-menu-item>
      <el-menu-item index="/data-management/website/applet" class="flat-menu">
        <i class="el-icon-s-grid"></i>
        <span slot="title">小程序信息管理</span>
      </el-menu-item> -->

      <!-- 网吧管理 -->
      <!-- <el-menu-item index="/data-management/netbar" class="flat-menu">
        <i class="el-icon-coffee-cup"></i>
        <span slot="title">网吧管理</span>
      </el-menu-item> -->

      <!-- 运营商管理 -->
      <!-- <el-menu-item index="/data-management/operator/baseinfo" class="flat-menu">
        <i class="el-icon-phone"></i>
        <span slot="title">运营商基础信息</span>
      </el-menu-item> -->

      <!-- 非经营场所管理 -->
      <el-tooltip :content="'非经营场所管理'" placement="right" :disabled="!isCollapsed">
        <el-menu-item index="/data-management/wifi/site" class="flat-menu">
          <i class="el-icon-connection"></i>
          <span slot="title">非经营场所管理</span>
        </el-menu-item>
      </el-tooltip>
      <el-tooltip :content="'非经营设备管理'" placement="right" :disabled="!isCollapsed">
        <el-menu-item index="/data-management/wifi/equipment" class="flat-menu">
          <i class="el-icon-cpu"></i>
          <span slot="title">非经营设备管理</span>
        </el-menu-item>
      </el-tooltip>

      <!-- 行政管理 -->
      <!-- <el-menu-item index="/data-management/scene" class="flat-menu">
        <i class="el-icon-warning"></i>
        <span slot="title">行政监管</span>
      </el-menu-item> -->
    </el-menu>
  </div>
</template>

<script>
export default {
  name: 'BasicDataSidebar',

  data() {
    return {
      isCollapsed: false
    }
  },

  computed: {
    activeMenu() {
      const currentPath = this.$route.path;
      // 根据当前路径确定激活的菜单项，注意顺序：先判断更具体的路径
      if (currentPath.includes('/data-management/screen/site')) {
        return '/data-management/screen/site';
      } else if (currentPath.includes('/data-management/screen/enterprise')) {
        return '/data-management/screen/enterprise';
      } else if (currentPath.includes('/data-management/screen/netsystem')) {
        return '/data-management/screen/netsystem';
      } else if (currentPath.includes('/data-management/screen/keyarea')) {
        return '/data-management/screen/keyarea';
      } else if (currentPath.includes('/data-management/screen/special')) {
        return '/data-management/screen/special';
      } else if (currentPath.includes('/data-management/screen')) {
        return '/data-management/screen';
      } else if (currentPath.includes('/data-management/levelprotect')) {
        return '/data-management/levelprotect';
      } else if (currentPath.includes('/data-management/website/applet')) {
        return '/data-management/website/applet';
      } else if (currentPath.includes('/data-management/website/app')) {
        return '/data-management/website/app';
      } else if (currentPath.includes('/data-management/website')) {
        return '/data-management/website';
      } else if (currentPath.includes('/data-management/netbar')) {
        return '/data-management/netbar';
      } else if (currentPath.includes('/data-management/operator/baseinfo')) {
        return '/data-management/operator/baseinfo';
      } else if (currentPath.includes('/data-management/wifi/equipment')) {
        return '/data-management/wifi/equipment';
      } else if (currentPath.includes('/data-management/wifi/site')) {
        return '/data-management/wifi/site';
      } else if (currentPath.includes('/data-management/scene/punish')) {
        return '/data-management/scene/punish';
      } else if (currentPath.includes('/data-management/scene/disposal')) {
        return '/data-management/scene/disposal';
      } else {
        return '/data-management/screen';
      }
    },


  },

  mounted() {
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize)
    // 初始检查窗口大小
    this.handleResize()
  },

  beforeDestroy() {
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleResize)
  },

  methods: {
    // 处理菜单选择
    handleMenuSelect(index) {
      // 避免重复导航，减少页面闪烁
      if (this.$route.path !== index) {
        this.$router.push(index).catch(err => {
          // 忽略重复导航错误
          if (err.name !== 'NavigationDuplicated') {
            console.error(err);
          }
        });
      }
    },

    // 切换展开收起状态
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed
    },

    // 处理窗口大小变化
    handleResize() {
      const windowWidth = window.innerWidth
      if (windowWidth < 1025) {
        this.isCollapsed = true
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.basic-data-sidebar {
  width: 220px;
  background: white;
  border-radius: 8px;
  padding: 0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: relative;
  transition: width 0.3s ease;

  &.collapsed {
    width: 64px;
  }

  .toggle-button {
    position: absolute;
    top: 10px;
    right: 20px;
    width: 24px;
    height: 24px;
    background: #f5f7fa;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;

    &:hover {
      background: #ecf5ff;
      border-color: #4584FF;
      color: #4584FF;
    }

    i {
      font-size: 12px;
      color: #666;
    }
  }

  ::v-deep .el-menu {
    border-right: none;

    // 平铺菜单样式
    .el-menu-item {
      height: 45px;
      line-height: 45px;
      color: #666;
      border-radius: 0;
      margin: 0;
      padding-left: 20px;
      background-color: #ffffff;
      border-bottom: 1px solid #f0f2f5;

      &.flat-menu {
        &:hover {
          background-color: #f0f2f5;
          color: #4584FF;
        }

        &.is-active {
          background-color: #4584FF;
          color: #ffffff;

          i {
            color: #ffffff;
          }
        }
      }

      i {
        margin-right: 8px;
        font-size: 14px;
      }
    }

    // 收起状态样式
    &.el-menu--collapse {
      .el-menu-item {
        padding-left: 20px;
        text-align: center;

        i {
          margin-right: 0;
          font-size: 16px;
        }

        span {
          display: none;
        }
      }
    }
  }
}
</style>
