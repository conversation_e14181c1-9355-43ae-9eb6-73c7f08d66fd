<template>
  <div class="detail-section security-assessment-section">
    <!-- <h3 class="section-title">企业网络安全评估</h3> -->

    <!-- 父级蓝色模块 -->
    <div class="security-assessment" :style="{ backgroundColor: '#4584FF' }">
      <div class="assessment-header">

        <el-row  class="header-content">
          <el-col :span="6" :xs="24" :sm="24" :md="6" class="score-section">
            <div class="score-title">企业网络安全综合评分</div>
            <div class="score-value">{{ currentAssessment.generalScore || 0 }}</div>
            <div class="score-change">
              <span>{{ getScoreChangeText() }}</span>
            </div>
            <div class="score-compare">
              {{ getScoreCompareText() }}
            </div>
            <div class="score-stars">
              <i v-for="n in 5" :key="n" class="el-icon-star-on"
                 :style="{ color: n <= getStarCountByRiskLevel() ? '#F7BA2A' : '#EBEEF5' }"></i>
            </div>
            <div class="risk-level" :style="{ background: getSecurityRiskColor(currentAssessment.riskLevelLabel) }">
             风险等级： {{ currentAssessment.riskLevelLabel || '未知' }}
            </div>
          </el-col>

          <el-col :span="11" :xs="24" :sm="24" :md="10" class="radar-section">
            <div :id="chartId" style="width: 100%; height: 350px;"></div>
          </el-col>

          <el-col :span="7" :xs="24" :sm="24" :md="8" class="description-section">
            <h4 style="margin-top:10px;">整体评估</h4>
            <p class="multiline-ellipsis-3" style="color:#ffdfb0;margin-top:10px;">{{ currentAssessment.generalDesp || '暂无评估描述' }}</p>
            <ul class="description-section-sub text-ellipsis">
            <li
              v-for="(item, index) in currentAssessment.scoreItemList || []"
              :key="index"
              class="score-item"
            >
              <div
                class="itemName"
                :class="getItemNameClass(item.generalDesp)"
              >
                {{ item.itemName }}
              </div>
              <el-tooltip
                :content="item.generalDesp"
                placement="top"
                :disabled="!isTextOverflow(item.generalDesp)"
              >
                <div class="subitemDesp">{{ item.generalDesp }}</div>
              </el-tooltip>
            </li>
          </ul>
          </el-col>
          
        </el-row>

        <!-- 数据更新时间 -->
        <div class="update-time">
          数据更新时间：{{ formatUpdateTime(currentAssessment.updateTime || currentAssessment.createTime) }}
        </div>

        <div class="expand-button" @click="toggleExpand">
          <i :class="isExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
          <span>{{ isExpanded ? '收起' : '展开' }}详细评估</span>
        </div>
      </div>
    </div>

    <!-- 子级模块 -->
    <div v-if="isExpanded" class="security-sub-modules">
      <div v-if="isLoadingSubModules" class="loading-container">
        <i class="el-icon-loading"></i>
        <span>正在加载详细评估数据...</span>
      </div>

      <!-- 安全评估子模块容器 -->
      <div v-else class="security-sub-modules">
        <!-- 第一行：资质与合规情况 + 历史安全事件与威胁情报 -->
        <div class="row-1">
          <div
            v-for="(subModule, index) in getRowModules(1)"
            :key="index"
            class="sub-module"
          >
            <div class="sub-module-title">{{ subModule.itemName }}</div>
            <div class="sub-module-content">
              <!-- 资质与合规情况模块特殊布局 -->
              <div v-if="subModule.itemKey === 'certification_and_compliance'" class="compliance-module">
              <!-- 第一行：综合评分和描述 -->
              <el-row class="compliance-row" :gutter="10">
                <el-col :span="7" :lg="7" :md="7" :sm="24" :xs="24" class="score-block" style="margin-bottom: 10px;">
                  <div class="score-label">综合评分</div>
                  <div class="score-number">{{ subModule.generalScore }}</div>
                </el-col>
                <el-col :span="16" :lg="16" :md="16" :sm="24" :xs="24" :offset="1" class="description-block">
                 <p class="multiline-ellipsis-4">{{ subModule.generalDesp }}</p> 
                </el-col>
              </el-row>

              <!-- 第二行：两个子项卡片 -->
              <div class="compliance-row">
                <div
                  v-for="(subItem, idx) in subModule.subItem.slice(0, 2)"
                  :key="idx"
                  class="sub-item-card"
                >
                  <div class="sub-item-name">{{ subItem.subitemName }}（{{subItem.generalScore100}}）</div>
                  <div class="sub-item-result">{{ subItem.assessResult }} {{ subItem.assessValue  }}</div>
                </div>
              </div>

              <!-- 第三行和第四行：长卡片 -->
              <div
                v-for="(item, index) in subModule.subItem.slice(2, 4)"
                :key="index + 2"
                class="compliance-row"
              >
                <el-row class="long-card">
                  <el-col :span="8" :lg="8" :md="20" :sm="24" :xs="24" class="long-card-left">
                    <div class="long-card-title">{{ item.subitemName }}（{{ item.generalScore100 }}）</div>
                    <div class="long-card-result">{{ item.assessResult }}</div>
                  </el-col>
                  <el-col  :span="16" :lg="16" :md="24" :sm="24" :xs="24" class="long-card-right">
                    <el-col :span="7"  :lg="11" :md="11" :sm="24" :xs="24"
                      v-for="(dataItem, dataIndex) in (item.dataItem || []).slice(0, 2)"
                      :key="dataIndex"
                      class="data-block"
                      :class="dataIndex === 0 ? 'warning' : 'danger'"
                    >
                      <div class="data-label">{{ dataItem.dataName }}</div>
                      <div class="data-value">{{ dataItem.dataValue }}</div>
                    </el-col>
                  </el-col>
                </el-row>
              </div>
            </div>

              <!-- 历史安全事件与威胁情报模块特殊布局 -->
              <div v-else-if="subModule.itemKey === 'history_and_intelligence'" class="history-intelligence-module">
                <!-- 第一行：综合评分模块 -->
                <div class="history-row" style="display: flex;gap:15px;margin-bottom:15px;">
                  <div class="score-block" style="min-width: 100px;">
                    <div class="score-label">综合评分</div>
                    <div class="score-number">{{ subModule.generalScore }}</div>
                  </div>
                  <div class="description-block">
                    {{ subModule.generalDesp }}
                  </div>
                </div>

                <!-- 第二行：四个背景色为#F8FBFF的色块  -->
                <el-row :gutter="8" class="history-row" >
                  <!-- 第一个色块：安全事件与威胁情报总览 -->
                  <el-col :span="5" :lg="5" :md="12" :sm="24" :xs="24" class="info-card overview-card" style="min-height: 274px;">
                    <div class="card-title">安全事件与威胁情报</div>
                    <!-- 修改：只保留环状图容器 -->
                    <div class="chart-legend-wrap">
                      <div class="donut-chart-container">
                        <div :id="`historyDonutChart_${subModule.itemKey}`" class="donut-chart"></div>
                      </div>
                    </div>
                  </el-col>

                  <!-- 第二个色块：历史处罚情况 -->
                  <el-col :span="8" :lg="8" :md="12" :sm="24" :xs="24" v-if="subModule.subItem[0]" class="info-card punishment-card" style="min-height: 274px;">
                    <div class="card-title">{{ subModule.subItem[0].subitemName }}</div>
                    <!-- 修改：punishment-count右侧增加环比和同比数据 -->
                    <div class="punishment-header">
                      <div class="punishment-count">{{ getPunishmentTotalData(subModule.subItem[0]).dataValue }}次</div>
                      <div class="punishment-trends">
                        <div class="trend-summary">
                          <span>环比</span>
                          <span :class="getTrendClass(getPunishmentTotalData(subModule.subItem[0]).qoqRate)">
                            {{ getTrendIcon(getPunishmentTotalData(subModule.subItem[0]).qoqRate) }}{{ Math.abs(getPunishmentTotalData(subModule.subItem[0]).qoqRate) }}%
                          </span>
                        </div>
                        <div class="trend-summary">
                          <span>同比</span>
                          <span :class="getTrendClass(getPunishmentTotalData(subModule.subItem[0]).yoyRate)">
                            {{ getTrendIcon(getPunishmentTotalData(subModule.subItem[0]).yoyRate) }}{{ Math.abs(getPunishmentTotalData(subModule.subItem[0]).yoyRate) }}%
                          </span>
                        </div>
                      </div>
                    </div>

                    <!-- 动态渲染非合计项 -->
                    <div
                      v-for="(item, idx) in getPunishmentNonTotalData(subModule.subItem[0])"
                      :key="idx"
                      class="punishment-item"
                    >
                      <div class="punishment-chart">
                        <!-- 修改：使用放大版本的环状图并显示pctRate，根据数值动态变化 -->
                        <div class="mini-donut-large dynamic-donut" :style="getPunishmentDonutStyle(item.pctRate, idx)">
                          <div class="donut-inner"></div>
                          <span class="percent">{{ item.pctRate || 0 }}%</span>
                        </div>
                      </div>
                      <div class="punishment-info">
                        <div class="info-title">{{ item.dataName }}</div>
                        <div class="info-value">{{ item.dataValue }}</div>
                      </div>
                      <div class="punishment-trend">
                        <div class="trend-item">
                          <span>环比</span>
                          <span :class="getTrendClass(item.qoqRate)">
                            {{ getTrendIcon(item.qoqRate) }}{{ Math.abs(item.qoqRate) }}%
                          </span>
                        </div>
                        <div class="trend-item">
                          <span>同比</span>
                          <span :class="getTrendClass(item.yoyRate)">
                            {{ getTrendIcon(item.yoyRate) }}{{ Math.abs(item.yoyRate) }}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </el-col>

                  <!-- 第三个色块：安全检查 -->
                  <el-col :span="5" :lg="5" :md="12" :sm="24" :xs="12" v-if="subModule.subItem[1]" class="info-card security-check-card" style="min-height: 274px;">
                    <div class="card-title">{{ subModule.subItem[1].subitemName }}</div>

                    <!-- 四个颜色不同的色块 -->
                    <div class="check-stats">
                      <div
                        v-for="(item, idx) in subModule.subItem[1].dataItem.slice(0, 4)"
                        :key="idx"
                        class="stat-block"
                        :style="{
                          backgroundColor: getCheckColor(idx),
                          borderColor: getCheckTextColor(idx),
                          color: getCheckTextColor(idx)
                        }"
                      >
                        <div class="stat-name">{{ item.dataName }}</div>
                        <div class="stat-rate" v-if="item.dataKey === '1'">{{ item.dataValue }}</div>
                        <div class="stat-rate" v-else>{{ item.pctRate }}%</div>
                      </div>
                    </div>

                    <!-- 近半年检查频发问题类型 -->
                    <div class="frequent-issues">
                      <div class="issues-title">近半年检查频发问题类型：</div>
                      <div class="issues-list">
                        <div
                          v-for="(item, idx) in subModule.subItem[1].dataItem.slice(4, 6)"
                          :key="idx"
                          class="issue-item"
                          style="background-color: #E6E7E8;"
                        >
                          {{ item.dataName }} {{ item.dataValue }}
                        </div>
                      </div>
                    </div>
                  </el-col>

                  <!-- 第四个色块：重大安全事件 -->
                  <el-col :span="5" :lg="5" :md="12" :sm="24" :xs="12" v-if="subModule.subItem[2]" class="info-card major-events-card" style="min-height: 274px;">
                    <div class="card-title">{{ subModule.subItem[2].subitemName }}</div>

                    <!-- 总量和趋势 -->
                    <div class="total-count-header">
                      <div class="total-count">
                        总量： <span style="color:#4584FF;">{{ getMajorEventTotalData(subModule.subItem[2]).dataValue }}</span>
                      </div>
                      <div class="total-trends">
                        <span class="trend-item">
                          环比
                          <span :class="getTrendClass(getMajorEventTotalData(subModule.subItem[2]).qoqRate)">
                            {{ getTrendIcon(getMajorEventTotalData(subModule.subItem[2]).qoqRate) }}{{ Math.abs(getMajorEventTotalData(subModule.subItem[2]).qoqRate) }}%
                          </span>
                        </span>
                        <span class="trend-item">
                          同比
                          <span :class="getTrendClass(getMajorEventTotalData(subModule.subItem[2]).yoyRate)">
                            {{ getTrendIcon(getMajorEventTotalData(subModule.subItem[2]).yoyRate) }}{{ Math.abs(getMajorEventTotalData(subModule.subItem[2]).yoyRate) }}%
                          </span>
                        </span>
                      </div>
                    </div>

                    <!-- 三个横向柱状图 -->
                    <div class="event-bars">
                      <div
                        v-for="(item, idx) in subModule.subItem[2].dataItem.filter(item => item.dataKey !== '1')"
                        :key="idx"
                        class="event-bar"
                      >
                        <div class="bar-info">
                          <span class="bar-name">{{ item.dataName }}</span>
                          <span class="bar-value">{{ item.dataValue }}</span>
                          <span :class="getTrendClass(item.yoyRate)" class="bar-trend">
                            {{ getTrendIcon(item.yoyRate) }}{{ Math.abs(item.yoyRate) }}%
                          </span>
                        </div>
                        <div class="bar-container">
                          <div
                            class="bar-fill"
                            :style="{
                              backgroundColor: getEventBarColor(idx),
                              width: getBarWidth(item.dataValue, subModule.subItem[2].dataItem) + '%'
                            }"
                          ></div>
                        </div>
                      </div>
                    </div>
                  </el-col>
                </el-row>

                <!-- 第三行：事件响应能力 + 安全监测 -->
                <el-row class="history-row" style="width:100%;">
                  <!-- 左边：事件响应能力 -->
                  <el-col :span="10" v-if="subModule.subItem[3]" class="info-card response-card">
                    <div class="card-title">{{ subModule.subItem[3].subitemName }}</div>
                    <!-- 修改：两个色块在同一行展示，背景色加深，文字颜色和谐 -->
                    <el-row class="sa-response-stats" :gutter="10">
                      <el-col :span="8" :lg="8" :md="20" :sm="24" :xs="24"
                        v-for="(item, idx) in subModule.subItem[3].dataItem"
                        :key="idx"
                        class="sa-response-item"
                      >
                        <div class="sa-response-name">{{ item.dataName }}</div>
                        <div class="sa-response-value">{{ item.dataValue }}</div>
                      </el-col>
                      <!-- 企业数据泄露数据量色块 -->
                      <el-col :span="8" :lg="8" :md="20" :sm="24" :xs="24" v-if="subModule.subItem[4].dataItem[0]" class="sa-monitoring-block">
                        <div class="sa-monitoring-name">{{ subModule.subItem[4].dataItem[0].dataName }}</div>
                        <div class="sa-monitoring-value">{{ subModule.subItem[4].dataItem[0].dataValue }}</div>
                      </el-col>
                    </el-row>
                     
                  </el-col>

                  <!-- 右边：安全监测 -->
                  <el-col :span="14" v-if="subModule.subItem[4]" class="info-card monitoring-card">
                    <div class="card-title">{{ subModule.subItem[4].subitemName }}</div>
                    <!-- 其余数据渲染为环状图 -->
                      <div class="sa-monitoring-donuts">
                          <!-- 使用真实API数据 -->
                          <div
                            v-for="(item, idx) in getMonitoringRealData(subModule)"
                            :key="idx"
                            class="sa-monitoring-donut-item"
                          >
                            <div class="sa-mini-monitoring-donut" :style="{ background: getMonitoringDonutStyle(item.pctRate, idx) }">
                              <span class="sa-donut-percent">{{ item.pctRate || 0 }}%</span>
                            </div>
                            <div class="sa-donut-label">{{ item.dataName }}</div>
                          </div>
                      </div>
                   
                  </el-col>
                </el-row>

              </div>

              <!-- 其他模块的通用布局 -->
              <div v-else class="general-module">
                <div class="module-score">
                  <span class="score-label">综合评分：</span>
                  <span class="score-value">{{ subModule.generalScore }}</span>
                </div>
                <div class="module-description">{{ subModule.generalDesp }}</div>

                <div v-if="subModule.subItem && subModule.subItem.length > 0" class="sub-items">
                  <div
                    v-for="(subItem, idx) in subModule.subItem"
                    :key="idx"
                    class="sub-item"
                  >
                    <div class="sub-item-header">
                      <span class="sub-item-name">{{ subItem.subitemName }}</span>
                      <span class="sub-item-score">{{ subItem.generalScore }}</span>
                    </div>
                    <div class="sub-item-result">{{ subItem.assessResult }}</div>

                    <div v-if="subItem.dataItem && subItem.dataItem.length > 0" class="data-items">
                      <div
                        v-for="(dataItem, dataIdx) in subItem.dataItem"
                        :key="dataIdx"
                        class="data-item"
                      >
                        <span class="data-name">{{ dataItem.dataName }}：</span>
                        <span class="data-value">{{ dataItem.dataValue }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 第二行：互联网暴露面与资产风险 + 运营安全能力 + 舆情与社会影响 -->
        <section class="row-2">
          <el-row>
            <el-col
              :span="8" :lg="8" :md="8" :sm="24" :xs="24"
              v-for="(subModule, index) in getRowModules(2)"
              :key="index"
              class="sub-module"
            >
              <div class="sub-module-title">{{ subModule.itemName }}</div>
              <div class="sub-module-content">
                <!-- 互联网暴露面与资产风险模块特殊布局 -->
                <div v-if="subModule.itemKey === 'internet_and_assetrisk'" class="internet-asset-module">
                  <!-- 第一行：综合评分模块 -->
                  <div class="internet-row">
                    <div class="score-block">
                      <div class="score-label">综合评分</div>
                      <div class="score-number">{{ subModule.generalScore }}</div>
                    </div>
                    <div class="description-block">
                      {{ subModule.generalDesp }}
                    </div>
                  </div>

                  <!-- 第二行：环状图 + 六边形色块 -->
                  <div class="internet-row">
                    <!-- 左侧：环状图 -->
                    <div class="chart-section">
                      <div class="donut-chart-container">
                        <div :id="`internetDonutChart_${subModule.itemKey}`" class="donut-chart"></div>
                      </div>
                    </div>

                    <!-- 右侧：六边形色块 -->
                    <div class="hexagon-section" style="margin-left:-50px;">
                      <div
                        v-for="(item, idx) in getInternetDonutData(subModule)"
                        :key="idx"
                        class="hexagon-item"
                      >
                        <div class="hexagon-shape" :style="{ backgroundColor: getHexagonColor(idx) }">
                          <div class="hexagon-content">
                            <div class="hex-name" :style="{ color: getHexagonTextColor(idx) }">{{ item.dataName }}</div>
                            <div class="hex-value" :style="{ color: getHexagonTextColor(idx) }">{{ item.dataValue }}</div>
                            <div class="hex-trend">
                              <span :style="{ color: getHexagonTextColor(idx) }">同比</span>
                              <span :class="getTrendClass(item.yoyRate)">
                                {{ getTrendIcon(item.yoyRate) }}{{ Math.abs(item.yoyRate) }}%
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 运营安全能力模块特殊布局 -->
                <div v-else-if="subModule.itemKey === 'operation_and_safety'" class="sa-operation-safety-module">
                  <!-- 第一行：综合评分 + 描述（参照history-row样式） -->
                  <div class="sa-operation-row compliance-row">
                    <div class="score-block" style="min-width: 100px;">
                      <div class="score-label">综合评分</div>
                      <div class="score-number">{{ subModule.generalScore }}</div>
                    </div>
                    <div class="description-block">
                      {{ subModule.generalDesp }}
                    </div>
                  </div>

                  <!-- 第二行：两个浅灰色块 -->
                  <div class="sa-operation-row">
                    <!-- 第一个浅灰色块：40%宽度 -->
                    <div v-if="subModule.subItem[0]" class="sa-operation-card sa-operation-card-40">
                      <div class="sa-operation-title">{{ subModule.subItem[0].subitemName }}</div>
                      <div class="sa-operation-result">{{ subModule.subItem[0].assessResult }}</div>
                    </div>

                    <!-- 第二个浅灰色块：60%宽度 -->
                    <div v-if="subModule.subItem[1]" class="sa-operation-card sa-operation-card-60">
                      <div class="sa-operation-title">{{ subModule.subItem[1].subitemName }}</div>
                      <div class="sa-operation-data-blocks">
                        <div
                          v-for="(item, idx) in subModule.subItem[1].dataItem.slice(0, 2)"
                          :key="idx"
                          class="sa-operation-data-block"
                          :class="idx === 0 ? 'sa-light-red' : 'sa-light-blue'"
                        >
                          <div class="sa-data-name">{{ item.dataName }}</div>
                          <div class="sa-data-value">{{ item.dataValue }}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 第三行：两个浅灰色块 -->
                  <div class="sa-operation-row">
                    <!-- 左边浅灰色块：60%宽度 -->
                    <div v-if="subModule.subItem[2]" class="sa-operation-card sa-operation-card-60">
                      <div class="sa-operation-title">{{ subModule.subItem[2].subitemName }}</div>
                      <div class="sa-operation-data-blocks">
                        <div
                          v-for="(item, idx) in subModule.subItem[2].dataItem.slice(0, 2)"
                          :key="idx"
                          class="sa-operation-data-block"
                          :class="idx === 0 ? 'sa-light-red' : 'sa-light-blue'"
                        >
                          <div class="sa-data-name">{{ item.dataName }}</div>
                          <div class="sa-data-value">{{ item.dataValue }}</div>
                        </div>
                      </div>
                    </div>

                    <!-- 右边浅灰色块：40%宽度 -->
                    <div v-if="subModule.subItem[3]" class="sa-operation-card sa-operation-card-40">
                      <div class="sa-operation-title">{{ subModule.subItem[3].subitemName }}</div>
                      <div class="sa-operation-data-blocks-vertical">
                        <div v-if="subModule.subItem[3].dataItem[0]" class="sa-operation-data-block sa-light-blue">
                          <div class="sa-data-name">{{ subModule.subItem[3].dataItem[0].dataName }}</div>
                        </div>
                        <div v-if="subModule.subItem[3].dataItem[0]" class="sa-operation-data-block sa-light-red">
                          <div class="sa-data-value">{{ subModule.subItem[3].dataItem[0].dataValue }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 舆情与社会影响力模块特殊布局 -->
                <div v-else-if="subModule.itemKey === 'sentiment_and_community'" class="sa-sentiment-community-module">
                  <!-- 第一行：综合评分 + 描述（参照compliance-row样式） -->
                  <div class="sa-sentiment-row compliance-row">
                    <div class="score-block" style="min-width: 100px;">
                      <div class="score-label">综合评分</div>
                      <div class="score-number">{{ subModule.generalScore }}</div>
                    </div>
                    <div class="description-block">
                      {{ subModule.generalDesp }}
                    </div>
                  </div>

                  <!-- 第二行：浅灰色块 + 两个#F9EEE2色块 -->
                  <div v-if="subModule.subItem[0]" class="sa-sentiment-row">
                    <div class="sa-sentiment-card">
                      <div class="sa-sentiment-title">{{ subModule.subItem[0].subitemName }}</div>
                      <div class="sa-sentiment-data-blocks">
                        <!-- 第一个#F9EEE2色块 -->
                        <div v-if="subModule.subItem[0].dataItem[0]" class="sa-sentiment-data-block sa-orange-light">
                          <div class="sa-data-main">
                            <span class="sa-data-value">{{ subModule.subItem[0].dataItem[0].dataValue }}</span>
                            <span class="sa-data-trend" :class="getTrendClass(subModule.subItem[0].dataItem[0].yoyRate)">
                              {{ getTrendIcon(subModule.subItem[0].dataItem[0].yoyRate) }}  同比  {{ Math.abs(subModule.subItem[0].dataItem[0].yoyRate) }}%
                            </span>
                          </div>
                          <div class="sa-data-name">{{ subModule.subItem[0].dataItem[0].dataName }}</div>
                        </div>
                        <!-- 第二个#F9EEE2色块 -->
                        <div v-if="subModule.subItem[0].dataItem[1]" class="sa-sentiment-data-block sa-orange-light">
                          <div class="sa-data-value">{{ subModule.subItem[0].dataItem[1].dataValue }}</div>
                          <div class="sa-data-name">{{ subModule.subItem[0].dataItem[1].dataName }}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 第三行：浅灰色块 + 两个浅蓝色块 -->
                  <div v-if="subModule.subItem[1]" class="sa-sentiment-row">
                    <div class="sa-sentiment-card">
                      <div class="sa-sentiment-title">{{ subModule.subItem[1].subitemName }}</div>
                      <div class="sa-sentiment-data-blocks">
                        <!-- 第一个浅蓝色块 -->
                        <div v-if="subModule.subItem[1].dataItem[0]" class="sa-sentiment-data-block sa-blue-light">
                          <div class="sa-data-main">
                            <span class="sa-data-value">{{ subModule.subItem[1].dataItem[0].dataValue }}</span>
                            <span class="sa-data-trend" :class="getTrendClass(subModule.subItem[1].dataItem[0].yoyRate)">
                              {{ getTrendIcon(subModule.subItem[1].dataItem[0].yoyRate) }}  同比  {{ Math.abs(subModule.subItem[1].dataItem[0].yoyRate) }}%
                            </span>
                          </div>
                          <div class="sa-data-name">{{ subModule.subItem[1].dataItem[0].dataName }}</div>
                        </div>
                        <!-- 第二个浅蓝色块 -->
                        <div v-if="subModule.subItem[1].dataItem[1]" class="sa-sentiment-data-block sa-blue-light">
                          <div class="sa-data-value">{{ subModule.subItem[1].dataItem[1].dataValue }}</div>
                          <div class="sa-data-name">{{ subModule.subItem[1].dataItem[1].dataName }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>



                <!-- 其他模块的通用布局 -->
                <div v-else class="general-module">
                  <div class="module-score">
                    <span class="score-label">综合评分：</span>
                    <span class="score-value">{{ subModule.generalScore }}</span>
                  </div>
                  <div class="module-description">{{ subModule.generalDesp }}</div>

                  <div v-if="subModule.subItem && subModule.subItem.length > 0" class="sub-items">
                    <div
                      v-for="(subItem, idx) in subModule.subItem"
                      :key="idx"
                      class="sub-item"
                    >
                      <div class="sub-item-header">
                        <span class="sub-item-name">{{ subItem.subitemName }}</span>
                        <span class="sub-item-score">{{ subItem.generalScore }}</span>
                      </div>
                      <div class="sub-item-result">{{ subItem.assessResult }}</div>

                      <div v-if="subItem.dataItem && subItem.dataItem.length > 0" class="data-items">
                        <div
                          v-for="(dataItem, dataIdx) in subItem.dataItem"
                          :key="dataIdx"
                          class="data-item"
                        >
                          <span class="data-name">{{ dataItem.dataName }}：</span>
                          <span class="data-value">{{ dataItem.dataValue }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </section>

        <!-- 第三行：人员与内部安全管理 + 数据质量管理 -->
        <section class="row-3">
          <el-row :span="8" :lg="8" :md="8" :sm="24" :xs="24" style="width: 100%;">
            <el-col
              v-for="(subModule, index) in getRowModules(3)"
              :key="index"
              class="sub-module"
            >
              <div class="sub-module-title">{{ subModule.itemName }}</div>
              <div class="sub-module-content">
                <!-- 人员与内部安全管理模块特殊布局 -->
                <div v-if="subModule.itemKey === 'personnel_and_safetymanagement'" class="sa-personnel-safety-module">
                  <!-- 第一行：综合评分 + 描述（参照compliance-row样式） -->
                  <div class="sa-personnel-row compliance-row">
                    <div class="score-block">
                      <div class="score-label">综合评分</div>
                      <div class="score-number">{{ subModule.generalScore }}</div>
                    </div>
                    <div class="description-block">
                      {{ subModule.generalDesp }}
                    </div>
                  </div>

                  <!-- 第二行：三个并排的浅灰色块 -->
                  <div class="sa-personnel-row">
                    <!-- 第一个浅灰色块：安全培训（浅紫色块） -->
                    <div v-if="subModule.subItem[0]" class="sa-personnel-card">
                      <div class="sa-personnel-title">{{ subModule.subItem[0].subitemName }}</div>
                      <div class="sa-personnel-data-blocks">
                        <div
                          v-for="(item, idx) in subModule.subItem[0].dataItem"
                          :key="idx"
                          class="sa-personnel-data-block sa-light-purple"
                        >
                          <div class="sa-data-value">{{ item.dataValue }}</div>
                          <div class="sa-data-name">{{ item.dataName }}</div>
                        </div>
                      </div>
                    </div>

                    <!-- 第二个浅灰色块：权限管理（浅黄色块） -->
                    <div v-if="subModule.subItem[1]" class="sa-personnel-card">
                      <div class="sa-personnel-title">{{ subModule.subItem[1].subitemName }}</div>
                      <div class="sa-personnel-data-blocks">
                        <div
                          v-for="(item, idx) in subModule.subItem[1].dataItem"
                          :key="idx"
                          class="sa-personnel-data-block sa-light-yellow"
                        >
                          <div class="sa-data-value">{{ item.dataValue }}</div>
                          <div class="sa-data-name">{{ item.dataName }}</div>
                        </div>
                      </div>
                    </div>

                    <!-- 第三个浅灰色块：安全意识测试（浅绿色块） -->
                    <div v-if="subModule.subItem[2]" class="sa-personnel-card">
                      <div class="sa-personnel-title">{{ subModule.subItem[2].subitemName }}</div>
                      <div class="sa-personnel-data-blocks">
                        <div
                          v-for="(item, idx) in subModule.subItem[2].dataItem"
                          :key="idx"
                          class="sa-personnel-data-block sa-light-green"
                        >
                          <div class="sa-data-value">{{ item.dataValue }}</div>
                          <div class="sa-data-name">{{ item.dataName }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 数据质量管理模块特殊布局 -->
                <div v-else-if="subModule.itemKey === 'data_and_quality'" class="sa-data-quality-module">
                  <!-- 第一行：综合评分 + 描述（参照compliance-row样式） -->
                  <div class="sa-data-row compliance-row">
                    <div class="score-block">
                      <div class="score-label">综合评分</div>
                      <div class="score-number">{{ subModule.generalScore }}</div>
                    </div>
                    <div class="description-block">
                      {{ subModule.generalDesp }}
                    </div>
                  </div>

                  <!-- 第二行：三个并排的浅灰色块 -->
                  <div class="sa-data-row">
                    <!-- 第一个浅灰色块：数据完整性（浅紫色块） -->
                    <div v-if="subModule.subItem[0]" class="sa-data-card">
                      <div class="sa-data-title">{{ subModule.subItem[0].subitemName }}</div>
                      <div class="sa-data-data-blocks">
                        <div
                          v-for="(item, idx) in subModule.subItem[0].dataItem"
                          :key="idx"
                          class="sa-data-data-block sa-light-purple"
                        >
                          <div class="sa-data-value">{{ item.dataValue }}</div>
                          <div class="sa-data-name">{{ item.dataName }}</div>
                        </div>
                      </div>
                    </div>

                    <!-- 第二个浅灰色块：数据一致性（浅黄色块） -->
                    <div v-if="subModule.subItem[1]" class="sa-data-card">
                      <div class="sa-data-title">{{ subModule.subItem[1].subitemName }}</div>
                      <div class="sa-data-data-blocks">
                        <div
                          v-for="(item, idx) in subModule.subItem[1].dataItem"
                          :key="idx"
                          class="sa-data-data-block sa-light-yellow"
                        >
                          <div class="sa-data-value">{{ item.dataValue }}</div>
                          <div class="sa-data-name">{{ item.dataName }}</div>
                        </div>
                      </div>
                    </div>

                    <!-- 第三个浅灰色块：数据可追溯性（浅绿色块） -->
                    <div v-if="subModule.subItem[2]" class="sa-data-card">
                      <div class="sa-data-title">{{ subModule.subItem[2].subitemName }}</div>
                      <div class="sa-data-data-blocks">
                        <div
                          v-for="(item, idx) in subModule.subItem[2].dataItem"
                          :key="idx"
                          class="sa-data-data-block sa-light-green"
                        >
                          <div class="sa-data-value">{{ item.dataValue }}</div>
                          <div class="sa-data-name">{{ item.dataName }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 通用布局 -->
                <div v-else class="general-module">
                  <div class="module-score">
                    <span class="score-label">综合评分：</span>
                    <span class="score-value">{{ subModule.generalScore }}</span>
                  </div>
                  <div class="module-description">{{ subModule.generalDesp }}</div>

                  <div v-if="subModule.subItem && subModule.subItem.length > 0" class="sub-items">
                    <div
                      v-for="(subItem, idx) in subModule.subItem"
                      :key="idx"
                      class="sub-item"
                    >
                      <div class="sub-item-header">
                        <span class="sub-item-name">{{ subItem.subitemName }}</span>
                        <span class="sub-item-score">{{ subItem.generalScore }}</span>
                      </div>
                      <div class="sub-item-result">{{ subItem.assessResult }}</div>

                      <div v-if="subItem.dataItem && subItem.dataItem.length > 0" class="data-items">
                        <div
                          v-for="(dataItem, dataIdx) in subItem.dataItem"
                          :key="dataIdx"
                          class="data-item"
                        >
                          <span class="data-name">{{ dataItem.dataName }}：</span>
                          <span class="data-value">{{ dataItem.dataValue }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </section>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getErpAssessCurrent, getErpAssessInfo } from '@/api/enterprise'
export default {
  name: 'SecurityAssessment',
  props: {
    enterpriseData: {
      type: Object,
      default: () => ({})
    },
    chartId: {
      type: String,
      default: 'securityRadarChart'
    }
  },
  data() {
    return {
      securityChart: null,
      historyCharts: {},
      isExpanded: false, // 默认收起
      isLoadingSubModules: false,
      currentAssessment: {},
      subModules: [],
      domObserver: null, // DOM观察器
      // 七个子模块的itemKey（按UI展示顺序）
      subModuleKeys: [
        'certification_and_compliance',
        'history_and_intelligence',
        'internet_and_assetrisk',
        'operation_and_safety',
        'sentiment_and_community',
        'personnel_and_safetymanagement',
        'data_and_quality'
      ]
    }
  },
  mounted() {
    console.log('SecurityAssessment - mounted时的enterpriseData:', this.enterpriseData)

    // 首先加载当前评估数据（getErpAssessCurrent）
    // 这会触发currentAssessment的watch，进而调用loadSubModulesWithCorrectParams
    this.loadCurrentAssessment()

    // 强制初始化历史安全事件图表
    this.$nextTick(() => {
      setTimeout(() => {
        this.forceInitHistoryChart()
      }, 1000)
    })

    // 注意：不再直接调用preloadSubModules，而是通过watch机制触发
    // 这确保了API调用的正确顺序：先getErpAssessCurrent，再getErpAssessInfo

    // 使用MutationObserver监听DOM变化
    this.observeDOM()

    // 初始化测试图表（仅用于测试区域）
    this.$nextTick(() => {
      // 延迟初始化，确保DOM完全渲染
      setTimeout(() => {
        console.log('mounted - 延迟初始化测试图表')
        this.initTestCharts()
      }, 100)
    })

    // 额外的重试机制
    setTimeout(() => {
      console.log('mounted - 额外重试初始化测试图表')
      this.initTestCharts()
    }, 500)

    setTimeout(() => {
      console.log('mounted - 最后重试初始化测试图表')
      this.initTestCharts()
    }, 1000)

    setTimeout(() => {
      console.log('mounted - 最终重试初始化测试图表')
      this.initTestCharts()
    }, 2000)
  },

  beforeDestroy() {
    // 清理DOM观察器
    if (this.domObserver) {
      this.domObserver.disconnect()
      console.log('beforeDestroy - DOM观察器已清理')
    }
  },
  watch: {
    // 监听enterpriseData变化
    enterpriseData: {
      handler(newVal, oldVal) {
        console.log('SecurityAssessment - enterpriseData变化:', { newVal, oldVal })
        if (newVal && newVal.creditCode && (!oldVal || !oldVal.creditCode || newVal.creditCode !== oldVal.creditCode)) {
          console.log('SecurityAssessment - 检测到新的creditCode，重新加载数据:', newVal.creditCode)
          this.loadCurrentAssessment()
        }
      },
      deep: true,
      immediate: false
    },

    // 监听currentAssessment变化，当getErpAssessCurrent返回后触发子模块数据加载
    currentAssessment: {
      handler(newVal, oldVal) {
        console.log('SecurityAssessment - currentAssessment变化:', { newVal, oldVal })

        // 检查是否有有效的currentAssessment数据和enterpriseData
        if (newVal &&
            this.enterpriseData &&
            this.enterpriseData.creditCode &&
            newVal.periodYear &&
            newVal.periodMonth) {

          console.log('SecurityAssessment - currentAssessment数据完整，触发子模块数据加载')
          console.log('SecurityAssessment - 参数来源:', {
            creditCode: this.enterpriseData.creditCode,
            periodYear: newVal.periodYear,
            periodMonth: newVal.periodMonth
          })

          // 延迟一下确保currentAssessment完全设置
          this.$nextTick(() => {
            this.loadSubModulesWithCorrectParams()
          })
        } else {
          console.warn('SecurityAssessment - currentAssessment或enterpriseData数据不完整:', {
            currentAssessment: newVal,
            enterpriseData: this.enterpriseData
          })
        }
      },
      deep: true,
      immediate: false
    }
  },
  beforeDestroy() {
    if (this.securityChart) {
      this.securityChart.dispose()
      this.securityChart = null
    }

    // 销毁历史图表
    if (this.historyCharts) {
      Object.values(this.historyCharts).forEach(chart => {
        if (chart) {
          chart.dispose()
        }
      })
      this.historyCharts = {}
    }
  },
  methods: {
    // 获取评分变化文本
    getScoreChangeText() {
      if (!this.currentAssessment) return '综合评分较上月持平'

      const lastMonthScore = this.currentAssessment.lastMonthScore || 0

      if (lastMonthScore === 0) {
        return '综合评分较上月持平'
      } else if (lastMonthScore > 0) {
        return `综合评分较上月升高了 ${lastMonthScore} 分`
      } else {
        return `综合评分较上月下降了 ${Math.abs(lastMonthScore)} 分`
      }
    },

    // 获取评分对比文本
    getScoreCompareText() {
      if (!this.currentAssessment) return '与平均分持平'

      const averageScore = this.currentAssessment.averageScore || 0

      if (averageScore > 0) {
        return `平均分高出 ${averageScore} 分`
      } else if (averageScore < 0) {
        return `平均分低出 ${Math.abs(averageScore)} 分`
      } else {
        return '与平均分持平'
      }
    },

    // 根据风险等级获取星星数量
    getStarCountByRiskLevel() {
      if (!this.currentAssessment || !this.currentAssessment.riskLevelLabel) {
        return 0
      }

      const riskLevel = this.currentAssessment.riskLevelLabel
      switch (riskLevel) {
        case '低风险':
          return 4.5
        case '中风险':
          return 2.5
        case '高风险':
          return 1.5
        default:
          return 0
      }
    },

    // 获取安全事件与威胁情报环状图数据
    getHistoryDonutData(subModule) {
      if (!subModule || !subModule.subItem) return []

      // 查找subitemKey为'subitem_total'的项
      const totalItem = subModule.subItem.find(item => item.subitemKey === 'subitem_total')
      if (!totalItem || !totalItem.dataItem) return []

      return totalItem.dataItem
    },

    // 获取历史处罚情况的合计数据
    getPunishmentTotalData(subItem) {
      if (!subItem || !subItem.dataItem) return { dataValue: 0, qoqRate: 0, yoyRate: 0 }

      // 根据dataKey="1"或dataName="合计"查找合计数据
      const totalItem = subItem.dataItem.find(item => item.dataKey === '1' || item.dataName === '合计')
      console.log('getPunishmentTotalData - 查找合计数据:', totalItem)
      return totalItem || { dataValue: 0, qoqRate: 0, yoyRate: 0 }
    },

    // 获取历史处罚情况的非合计数据
    getPunishmentNonTotalData(subItem) {
      if (!subItem || !subItem.dataItem) return []

      // 过滤掉dataKey="1"或dataName="合计"的数据
      const nonTotalData = subItem.dataItem.filter(item => item.dataKey !== '1' && item.dataName !== '合计')
      console.log('getPunishmentNonTotalData - 非合计数据:', nonTotalData)
      return nonTotalData
    },

    // 获取重大安全事件的合计数据
    getMajorEventTotalData(subItem) {
      if (!subItem || !subItem.dataItem) return { dataValue: 0, qoqRate: 0, yoyRate: 0 }

      // 根据dataKey="1"或dataName="合计"查找合计数据
      const totalItem = subItem.dataItem.find(item => item.dataKey === '1' || item.dataName === '合计')
      console.log('getMajorEventTotalData - 查找合计数据:', totalItem)
      return totalItem || { dataValue: 0, qoqRate: 0, yoyRate: 0 }
    },

    // 获取互联网暴露面与资产风险环状图数据
    getInternetDonutData(subModule) {
      if (!subModule || !subModule.subItem) return []

      // 查找subitemKey为'subitem_total'的项
      const totalItem = subModule.subItem.find(item => item.subitemKey === 'subitem_total')
      if (!totalItem || !totalItem.dataItem) return []

      return totalItem.dataItem
    },

    // 加载当前评估数据
    async loadCurrentAssessment() {
      console.log('SecurityAssessment - 开始加载评估数据')
      console.log('SecurityAssessment - enterpriseData:', this.enterpriseData)

      if (!this.enterpriseData || !this.enterpriseData.creditCode) {
        console.warn('SecurityAssessment - 缺少企业信用代码，使用默认数据')
        // this.currentAssessment = this.getDefaultAssessmentData()
        this.$nextTick(() => {
          this.initSecurityRadarChart()
        })
        return
      }

      try {
        console.log('SecurityAssessment - 调用getErpAssessCurrent接口，creditCode:', this.enterpriseData.creditCode)
        const response = await getErpAssessCurrent(this.enterpriseData.creditCode)
        console.log('SecurityAssessment - getErpAssessCurrent响应:', response)

        if (response.code === 200 && response.data) {
          this.currentAssessment = response.data
          console.log('SecurityAssessment - 设置currentAssessment:', this.currentAssessment)
          console.log('SecurityAssessment - currentAssessment包含的参数:', {
            periodYear: this.currentAssessment.periodYear,
            periodMonth: this.currentAssessment.periodMonth,
            creditCode: this.enterpriseData.creditCode
          })
          this.$nextTick(() => {
            this.initSecurityRadarChart()
          })
        } else {
          console.error('SecurityAssessment - 获取安全评估数据失败:', response.msg)
          // 使用默认数据
          // this.currentAssessment = this.getDefaultAssessmentData()
          this.$nextTick(() => {
            this.initSecurityRadarChart()
          })
        }
      } catch (error) {
        console.error('SecurityAssessment - 加载安全评估数据出错:', error)
        // 使用默认数据
        // this.currentAssessment = this.getDefaultAssessmentData()
        this.$nextTick(() => {
          this.initSecurityRadarChart()
        })
      }
    },

    // 使用正确参数加载子模块数据（creditCode来自enterpriseData，其他参数来自currentAssessment）
    async loadSubModulesWithCorrectParams() {
      console.log('SecurityAssessment - 使用正确参数加载子模块数据')

      if (!this.enterpriseData || !this.enterpriseData.creditCode) {
        console.warn('SecurityAssessment - 缺少企业信用代码，无法加载子模块数据')
        return
      }

      if (!this.currentAssessment || !this.currentAssessment.periodYear || !this.currentAssessment.periodMonth) {
        console.warn('SecurityAssessment - 缺少currentAssessment参数，无法加载子模块数据')
        return
      }

      try {
        this.isLoadingSubModules = true

        const requestParams = {
          creditCode: this.enterpriseData.creditCode,
          periodYear: this.currentAssessment.periodYear,
          periodMonth: this.currentAssessment.periodMonth
        }

        console.log('SecurityAssessment - 子模块请求参数（正确来源）:', requestParams)
        console.log('SecurityAssessment - 参数来源详情:', {
          creditCode: `来自enterpriseData: ${this.enterpriseData.creditCode}`,
          periodYear: `来自currentAssessment: ${this.currentAssessment.periodYear}`,
          periodMonth: `来自currentAssessment: ${this.currentAssessment.periodMonth}`
        })
        console.log('SecurityAssessment - 子模块Keys:', this.subModuleKeys)

        const promises = this.subModuleKeys.map(async (itemKey, index) => {
          console.log(`SecurityAssessment - 发起第${index + 1}个子模块请求:`, itemKey)

          try {
            const response = await getErpAssessInfo({
              itemKey,
              creditCode: requestParams.creditCode,
              periodYear: requestParams.periodYear,
              periodMonth: requestParams.periodMonth
            })

            console.log(`SecurityAssessment - 第${index + 1}个子模块响应:`, itemKey, response)

            if (response && response.code === 200 && response.data) {
              console.log(`SecurityAssessment - 第${index + 1}个子模块请求成功:`, itemKey, response.data)
              return {
                itemKey,
                ...response.data
              }
            } else {
              console.warn(`SecurityAssessment - 第${index + 1}个子模块请求无数据:`, itemKey, response)
              // return this.getDefaultSubModuleData(itemKey)
            }
          } catch (error) {
            console.error(`SecurityAssessment - 第${index + 1}个子模块请求失败:`, itemKey, error)
            // return this.getDefaultSubModuleData(itemKey)
          }
        })

        const results = await Promise.all(promises)
        this.subModules = results.filter(result => result !== null)

        console.log('SecurityAssessment - 子模块数据加载完成（正确参数）:', this.subModules)

      } catch (error) {
        console.error('SecurityAssessment - 加载子模块数据失败（正确参数）:', error)
        // this.subModules = this.subModuleKeys.map(itemKey =>
        //   this.getDefaultSubModuleData(itemKey)
        // )
      } finally {
        this.isLoadingSubModules = false
      }
    },

    // 获取默认评估数据
    getDefaultAssessmentData() {
      const currentDate = new Date()
      return {
        generalScore: 85,
        lastMonthChange: 5,
        aboveAverage: 12,
        riskLevel: '低风险',
        starCount: 4,
        generalDesp: '该企业网络安全状况良好，各项指标表现稳定，建议继续保持现有安全措施。',
        // 添加必要的参数，用于后续的getErpAssessInfo调用
        periodYear: currentDate.getFullYear(),
        periodMonth: currentDate.getMonth() + 1,
        radarData: [
          { name: '资质与合规情况', value: 3.2 },
          { name: '历史安全事件与威胁情报', value: 2.4 },
          { name: '互联网暴露面与资产风险', value: 1.0 },
          { name: '运营安全能力', value: 1.0 },
          { name: '舆情与社会影响', value: 0.2 },
          { name: '人员与内部安全管理', value: 0.2 },
          { name: '数据质量管理', value: 0.6 }
        ]
      }
    },

    // 加载假数据确保模块展开
    loadFakeSubModules() {
      console.log('SecurityAssessment - 加载假数据')

      // 使用默认数据方法生成假数据
      // this.subModules = this.subModuleKeys.map(itemKey =>
      //   this.getDefaultSubModuleData(itemKey)
      // )

      console.log('SecurityAssessment - 假数据加载完成:', this.subModules)

      // 延迟初始化图表，确保DOM渲染完成
      this.$nextTick(() => {
        setTimeout(() => {
          console.log('SecurityAssessment - 开始初始化真实模块图表')

          const historyModule = this.subModules.find(module => module.itemKey === 'history_and_intelligence')
          if (historyModule) {
            console.log('SecurityAssessment - 找到历史安全事件模块，初始化图表')
            this.initHistoryDonutChart(historyModule)
          }

          const internetModule = this.subModules.find(module => module.itemKey === 'internet_and_assetrisk')
          if (internetModule) {
            console.log('SecurityAssessment - 找到互联网暴露面模块，初始化图表')
            this.initInternetDonutChart(internetModule)
          }
        }, 1000) // 延迟1秒确保DOM完全渲染
      })
    },

    // 强制调用API的测试方法（用于调试）
    async forceCallAPI() {
      console.log('SecurityAssessment - 强制调用API测试')

      try {
        this.isLoadingSubModules = true

        // 使用测试用的creditCode
        const requestParams = {
          creditCode: '91110000717841644J', // 测试用的creditCode
          periodYear: new Date().getFullYear(),
          periodMonth: new Date().getMonth() + 1
        }

        console.log('SecurityAssessment - 强制API调用请求参数:', requestParams)
        console.log('SecurityAssessment - 子模块Keys:', this.subModuleKeys)

        const promises = this.subModuleKeys.map(async (itemKey, index) => {
          console.log(`SecurityAssessment - 强制发起第${index + 1}个子模块请求:`, itemKey)

          try {
            const response = await getErpAssessInfo({
              itemKey,
              creditCode: requestParams.creditCode,
              periodYear: requestParams.periodYear,
              periodMonth: requestParams.periodMonth
            })

            console.log(`SecurityAssessment - 强制第${index + 1}个子模块响应:`, itemKey, response)

            if (response && response.code === 200 && response.data) {
              console.log(`SecurityAssessment - 强制第${index + 1}个子模块请求成功:`, itemKey, response.data)
              return {
                itemKey,
                ...response.data
              }
            } else {
              console.warn(`SecurityAssessment - 强制第${index + 1}个子模块请求无数据:`, itemKey, response)
              return 
            }
          } catch (error) {
            console.error(`SecurityAssessment - 强制第${index + 1}个子模块请求失败:`, itemKey, error)
            return 
          }
        })

        const results = await Promise.all(promises)
        this.subModules = results.filter(result => result !== null)

        console.log('SecurityAssessment - 强制API调用完成:', this.subModules)

      } catch (error) {
        // console.error('SecurityAssessment - 强制API调用失败:', error)
        // this.subModules = this.subModuleKeys.map(itemKey =>
        //   // this.getDefaultSubModuleData(itemKey)
        // )
      } finally {
        this.isLoadingSubModules = false
      }
    },

    // 预加载子模块数据（不显示UI）
    async preloadSubModules() {
      console.log('SecurityAssessment - 预加载子模块数据')
      console.log('SecurityAssessment - 当前enterpriseData:', this.enterpriseData)

      if (!this.enterpriseData || !this.enterpriseData.creditCode) {
        console.warn('SecurityAssessment - 企业数据不完整，调用强制API测试方法')
        // 调用强制API测试方法
        await this.forceCallAPI()
        return
      }

      try {
        this.isLoadingSubModules = true

        const requestParams = {
          creditCode: this.enterpriseData.creditCode,
          periodYear: new Date().getFullYear(),
          periodMonth: new Date().getMonth() + 1
        }

        console.log('SecurityAssessment - 预加载子模块请求参数:', requestParams)
        console.log('SecurityAssessment - 子模块Keys:', this.subModuleKeys)

        const promises = this.subModuleKeys.map(async (itemKey, index) => {
          console.log(`SecurityAssessment - 发起第${index + 1}个子模块请求:`, itemKey)

          try {
            const response = await getErpAssessInfo({
              itemKey,
              creditCode: requestParams.creditCode,
              periodYear: requestParams.periodYear,
              periodMonth: requestParams.periodMonth
            })

            

            if (response && response.code === 200 && response.data) {
              data = response.data
              if (itemKey === 'history_and_intelligence') {
                data = {
    "itemKey": "history_and_intelligence",
    "itemName": "历史安全事件与威胁情报",
    "generalScore": 10,
    "generalDesp": "123123历史安全事件与威胁情报综合评分10.00分，该企业因安全问题被警告有0次，罚款次数有0次，其它项正常。",
    "subItem": [
        {
            "subitemKey": "history_and_intelligence_01",
            "subitemName": "历史处罚情况",
            "generalScore100": 100,
            "generalScore": 2,
            "assessResult": "",
            "assessValue": "0",
            "dataItem": [
                {
                    "dataKey": "1",
                    "dataName": "合计",
                    "dataValue": "0",
                    "yoyRate": 1.8,
                    "qoqRate": -8.9,
                    "pctRate": 30
                },
                {
                    "dataKey": "2",
                    "dataName": "警告占比",
                    "dataValue": "0",
                    "yoyRate": 30.7,
                    "qoqRate": -27,
                    "pctRate": 20
                },
                {
                    "dataKey": "3",
                    "dataName": "罚款占比",
                    "dataValue": "0",
                    "yoyRate": -99,
                    "qoqRate": 12.8,
                    "pctRate": 54
                }
            ]
        },
        {
            "subitemKey": "history_and_intelligence_02",
            "subitemName": "安全检查",
            "generalScore100": 100,
            "generalScore": 2,
            "assessResult": "",
            "assessValue": "0",
            "dataItem": [
                {
                    "dataKey": "1",
                    "dataName": "累计检查次数",
                    "dataValue": "0",
                    "yoyRate": 0,
                    "qoqRate": 0,
                    "pctRate": 0
                },
                {
                    "dataKey": "2",
                    "dataName": "平均问题占比",
                    "dataValue": "0",
                    "yoyRate": 0,
                    "qoqRate": 0,
                    "pctRate": 0
                },
                {
                    "dataKey": "3",
                    "dataName": "行政处罚占比",
                    "dataValue": "0",
                    "yoyRate": 0,
                    "qoqRate": 0,
                    "pctRate": 100
                },
                {
                    "dataKey": "4",
                    "dataName": "问题整改通过率",
                    "dataValue": "0",
                    "yoyRate": 0,
                    "qoqRate": 0,
                    "pctRate": 0
                },
                {
                    "dataKey": "5",
                    "dataName": "数据更新不及时",
                    "dataValue": "0",
                    "yoyRate": 0,
                    "qoqRate": 0,
                    "pctRate": 0
                },
                {
                    "dataKey": "6",
                    "dataName": "系统高危漏洞",
                    "dataValue": "0",
                    "yoyRate": 0,
                    "qoqRate": 0,
                    "pctRate": 0
                }
            ]
        },
        {
            "subitemKey": "history_and_intelligence_03",
            "subitemName": "重大安全事件",
            "generalScore100": 100,
            "generalScore": 2,
            "assessResult": "",
            "assessValue": "0",
            "dataItem": [
                {
                    "dataKey": "1",
                    "dataName": "合计",
                    "dataValue": "0",
                    "yoyRate": 0,
                    "qoqRate": 0,
                    "pctRate": 0
                },
                {
                    "dataKey": "2",
                    "dataName": "黑客攻击",
                    "dataValue": "0",
                    "yoyRate": 0,
                    "qoqRate": 0,
                    "pctRate": 0
                },
                {
                    "dataKey": "3",
                    "dataName": "数据泄露",
                    "dataValue": "0",
                    "yoyRate": 0,
                    "qoqRate": 0,
                    "pctRate": 0
                },
                {
                    "dataKey": "4",
                    "dataName": "漏洞报警",
                    "dataValue": "0",
                    "yoyRate": 0,
                    "qoqRate": 0,
                    "pctRate": 0
                }
            ]
        },
        {
            "subitemKey": "history_and_intelligence_04",
            "subitemName": "事件响应能力",
            "generalScore100": 100,
            "generalScore": 2,
            "assessResult": "",
            "assessValue": "0",
            "dataItem": [
                {
                    "dataKey": "1",
                    "dataName": "平均修复时间",
                    "dataValue": "0.0h",
                    "yoyRate": 0,
                    "qoqRate": 0,
                    "pctRate": 0
                },
                {
                    "dataKey": "2",
                    "dataName": "最长一次修复时间",
                    "dataValue": "0.0h",
                    "yoyRate": 0,
                    "qoqRate": 0,
                    "pctRate": 0
                }
            ]
        },
        {
            "subitemKey": "history_and_intelligence_05",
            "subitemName": "安全监测",
            "generalScore100": 100,
            "generalScore": 2,
            "assessResult": "",
            "assessValue": "0",
            "dataItem": [
                {
                    "dataKey": "1",
                    "dataName": "企业数据泄露数量",
                    "dataValue": "0",
                    "yoyRate": 0,
                    "qoqRate": 0,
                    "pctRate": 0
                },
                {
                    "dataKey": "2",
                    "dataName": "IP地址段",
                    "dataValue": "0",
                    "yoyRate": 0,
                    "qoqRate": 0,
                    "pctRate": 0
                },
                {
                    "dataKey": "3",
                    "dataName": "IDC机房",
                    "dataValue": "0",
                    "yoyRate": 0,
                    "qoqRate": 0,
                    "pctRate": 0
                },
                {
                    "dataKey": "4",
                    "dataName": "宽带用户",
                    "dataValue": "0",
                    "yoyRate": 0,
                    "qoqRate": 0,
                    "pctRate": 0
                },
                {
                    "dataKey": "5",
                    "dataName": "域名注册",
                    "dataValue": "0",
                    "yoyRate": 0,
                    "qoqRate": 0,
                    "pctRate": 0
                },
                {
                    "dataKey": "6",
                    "dataName": "专线用户",
                    "dataValue": "0",
                    "yoyRate": 0,
                    "qoqRate": 0,
                    "pctRate": 0
                }
            ]
        },
        {
            "subitemKey": "subitem_total",
            "subitemName": "安全事件与威胁情报",
            "generalScore100": 0,
            "generalScore": 0,
            "assessResult": "总数",
            "assessValue": "0",
            "dataItem": [
                {
                    "dataKey": "1",
                    "dataName": "历史处罚情况",
                    "dataValue": "0",
                    "yoyRate": 0,
                    "qoqRate": 0,
                    "pctRate": 0
                },
                {
                    "dataKey": "2",
                    "dataName": "安全检查",
                    "dataValue": "0",
                    "yoyRate": 0,
                    "qoqRate": 0,
                    "pctRate": 0
                },
                {
                    "dataKey": "3",
                    "dataName": "重大安全事件",
                    "dataValue": "0",
                    "yoyRate": 0,
                    "qoqRate": 0,
                    "pctRate": 0
                },
                {
                    "dataKey": "5",
                    "dataName": "安全监测",
                    "dataValue": "0",
                    "yoyRate": 0,
                    "qoqRate": 0,
                    "pctRate": 0
                }
            ]
        }
    ]
}
              }
              // console.log(`SecurityAssessment - 第${index + 1}个子模块响应:`, itemKey, response)
              console.log(`SecurityAssessment - 第${index + 1}个子模块请求成---:`, itemKey, data)
              return {
                itemKey,
                ...data
              }
            } else {
              console.warn(`SecurityAssessment - 第${index + 1}个子模块请求无数据:`, itemKey, response)
              // return this.getDefaultSubModuleData(itemKey)
            }
          } catch (error) {
            console.error(`SecurityAssessment - 第${index + 1}个子模块请求失败:`, itemKey, error)
            // return this.getDefaultSubModuleData(itemKey)
          }
        })

        const results = await Promise.all(promises)
        this.subModules = results.filter(result => result !== null)

        console.log('SecurityAssessment - 预加载子模块数据完成:', this.subModules)

      } catch (error) {
        // console.error('SecurityAssessment - 预加载子模块数据失败:', error)
        // this.subModules = this.subModuleKeys.map(itemKey =>
        //   // this.getDefaultSubModuleData(itemKey)
        // )
      } finally {
        this.isLoadingSubModules = false
      }
    },

    // 观察DOM变化
    observeDOM() {
      console.log('observeDOM - 开始观察DOM变化')

      // 创建MutationObserver
      this.domObserver = new MutationObserver((mutations) => {
        let shouldInitCharts = false

        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === 1) { // Element node
                // 检查是否添加了图表容器
                if (node.id === 'testHistoryDonutChart' ||
                    node.id === 'testInternetDonutChart' ||
                    node.id === 'testWarningDonut' ||
                    node.id === 'testPenaltyDonut') {
                  console.log('observeDOM - 检测到图表容器被添加:', node.id)
                  shouldInitCharts = true
                }

                // 检查子节点
                const chartElements = node.querySelectorAll && node.querySelectorAll('#testHistoryDonutChart, #testInternetDonutChart, #testWarningDonut, #testPenaltyDonut')
                if (chartElements && chartElements.length > 0) {
                  console.log('observeDOM - 检测到图表容器在子节点中被添加')
                  shouldInitCharts = true
                }
              }
            })
          }
        })

        if (shouldInitCharts) {
          console.log('observeDOM - 触发图表初始化')
          setTimeout(() => {
            this.initTestCharts()
          }, 100)
        }
      })

      // 开始观察
      this.domObserver.observe(document.body, {
        childList: true,
        subtree: true
      })
    },

    // 切换展开状态
    async toggleExpand() {
      console.log('SecurityAssessment - toggleExpand被调用，当前isExpanded:', this.isExpanded)
      this.isExpanded = !this.isExpanded
      console.log('SecurityAssessment - 切换后isExpanded:', this.isExpanded)

      if (this.isExpanded) {
        console.log('SecurityAssessment - 展开UI，当前subModules长度:', this.subModules.length)

        // 如果没有数据，先加载数据
        if (this.subModules.length === 0) {
          console.log('SecurityAssessment - 没有预加载数据，现在加载')
          await this.preloadSubModules()
        }

        // 延迟初始化图表，确保DOM渲染完成
        this.$nextTick(() => {
          setTimeout(() => {
            console.log('SecurityAssessment - 开始初始化真实模块图表')

            const historyModule = this.subModules.find(module => module.itemKey === 'history_and_intelligence')
            if (historyModule) {
              console.log('SecurityAssessment - 初始化历史安全事件图表')
              this.initHistoryDonutChart(historyModule)
            }

            const internetModule = this.subModules.find(module => module.itemKey === 'internet_and_assetrisk')
            if (internetModule) {
              console.log('SecurityAssessment - 初始化互联网暴露面图表')
              this.initInternetDonutChart(internetModule)
            }
          }, 500) // 延迟500ms确保DOM完全渲染
        })
      }
    },

    // 加载子模块数据（保留原方法，但现在主要用于备用）
    async loadSubModules() {
      console.log('SecurityAssessment - loadSubModules被调用，检查是否已有数据')

      // 如果已经有数据，直接返回
      if (this.subModules.length > 0) {
        console.log('SecurityAssessment - 已有子模块数据，无需重新加载')
        return
      }

      // 如果没有数据，调用预加载方法
      console.log('SecurityAssessment - 没有子模块数据，调用预加载方法')
      await this.preloadSubModules()
    },

    // 原始加载方法（备用）
    async originalLoadSubModules() {
      console.log('SecurityAssessment - 开始加载子模块数据')

      if (!this.currentAssessment.creditCode && !this.enterpriseData.creditCode) {
        console.warn('SecurityAssessment - 缺少企业信用代码，无法加载子模块数据')
        return
      }

      this.isLoadingSubModules = true

      try {
        const creditCode = this.currentAssessment.creditCode || this.enterpriseData.creditCode
        const periodYear = this.currentAssessment.periodYear || new Date().getFullYear()
        const periodMonth = this.currentAssessment.periodMonth || (new Date().getMonth() + 1)

        console.log('SecurityAssessment - 子模块请求参数:', { creditCode, periodYear, periodMonth })
        console.log('SecurityAssessment - 子模块Keys:', this.subModuleKeys)

        const promises = this.subModuleKeys.map((itemKey, index) => {
          console.log(`SecurityAssessment - 发起第${index + 1}个子模块请求:`, itemKey)
          return getErpAssessInfo({
            itemKey,
            creditCode,
            periodYear,
            periodMonth
          })
        })

        const responses = await Promise.all(promises)
        console.log('SecurityAssessment - 所有子模块响应:', responses)

        this.subModules = responses.map((response, index) => {
          console.log(`SecurityAssessment - 处理第${index + 1}个子模块响应:`, response)
          if (response.code === 200 && response.data) {
            return {
              itemKey: this.subModuleKeys[index],
              ...response.data
            }
          } else {
            // 返回默认数据
            console.log(`SecurityAssessment - 第${index + 1}个子模块使用默认数据`)
            // return this.getDefaultSubModuleData(this.subModuleKeys[index])
          }
        })

        console.log('SecurityAssessment - 最终子模块数据:', this.subModules)

        // 初始化历史安全事件图表和互联网暴露面图表
        this.$nextTick(() => {
          setTimeout(() => {
            const historyModule = this.subModules.find(module => module.itemKey === 'history_and_intelligence')
            console.log('SecurityAssessment - 查找历史安全事件模块:', historyModule)
            if (historyModule) {
              console.log('SecurityAssessment - 初始化历史安全事件图表')
              this.initHistoryDonutChart(historyModule)
            } else {
              console.warn('SecurityAssessment - 未找到历史安全事件模块')
            }

            const internetModule = this.subModules.find(module => module.itemKey === 'internet_and_assetrisk')
            console.log('SecurityAssessment - 查找互联网暴露面模块:', internetModule)
            if (internetModule) {
              console.log('SecurityAssessment - 初始化互联网暴露面图表')
              this.initInternetDonutChart(internetModule)
            } else {
              console.warn('SecurityAssessment - 未找到互联网暴露面模块')
            }
          }, 500) // 延迟500ms确保DOM完全渲染
        })

      } catch (error) {
        console.error('SecurityAssessment - 加载子模块数据出错:', error)
        // 使用默认数据
        // this.subModules = this.subModuleKeys.map(itemKey =>
        //   this.getDefaultSubModuleData(itemKey)
        // )

        // 初始化历史安全事件图表和互联网暴露面图表
        this.$nextTick(() => {
          const historyModule = this.subModules.find(module => module.itemKey === 'history_and_intelligence')
          console.log('SecurityAssessment - 查找历史安全事件模块(默认数据):', historyModule)
          if (historyModule) {
            console.log('SecurityAssessment - 初始化历史安全事件图表(默认数据)')
            this.initHistoryDonutChart(historyModule)
          } else {
            console.warn('SecurityAssessment - 未找到历史安全事件模块(默认数据)')
          }

          const internetModule = this.subModules.find(module => module.itemKey === 'internet_and_assetrisk')
          console.log('SecurityAssessment - 查找互联网暴露面模块(默认数据):', internetModule)
          if (internetModule) {
            console.log('SecurityAssessment - 初始化互联网暴露面图表(默认数据)')
            this.initInternetDonutChart(internetModule)
          } else {
            console.warn('SecurityAssessment - 未找到互联网暴露面模块(默认数据)')
          }
        })
      } finally {
        this.isLoadingSubModules = false
      }
    },

    // 获取默认子模块数据
    getDefaultSubModuleData(itemKey) {
      const moduleNames = {
        'certification_and_compliance': '资质与合规情况',
        'history_and_intelligence': '历史安全事件与威胁情报',
        'internet_and_assetrisk': '互联网暴露面与资产风险',
        'operation_and_safety': '运营安全能力',
        'sentiment_and_community': '舆情与社会影响',
        'personnel_and_safetymanagement': '人员与内部安全管理',
        'data_and_quality': '数据质量管理'
      }

      // 为历史安全事件与威胁情报模块提供特殊的默认数据
      if (itemKey === 'history_and_intelligence') {
        return {
          itemKey,
          itemName: moduleNames[itemKey],
          generalScore: 10,
          generalDesp: '历史安全事件与威胁情报综合评分10.00分，该企业因安全问题被警告有0次，罚款次数有0次，其它项正常。',
          subItem: [
            {
              subitemKey: 'history_and_intelligence_01',
              subitemName: '历史处罚情况',
              generalScore: 2,
              assessValue: '0',
              dataItem: [
                { dataKey: '1', dataName: '合计', dataValue: '0', yoyRate: 0, qoqRate: 0, pctRate: 0 },
                { dataKey: '2', dataName: '警告占比', dataValue: '0', yoyRate: 0, qoqRate: 0, pctRate: 0 },
                { dataKey: '3', dataName: '罚款占比', dataValue: '0', yoyRate: 0, qoqRate: 0, pctRate: 0 }
              ]
            },
            {
              subitemKey: 'history_and_intelligence_02',
              subitemName: '安全检查',
              generalScore: 2,
              dataItem: [
                { dataName: '累计检查次数', pctRate: 0 },
                { dataName: '平均问题占比', pctRate: 0 },
                { dataName: '行政处罚占比', pctRate: 100 },
                { dataName: '问题整改通过率', pctRate: 0 },
                { dataName: '数据更新不及时', dataValue: '0' },
                { dataName: '系统高危漏洞', dataValue: '0' }
              ]
            },
            {
              subitemKey: 'history_and_intelligence_03',
              subitemName: '重大安全事件',
              generalScore: 2,
              dataItem: [
                { dataKey: '1', dataName: '合计', dataValue: '0', yoyRate: 0, qoqRate: 0, pctRate: 0 },
                { dataKey: '2', dataName: '黑客攻击', dataValue: '0', yoyRate: 0, qoqRate: 0, pctRate: 0 },
                { dataKey: '3', dataName: '数据泄露', dataValue: '0', yoyRate: 0, qoqRate: 0, pctRate: 0 },
                { dataKey: '4', dataName: '漏洞报警', dataValue: '0', yoyRate: 0, qoqRate: 0, pctRate: 0 }
              ]
            },
            {
              subitemKey: 'history_and_intelligence_04',
              subitemName: '事件响应能力',
              generalScore: 2,
              dataItem: [
                { dataName: '平均修复时间', dataValue: '0' },
                { dataName: '最长修复时间', dataValue: '0' }
              ]
            },
            {
              subitemKey: 'history_and_intelligence_05',
              subitemName: '安全监测',
              generalScore: 2,
              dataItem: [
                { dataName: '企业数据泄露数量', dataValue: '0' }
              ]
            }
          ]
        }
      }

      // 为互联网暴露面与资产风险模块提供特殊的默认数据
      if (itemKey === 'internet_and_assetrisk') {
        return {
          itemKey,
          itemName: '互联网暴露面与资产风险',
          generalScore: 10,
          generalDesp: '互联网暴露面与资产风险综合评分为10.00分，目前整体评分良好。',
          subItem: [
            {
              subitemKey: 'internet_and_assetrisk_01',
              subitemName: '网站漏洞',
              generalScore100: 100,
              generalScore: 10,
              assessResult: '',
              assessValue: '0',
              dataItem: [
                {
                  dataKey: '1',
                  dataName: '企业数据泄露数量',
                  dataValue: '0',
                  yoyRate: 0,
                  qoqRate: 0,
                  pctRate: 0
                },
                {
                  dataKey: '2',
                  dataName: '危险漏洞',
                  dataValue: '0',
                  yoyRate: 0,
                  qoqRate: 0,
                  pctRate: 0
                },
                {
                  dataKey: '3',
                  dataName: '高危漏洞',
                  dataValue: '0',
                  yoyRate: 0,
                  qoqRate: 0,
                  pctRate: 0
                },
                {
                  dataKey: '4',
                  dataName: '中危漏洞',
                  dataValue: '0',
                  yoyRate: 0,
                  qoqRate: 0,
                  pctRate: 0
                },
                {
                  dataKey: '5',
                  dataName: '低危漏洞',
                  dataValue: '0',
                  yoyRate: 0,
                  qoqRate: 0,
                  pctRate: 0
                },
                {
                  dataKey: '6',
                  dataName: '安全',
                  dataValue: '0',
                  yoyRate: 0,
                  qoqRate: 0,
                  pctRate: 0
                }
              ]
            }
          ]
        }
      }

      return {
        itemKey,
        itemName: moduleNames[itemKey] || '未知模块',
        generalScore: Math.floor(Math.random() * 40) + 60,
        generalDesp: '该模块评估数据暂未获取，显示为模拟数据。',
        subItem: [
          {
            subitemName: '子项目1',
            assessResult: '良好',
            generalScore: Math.floor(Math.random() * 20) + 80,
            dataItem: [
              { dataName: '数据项1', dataValue: Math.floor(Math.random() * 10) },
              { dataName: '数据项2', dataValue: Math.floor(Math.random() * 5) }
            ]
          }
        ]
      }
    },

    // 初始化安全雷达图
    initSecurityRadarChart() {
      console.log('SecurityAssessment - 初始化雷达图，chartId:', this.chartId)
      const chartDom = document.getElementById(this.chartId)
      console.log('SecurityAssessment - 雷达图DOM元素:', chartDom)

      if (!chartDom) {
        console.error('SecurityAssessment - 找不到雷达图DOM元素，chartId:', this.chartId)
        return
      }

      // 如果已存在图表，先销毁
      if (this.securityChart) {
        this.securityChart.dispose()
      }

      this.securityChart = echarts.init(chartDom)
      console.log('SecurityAssessment - 雷达图实例创建成功')

      // 使用scoreItemList作为雷达图数据源
      let radarData = []
      if (this.currentAssessment && this.currentAssessment.scoreItemList && Array.isArray(this.currentAssessment.scoreItemList)) {
        radarData = this.currentAssessment.scoreItemList.map(item => ({
          name: item.itemName || '未知',
          value: item.subMainScore || 0,
          subMainScore: item.subMainScore || 0
        }))
      } 
      // else {
      //   radarData = this.getDefaultAssessmentData().radarData
      // }
      console.log('SecurityAssessment - 雷达图数据:', radarData)

      if (!Array.isArray(radarData) || radarData.length === 0) {
        console.error('SecurityAssessment - 雷达图数据无效:', radarData)
        return
      }
      const maxValue = Math.max(...radarData.map(obj => obj.value));
      const indicator = radarData.map(item => ({
        name: `${item.name || '未知'}\n${item.subMainScore || 0}`,
        max: maxValue * 1.01
      }))
      const seriesData = [{
        value: radarData.map(item => item.value || 0),
        name: '安全评估',
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(255, 215, 0, 0.6)' },
            { offset: 1, color: 'rgba(255, 215, 0, 0.1)' }
          ])
        }
      }]

      const option = {
        tooltip: {
          trigger: 'item',
           outside: true,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          textStyle: {
            color: '#fff'
          }
        },
        radar: {
          indicator: indicator,
          radius: '65%',
          splitNumber: 5,
          axisName: {
            color: '#fff',
            fontSize: 12,
            fontWeight: 'bold'
          },
          splitArea: {
            areaStyle: {
              color: ['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)', 'rgba(255, 255, 255, 0.02)', 'rgba(255, 255, 255, 0.01)'],
              shadowColor: 'rgba(255, 255, 255, 0.1)',
              shadowBlur: 10
            }
          },
          axisLine: {
            lineStyle: {
              color: '#fff',
              width: 2
            }
          },
          splitLine: {
            lineStyle: {
              color: '#fff',
              width: 1,
              opacity: 0.6
            }
          }
        },
        series: [{
          type: 'radar',
          data: seriesData,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            width: 3,
            color: '#FFD700'
          },
          itemStyle: {
            color: '#FFD700',
            borderColor: '#fff',
            borderWidth: 2
          },
          emphasis: {
            lineStyle: {
              width: 4,
              color: '#FFF700'
            },
            itemStyle: {
              color: '#FFF700',
              borderColor: '#fff',
              borderWidth: 3
            },
            // label: {
            //         normal: {
            //             show: true,
            //             formatter: function (params) {
            //               console.log('7777777---',params)
            //                 return params.name + '\n' + params.value;
            //             }
            //         }
            //     }
            
          }
        }]
      }

      this.securityChart.setOption(option)
      // 监听窗口大小变化
        window.addEventListener('resize', () => {
          this.securityChart && this.securityChart.resize()
        })
      console.log('SecurityAssessment - 雷达图配置设置完成')
    },

    // 获取变化图标
    getChangeIcon(change) {
      if (change > 0) {
        return { icon: 'el-icon-arrow-up', color: '#67C23A' }
      } else if (change < 0) {
        return { icon: 'el-icon-arrow-down', color: '#F56C6C' }
      } else {
        return { icon: 'el-icon-minus', color: '#909399' }
      }
    },

    // 获取安全风险等级颜色
    getSecurityRiskColor(riskLevel) {
      const colors = {
        '低风险': '#60FB66',
        '中风险': '#E6A23C',
        '高风险': '#F56C6C'
      }
      return colors[riskLevel] || '#EBEEF5'
    },

    // 格式化更新时间
    formatUpdateTime(time) {
      if (!time) return '暂无'
      const date = new Date(time)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    // 获取itemName的样式类
    getItemNameClass(subitemDesp) {
      if (!subitemDesp) return ''
      if (subitemDesp.includes('降低')) {
        return 'decrease'
      } else if (subitemDesp.includes('上升')) {
        return 'increase'
      }
      return ''
    },

    // 检查文本是否溢出
    isTextOverflow(text) {
      if (!text) return false
      // 简单的字符长度判断，可以根据实际情况调整
      return text.length > 20
    },

    // 根据行号获取对应的模块
    getRowModules(rowNumber) {
      console.log(`getRowModules - 获取第${rowNumber}行模块，当前subModules:`, this.subModules)

      if (!this.subModules || this.subModules.length === 0) {
        console.warn(`getRowModules - subModules为空，返回空数组`)
        return []
      }

      // 定义每行包含的模块索引
      const rowMapping = {
        1: [0, 1], // 第一行：资质与合规情况 + 历史安全事件与威胁情报
        2: [2, 3, 4], // 第二行：互联网暴露面与资产风险 + 运营安全能力 + 舆情与社会影响
        3: [5, 6] // 第三行：人员与内部安全管理 + 数据质量管理
      }

      const moduleIndexes = rowMapping[rowNumber] || []
      const result = moduleIndexes.map(index => this.subModules[index]).filter(module => module)

      console.log(`getRowModules - 第${rowNumber}行模块索引:`, moduleIndexes)
      console.log(`getRowModules - 第${rowNumber}行模块结果:`, result)

      return result
    },

    // 获取环形图颜色
    getDonutColor(index) {
      const colors = ['#3481FF', '#1ABB78', '#FFC53D', '#FF7A45', '#722ED1']
      return colors[index % colors.length]
    },

    // 计算分数占比
    getScorePercent(score, subItems) {
      const total = subItems.reduce((sum, item) => sum + (item.generalScore || 0), 0)
      return total > 0 ? Math.round((score / total) * 100) : 0
    },

    // 获取趋势图标
    getTrendIcon(rate) {
      if (rate > 0) return '↑'
      if (rate < 0) return '↓'
      return ''
    },

    // 计算dataItem子项的平均环比率
    getAverageQoqRate(dataItems) {
      if (!dataItems || dataItems.length === 0) return 0

      const totalQoqRate = dataItems.reduce((sum, item) => {
        return sum + (item.qoqRate || 0)
      }, 0)

      const averageRate = totalQoqRate / dataItems.length
      console.log('SecurityAssessment - 计算平均环比率:', {
        dataItems: dataItems.map(item => ({ dataName: item.dataName, qoqRate: item.qoqRate })),
        totalQoqRate,
        count: dataItems.length,
        averageRate: Math.round(averageRate)
      })

      return Math.round(averageRate)
    },

    // 计算dataItem子项的平均同比率
    getAverageYoyRate(dataItems) {
      if (!dataItems || dataItems.length === 0) return 0

      const totalYoyRate = dataItems.reduce((sum, item) => {
        return sum + (item.yoyRate || 0)
      }, 0)

      const averageRate = totalYoyRate / dataItems.length
      console.log('SecurityAssessment - 计算平均同比率:', {
        dataItems: dataItems.map(item => ({ dataName: item.dataName, yoyRate: item.yoyRate })),
        totalYoyRate,
        count: dataItems.length,
        averageRate: Math.round(averageRate)
      })

      return Math.round(averageRate)
    },

    // 获取趋势样式类
    getTrendClass(rate) {
      if (rate > 0) return 'trend-up'
      if (rate < 0) return 'trend-down'
      return 'trend-neutral'
    },

    // 获取检查统计颜色（浅色背景版本）
    getCheckColor(index) {
      const colors = ['#E3F2FD', '#E8F5E8', '#FFF3E0', '#F3E5F5']
      return colors[index % colors.length]
    },

    // 获取检查统计文字和边框颜色（深色版本）
    getCheckTextColor(index) {
      const colors = ['#1976D2', '#388E3C', '#F57C00', '#7B1FA2']
      return colors[index % colors.length]
    },

    // 获取事件柱状图颜色
    getEventBarColor(index) {
      const colors = ['#FC6A7A', '#58DBD3', '#434CBD']
      return colors[index % colors.length]
    },

    // 获取响应能力颜色
    getResponseColor(index) {
      const colors = ['#E3F2FD', '#E8F5E8']
      return colors[index % colors.length]
    },

    // 获取响应能力颜色（加深版本）
    getResponseColorDeep(index) {
      const colors = ['#1976D2', '#388E3C']
      return colors[index % colors.length]
    },

    // 获取监测环状图样式
    getMonitoringDonutStyle(pctRate, index) {
      const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
      const color = colors[index % colors.length]
      const percentage = pctRate || 0
      const angle = (percentage / 100) * 360

      return `conic-gradient(${color} 0deg ${angle}deg, #E5E5E5 ${angle}deg 360deg)`
    },

    // 获取监测模块真实数据
    getMonitoringRealData(subModule) {
      if (!subModule || !subModule.subItem || !subModule.subItem[4] || !subModule.subItem[4].dataItem) {
        console.warn('getMonitoringRealData - 监测模块数据结构不正确')
        return []
      }

      // 从subItem[4].dataItem中获取除第一个外的其他数据项（第一个用于显示总数）
      const dataItems = subModule.subItem[4].dataItem.slice(1) // 跳过第一个总数项

      return dataItems.map(item => ({
        dataName: item.dataName || '',
        dataValue: item.dataValue || 0,
        pctRate: item.pctRate || 0
      }))
    },

    // 获取历史处罚情况圆环样式（根据pctRate动态变化）
    getPunishmentDonutStyle(pctRate, index) {
      const rate = pctRate || 0
      const color = index === 0 ? '#67C23A' : '#F56C6C' // 绿色或红色

      if (rate === 0) {
        // 当pctRate为0时，显示灰色圆环（有空洞），不显示彩色部分
        return {
          background: '#E5E5E5 !important', // 全灰色背景
          width: '66px !important',
          height: '66px !important',
          borderRadius: '50% !important',
          display: 'flex !important',
          alignItems: 'center !important',
          justifyContent: 'center !important',
          position: 'relative !important'
        }
      } else {
        // 根据pctRate计算角度（百分比转换为360度）
        const degrees = Math.round((rate / 100) * 360)
        return {
          background: `conic-gradient(${color} 0deg ${degrees}deg, #E5E5E5 ${degrees}deg 360deg) !important`,
          width: '66px !important',
          height: '66px !important',
          borderRadius: '50% !important',
          display: 'flex !important',
          alignItems: 'center !important',
          justifyContent: 'center !important',
          position: 'relative !important'
        }
      }
    },

    // 获取互联网暴露面环状图颜色
    getInternetColor(index) {
      const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
      return colors[index % colors.length]
    },

    // 获取六边形色块颜色
    getHexagonColor(index) {
      const colors = ['#FFE5E5', '#E5F9F6', '#E5F3FF', '#F0FFF0', '#FFF8E1', '#F3E5F5']
      return colors[index % colors.length]
    },

    // 获取六边形文字颜色（比背景色深的同色系）
    getHexagonTextColor(index) {
      const textColors = ['#B71C1C', '#004D40', '#0D47A1', '#1B5E20', '#E65100', '#4A148C']
      return textColors[index % textColors.length]
    },

    // 计算总事件数量
    getTotalEventCount(dataItems) {
      return dataItems.reduce((sum, item) => sum + (parseInt(item.dataValue) || 0), 0)
    },

    // 计算柱状图宽度
    getBarWidth(value, dataItems) {
      const maxValue = Math.max(...dataItems.map(item => parseInt(item.dataValue) || 0))
      return maxValue > 0 ? (parseInt(value) / maxValue) * 100 : 0
    },

    // 初始化历史安全事件环形图
    initHistoryDonutChart(subModule) {
      console.log('initHistoryDonutChart - 开始初始化，subModule:', subModule)

      // 强制使用假数据确保图表显示
      const chartId = `historyDonutChart_${subModule ? subModule.itemKey : 'history_and_intelligence'}`
      console.log('initHistoryDonutChart - 查找图表DOM，chartId:', chartId)

      // 延迟查找DOM元素，确保元素已经渲染
      setTimeout(() => {
        const chartDom = document.getElementById(chartId)
        console.log('initHistoryDonutChart - 找到的DOM元素:', chartDom)

        if (!chartDom) {
          console.error('initHistoryDonutChart - 未找到DOM元素:', chartId)
          // 尝试查找所有可能的历史图表元素
          const allHistoryCharts = document.querySelectorAll('[id*="historyDonutChart"]')
          console.log('initHistoryDonutChart - 所有历史图表元素:', allHistoryCharts)
          if (allHistoryCharts.length > 0) {
            const firstChart = allHistoryCharts[0]
            console.log('initHistoryDonutChart - 使用第一个找到的图表元素:', firstChart)
            this.renderHistoryChart(firstChart)
          }
          return
        }

        this.renderHistoryChart(chartDom, subModule)
      }, 100)
    },

    // 渲染历史安全事件图表
    renderHistoryChart(chartDom, subModule) {
      const chartId = chartDom.id

      // 强制设置容器尺寸
      chartDom.style.width = '180px'
      chartDom.style.height = '220px'
      chartDom.style.minWidth = '180px'
      chartDom.style.minHeight = '220px'

      // 如果已存在图表，先销毁
      if (this.historyCharts && this.historyCharts[chartId]) {
        this.historyCharts[chartId].dispose()
      }

      const chart = echarts.init(chartDom)
      console.log('renderHistoryChart - ECharts实例创建成功，容器尺寸:', {
        width: chartDom.offsetWidth,
        height: chartDom.offsetHeight,
        style: chartDom.style.cssText
      })

      // 获取真实数据，如果没有则使用默认数据
      let dataItems = this.getHistoryDonutData(subModule)

      // 如果没有数据，使用您提供的数据结构作为默认值
      if (!dataItems || dataItems.length === 0) {
        console.log('renderHistoryChart - 使用默认数据结构')
        dataItems = [
          { dataKey: "1", dataName: "历史处罚情况", dataValue: "0", yoyRate: 0, qoqRate: 0, pctRate: 0 },
          { dataKey: "2", dataName: "安全检查", dataValue: "0", yoyRate: 0, qoqRate: 0, pctRate: 0 },
          { dataKey: "3", dataName: "重大安全事件", dataValue: "0", yoyRate: 0, qoqRate: 0, pctRate: 0 },
          { dataKey: "5", dataName: "安全监测", dataValue: "0", yoyRate: 0, qoqRate: 0, pctRate: 0 }
        ]
      } else {
        console.log('renderHistoryChart - 使用API数据:', dataItems)
      }

      const data = dataItems.map((item, index) => ({
        name: item.dataName,
        value: parseInt(item.dataValue) || 0,
        itemStyle: { color: this.getDonutColor(index) }
      }))

      const total = data.reduce((sum, item) => sum + item.value, 0)
      console.log('renderHistoryChart - 图表数据:', data, '总数:', total)

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        grid: {
            left: '0%', // 图表左侧距离容器的距离
            // right: '40%', // 图表右侧距离容器的距离
            bottom: '0%', // 图表底部距离容器的距离
            containLabel: true // 是否包含标签文本在内
          },
        legend: {
          show: true,
          orient: 'horizontal', // 水平排列
          left: 'left', // 居中
          bottom: '0%', // 位置在底部
          icon: 'circle',
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 10, // 图例项之间的间距
          textStyle: {
            fontSize: 11,
            color: '#666'
          },
          formatter: function(name) {
            const item = data.find(d => d.name === name)
            const dataItem = dataItems.find(d => d.dataName === name)
            const pctRate = dataItem ? dataItem.pctRate : 0
            return `${name}: ${item ? item.value : 0} ${pctRate}%`
          }
        },
        series: [
          {
            name: '安全事件与威胁情报',
            type: 'pie',
            radius: ['35%', '72%'],
            center: ['36%', '30%'], // 居中显示，稍微上移为图例留空间
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 5,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            data: data
          }
        ],
        graphic: {
          type: 'text',
          left: '28%', // 居中
          top: '25%', // 与圆环中心对齐
          style: {
            text: `${total}\n总数`,
            textAlign: 'center',
            textVerticalAlign: 'middle',
            fill: '#666',
            fontSize: 14,
            fontWeight: 'bold'
          }
        }
      }

      chart.setOption(option)
      console.log('renderHistoryChart - 图表配置设置完成')

      // 保存图表实例以便后续销毁
      if (!this.historyCharts) {
        this.historyCharts = {}
      }
      this.historyCharts[chartId] = chart // 延迟100ms确保DOM渲染完成
    },

    // 强制初始化历史安全事件图表
    forceInitHistoryChart() {
      console.log('forceInitHistoryChart - 开始强制初始化历史图表')

      // 查找所有可能的历史图表容器
      const possibleSelectors = [
        '#historyDonutChart_history_and_intelligence',
        '[id*="historyDonutChart"]',
        '.donut-chart[id*="history"]'
      ]

      let chartDom = null
      for (const selector of possibleSelectors) {
        chartDom = document.querySelector(selector)
        if (chartDom) {
          console.log('forceInitHistoryChart - 找到图表容器:', selector, chartDom)
          break
        }
      }

      if (!chartDom) {
        console.error('forceInitHistoryChart - 未找到任何历史图表容器')
        return
      }

      this.renderHistoryChart(chartDom)
    },

    // 初始化互联网暴露面环状图
    initInternetDonutChart(subModule) {
      console.log('initInternetDonutChart - 开始初始化，subModule:', subModule)
      if (!subModule || !subModule.subItem || !subModule.subItem[0] || !subModule.subItem[0].dataItem) {
        console.warn('initInternetDonutChart - 数据结构不正确')
        return
      }

      const chartId = `internetDonutChart_${subModule.itemKey}`
      console.log('initInternetDonutChart - 查找图表DOM，chartId:', chartId)

      // 延迟查找DOM元素，确保元素已经渲染
      setTimeout(() => {
        const chartDom = document.getElementById(chartId)
        console.log('initInternetDonutChart - 找到的DOM元素:', chartDom)

        if (!chartDom) {
          console.error('initInternetDonutChart - 未找到DOM元素:', chartId)
          return
        }

        // 如果已存在图表，先销毁
        if (this.historyCharts && this.historyCharts[chartId]) {
          this.historyCharts[chartId].dispose()
        }

        const chart = echarts.init(chartDom)
        console.log('initInternetDonutChart - ECharts实例创建成功')

        const dataItems = this.getInternetDonutData(subModule)
        const data = dataItems.map((item, index) => ({
          name: item.dataName,
          value: parseInt(item.dataValue) || 0,
          itemStyle: { color: this.getInternetColor(index) }
        }))

        const total = data.reduce((sum, item) => sum + item.value, 0)
        console.log('initInternetDonutChart - 图表数据:', data, '总数:', total)

        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          grid: {
            left: '0%', // 图表左侧距离容器的距离
            // right: '40%', // 图表右侧距离容器的距离
            bottom: '0%', // 图表底部距离容器的距离
            containLabel: true // 是否包含标签文本在内
          },
          legend: {
            show: true,
            orient: 'horizontal', // 水平排列
            left: 'left', // 居中
            bottom: '0%', // 位置在底部
            itemWidth: 8,
            itemHeight: 8,
            icon: 'circle',
            itemGap: 10, // 图例项之间的间距
            textStyle: {
              fontSize: 11,
              color: '#666'
            },
            formatter: function(name) {
              const item = data.find(d => d.name === name)
              const dataItem = dataItems.find(d => d.dataName === name)
              const pctRate = dataItem ? dataItem.pctRate : 0
              return `${name}: ${item ? item.value : 0} ${pctRate}%`
            }
          },
          series: [
            {
              name: '互联网暴露面与资产风险',
              type: 'pie',
              radius: ['32%', '55%'],
              center: ['28%', '28%'], // 居中显示，稍微上移为图例留空间
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 5,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: false
              },
              labelLine: {
                show: false
              },
              data: data
            }
          ],
          graphic: {
            type: 'text',
            left: '16%', // 居中
            top: '18%', // 与圆环中心对齐
            style: {
              text:  `${total}\n\n全部漏洞`,
              textAlign: 'center',
              textVerticalAlign: 'middle',
              fill: '#333',
              fontSize: 13,
              fontWeight: 'bold'
            }
          }
        }

        chart.setOption(option)
        console.log('initInternetDonutChart - 图表配置设置完成')

        // 保存图表实例以便后续销毁
        if (!this.historyCharts) {
          this.historyCharts = {}
        }
        this.historyCharts[chartId] = chart
      }, 100) // 延迟100ms确保DOM渲染完成
    },

    // 初始化测试图表
    initTestCharts() {
      console.log('initTestCharts - 开始初始化测试图表')

      // 检查DOM是否存在
      console.log('initTestCharts - 检查DOM元素:')
      console.log('testHistoryDonutChart:', document.getElementById('testHistoryDonutChart'))
      console.log('testInternetDonutChart:', document.getElementById('testInternetDonutChart'))
      console.log('testWarningDonut:', document.getElementById('testWarningDonut'))
      console.log('testPenaltyDonut:', document.getElementById('testPenaltyDonut'))

      // 立即尝试初始化
      this.tryInitTestCharts()

      // 延迟重试机制
      setTimeout(() => {
        console.log('initTestCharts - 第一次重试')
        this.tryInitTestCharts()
      }, 100)

      setTimeout(() => {
        console.log('initTestCharts - 第二次重试')
        this.tryInitTestCharts()
      }, 500)

      setTimeout(() => {
        console.log('initTestCharts - 第三次重试')
        this.tryInitTestCharts()
      }, 1000)
    },

    // 尝试初始化测试图表
    tryInitTestCharts() {
      console.log('tryInitTestCharts - 尝试初始化图表')

      // 检查所有DOM元素
      const historyDom = document.getElementById('testHistoryDonutChart')
      const internetDom = document.getElementById('testInternetDonutChart')
      const warningDom = document.getElementById('testWarningDonut')
      const penaltyDom = document.getElementById('testPenaltyDonut')

      console.log('tryInitTestCharts - DOM检查结果:')
      console.log('historyDom:', historyDom)
      console.log('internetDom:', internetDom)
      console.log('warningDom:', warningDom)
      console.log('penaltyDom:', penaltyDom)

      // 初始化主图表
      if (historyDom) {
        this.initTestHistoryChart()
      }

      if (internetDom) {
        this.initTestInternetChart()
      }

      // 初始化小环状图
      if (warningDom || penaltyDom) {
        this.initTestMiniCharts()
      }
    },

    // 初始化测试历史安全事件图表
    initTestHistoryChart() {
      console.log('initTestHistoryChart - 开始查找DOM元素')
      console.log('initTestHistoryChart - $refs.testHistoryChart:', this.$refs.testHistoryChart)
      console.log('initTestHistoryChart - getElementById结果:', document.getElementById('testHistoryDonutChart'))
      console.log('initTestHistoryChart - querySelector结果:', document.querySelector('#testHistoryDonutChart'))
      console.log('initTestHistoryChart - 所有ID为testHistoryDonutChart的元素:', document.querySelectorAll('#testHistoryDonutChart'))

      // 优先使用$refs
      let chartDom = this.$refs.testHistoryChart
      if (!chartDom) {
        // 备用方案：使用getElementById
        chartDom = document.getElementById('testHistoryDonutChart')
      }
      if (!chartDom) {
        // 最后备用方案：使用querySelector
        chartDom = document.querySelector('#testHistoryDonutChart')
      }

      console.log('initTestHistoryChart - 最终找到的DOM元素:', chartDom)

      if (!chartDom) {
        console.error('initTestHistoryChart - 所有方法都未找到DOM元素')
        return
      }

      this.createHistoryChart(chartDom)
    },

    // 创建历史安全事件图表
    createHistoryChart(chartDom) {
      console.log('createHistoryChart - 开始创建图表，DOM元素:', chartDom)
      console.log('createHistoryChart - DOM元素尺寸:', {
        width: chartDom.offsetWidth,
        height: chartDom.offsetHeight,
        clientWidth: chartDom.clientWidth,
        clientHeight: chartDom.clientHeight
      })

      // 确保容器有尺寸
      if (chartDom.offsetWidth === 0 || chartDom.offsetHeight === 0) {
        console.warn('createHistoryChart - 容器尺寸为0，设置默认尺寸')
        chartDom.style.width = '200px'
        chartDom.style.height = '200px'
      }

      const chart = echarts.init(chartDom)
      console.log('createHistoryChart - ECharts实例创建成功:', chart)

      const data = [
        { name: '历史处罚情况', value: 2, itemStyle: { color: '#3481FF' } },
        { name: '安全检查', value: 2, itemStyle: { color: '#1ABB78' } },
        { name: '重大安全事件', value: 2, itemStyle: { color: '#FFC53D' } },
        { name: '事件响应能力', value: 2, itemStyle: { color: '#FF7A45' } },
        { name: '安全监测', value: 2, itemStyle: { color: '#722ED1' } }
      ]

      const total = data.reduce((sum, item) => sum + item.value, 0)

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} {d}%'
        },
        grid: {
          left: '10%', // 图表左侧距离容器的距离
          right: '40%', // 图表右侧距离容器的距离
          bottom: '10%', // 图表底部距离容器的距离
          containLabel: true // 是否包含标签文本在内
      },
        legend: {
            show: true,
            orient: 'horizontal', // 水平排列
            left: 'left', // 居中
            bottom: '0%', // 位置在底部
            itemWidth: 8,
            itemHeight: 8,
             icon: 'circle',
            itemGap: 15, // 图例项之间的间距
            textStyle: {
              fontSize: 11,
              color: '#666'
            },
            formatter: function(name) {
              const item = data.find(d => d.name === name)
              return `${name}: ${item ? item.value : 0}`
            }
          },
        series: [
          {
            name: '安全事件与威胁情报',
            type: 'pie',
            radius: ['70%', '90%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            // itemStyle: {
            //   borderRadius: 5,
            //   borderColor: '#fff',
            //   borderWidth: 2
            // },
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            data: data
          }
        ],
        graphic: {
          type: 'text',
          left: '50%',
          top: '50%',
          style: {
            text: `${total}\n总数`,
            textAlign: 'center',
            fill: '#333',
            fontSize: 14,
            fontWeight: 'bold'
          }
        }
      }

      console.log('createHistoryChart - 设置图表配置:', option)
      chart.setOption(option)

      // 强制重绘
      setTimeout(() => {
        chart.resize()
        console.log('createHistoryChart - 图表重绘完成')
      }, 100)

      console.log('createHistoryChart - 图表初始化完成')
      return chart
    },

    // 初始化测试互联网暴露面图表
    initTestInternetChart() {
      console.log('initTestInternetChart - 开始查找DOM元素')
      console.log('initTestInternetChart - $refs.testInternetChart:', this.$refs.testInternetChart)
      console.log('initTestInternetChart - getElementById结果:', document.getElementById('testInternetDonutChart'))
      console.log('initTestInternetChart - querySelector结果:', document.querySelector('#testInternetDonutChart'))
      console.log('initTestInternetChart - 所有ID为testInternetDonutChart的元素:', document.querySelectorAll('#testInternetDonutChart'))

      // 优先使用$refs
      let chartDom = this.$refs.testInternetChart
      if (!chartDom) {
        // 备用方案：使用getElementById
        chartDom = document.getElementById('testInternetDonutChart')
      }
      if (!chartDom) {
        // 最后备用方案：使用querySelector
        chartDom = document.querySelector('#testInternetDonutChart')
      }

      console.log('initTestInternetChart - 最终找到的DOM元素:', chartDom)

      if (!chartDom) {
        console.error('initTestInternetChart - 所有方法都未找到DOM元素')
        return
      }

      this.createInternetChart(chartDom)
    },

    // 创建互联网暴露面图表
    createInternetChart(chartDom) {
      console.log('createInternetChart - 开始创建图表，DOM元素:', chartDom)
      console.log('createInternetChart - DOM元素尺寸:', {
        width: chartDom.offsetWidth,
        height: chartDom.offsetHeight,
        clientWidth: chartDom.clientWidth,
        clientHeight: chartDom.clientHeight
      })

      // 确保容器有尺寸
      if (chartDom.offsetWidth === 0 || chartDom.offsetHeight === 0) {
        console.warn('createInternetChart - 容器尺寸为0，设置默认尺寸')
        chartDom.style.width = '200px'
        chartDom.style.height = '200px'
      }

      const chart = echarts.init(chartDom)
      console.log('createInternetChart - ECharts实例创建成功:', chart)

      const data = [
        { name: '企业数据泄露数量', value: 0, itemStyle: { color: '#FF6B6B' } },
        { name: '危险漏洞', value: 0, itemStyle: { color: '#4ECDC4' } },
        { name: '高危漏洞', value: 0, itemStyle: { color: '#45B7D1' } },
        { name: '中危漏洞', value: 0, itemStyle: { color: '#96CEB4' } },
        { name: '低危漏洞', value: 0, itemStyle: { color: '#FFEAA7' } },
        { name: '安全', value: 1, itemStyle: { color: '#DDA0DD' } }
      ]

      const total = data.reduce((sum, item) => sum + item.value, 0)

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          show: false
        },
        series: [
          {
            name: '互联网暴露面与资产风险',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 5,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            data: data
          }
        ],
        graphic: {
          type: 'text',
          left: '50%',
          top: '50%',
          style: {
            text: `${total}\n总数`,
            textAlign: 'center',
            fill: '#333',
            fontSize: 14,
            fontWeight: 'bold'
          }
        }
      }

      console.log('createInternetChart - 设置图表配置:', option)
      chart.setOption(option)

      // 强制重绘
      setTimeout(() => {
        chart.resize()
        console.log('createInternetChart - 图表重绘完成')
      }, 100)

      console.log('createInternetChart - 图表初始化完成')
      return chart
    },

    // 创建小环状图（警告和罚款）
    createMiniDonutChart(chartId, percentage, color) {
      const chartDom = document.getElementById(chartId)
      if (!chartDom) {
        console.error('createMiniDonutChart - 未找到DOM元素:', chartId)
        return
      }

      console.log('createMiniDonutChart - 开始创建小环状图:', chartId)
      console.log('createMiniDonutChart - DOM元素尺寸:', {
        width: chartDom.offsetWidth,
        height: chartDom.offsetHeight,
        clientWidth: chartDom.clientWidth,
        clientHeight: chartDom.clientHeight
      })

      // 确保容器有尺寸
      if (chartDom.offsetWidth === 0 || chartDom.offsetHeight === 0) {
        console.warn('createMiniDonutChart - 容器尺寸为0，设置默认尺寸')
        chartDom.style.width = '50px'
        chartDom.style.height = '50px'
      }

      const chart = echarts.init(chartDom)
      console.log('createMiniDonutChart - ECharts实例创建成功:', chart)

      const option = {
        series: [
          {
            type: 'pie',
            radius: ['60%', '80%'],
            center: ['50%', '50%'],
            startAngle: 90,
            data: [
              {
                value: percentage,
                itemStyle: { color: color }
              },
              {
                value: 100 - percentage,
                itemStyle: { color: '#E4E7ED' }
              }
            ],
            label: {
              show: false
            },
            labelLine: {
              show: false
            }
          }
        ],
        graphic: {
          type: 'text',
          left: '50%',
          top: '50%',
          style: {
            text: `${percentage}%`,
            textAlign: 'center',
            fill: color,
            fontSize: 10,
            fontWeight: 'bold'
          }
        }
      }

      console.log('createMiniDonutChart - 设置图表配置:', option)
      chart.setOption(option)

      // 强制重绘
      setTimeout(() => {
        chart.resize()
        console.log('createMiniDonutChart - 图表重绘完成:', chartId)
      }, 100)

      console.log('createMiniDonutChart - 小环状图初始化完成:', chartId)
      return chart
    },

    // 初始化测试小环状图
    initTestMiniCharts() {
      console.log('initTestMiniCharts - 开始初始化小环状图')

      // 立即尝试
      this.tryCreateMiniCharts()

      // 延迟重试
      setTimeout(() => {
        console.log('initTestMiniCharts - 第一次重试')
        this.tryCreateMiniCharts()
      }, 200)

      setTimeout(() => {
        console.log('initTestMiniCharts - 第二次重试')
        this.tryCreateMiniCharts()
      }, 500)
    },

    // 尝试创建小环状图
    tryCreateMiniCharts() {
      console.log('tryCreateMiniCharts - 尝试创建小环状图')

      const warningDom = document.getElementById('testWarningDonut')
      const penaltyDom = document.getElementById('testPenaltyDonut')

      console.log('tryCreateMiniCharts - DOM检查:')
      console.log('warningDom:', warningDom)
      console.log('penaltyDom:', penaltyDom)

      if (warningDom) {
        console.log('tryCreateMiniCharts - 创建警告占比图表')
        this.createMiniDonutChart('testWarningDonut', 0, '#67C23A')
      }

      if (penaltyDom) {
        console.log('tryCreateMiniCharts - 创建罚款占比图表')
        this.createMiniDonutChart('testPenaltyDonut', 0, '#F56C6C')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import './css/SecurityAssessment.scss';
</style>

