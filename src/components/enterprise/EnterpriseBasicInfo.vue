<template>
  <div class="detail-section enterprise-basic-section">
    <div class="section-header" @click="toggleExpanded">
      <h3 class="section-title">企业基础信息</h3>
      <div class="section-actions">
        <span class="count-badge">3</span>
        <i :class="expanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
      </div>
    </div>

    <div class="section-content" v-show="expanded">
      <el-row :gutter="20">
        <el-col
          v-for="field in basicInfoFields"
          :key="field.key"
          :span="field.span || 8"
        >
          <div class="info-item" v-if="shouldShowField(field, enterpriseData)">
            <span class="label">{{ field.label }}：</span>
            <span
              class="value"
              :style="field.key === 'statusLabel' ? { color: getStateColor(getFieldValue(field, enterpriseData)) } : {}"
            >
              {{ getFieldValue(field, enterpriseData) }}
            </span>
          </div>
        </el-col>
      </el-row>

      <!-- 企业法人信息模块 -->
      <person-info-section
        title="企业法人信息"
        :person-data="getLegalPersonData(enterpriseData)"
        :expanded="legalPersonExpanded"
        :show-extra-fields="false"
        :show-certificate-photos="true"
        @toggle="toggleLegalPerson"
      />

      <!-- 企业负责人信息模块 -->
      <person-info-section
        title="企业责任人信息"
        :person-data="getResponsiblePersonData(enterpriseData)"
        :expanded="responsiblePersonExpanded"
        :show-extra-fields="true"
        :show-certificate-photos="true"
        @toggle="toggleResponsiblePerson"
      />
    </div>
  </div>
</template>

<script>
import PersonInfoSection from './PersonInfoSection.vue'

export default {
  name: 'EnterpriseBasicInfo',
  components: {
    PersonInfoSection
  },
  props: {
    enterpriseData: {
      type: Object,
      default: () => ({})
    },
    legalPerson: {
      type: Object,
      default: () => ({})
    },
    responsiblePerson: {
      type: Object,
      default: () => ({})
    },
    expanded: {
      type: Boolean,
      default: true
    },
    legalPersonExpanded: {
      type: Boolean,
      default: true
    },
    responsiblePersonExpanded: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // 企业基础信息字段配置 - 与API字段一一对应，所有字段都显示
      basicInfoFields: [
        {
          key: 'createTime',
          label: '创建时间',
          span: 8,
          required: true
        },
        {
          key: 'buzType',
          label: '业务类型',
          span: 8,
          required: true
        },
        {
          key: 'areaCodeLabel',
          label: '所属区域',
          span: 8,
          required: true
        },
        {
          key: 'handlerName',
          label: '负责人',
          span: 8,
          required: true
        },
        {
          key: 'handlerPhoneNumber',
          label: '负责人电话',
          span: 8,
          required: true
        },
        {
          key: 'statusLabel',
          label: '企业状态',
          span: 8,
          required: true
        },
        {
          key: 'staffNumRange',
          label: '人员规模',
          span: 8,
          required: true
        },
        {
          key: 'handlerEmail',
          label: '负责人邮箱',
          span: 8,
          required: true
        },
        {
          key: 'handlerPermanentAddress',
          label: '负责人常住地址',
          span: 24,
          required: true
        }
      ]
    }
  },
  methods: {
    // 获取字段值
    getFieldValue(field, data) {
      const value = data[field.key]

      // 处理特殊字段
      if (field.key === 'statusLabel' && !value && data.status) {
        return data.status === '1' ? '有效' : '无效'
      }

      // 处理时间格式
      if (field.key === 'createTime' && value) {
        return value.split(' ')[0] // 只显示日期部分
      }

      // 所有字段都显示，无数据时用"-"填充
      return value || '-'
    },

    // 判断是否显示字段
    shouldShowField(field, data) {
      // 所有字段都显示
      return true
    },

    // 获取法人信息数据
    getLegalPersonData(enterpriseData) {
      return {
        name: enterpriseData.corporationName,
        phoneNumber: enterpriseData.corporationPhoneNumber,
        certificateType: enterpriseData.corporationCertificateTypeLabel,
        certificateCode: enterpriseData.corporationCertificateCode,
        certificateValidityPeriod: enterpriseData.corporationCertificateValidityPeriod,
        certificateFront: enterpriseData.corporationCertificateFront,
        certificateBack: enterpriseData.corporationCertificateBack,
        certificateHand: enterpriseData.corporationCertificateHand,
        certificateCodeValid: enterpriseData.corporationCertificateCodeValid
      }
    },

    // 获取责任人信息数据
    getResponsiblePersonData(enterpriseData) {
      return {
        name: enterpriseData.handlerName,
        phoneNumber: enterpriseData.handlerPhoneNumber,
        officePhoneNumber: enterpriseData.handlerOfficePhoneNumber,
        email: enterpriseData.handlerEmail,
        permanentAddress: enterpriseData.handlerPermanentAddress,
        certificateType: enterpriseData.handlerCertificateTypeLabel,
        certificateCode: enterpriseData.handlerCertificateCode,
        certificateValidityPeriod: enterpriseData.handlerCertificateValidityPeriod,
        certificateFront: enterpriseData.handlerCertificateFront,
        certificateBack: enterpriseData.handlerCertificateBack,
        certificateHand: enterpriseData.handlerCertificateHand,
        certificateCodeValid: enterpriseData.handlerCertificateCodeValid
      }
    },

    toggleExpanded() {
      this.$emit('toggle-expanded')
    },

    toggleLegalPerson() {
      this.$emit('toggle-legal-person')
    },

    toggleResponsiblePerson() {
      this.$emit('toggle-responsible-person')
    },

    // 获取状态颜色
    getStateColor(state) {
      const stateColors = {
        '有效': '#4584FF',
        '无效': '#F56C6C',
      }
      return stateColors[state] || '#909399'
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-section {
  margin-bottom: 30px;

  &.enterprise-basic-section {
    border: 1px solid #ebeef5;
    border-radius: 4px;
    overflow: hidden;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      background-color: #f5f7fa;
      cursor: pointer;
      border-bottom: none;

      &:hover {
        background-color: #eef1f6;
      }

      .section-title {
        margin: 0;
        padding: 0;
        border: none;
        font-size: 18px;
        font-weight: 600;
      }

      .section-actions {
        display: flex;
        align-items: center;

        .count-badge {
          display: inline-block;
          padding: 2px 8px;
          margin-right: 10px;
          background-color: #409EFF;
          color: #fff;
          border-radius: 10px;
          font-size: 12px;
        }

        i {
          font-size: 16px;
          color: #909399;
        }
      }
    }

    .section-content {
      padding: 20px;
      background-color: #fff;
    }
  }

  .info-item {
    margin-bottom: 15px;

    .label {
      color: #333; // 加深标签颜色
      margin-right: 5px;
      font-size: 14px; // 调整字体大小
      font-weight: 500; // 增加字重
    }

    .value {
      color: #333; // 加深值的颜色
      font-size: 14px; // 调整字体大小
      word-break: break-all;
    }
  }
}
</style>
