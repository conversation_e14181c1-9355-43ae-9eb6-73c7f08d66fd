<template>
  <div class="enterprise-overview">
    <el-row :gutter="20">
      <!-- 左侧：企业照片 -->
      <el-col :span="4" >
        <div class="overview-left">
          <div class="enterprise-photo" :style="getEnterprisePhotoStyle()">
            <img v-if="enterpriseData.photo" :src="enterpriseData.photo" alt="企业照片" />
            <div v-else class="enterprise-name-display">
              <div class="name-line-1">{{ getEnterpriseNameDisplay().line1 }}</div>
              <div class="name-line-2">{{ getEnterpriseNameDisplay().line2 }}</div>
            </div>
          </div>
        </div>
      </el-col>

      <!-- 中间：企业信息 -->
      <el-col :span="16" :xs="15" :sm="14" >
        <div class="overview-middle">
          <!-- 第一行：企业名称和统一社会信用代码 -->
          <div class="enterprise-header">
            <h3 class="enterprise-name">{{ enterpriseData.name }}</h3>
            <div class="credit-code">
              <span class="label">统一社会信用代码：</span>
              <span class="value">{{ enterpriseData.creditCode }}</span>
            </div>
          </div>

          <!-- 第二行：企业标签 -->
          <div class="enterprise-tags">
            <el-tag
              v-for="(tag, index) in matchedRiskTags"
              :key="index"
              plain
              size="medium"
              :style="{ color: tag.tagColor, borderColor: tag.tagColor, backgroundColor: getLightenedColorByHex(tag.tagColor) }"
              class="risk-tag"
            >
              {{ tag.label }}
            </el-tag>
          </div>

          <!-- 其他企业信息 -->
          <div class="enterprise-info">
            <el-row :gutter="20">
              <el-col
                v-for="field in overviewFields"
                :key="field.key"
                :span="field.span || 12"
              >
                <div class="info-item" v-if="shouldShowField(field, enterpriseData)">
                  <span class="label">{{ field.label }}：</span>
                  <span
                    class="value"
                    :style="field.key === 'statusLabel' ? { color: getStateColor(getFieldValue(field, enterpriseData)) } : {}"
                  >
                    {{ getFieldValue(field, enterpriseData) }}
                  </span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-col>

      <!-- 右侧：营业执照照片 -->
      <el-col :span="4"  >
        <div class="overview-right">
          <div class="license-photo">
            <div class="photo-title">营业执照</div>
            <div class="photo-container" >
              <!-- <img v-if="enterpriseData.licensePic" :src="enterpriseData.licensePic" alt="营业执照" /> -->
               <!-- <image-viewer v-if="enterpriseData.licensePic"  :src="enterpriseData.licensePic"  /> -->
              <image-preview v-if="enterpriseData.licensePic" :maxHeight="130" :src="enterpriseData.licensePic"  />
              <i v-else class="el-icon-picture-outline"></i>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { lightenColor } from '@/utils/index'
import ImageViewer from '@/components/ImageViewer'
export default {
  name: 'EnterpriseOverview',
  props: {
    enterpriseData: {
      type: Object,
      default: () => ({})
    },
    riskLevelOptions: {
      type: Array,
      default: () => []
    }
  },
   components: { ImageViewer },
  data() {
    return {
      // 企业概览字段配置 - 与API字段一一对应，所有字段都显示，每行三个字段
      overviewFields: [
        {
          key: 'property',
          label: '单位性质',
          span: 8, // 改为8，每行三个字段
          required: true
        },
        {
          key: 'publicCode',
          label: '公安备案号',
          span: 8, // 改为8，每行三个字段
          required: true
        },
        {
          key: 'staffNumRange',
          label: '人员规模',
          span: 8, // 改为8，每行三个字段
          required: true
        },
        {
          key: 'corporationName',
          label: '法定代表人',
          span: 8, // 改为8，每行三个字段
          required: true
        },
        {
          key: 'corporationPhoneNumber',
          label: '联系电话',
          span: 8, // 改为8，每行三个字段
          required: true
        },
        {
          key: 'statusLabel',
          label: '企业状态',
          span: 8, // 改为8，每行三个字段
          required: true
        },
        {
          key: 'regLocation',
          label: '注册地址',
          span: 24, // 地址字段保持24，独占一行
          required: true
        },
        {
          key: 'realLocation',
          label: '实际办公地址',
          span: 24, // 地址字段保持24，独占一行
          required: true
        }
      ]
    }
  },
  computed: {
    // 获取匹配的风险标签
    matchedRiskTags() {
      return this.matchRiskTags(this.enterpriseData)
    }
  },
  methods: {
    // 获取字段值
    getFieldValue(field, data) {
      const value = data[field.key]

      // 处理特殊字段
      if (field.key === 'statusLabel' && !value && data.status) {
        return data.status === '1' ? '有效' : '无效'
      }

      // 所有字段都显示，无数据时用"-"填充
      return value || '-'
    },

    // 判断是否显示字段
    shouldShowField(field, data) {
      // 所有字段都显示
      return true
    },

    /**
     * 根据传入的数据对象匹配标签
     * @param {Object} dataObj - 包含各种数量字段的数据对象
     * @param {Object} riskLevelOptions - 风险等级选项对象
     * @returns {Array} 返回匹配的标签数组，包含 label、value、tagColor
     */
    matchRiskTags(dataObj) {
      const matchedTags = []

      // 使用API返回的字段进行标签匹配
      const tagMappings = [
        { apiField: 'netBarNum', label: '网吧单位', tagColor: '#FB6B2A' },
        { apiField: 'screenNum', label: '电子屏单位', tagColor: '#60B8FF' },
        { apiField: 'levelProjectNum', label: '等保备案单位', tagColor: '#44C991' },
        { apiField: 'websiteNum', label: '网站备案单位', tagColor: '#F5BC6C' },
        { apiField: 'operatorNum', label: '运营商单位', tagColor: '#24A8BB' },
        { apiField: 'wifiNum', label: '非经营单位', tagColor: '#D789D4' },
        { apiField: 'appNum', label: 'APP备案单位', tagColor: '#95ABD4' },
        { apiField: 'appletNum', label: '小程序备案单位', tagColor: '#8E44AD' }
      ]

      // 遍历标签映射配置
      tagMappings.forEach(mapping => {
        const value = dataObj[mapping.apiField]

        // 只处理大于0的数值
        if (value && value > 0) {
          matchedTags.push({
            label: mapping.label,
            value: value,
            tagColor: mapping.tagColor
          })
        }
      })

      return matchedTags
    },

    // 获取风险等级颜色
    getRiskLevelColor(level) {
      const option = this.riskLevelOptions.find(opt => opt.value === level)
      return option ? option.tagColor : '#909399'
    },

    // 获取风险等级标签
    getRiskLevelLabel(level) {
      const option = this.riskLevelOptions.find(opt => opt.value === level)
      return option ? option.label : '未知'
    },

    // 获取浅色背景
    getLightenedColor(level) {
      const color = this.getRiskLevelColor(level)
      return lightenColor(color, 0.9)
    },

    // 根据十六进制颜色获取浅色背景
    getLightenedColorByHex(hexColor) {
      return lightenColor(hexColor, 85)
    },

    // 获取状态颜色
    getStateColor(state) {
      const stateColors = {
        '有效': '#4584FF',
        '无效': '#F56C6C',
      }
      return stateColors[state] || '#909399'
    },

    // 获取企业名称显示内容
    getEnterpriseNameDisplay() {
      const name = this.enterpriseData.name || '企业'
      const chars = name.substring(0, 3) // 取前三个字

      if (chars.length >= 3) {
        return {
          line1: chars.substring(0, 2), // 前两个字
          line2: chars.substring(2, 3)  // 第三个字
        }
      } else if (chars.length === 2) {
        return {
          line1: chars,
          line2: ''
        }
      } else {
        return {
          line1: chars,
          line2: ''
        }
      }
    },

    // 获取企业照片样式
    getEnterprisePhotoStyle() {
      if (this.enterpriseData.photo) {
        return {} // 有照片时不需要特殊样式
      }

      // 生成随机背景色，调深一点
      const colors = [
        '#90CAF9', // 更深的蓝
        '#CE93D8', // 更深的紫
        '#A5D6A7', // 更深的绿
        '#FFCC80', // 更深的橙
        '#F48FB1', // 更深的粉
        '#C5E1A5', // 更深的青绿
        '#80CBC4', // 更深的青
        '#FFF176', // 更深的黄
        '#BCAAA4', // 更深的棕
        '#DCE775'  // 更深的黄绿
      ]

      // 根据企业名称生成固定的颜色索引
      const name = this.enterpriseData.name || 'default'
      let hash = 0
      for (let i = 0; i < name.length; i++) {
        hash = name.charCodeAt(i) + ((hash << 5) - hash)
      }
      const colorIndex = Math.abs(hash) % colors.length

      return {
        backgroundColor: colors[colorIndex]
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.enterprise-overview {
  padding: 20px;
  margin-bottom: 5px;
  border-radius: 4px;

  .overview-left {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .enterprise-photo {
      width: 140px; 
      height: 140px; // 固定高度140px，正方形
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      overflow: hidden;

      img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
      }
    }
     .enterprise-name-display {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        text-align: center;
      }
      .name-line-1 {
          font-size: 36px; // 第一行字体36像素
          line-height: 1.2;
          margin-bottom: 2px;
        }

        .name-line-2 {
          font-size: 24px; // 第二行字体24像素
          line-height: 1.2;
        }
    @media (max-width: 1050px) {
      .enterprise-photo {
          width: 100px; 
          height: 100px;
        }
    }
    @media (max-width: 768px) {
      .enterprise-photo {
          width: 60px; 
          height: 60px;
        }
        .name-line-1 {
          font-size: 20px; // 第一行字体36像素
        }

        .name-line-2 {
          font-size: 16px; // 第二行字体24像素
        }
    }
  }

  .overview-middle {
    .enterprise-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .enterprise-name {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        margin-right: 20px;
      }

      .credit-code {
        font-size: 16px;
        color: #4584FF;
      }
    }

    .enterprise-tags {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 15px;
      padding-bottom: 15px;
      border-bottom: 1px solid #ebeef5;

      .risk-tag {
        margin-right: 8px;
        margin-bottom: 5px;
      }
    }

    .enterprise-info {
      .info-item {
        margin-bottom: 10px;

        .label {
          color: #333; // 加深标签颜色
          margin-right: 5px;
          font-size: 14px; // 调整字体大小
          font-weight: 500; // 增加字重
        }

        .value {
          color: #333; // 加深值的颜色
          font-size: 14px; // 调整字体大小
          word-break: break-all;
        }
      }
    }
  }

  .overview-right {
    height: 100%;

    .license-photo {
      height: 100%;
      display: flex;
      flex-direction: column;
      background-color: #fff;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      overflow: hidden;

      .photo-title {
        padding: 8px;
        text-align: center;
        font-size: 14px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #ebeef5;
      }

      .photo-container {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px;
        position: relative;
        width: 100%;

        // 调整宽高比，让营业执照卡片更小
        &:before {
          content: "";
          display: block;
          padding-top: 75%; // 4:3比例，比16:9更小
        }

        img {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          object-fit: contain;
          padding: 10px;
        }

        i {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 60px;
          color: #909399;
        }
      }
    }
  }


}



</style>
