<template>
  <div class="detail-section record-cards-section">
    <el-row :gutter="20">
      <!-- 等保备案卡片 -->
      <el-col :span="6">
        <div class="record-card">
          <div class="card-title">等保备案</div>
          <div class="card-content">
            <div class="card-item">
              <span class="item-label">当前备案等级：</span>
              <span class="item-value">{{ recordCards.securityRecord.level }}</span>
            </div>
            <div class="card-item">
              <span class="item-label">有效期：</span>
              <span class="item-value">{{ recordCards.securityRecord.expiryDate }}</span>
            </div>
            <div class="card-item">
              <span class="item-label">测评结果：</span>
              <span class="item-value" :style="{ color: getStatusColor(recordCards.securityRecord.result) }">
                {{ recordCards.securityRecord.result }}
              </span>
            </div>
          </div>
        </div>
      </el-col>

      <!-- APP备案卡片 -->
      <el-col :span="6">
        <div class="record-card">
          <div class="card-title">APP备案</div>
          <div class="card-content">
            <div class="card-item">
              <span class="item-label">备案号：</span>
              <span class="item-value">{{ recordCards.appRecord.recordNumber }}</span>
            </div>
            <div class="card-item">
              <span class="item-label">运营状态：</span>
              <span class="item-value" :style="{ color: getStatusColor(recordCards.appRecord.operationStatus) }">
                {{ recordCards.appRecord.operationStatus }}
              </span>
            </div>
            <div class="card-item">
              <span class="item-label">更新时间：</span>
              <span class="item-value">{{ recordCards.appRecord.updateTime }}</span>
            </div>
          </div>
        </div>
      </el-col>

      <!-- 小程序备案卡片 -->
      <el-col :span="6">
        <div class="record-card">
          <div class="card-title">小程序备案</div>
          <div class="card-content">
            <div class="card-item">
              <span class="item-label">备案号：</span>
              <span class="item-value">{{ recordCards.miniProgramRecord.recordNumber }}</span>
            </div>
            <div class="card-item">
              <span class="item-label">运营状态：</span>
              <span class="item-value" :style="{ color: getStatusColor(recordCards.miniProgramRecord.operationStatus) }">
                {{ recordCards.miniProgramRecord.operationStatus }}
              </span>
            </div>
            <div class="card-item">
              <span class="item-label">更新时间：</span>
              <span class="item-value">{{ recordCards.miniProgramRecord.updateTime }}</span>
            </div>
          </div>
        </div>
      </el-col>

      <!-- 数字资产备案卡片 -->
      <el-col :span="6">
        <div class="record-card">
          <div class="card-title">数字资产备案</div>
          <div class="card-content">
            <div class="card-item">
              <span class="item-label">IP地址段：</span>
              <span class="item-value">{{ recordCards.digitalAssetRecord.ipRange }}</span>
            </div>
            <div class="card-item">
              <span class="item-label">IDC机房位置：</span>
              <span class="item-value">{{ recordCards.digitalAssetRecord.idcLocation }}</span>
            </div>
            <div class="card-item">
              <span class="item-label">带宽用户量：</span>
              <span class="item-value">{{ recordCards.digitalAssetRecord.bandwidthUsers }}</span>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'RecordCards',
  props: {
    recordCards: {
      type: Object,
      default: () => ({
        securityRecord: {},
        appRecord: {},
        miniProgramRecord: {},
        digitalAssetRecord: {}
      })
    }
  },
  methods: {
    // 获取状态颜色
    getStatusColor(status) {
      const statusColors = {
        '通过': '#10D205',
        '正常运营': '#10D205',
        '未通过': '#F56C6C',
        '停止运营': '#F56C6C',
        '待审核': '#E6A23C'
      }
      return statusColors[status] || '#909399'
    }
  }
}
</script>

<style lang="scss" scoped>
.record-cards-section {
  margin-bottom: 20px;
display: none;
  .record-card {
    height: 100%;
    background-color: #F6F7FB;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .card-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #EBEEF5;
    }

    .card-content {
      .card-item {
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .item-label {
          color: #606266;
          font-size: 14px;
          margin-right: 5px;
        }

        .item-value {
          color: #303133;
          font-weight: 500;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
