<template>
  <div class="detail-section compliance-records-section">
    <!-- <h3 class="section-title">历史合规记录</h3> -->
    <el-row :gutter="20">
      <el-col :span="8" :xl="8" :lg="8" :md="8" :sm="24" :xs="24" v-for="record in complianceRecords" :key="record.id" style="margin: 5px 0;">
        <div class="compliance-card">

          <el-row class="card-content">
            <el-col :span="8" class="card-left">
                <el-tooltip :content="record.type" placement="top" :disabled="record.type.length <= 20"> 
                   <div class="record-type text-ellipsis">{{ record.type }}</div>
                </el-tooltip>
             
              <div class="record-stats">
                <span class="total-count">{{ record.totalCount }}</span>
                <el-tag size="mini" type="info" class="recent-period">{{ record.recentPeriod }}</el-tag>
              </div>
            </el-col>
            <el-col :span="16" class="card-right">
              <!-- 第一个卡片：立即现场检查记录 -->
              <template v-if="record.id === 1">
                <div class="action-time"><span>检查时间：</span><span>{{ record.lastCheckTime }}</span></div>
                <div class="problem-stats">
                  <span class="problem-count">问题数量：{{ record.problemCount }}</span>
                  <div class="resolution-status">
                    <span class="status-label">整改情况：</span>
                    <span>
                      <el-tag v-if="record.unresolved > 0" size="mini" type="danger" class="status-tag">
                      未整改 {{ record.unresolved }}
                      </el-tag>
                      <el-tag v-if="record.resolved > 0" size="mini" type="success" class="status-tag">
                        已整改 {{ record.resolved }}
                      </el-tag>
                    </span>
                  </div>
                </div>
              </template>

              <!-- 第二个和第三个卡片：行政处罚记录和行政处置 -->
              <template v-else>
                <div class="action-time"><span>{{ record.timeLabel }}：</span><span>{{ record.lastTime }}</span></div>
                <div class="action-details">
                  <div class="action-reason">
                    <span class="reason-label">{{ record.reasonLabel }}：</span>
                    <el-tooltip :content="record.reason" placement="top" :disabled="record.reason.length <= 20">
                      <span class="reason-text multiline-ellipsis-2">{{ record.reason }}</span>
                    </el-tooltip>
                  </div>
                  <div class="action-result">
                    <span class="result-label">{{ record.resultLabel }}：</span>
                    <el-tooltip :content="record.result" placement="top" :disabled="record.result.length <= 20">
                      <span class="result-text multiline-ellipsis-2">{{ record.result }}</span>
                    </el-tooltip>
                  </div>
                </div>
              </template>
            </el-col>
          </el-row>

        </div>
      </el-col>
    </el-row>

    <!-- 历史合规记录详细列表 -->
    <div class="compliance-detail-list">
      <el-table
        :data="paginatedDetailList"
        style="width: 100%"
        border
        :header-cell-style="{ background: '#EBF1FF', color: '#303133' }"
        :row-style="{ height: 'auto' }"
      >
        <el-table-column
          prop="decisionNumber"
          label="行政处罚决定书编号"
          width="200"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="decisionDate"
          label="处罚决定时间"
          width="150"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="caseDescription"
          label="简要案情"
          min-width="400"
        >
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.caseDescription" placement="top" :disabled="scope.row.caseDescription.length <= 50">
              <div class="case-description">
                {{ scope.row.caseDescription }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container" v-if="complianceDetailList.length > pageSize">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="prev, pager, next"
          :total="complianceDetailList.length"
          background
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { getComplianceStatistics, getPunishList } from '@/api/enterprise'

export default {
  name: 'ComplianceRecords',
  props: {
    enterpriseData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      complianceRecords: [],
      complianceDetailList: [],
      isLoading: false,
      currentPage: 1,
      pageSize: 5
    }
  },
  computed: {
    // 分页后的详细列表
    paginatedDetailList() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.complianceDetailList.slice(start, end)
    }
  },
  mounted() {
    console.log('ComplianceRecords - mounted时的enterpriseData:', this.enterpriseData)
    this.loadComplianceData()
    
  },
  watch: {
    // 监听enterpriseData变化
    enterpriseData: {
      handler(newVal, oldVal) {
        console.log('ComplianceRecords - enterpriseData变化:', { newVal, oldVal })
        if (newVal && newVal.creditCode && (!oldVal || !oldVal.creditCode || newVal.creditCode !== oldVal.creditCode)) {
          console.log('ComplianceRecords - 检测到新的creditCode，重新加载数据:', newVal)
          this.loadComplianceData()
        }
      },
      deep: true,
      immediate: false
    }
  },
  methods: {
    // 加载合规数据
    async loadComplianceData() {
      console.log('ComplianceRecords - loadComplianceData被调用')
      console.log('ComplianceRecords - 当前enterpriseData:', this.enterpriseData)
      console.log('ComplianceRecords - enterpriseData类型:', typeof this.enterpriseData)
      console.log('ComplianceRecords - enterpriseData是否为null:', this.enterpriseData === null)
      console.log('ComplianceRecords - enterpriseData是否为undefined:', this.enterpriseData === undefined)

      if (this.enterpriseData) {
        console.log('ComplianceRecords - enterpriseData的所有键:', Object.keys(this.enterpriseData))
        console.log('ComplianceRecords - creditCode值:', this.enterpriseData.creditCode)
        console.log('ComplianceRecords - creditCode类型:', typeof this.enterpriseData.creditCode)
      }

      // 使用真实的creditCode
      if (!this.enterpriseData?.creditCode) {
        console.warn('ComplianceRecords - 缺少creditCode，无法加载数据')
        // this.loadDefaultData()
        return
      }
      const creditCode = this.enterpriseData.creditCode
      console.log('ComplianceRecords - 使用的creditCode:', creditCode)

      console.log('ComplianceRecords - 开始加载合规数据，creditCode:', creditCode)
      this.isLoading = true

      try {
        console.log('ComplianceRecords - 准备并发请求两个接口')
        // 并发请求两个接口
        let [statisticsResponse, punishListResponse] = await Promise.all([
          getComplianceStatistics(creditCode),
          getPunishList({
            pageNum: 1,
            pageSize: 10,
            creditCode: creditCode
          })
        ])
        
        console.log('ComplianceRecords - 统计数据响应:', statisticsResponse)
        console.log('ComplianceRecords - 处罚列表响应:', punishListResponse)

        // 处理统计数据
        if (statisticsResponse.code === 200 && statisticsResponse.data) {
          this.processStatisticsData(statisticsResponse.data)
          
          this.$nextTick(() => {
       const firstElement = document.querySelector('.compliance-card');
          if (firstElement) {
          firstElement.classList.add('compliance-card-first');
        }
    })
          
         
        } else {
          console.error('ComplianceRecords - 获取统计数据失败:', statisticsResponse.msg)
          // 移除默认数据加载，确保只使用API数据
          this.complianceRecords = []
        }

        // 处理处罚列表数据
        if (punishListResponse.code === 200 && punishListResponse.rows) {
          this.processPunishListData(punishListResponse.rows)
        } else {
          console.error('ComplianceRecords - 获取处罚列表失败:', punishListResponse.msg)
          // 移除默认数据加载，确保只使用API数据
          this.complianceDetailList = []
          this.totalDetailRecords = 0
        }

      } catch (error) {
        console.error('ComplianceRecords - 加载合规数据出错:', error)
        // 移除默认数据加载，确保只使用API数据
        this.complianceRecords = []
        this.complianceDetailList = []
        this.totalDetailRecords = 0
      } finally {
        this.isLoading = false
      }
    },

    // 处理统计数据
    processStatisticsData(data) {
      console.log('data===========', data)
      this.complianceRecords = [
        {
          id: 1,
          type: '立即现场检查记录',
          totalCount: data.inspection?.total || 0,
          recentPeriod: `近半年${data.inspection?.timeSlotTotal || 0}次`,
          lastCheckTime: data.inspection?.lastInspectionTime || '-',
         problemCount: data.inspection?.questionNum || 0,
          unresolved: data.inspection?.unCorrectionNum || 0,
          resolved: data.inspection?.correctionNum || 0
        },
        {
          id: 2,
          type: '累计行政处罚记录',
          totalCount: data.punish?.total || 0,
          recentPeriod: `近半年${data.punish?.timeSlotTotal || 0}次`,
          lastTime: data.punish?.lastPunishDate || '-',
          reason: data.punish?.problem || '-',
          result: data.punish?.punishResult || '-',
          timeLabel: '行政处罚时间',
          reasonLabel: '事由',
          resultLabel: '处罚结果'
        },
        {
          id: 3,
          type: '累计行政处置记录',
          totalCount: data.disposal?.total || 0,
          recentPeriod: `近半年${data.disposal?.timeSlotTotal || 0}次`,
          lastTime: data.disposal?.lastDisposalDate || '-',
          reason: data.disposal?.problem || '-',
          result: data.disposal?.disposalResult || '-',
          timeLabel: '行政处置时间',
          reasonLabel: '简要案情',
          resultLabel: '处置结果'
        }
      ]
    },

    // 处理处罚列表数据
    processPunishListData(rows) {
      this.complianceDetailList = rows.map(item => ({
        id: item.punishId,
        decisionNumber: item.punishNum,
        decisionDate: item.punishDate,
        caseDescription: item.problem,
        punishStatus: item.punishStatus,
        remark: item.remark
      }))
    },

    // 加载默认数据
    loadDefaultData() {
      this.loadDefaultStatistics()
      this.loadDefaultPunishList()
    },

    // 加载默认统计数据
    loadDefaultStatistics() {
      this.complianceRecords = [
        {
          id: 1,
          type: '立即现场检查记录',
          totalCount: 12,
          recentPeriod: '近半年2次',
          lastCheckTime: '2023-10-15',
          problemCount: 8,
          unresolved: 2,
          resolved: 6
        },
        {
          id: 2,
          type: '累计行政处罚记录',
          totalCount: 8,
          recentPeriod: '近半年1次',
          lastTime: '2023-11-20',
          reason: '在网络发布恶意言论',
          result: '罚款2000，停业整改',
          timeLabel: '行政处罚时间',
          reasonLabel: '事由',
          resultLabel: '处罚结果'
        },
        {
          id: 3,
          type: '累计行政处置记录',
          totalCount: 5,
          recentPeriod: '近半年0次',
          lastTime: '2023-08-10',
          reason: '网络安全防护措施不到位',
          result: '警告并责令整改',
          timeLabel: '行政处置时间',
          reasonLabel: '简要案情',
          resultLabel: '处置结果'
        }
      ]
    },

    // 加载默认处罚列表
    loadDefaultPunishList() {
      this.complianceDetailList = [
        {
          id: 1,
          decisionNumber: '京网安罚字2018第001号',
          decisionDate: '2018-07-15',
          caseDescription: '未履行用户隐私保护责任，导致用户数据泄露。'
        },
        {
          id: 2,
          decisionNumber: '京网安罚字2019第002号',
          decisionDate: '2019-03-20',
          caseDescription: '网络安全防护措施不到位，存在安全隐患。'
        },
        {
          id: 3,
          decisionNumber: '京网安罚字2020第003号',
          decisionDate: '2020-06-10',
          caseDescription: '违反网络实名制规定，未对用户进行实名认证。'
        },
        {
          id: 4,
          decisionNumber: '京网安罚字2021第004号',
          decisionDate: '2021-09-15',
          caseDescription: '网络内容审核不严，传播违法信息。'
        },
        {
          id: 5,
          decisionNumber: '京网安罚字2022第005号',
          decisionDate: '2022-12-05',
          caseDescription: '数据安全管理制度不完善，存在数据泄露风险。'
        },
        {
          id: 6,
          decisionNumber: '京网安罚字2023第006号',
          decisionDate: '2023-04-18',
          caseDescription: '未按规定报告网络安全事件。'
        }
      ]
      // 重置分页
      this.currentPage = 1
    },

    // 分页处理
    handleCurrentChange(page) {
      this.currentPage = page
    }
  }
}
</script>

<style lang="scss" scoped>
.compliance-records-section {
  .section-title {
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    border-bottom: 1px solid #ebeef5;
  }

  .compliance-card {
    height: 100%;
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    border: 1px solid #ebeef5;

    .card-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      height: 100%;
      .card-left {
        margin-right: 20px;
        .record-type {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 15px;
          line-height: 1.4;
        }

        .record-stats {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .total-count {
            font-size: 28px;
            font-weight: bold;
            color: #409EFF;
          }

          .recent-period {
            background-color: #f0f9ff;
            color: #409EFF;
            border-color: #409EFF;
            font-weight: 500;
            padding: 6px 3px !important;
          }
        }
      }

      .card-right {
      //   flex: 1;
      //  width: 50%;
        // display: flex;
        // flex-direction: column;
        // justify-content: space-between;
        height: 100%;

        .action-time {
          display: flex;
           flex-wrap: wrap; 
          font-size: 14px;
          margin-bottom: 15px;
          span {
             word-break: keep-all;
             white-space: nowrap;
          }
        }

        .problem-stats {
          .problem-count {
            font-size: 14px;
            color: #303133;
            margin-bottom: 10px;
            display: block;
          }

          .resolution-status {
            display: flex;
            align-items: center;
            flex-wrap: wrap;

            .status-label {
              font-size: 14px;
              margin-right: 8px;
              white-space: nowrap;
            }

            .status-tag {
           
            
              &.el-tag--danger {
                background-color: #fef0f0;
                border-color: #fbc4c4;
                color: #f56c6c;
                padding: 3px 5px !important;
                margin-right: 8px;
              }

              &.el-tag--success {
                background-color: rgb(240, 249, 235);
                border-color: rgb(194, 231, 176);
                color: #17B26D;
                padding: 3px 5px !important;
                margin-left: 8px;
              }
            }
          }
        }
      .text-overflow {
          word-break: break-all;
          white-space: pre-wrap;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          line-clamp: 2; /* 限制行数为2 */
          overflow: hidden;
        }
        // 行政处罚和行政处置样式
       

        .action-details {
          .action-reason {
            font-size: 14px;
            color: #303133;
            margin-bottom: 12px;
            line-height: 1.4;
            display: flex;
            align-items: flex-start;
            .reason-label {
              flex-shrink: 0;
            }

            .reason-text {
              flex: 1;
            }
          }

          .action-result {
            font-size: 14px;
            line-height: 1.4;
            display: flex;
            align-items: flex-start;

            .result-label {
               color: #E6A23C;
                font-weight: 500;
              flex-shrink: 0;
            }

            .result-text {
              font-weight: 400;
              flex: 1;
            }
          }
        }
      }
    }

    &:hover {
      box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
      transition: all 0.3s ease;
    }
  }
  .compliance-card-first {
    padding: 20px 0 20px 20px;
  }
 
  // 表格中的案情描述样式
  .case-description {
    // overflow: hidden;
    // text-overflow: ellipsis;
    // white-space: nowrap;
    cursor: pointer;
    max-width: 100%;
  }

  // 历史合规记录详细列表样式
  .compliance-detail-list {
    margin-top: 30px;

    .el-table {
      border-radius: 8px;
      overflow: hidden;

      .el-table__header-wrapper {
        .el-table__header {
          th {
            background-color: #EBF1FF !important;
            color: #303133 !important;
            font-weight: 600;
            font-size: 14px;
            padding: 15px 0;
          }
        }
      }

      .el-table__body-wrapper {
        .el-table__body {
          tr {
            &:hover {
              background-color: #f5f7fa;
            }

            td {
              padding: 20px 12px;
              border-bottom: 1px solid #ebeef5;

              .case-description {
                line-height: 1.6;
                color: #606266;
                font-size: 14px;
                text-align: justify;
                word-break: break-word;
                white-space: pre-wrap;
              }
            }
          }
        }
      }

      // 表格边框样式
      .el-table--border {
        border: 1px solid #ebeef5;

        &::after {
          background-color: #ebeef5;
        }

        &::before {
          background-color: #ebeef5;
        }
      }

      // 表格单元格边框
      .el-table td, .el-table th {
        border-right: 1px solid #ebeef5;
      }

      // 最后一列不显示右边框
      .el-table td:last-child, .el-table th:last-child {
        border-right: none;
      }
    }

    // 分页容器样式
    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 20px;
      padding: 20px 0;

      .el-pagination {
        .el-pager li {
          background-color: #f5f7fa;
          color: #606266;
          border: 1px solid #dcdfe6;
          margin: 0 2px;

          &.active {
            background-color: #409EFF;
            color: white;
            border-color: #409EFF;
          }

          &:hover {
            color: #409EFF;
          }
        }

        .btn-prev, .btn-next {
          background-color: #f5f7fa;
          color: #606266;
          border: 1px solid #dcdfe6;

          &:hover {
            color: #409EFF;
          }

          &.disabled {
            color: #c0c4cc;
            cursor: not-allowed;
          }
        }
      }
    }
  }
}

</style>
