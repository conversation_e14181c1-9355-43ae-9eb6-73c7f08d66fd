<template>
  <div class="subsection">
    <div class="subsection-header" @click="toggleExpanded">
      <h4 class="subsection-title">{{ title }}</h4>
      <div class="subsection-actions">
        <span class="count-badge">1</span>
        <i :class="expanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
      </div>
    </div>

    <div class="subsection-content" v-show="expanded">
      <div class="person-item">
        <el-row :gutter="30" class="person-info">
          <el-col
            v-for="field in getPersonFields()"
            :key="field.key"
            :span="field.span || 8"
          >
            <div class="info-item" v-if="shouldShowField(field, personData)">
              <span class="label">{{ field.label }}：</span>
              <span class="value">{{ getFieldValue(field, personData) }}</span>
            </div>
          </el-col>
        </el-row>

        <div class="person-photos" v-if="showCertificatePhotos">
          <el-row :gutter="15" type="flex" justify=center>
            <el-col :span="6">
              <div class="photo-item">
                <div class="photo-title">{{ getCertificatePhotoLabel('front') }}</div>
                <div class="photo-container">
                  <img v-if="personData.certificateFront" :src="personData.certificateFront" :alt="getCertificatePhotoLabel('front')" />
                  <i v-else class="el-icon-picture-outline"></i>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="photo-item">
                <div class="photo-title">{{ getCertificatePhotoLabel('back') }}</div>
                <div class="photo-container">
                  <img v-if="personData.certificateBack" :src="personData.certificateBack" :alt="getCertificatePhotoLabel('back')" />
                  <i v-else class="el-icon-picture-outline"></i>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="photo-item">
                <div class="photo-title">{{ getCertificatePhotoLabel('hand') }}</div>
                <div class="photo-container">
                  <img v-if="personData.certificateHand" :src="personData.certificateHand" :alt="getCertificatePhotoLabel('hand')" />
                  <i v-else class="el-icon-picture-outline"></i>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PersonInfoSection',
  props: {
    title: {
      type: String,
      required: true
    },
    personData: {
      type: Object,
      default: () => ({})
    },
    expanded: {
      type: Boolean,
      default: true
    },
    showExtraFields: {
      type: Boolean,
      default: false
    },
    showCertificatePhotos: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    // 获取人员信息字段配置
    getPersonFields() {
      if (this.title.includes('法人')) {
        // 法人信息字段 - 根据apiData.json 548-555行
        return [
          {
            key: 'name',
            label: '法人姓名',
            span: 8,
            required: true
          },
          {
            key: 'phoneNumber',
            label: '法人联系电话',
            span: 8,
            required: true,
            format: 'phone'
          },
          {
            key: 'certificateType',
            label: '证件类型',
            span: 8,
            required: true
          },
          {
            key: 'certificateCode',
            label: '证件号码',
            span: 8,
            required: true,
            format: 'idCard'
          },
          {
            key: 'certificateValidityPeriod',
            label: '证件有效期',
            span: 8,
            required: true
          }
        ]
      } else {
        // 责任人信息字段 - 根据apiData.json 556-566行
        return [
          {
            key: 'name',
            label: '负责人姓名',
            span: 8,
            required: true
          },
          {
            key: 'phoneNumber',
            label: '负责人联系电话',
            span: 8,
            required: true,
            format: 'phone'
          },
          {
            key: 'certificateType',
            label: '证件类型',
            span: 8,
            required: true
          },
          {
            key: 'certificateCode',
            label: '证件号码',
            span: 8,
            required: true,
            format: 'idCard'
          },
          {
            key: 'certificateValidityPeriod',
            label: '证件有效期',
            span: 8,
            required: true
          },
          {
            key: 'officePhoneNumber',
            label: '办公室电话',
            span: 8,
            required: true
          },
          {
            key: 'email',
            label: '电子邮件',
            span: 8,
            required: true
          },
          {
            key: 'permanentAddress',
            label: '常驻地址',
            span: 24,
            required: true
          }
        ]
      }
    },

    // 获取字段值
    getFieldValue(field, data) {
      const value = data[field.key]

      // 处理格式化
      if (value && field.format) {
        if (field.format === 'phone') {
          return this.formatPhone(value)
        } else if (field.format === 'idCard') {
          return this.formatIdCard(value)
        }
      }

      // 所有字段都显示，无数据时用"-"填充
      return value || '-'
    },

    // 判断是否显示字段
    shouldShowField(field, data) {
      // 所有字段都显示
      return true
    },

    // 手机号脱敏格式化：188****9982
    formatPhone(phone) {
      if (!phone || phone.length < 11) return phone
      return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    },

    // 身份证号脱敏格式化：110105********8989
    formatIdCard(idCard) {
      if (!idCard || idCard.length < 15) return idCard
      return idCard.replace(/(\d{6})\d*(\d{4})/, '$1********$2')
    },

    // 获取证件照片标签
    getCertificatePhotoLabel(type) {
      const isLegal = this.title.includes('法人')

      if (type === 'front') {
        return isLegal ? '法人证件(人像)' : '负责人证件(人像)'
      } else if (type === 'back') {
        return isLegal ? '法人证件照(国微)' : '负责人证件照(国微)'
      } else if (type === 'hand') {
        return isLegal ? '法人证件(手持)' : '负责人证件(手持)'
      }
      return ''
    },

    toggleExpanded() {
      this.$emit('toggle')
    }
  }
}
</script>

<style lang="scss" scoped>
.subsection {
  margin-top: 25px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;

  .subsection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background-color: #f5f7fa;
    cursor: pointer;

    &:hover {
      background-color: #eef1f6;
    }

    .subsection-title {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
    }

    .subsection-actions {
      display: flex;
      align-items: center;

      .count-badge {
        display: inline-block;
        padding: 2px 8px;
        margin-right: 10px;
        background-color: #409EFF;
        color: #fff;
        border-radius: 10px;
        font-size: 12px;
      }

      i {
        font-size: 16px;
        color: #909399;
      }
    }
  }

  .subsection-content {
    padding: 15px;
    background-color: #fff;

    .person-item {
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }

      .person-info {
        margin-bottom: 15px;
      }

      .person-photos {
        margin-top: 15px; // 减少顶部间距

        .photo-item {
          height: 100%;
          display: flex;
          flex-direction: column;
          background-color: #fff;
          border: 1px solid #ebeef5;
          border-radius: 4px;
          overflow: hidden;

          .photo-title {
            padding: 6px; // 减少内边距
            text-align: center;
            font-weight: 500;
            font-size: 12px; // 减小字体
            background-color: #f5f7fa;
            border-bottom: 1px solid #ebeef5;
          }

          .photo-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            width: 100%;

            // 调整为更小的宽高比，减少高度
            &:before {
              content: "";
              display: block;
              padding-top: 40%; // 减少高度，让照片容器更小
            }

            img {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              object-fit: contain;
              padding: 8px; // 减少内边距
            }

            i {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              font-size: 40px; // 减小图标尺寸
              color: #909399;
            }
          }
        }
      }
    }
  }

  .info-item {
    margin-bottom: 15px;

    .label {
      color: #333; // 加深标签颜色
      margin-right: 5px;
      font-size: 14px; // 调整字体大小
      font-weight: 500; // 增加字重
    }

    .value {
      color: #333; // 加深值的颜色
      font-size: 14px; // 调整字体大小
      word-break: break-all;
    }
  }
}
</style>
