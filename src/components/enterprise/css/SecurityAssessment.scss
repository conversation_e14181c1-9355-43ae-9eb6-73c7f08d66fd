.text-ellipsis {
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 隐藏溢出的内容 */
  text-overflow: ellipsis; /* 显示省略符号来代表被修剪的文本 */
  // width: 200px; /* 指定一个宽度，超出这个宽度的文本会被修剪 */
}

.security-assessment-section {
  .section-title {
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    border-bottom: 1px solid #ebeef5;
  }

  .security-assessment {
    border-radius: 8px;
    margin-bottom: 20px;
    // overflow: hidden;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .assessment-header {
      position: relative;

      .header-content {
        // display: flex;
        padding: 20px 0;

        .score-section {
          // flex: 0 0 25%;
          text-align: center;
          color: white;

          .score-title {
           font-size: 18px;
            margin-bottom: 15px;
            margin-top: 20px;
            font-weight: 600;
          }

          .score-value {
            font-size: 68px;
            font-weight: bold;
            color: #FF8181;
            margin-bottom: 10px;
          }

          .score-change {
            margin-bottom: 12px;
            font-size: 16px;

            i {
              margin: 0 5px;
            }
          }

          .score-compare {
            margin-bottom: 15px;
            font-size: 16px;

            span {
              color: #409EFF;
              font-weight: bold;
            }
          }

          .score-stars {
            margin-bottom: 15px;
            font-size: 30px; // 从20px放大到30px，1.5倍

            i {
              margin: 0 3px; // 相应调整间距
            }
          }

          .risk-level {
            width: 80%;
            color:#4584FF;
            border-radius: 12px;
            padding: 4px 15px;
            font-size: 16px;
            margin: 0 auto;
            // font-weight: bold;
          }
        }

        .radar-section {
          // flex: 0 0 35%;
          min-height: 350px;
        }

        .description-section {
          // flex: 0 0 39%;
          padding: 0 20px;
          color: white;
          h4 {
            font-size: 16px;
            margin-top: 0;
            margin-bottom: 10px;
            font-weight: 500;
          }

          p {
            line-height: 1.6;
            margin: 0;
            font-size: 14px;
            white-space: pre-wrap;
            word-break: break-word;
          }
        }
        // @media (max-width: 1336px) {
        //      .description-section{
        //       max-width: 34%;
        //     }
        //   }
        //   @media (max-width: 1000px) {
        //      .description-section{
        //       max-width: 28%;
        //     }
        //   }

        .description-section-sub {
          list-style: none;
          padding: 0;
          margin: 15px 0 0 0;
          display: flex;
          flex-direction: column;
          gap: 8px;

          .score-item {
            display: flex;
            align-items: center;
            gap: 8px;
            max-width: 100%;
            .itemName {
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 12px;
              font-weight: 500;
              text-align: center;
              color: white;
              background-color: rgba(255, 255, 255, 0.2);
              white-space: nowrap;
              flex-shrink: 0;

              &.decrease {
                background-color: #17B26D;
                color: #fff;
              }

              &.increase {
                background-color: #FBEB3E;
                color: #f56c6cfe;
              }
            }

            .subitemDesp {
              font-size: 13px;
              color: rgba(255, 255, 255, 0.8);
              line-height: 1.3;
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              cursor: pointer;
            }
             &:hover{
              font-weight: 500;
             }
          }

          // @media (max-width: 1336px) {
          //   .score-item{
          //     max-width: 98%;
          //   }
          // }
          // @media (max-width: 1336px) {
          //   .score-item{
          //     max-width: 94%;
          //   }
          // }
          // @media (max-width: 1200px) {
          //   .score-item{
          //     max-width: 80%;
          //   }
          // }
          //  @media (max-width: 1080px) {
          //   .score-item{
          //     max-width: 70%;
          //   }
          // }
          //  @media (max-width: 1024px) {
          //   .score-item{
          //     max-width: 55%;
          //   }
          // }
          //  @media (max-width: 798px) {
          //   .score-item{
          //     max-width: 25%;
          //   }
          // }
        }
      }

      .expand-button {
        position: absolute;
        bottom: 0px;
        right: 20px;
        display: flex;
        align-items: center;
        gap: 5px;
        color: white;
        cursor: pointer;
        font-size: 14px;
        padding: 5px 10px;
        border-radius: 4px;
        transition: background-color 0.3s;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }

        i {
          font-size: 16px;
        }
      }

      .update-time {
        position: absolute;
        top: 10px;
        right: 20px;
        color: white;
        font-size: 12px;
        opacity: 0.8;
      }
    }
  }

  .security-sub-modules {
    .loading-container {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      padding: 40px;
      color: #666;

      i {
        font-size: 18px;
        animation: spin 1s linear infinite;
      }
    }

    .security-sub-modules {
      display: flex;
      flex-direction: column;
      gap: 20px;

      // 第一行：资质与合规情况(35%) + 历史安全事件与威胁情报(65%)
      .row-1 {
        display: flex;
        gap: 20px;

        .sub-module:nth-child(1) {
          flex: 0 0 35%;
        }

        .sub-module:nth-child(2) {
          flex: 0 0 65%;
        }
      }

      // 第二行：互联网暴露面与资产风险(35%) + 运营安全能力(32.5%) + 舆情与社会影响(32.5%)
      .row-2 {
        display: flex;
        gap: 20px;

        .sub-module:nth-child(1) {
          flex: 0 0 35%;
        }

        .sub-module:nth-child(2) {
          flex: 1;
        }

        .sub-module:nth-child(3) {
          flex: 1;
        }
      }

      // 第三行：人员与内部安全管理(40%) + 数据质量管理(60%)
      .row-3 {
        display: flex;
        gap: 20px;

        .sub-module:nth-child(1) {
          flex: 0 0 40%;
        }

        .sub-module:nth-child(2) {
          flex: 0 0 60%;
        }
      }

      .sub-module {
        background: white;
        border: 1px solid #e0e6ed;
        border-radius: 0;
        overflow: hidden;
        box-shadow: none;

        .sub-module-title {
          background: white;
          padding: 10px 16px;
          font-weight: 500;
          font-size: 13px;
          color: #333;
          border-bottom: 1px solid #e0e6ed;
          display: flex;
          align-items: center;
          gap: 8px;

          &::before {
            content: '';
            width: 3px;
            height: 14px;
            background-color: #409EFF;
            border-radius: 1px;
            flex-shrink: 0;
          }
        }

        .sub-module-content {
          padding: 20px;
        }
      }
    }
  }

  // 资质与合规情况模块特殊样式
  .compliance-module {
    .compliance-row {
        width: 100%;
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }

      .score-block {
        background: #D6E4FF;
        color: #2A52D7;
        padding: 20px; // 从15px增加到20px
        border-radius: 8px;
        text-align: center;

        .score-label {
          font-size: 14px; // 从12px放大到14px，1.2倍
          margin-bottom: 5px;
        }

        .score-number {
          font-size: 29px; // 从24px放大到29px，1.2倍
          font-weight: bold;
        }
      }

      .description-block {
        // flex: 1;
        color: #333;
        font-size: 17px; // 从14px放大到17px，1.2倍
        line-height: 1.6;
        // display: flex;
        // align-items: center;
      }

      .sub-item-card {
        flex: 1;
        background: #F8FBFF;
        padding: 20px; // 从15px增加到20px
        border-radius: 8px;
        min-height: 100px; // 增加最小高度
        margin-bottom: 8px;
        .sub-item-name {
          color: #666;
          font-size: 14px; // 从12px放大到14px，1.2倍
          margin-bottom: 8px;
        }

        .sub-item-result {
          color: #333;
          font-weight: bold;
          font-size: 17px; // 从14px放大到17px，1.2倍
        }
      }
      .long-card {
        width: 100%;
        background: #f5f5f5;
        border-radius: 8px;
        padding: 20px 16px 20px 20px;
        min-height: 100px; // 增加最小高度

        .long-card-left {

          .long-card-title {
            font-size: 17px; // 从14px放大到17px，1.2倍
            color: #333;
            margin-bottom: 8px;
          }

          .long-card-result {
            font-size: 14px; // 从12px放大到14px，1.2倍
            color: #666;
          }
        }

        .long-card-right {
        //   display: flex;
        //   gap: 10px;

          .data-block {
            border-radius: 6px;
            text-align: center;
             margin: 10px 0 8px 8px;
            // width: 49%;
            // min-width: 90px; // 从80px增加到90px
            min-height: 70px; // 增加最小高度
            // display: flex;
            // flex-direction: column;
            // align-items: center;
            // justify-content: center;
            .data-label {
              font-size: 17px; // 从14px放大到17px，1.2倍
              margin-bottom: 4px;
              margin-top: 10px;
            }

            .data-value {
              font-size: 17px; // 从14px放大到17px，1.2倍
              font-weight: bold;
            }

            &.warning {
              background: #FEF7DD;
              color: #AF7003;
            }

            &.danger {
              background: #F8EAEE;
              color: #E71F00;
            }
          }
        //   .data-block:first-of-type {
        //     margin-left: -10px;
        //   }
        }
      }
    }
  }

  // 通用模块样式
  .general-module {
    .module-score {
      margin-bottom: 15px;

      .score-label {
        color: #666;
        font-size: 14px;
      }

      .score-value {
        color: #333;
        font-weight: bold;
        font-size: 18px;
      }
    }

    .module-description {
      color: #666;
      font-size: 14px;
      line-height: 1.6;
      margin-bottom: 20px;
    }

    .sub-items {
      .sub-item {
        background: #f9f9f9;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0;
        }

        .sub-item-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .sub-item-name {
            font-weight: 500;
            color: #333;
          }

          .sub-item-score {
            color: #2196f3;
            font-weight: bold;
          }
        }

        .sub-item-result {
          color: #666;
          font-size: 14px;
          margin-bottom: 10px;
        }

        .data-items {
          .data-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #eee;

            &:last-child {
              border-bottom: none;
            }

            .data-name {
              color: #666;
              font-size: 12px;
            }

            .data-value {
              color: #333;
              font-weight: 500;
              font-size: 12px;
            }
          }
        }
      }
    }

    // 测试区域基本样式
    .test-modules {
      .sub-module {
        border: 1px solid #ddd;
        margin-bottom: 20px;
        padding: 15px;
        border-radius: 8px;

        .sub-module-title {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 15px;
          padding: 10px;
          background: #f5f5f5;
          border-radius: 4px;
        }

        .sub-module-content {
          padding: 10px;
        }
      }


    }

  

    // 互联网暴露面与资产风险模块样式
    .internet-asset-module {
      .internet-row {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
        flex-wrap: wrap;
        &:last-child {
          margin-bottom: 0;
        }
        
        // 基本色块样式
        .info-card {
          background: #F8FBFF;
          border-radius: 8px;
          padding: 15px;
          border: 1px solid #E6F0FF;

          .card-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
          }

          .donut-chart-container {
            height: 200px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;

            .donut-chart {
              width: 200px;
              height: 200px;
              min-width: 200px;
              min-height: 200px;
              background: white;
              border: 2px solid #67C23A;
              border-radius: 8px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 14px;
              color: #666;
            }
          }

          .chart-legend {
            .legend-item {
              display: flex;
              align-items: center;
              margin-bottom: 5px;
              font-size: 12px;

              .legend-dot {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                margin-right: 6px;
              }

              .legend-name {
                flex: 1;
                margin-right: 5px;
              }

              .legend-score {
                margin-right: 5px;
                font-weight: bold;
              }

              .legend-percent {
                color: #666;
              }
            }
          }








        }

        // 第一行：综合评分样式（复用历史安全事件的样式）
        .score-block {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 8px;
          padding: 20px;
          text-align: center;
          color: white;

          .score-label {
            font-size: 14px;
            margin-bottom: 10px;
            opacity: 0.9;
          }

          .score-number {
            font-size: 36px;
            font-weight: bold;
            color: #FFD700;
          }
        }

        .description-block {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 8px;
          padding: 20px;
          color: white;
          font-size: 14px;
          line-height: 1.6;
        }

        // 第二行：环状图 + 六边形色块
        .chart-section {
          // flex: 0 0 50%;
          // background: #F8FBFF;
          // border-radius: 8px;
          // padding: 20px;
          // border: 1px solid #E6F0FF;

          .donut-chart-container {
            height: 200px;
            margin-bottom: 15px;

            .donut-chart {
              width: 100%;
              height: 100%;
            }
          }

          .chart-legend-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-top: 10px;
            padding: 10px;
            background: #F8FBFF;
            border-radius: 6px;

            .legend-item-grid {
              display: flex;
              align-items: center;
              font-size: 12px;
              padding: 4px 8px;
              background: white;
              border-radius: 4px;
              border: 1px solid #e0e0e0;

              .legend-dot {
                width: 10px;
                height: 10px;
                border-radius: 50%;
                margin-right: 8px;
                flex-shrink: 0;
                border: 1px solid rgba(0, 0, 0, 0.1);
              }

              .legend-text {
                flex: 1;
                margin-right: 4px;
                color: #333;
                font-weight: 500;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              .legend-percent {
                font-weight: bold;
                color: #666;
                font-size: 11px;
              }
            }
          }
        }

       
      }
    }
  }
}
 // 第三行：事件响应能力 + 安全监测
.monitoring-card {

  
    .response-stats {
    display: flex !important;
    gap: 10px !important;

    .response-item {
        flex: 1 !important;
        padding: 15px !important;
        border-radius: 10px !important;
        text-align: center !important;
        color: white !important;
        background: linear-gradient(135deg, var(--bg-start) 0%, var(--bg-end) 100%) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        transition: all 0.3s ease !important;
        position: relative !important;
        overflow: hidden !important;

        &::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%) !important;
        pointer-events: none !important;
        }

        &:hover {
        transform: translateY(-4px) scale(1.02) !important;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25) !important;
        }

        &:nth-child(1) {
        --bg-start: #667eea;
        --bg-end: #764ba2;
        }

        &:nth-child(2) {
        --bg-start: #f093fb;
        --bg-end: #f5576c;
        }

        .response-name {
        font-size: 13px !important;
        margin-bottom: 8px !important;
        opacity: 0.95 !important;
        font-weight: 500 !important;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
        }

        .response-value {
        font-size: 20px !important;
        font-weight: bold !important;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
        letter-spacing: 0.5px !important;
        }
    }
    }

    .monitoring-block {
    background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%) !important;
    padding: 15px !important;
    border-radius: 8px !important;
    text-align: center !important;
    color: white !important;
    box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3) !important;
    transition: transform 0.2s ease !important;

    &:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 8px rgba(255, 107, 107, 0.4) !important;
    }

    .monitoring-name {
        font-size: 12px !important;
        margin-bottom: 6px !important;
        opacity: 0.9 !important;
    }

    .monitoring-value {
        font-size: 18px !important;
        font-weight: bold !important;
    }
    }

    .monitoring-donuts {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 15px !important;
    margin-top: 15px !important;
    justify-content: center !important;

    .monitoring-donut-item {
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        min-width: 60px !important;

        .mini-monitoring-donut {
        width: 50px !important;
        height: 50px !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        position: relative !important;
        margin-bottom: 8px !important;

        &::before {
            content: '' !important;
            position: absolute !important;
            width: 30px !important;
            height: 30px !important;
            background: white !important;
            border-radius: 50% !important;
            z-index: 1 !important;
        }

        .donut-percent {
            font-size: 10px !important;
            font-weight: bold !important;
            color: #333 !important;
            position: relative !important;
            z-index: 2 !important;
        }
        }

        .donut-label {
        font-size: 10px !important;
        color: #666 !important;
        text-align: center !important;
        line-height: 1.2 !important;
        max-width: 60px !important;
        word-break: break-all !important;
        }
    }
    }
}
.sub-module-title {
          background: white;
          padding: 10px 16px;
          font-weight: 500;
          font-size: 13px;
          color: #333;
          border-bottom: 1px solid #e0e6ed;
          display: flex;
          align-items: center;
          gap: 8px;

          &::before {
            content: '';
            width: 3px;
            height: 14px;
            background-color: #409EFF;
            border-radius: 1px;
            flex-shrink: 0;
          }
}
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// 简化的CSS选择器 - 直接针对类名，不依赖复杂的层级关系

// 统一的综合评分样式 - 所有sub-module容器内的综合评分都使用历史安全事件的样式
.compliance-row {
  margin-bottom: 15px !important;

  &:last-child {
    margin-bottom: 0 !important;
  }


}

// 监测环状图样式 
.sa-monitoring-donuts {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-evenly;
    

    .sa-monitoring-donut-item {
      display: flex !important;
      flex-direction: column !important;
      align-items: center !important;
      min-width: 60px !important;

      .sa-mini-monitoring-donut {
        width: 80px !important; 
        height: 80px !important; 
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        position: relative !important;
        margin-bottom: 8px !important;
        border-width: 4px !important; 

        &::before {
          content: '' !important;
          position: absolute !important;
          width: 65px !important;
          height: 65px !important;
          background: white !important;
          border-radius: 50% !important;
          z-index: 1 !important;
        }

        .sa-donut-percent {
          font-size: 14px !important;
          // font-weight: bold !important;
          color: #333 !important;
          position: relative !important;
          z-index: 2 !important;
        }
      }

      .sa-donut-label {
        font-size: 14px !important;
        color: #555 !important;
        text-align: center !important;
        line-height: 1.2 !important;
        max-width: 60px !important;
        word-break: break-all !important;
      }
    }
}

  // 监测色块样式 - 使用唯一类名
  .sa-monitoring-block {
    background: #F8EAEE !important;
    padding: 8px 5px !important;
    border-radius: 8px !important;
    text-align: center !important;
    color: #E70000;
    border: 1px solid #E70000; 
    // box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3) !important;
    transition: transform 0.2s ease !important;
    margin: 5px 0 0 5px;

    &:hover {
      transform: translateY(-2px) !important;
      // box-shadow: 0 4px 8px rgba(255, 107, 107, 0.4) !important;
    }

    .sa-monitoring-name {
      font-size: 12px !important;
      margin-bottom: 6px !important;
      opacity: 0.9 !important;
    }

    .sa-monitoring-value {
      font-size: 18px !important;
      font-weight: bold !important;
    }
  }

  // 运营安全能力模块样式 - 使用唯一类名
  .sa-operation-safety-module {
    .sa-operation-row {
      display: flex !important;
      gap: 10px !important; // 减少间距
      margin-bottom: 10px !important; // 减少底部间距

      &:last-child {
        margin-bottom: 0 !important;
      }

      // 运营安全能力卡片
      .sa-operation-card {
        background: #F8FBFF !important;
        border-radius: 8px !important;
        padding: 10px !important; // 减少内边距
        border: 1px solid #E0E0E0 !important;

        &.sa-operation-card-40 {
          flex: 0 0 40% !important;
        }

        &.sa-operation-card-60 {
          flex: 0 0 60% !important;
        }

        .sa-operation-title {
          font-size: 14px !important;
          font-weight: bold !important;
          color: #333 !important;
          margin-bottom: 12px !important;
        }

        .sa-operation-result {
          font-size: 13px !important;
          color: #666 !important;
          line-height: 1.5 !important;
        }

        // 水平排列的数据块
        .sa-operation-data-blocks {
          display: flex !important;
          gap: 10px !important;

          .sa-operation-data-block {
            flex: 1 !important;
            padding: 12px !important;
            border-radius: 6px !important;
            text-align: center !important;
            transition: transform 0.2s ease !important;

            &:hover {
              transform: translateY(-2px) !important;
            }

            &.sa-light-red {
              background: #FFE5E5 !important;
              border: 1px solid #FFB3B3 !important;
            }

            &.sa-light-blue {
              background: #E5F3FF !important;
              border: 1px solid #B3D9FF !important;
            }

            .sa-data-name {
              font-size: 12px !important;
              color: #666 !important;
              margin-bottom: 6px !important;
            }

            .sa-data-value {
              font-size: 16px !important;
              font-weight: bold !important;
              color: #333 !important;
            }
          }
        }

        // 垂直排列的数据块
        .sa-operation-data-blocks-vertical {
          display: flex !important;
          flex-direction: column !important;
          gap: 10px !important;

          .sa-operation-data-block {
            padding: 12px !important;
            border-radius: 6px !important;
            text-align: center !important;
            transition: transform 0.2s ease !important;

            &:hover {
              transform: translateY(-2px) !important;
            }

            &.sa-light-red {
              background: #FFE5E5 !important;
              border: 1px solid #FFB3B3 !important;
            }

            &.sa-light-blue {
              background: #E5F3FF !important;
              border: 1px solid #B3D9FF !important;
            }

            .sa-data-name {
              font-size: 12px !important;
              color: #666 !important;
            }

            .sa-data-value {
              font-size: 16px !important;
              font-weight: bold !important;
              color: #333 !important;
            }
          }
        }
      }
    }
  }

  // 舆情与社会影响力模块样式 - 使用唯一类名
  .sa-sentiment-community-module {
    .sa-sentiment-row {
      display: flex !important;
      gap: 10px !important; // 减少间距
      margin-bottom: 10px !important; // 减少底部间距

      &:last-child {
        margin-bottom: 0 !important;
      }

      // 舆情卡片
      .sa-sentiment-card {
        flex: 1 !important;
        background: #F8FBFF !important;
        border-radius: 8px !important;
        padding: 10px !important; // 减少内边距
        border: 1px solid #E0E0E0 !important;
        display: flex !important;
        align-items: center !important;
        gap: 15px !important;

        .sa-sentiment-title {
          flex: 0 0 120px !important;
          font-size: 14px !important;
          font-weight: bold !important;
          color: #333 !important;
        }

        .sa-sentiment-data-blocks {
          flex: 1 !important;
          display: flex !important;
          gap: 12px !important;

          .sa-sentiment-data-block {
            flex: 1 !important;
            padding: 12px !important;
            border-radius: 6px !important;
            text-align: center !important;
            transition: transform 0.2s ease !important;

            &:hover {
              transform: translateY(-2px) !important;
            }

            &.sa-orange-light {
              background: #F9EEE2 !important;
              border: 1px solid #E6D5B7 !important;
            }

            &.sa-blue-light {
              background: #E5F3FF !important;
              border: 1px solid #B3D9FF !important;
              color: #1976D2 !important;
            }

            .sa-data-main {
              display: flex !important;
              //flex-direction: column !important;
             // gap: 4px !important;
              align-items: center;
              justify-content: space-around;
              margin-bottom: 4px !important;

              .sa-data-value {
                font-size: 16px !important;
                font-weight: bold !important;
                color: #333 !important;
              }

              .sa-data-trend {
                font-size: 11px !important;
                font-weight: 500 !important;

                &.trend-up {
                  color: #F56C6C !important;
                }

                &.trend-down {
                  color: #67C23A !important;
                }

                &.trend-neutral {
                  color: #909399 !important;
                }
              }
            }

            .sa-data-value {
              font-size: 16px !important;
              font-weight: bold !important;
              color: #333 !important;
            }

            .sa-data-name {
              font-size: 12px !important;
              color: #666 !important;
              line-height: 1.3 !important;
            }
          }
        }
      }
    }
  }

  // 事件响应能力美化样式 - 使用唯一类名
  .sa-response-stats {

    .sa-response-item {
    //   flex: 1 !important;
      padding: 8px 5px !important;
      border-radius: 10px !important;
      text-align: center !important;
      color: white !important;
      background: linear-gradient(135deg, var(--bg-start) 0%, var(--bg-end) 100%) !important;
      transition: all 0.3s ease !important;
      position: relative !important;
      overflow: hidden !important;
        margin: 5px 0 5px 5px;
      &::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%) !important;
        pointer-events: none !important;
      }

      &:hover {
        transform: translateY(-4px) scale(1.02) !important;
        // box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25) !important;
      }

      &:nth-child(1) {
        --bg-start: #E8F5E8;
        --bg-end: #E8F5E8;
        background: #E8F5E8 !important;
        color: #388E3C !important;
        border: 1px solid #388E3C !important;
      }

      &:nth-child(2) {
        --bg-start: #E3F2FD;
        --bg-end: #E3F2FD;
        background: #E3F2FD !important;
        color: #1976D2 !important;
        border: 1px solid #1976D2 !important;
      }

      .sa-response-name {
        font-size: 13px !important;
        margin-bottom: 8px !important;
        opacity: 0.95 !important;
        font-weight: 500 !important;
      }

      .sa-response-value {
        font-size: 20px !important;
        font-weight: bold !important;
        letter-spacing: 0.5px !important;
      }
    }
  }

// 人员与内部安全管理模块样式
.sa-personnel-safety-module {
  .sa-personnel-row {
    display: flex !important;
    gap: 10px !important; // 减少间距
    margin-bottom: 10px !important; // 减少底部间距

    &:last-child {
      margin-bottom: 0 !important;
    }

    .sa-personnel-card {
      flex: 1 !important;
      background: #F8FBFF !important;
      border-radius: 8px !important;
      padding: 10px !important; // 减少内边距
      border: 1px solid #E6F0FF !important;
      box-shadow: 0 2px 8px rgba(0,0,0,0.05) !important;
      transition: transform 0.2s ease !important;

      &:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
      }

      .sa-personnel-title {
        font-size: 14px !important;
        font-weight: bold !important;
        color: #333 !important;
        margin-bottom: 12px !important;
        text-align: center !important;
        padding-bottom: 8px !important;
        border-bottom: 1px solid #E6F0FF !important;
      }

      .sa-personnel-data-blocks {
        display: flex !important;
        flex-direction: column !important;
        gap: 8px !important;

        .sa-personnel-data-block {
          padding: 10px !important;
          border-radius: 6px !important;
          text-align: center !important;
          transition: transform 0.2s ease !important;

          &:hover {
            transform: translateY(-1px) !important;
          }

          &.sa-light-purple {
            background: #F3E8FF !important;
            border: 1px solid #D8B4FE !important;
            color: #7C3AED !important;
          }

          &.sa-light-yellow {
            background: #F2F0F4 !important;
            border: 1px solid  !important;
            color: #936054 !important;
          }

          &.sa-light-green {
            background: #DCFCE7 !important;
            border: 1px solid #86EFAC !important;
            color: #16A34A !important;
          }

          .sa-data-value {
            font-size: 14px !important;
            font-weight: bold !important;
            margin-bottom: 4px !important;
          }

          .sa-data-name {
            font-size: 11px !important;
            opacity: 0.8 !important;
            line-height: 1.3 !important;
          }
        }
      }
    }
  }
}

// 数据质量管理模块样式
.sa-data-quality-module {
  .sa-data-row {
    display: flex !important;
    gap: 10px !important; // 减少间距
    margin-bottom: 10px !important; // 减少底部间距

    &:last-child {
      margin-bottom: 0 !important;
    }

    .sa-data-card {
      flex: 1 !important;
      background: #F8FBFF !important;
      border-radius: 8px !important;
      padding: 10px !important; // 减少内边距
      border: 1px solid #E6F0FF !important;
      box-shadow: 0 2px 8px rgba(0,0,0,0.05) !important;
      transition: transform 0.2s ease !important;

      &:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
      }

      .sa-data-title {
        font-size: 14px !important;
        font-weight: bold !important;
        color: #333 !important;
        margin-bottom: 12px !important;
        text-align: center !important;
        padding-bottom: 8px !important;
        border-bottom: 1px solid #E6F0FF !important;
      }

      .sa-data-data-blocks {
        display: flex !important;
        flex-direction: column !important;
        gap: 8px !important;

        .sa-data-data-block {
          padding: 10px !important;
          border-radius: 6px !important;
          text-align: center !important;
          transition: transform 0.2s ease !important;

          &:hover {
            transform: translateY(-1px) !important;
          }

          &.sa-light-purple {
            background: #E9F0FF !important;
            border: 1px solid #4584FF !important;
            color: #4584FF !important;
          }

          &.sa-light-yellow {
            background: #FEF3C7 !important;
            border: 1px solid #FCD34D !important;
            color: #D97706 !important;
          }

          &.sa-light-green {
            background: #DCFCE7 !important;
            border: 1px solid #86EFAC !important;
            color: #16A34A !important;
          }

          .sa-data-value {
            font-size: 14px !important;
            font-weight: bold !important;
            margin-bottom: 4px !important;
          }

          .sa-data-name {
            font-size: 11px !important;
            opacity: 0.8 !important;
            line-height: 1.3 !important;
          }
        }
      }
    }
  }
}

// 测试区域专用样式 - 直接针对测试区域的类名
.chart-legend-wrap {
    width: 100%;
    justify-content: center;
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 8px !important;
  padding: 10px !important;
  background: #F8FBFF !important;
  border-radius: 6px !important;
  margin-top: 10px !important;

  .legend-item {
    display: flex !important;
    align-items: center !important;
    font-size: 12px !important;
    margin-bottom: 4px !important;
    padding: 4px 8px !important;
    background: white !important;
    border-radius: 4px !important;
    border: 1px solid #e0e0e0 !important;

    .legend-dot {
      width: 10px !important;
      height: 10px !important;
      border-radius: 50% !important;
      margin-right: 8px !important;
      flex-shrink: 0 !important;
      border: 1px solid rgba(0, 0, 0, 0.1) !important;
    }

    .legend-text {
      white-space: nowrap !important;
      color: #333 !important;
      font-weight: 500 !important;
    }
  }
}

.chart-legend-grid {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  gap: 8px !important;
  margin-top: 10px !important;
  padding: 10px !important;
  background: #F8FBFF !important;
  border-radius: 6px !important;

  .legend-item-grid {
    display: flex !important;
    align-items: center !important;
    font-size: 12px !important;
    padding: 4px 8px !important;
    background: white !important;
    border-radius: 4px !important;
    border: 1px solid #e0e0e0 !important;

    .legend-dot {
      width: 10px !important;
      height: 10px !important;
      border-radius: 50% !important;
      margin-right: 8px !important;
      flex-shrink: 0 !important;
      border: 1px solid rgba(0, 0, 0, 0.1) !important;
    }

    .legend-text {
      flex: 1 !important;
      margin-right: 4px !important;
      color: #333 !important;
      font-weight: 500 !important;
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
    }

    .legend-percent {
      font-weight: bold !important;
      color: #666 !important;
      font-size: 11px !important;
    }
  }
}



.mini-donut-chart {
  width: 40px !important;
  height: 40px !important;
  min-width: 40px !important;
  min-height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 50% !important;
  position: relative !important;

  // 绿色圆环样式
  &.green {
    background: conic-gradient(#67C23A 0deg 180deg, #E5E5E5 180deg 360deg) !important;
  }

  // 红色圆环样式
  &.red {
    background: conic-gradient(#F56C6C 0deg 180deg, #E5E5E5 180deg 360deg) !important;
  }

  .percent {
    font-size: 10px !important;
    font-weight: bold !important;
    color: #333 !important;
    position: relative !important;
    z-index: 1 !important;
  }
}

// 趋势样式
.trend-up {
  color: #F56C6C !important; // 红色表示上升
}

.trend-down {
  color: #67C23A !important; // 绿色表示下降
}

.trend-neutral {
  color: #909399 !important; // 灰色表示无变化
}

// 互联网暴露面与资产风险模块专用样式 
.internet-asset-module {
  .internet-row {
    // display: flex !important;
    // gap: 20px !important;
    margin-bottom: 20px !important;

    &:last-child {
      margin-bottom: 0 !important;
    }

    // 第一行：综合评分样式（与资质与合规情况相同）
    .score-block {
      background: #D6E4FF !important;
      color: #2A52D7 !important;
      padding: 15px !important;
      border-radius: 8px !important;
      text-align: center !important;
        min-width: 100px;
      .score-label {
        font-size: 12px !important;
        margin-bottom: 5px !important;
      }

      .score-number {
        font-size: 24px !important;
        font-weight: bold !important;
      }
    }

    .description-block {
      color: #333 !important;
      font-size: 14px !important;
      line-height: 1.6 !important;
    }

    // 第二行：环状图 + 六边形色块
    .chart-section {
      // flex: 0 0 50% !important;
      // background: #F8FBFF !important;
      // border-radius: 8px !important;
      // padding: 20px !important;
      // border: 1px solid #E6F0FF !important;
      // display: flex !important;
      // gap: 15px !important;
      // align-items: flex-start !important;

      .donut-chart-container {
        flex: 0 0 200px !important;
        height: 200px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        position: relative !important;

        .donut-chart {
          width: 200px !important;
          height: 200px !important;
          min-width: 200px !important;
          min-height: 200px !important;
          background: white !important;
          border-radius: 8px !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          font-size: 14px !important;
          color: #666 !important;
        }
      }

      .chart-legend-grid {
        flex: 1 !important;
        display: grid !important;
        grid-template-columns: 1fr !important;
        gap: 6px !important;
        padding: 10px !important;
        background: rgba(248, 249, 250, 0.8) !important;
        border-radius: 6px !important;
        position: relative !important;
        z-index: 10 !important;
        max-height: 200px !important;
        overflow-y: auto !important;

        .legend-item-grid {
          display: flex !important;
          align-items: center !important;
          font-size: 11px !important;
          padding: 6px 8px !important;
          background: white !important;
          border-radius: 4px !important;
          border: 1px solid #e0e0e0 !important;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
          transition: transform 0.2s ease !important;

          &:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 2px 6px rgba(0,0,0,0.15) !important;
          }

          .legend-dot {
            width: 8px !important;
            height: 8px !important;
            border-radius: 50% !important;
            margin-right: 6px !important;
            flex-shrink: 0 !important;
            border: 1px solid rgba(0, 0, 0, 0.1) !important;
          }

          .legend-text {
            flex: 1 !important;
            margin-right: 4px !important;
            color: #333 !important;
            font-weight: 500 !important;
            white-space: nowrap !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            font-size: 10px !important;
          }

          .legend-percent {
            font-weight: bold !important;
            color: #666 !important;
            font-size: 10px !important;
            background: #f0f0f0 !important;
            padding: 2px 4px !important;
            border-radius: 3px !important;
          }
        }
      }
    }
    .hexagon-box.el-col-12 {
      margin-left: -50px;
    }
    .hexagon-box.el-col-24 {
      margin-left: -50px;
    }
    // .hexagon-box.el-col-xs-24, .hexagon-box.el-col-md-24, .hexagon-box.el-col-sm-24 {
    //   margin-left: -10px;
    // }
    .hexagon-section {
      flex: 0 0 50% !important;
      display: grid !important;
      grid-template-columns: repeat(3, 1fr) !important;
      grid-template-rows: repeat(2, 1fr) !important;
      gap: 10px !important;
      padding: 10px !important;
      justify-items: center !important;
      align-items: center !important;
      // background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
      // border-radius: 12px !important;
      // border: 1px solid #dee2e6 !important;

      .hexagon-item {
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        width: 100% !important;
        height: 80px !important;
        margin-bottom: 14px;
        .hexagon-shape {
          position: relative !important;
          width: 90px !important;
          height: 80px !important;
          // margin: 20px !important;
          text-align: center !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;

          // 使用clip-path创建六边形
          clip-path: polygon(15% 0,85% 0,100% 50%,85% 100%,15% 100%,0 50%)!important;

          // 添加渐变边框效果
          // border: 3px solid transparent !important;
          // background: linear-gradient(white, white) padding-box,
          //            linear-gradient(135deg, #667eea, #764ba2) border-box !important;

          // 添加增强阴影效果
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15),
                     0 2px 8px rgba(102, 126, 234, 0.2) !important;

          // 过渡效果
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;

          &:hover {
            transform: scale(1.08) translateY(-3px) !important;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25),
                       0 4px 12px rgba(102, 126, 234, 0.3) !important;
          }

          .hexagon-content {
            position: relative !important;
            z-index: 1 !important;
            // padding: 18px !important;
            width: 100% !important;
            height: 100% !important;
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            justify-content: center !important;

            .hex-name {
              font-size: 11px !important;
              margin-bottom: 8px !important;
              font-weight: 600 !important;
              line-height: 1.3 !important;
              text-align: center !important;
              word-break: break-all !important;
            }

            .hex-value {
              font-size: 14px !important;
              font-weight: bold !important;
              // margin-bottom: 8px !important;
            }

            .hex-trend {
              font-size: 11px !important;
              color: #6c757d !important;
              line-height: 1.3 !important;
              text-align: center !important;
              font-weight: 500 !important;

              span:first-child {
                margin-right: 4px !important;
                color: #495057 !important;
              }

              .trend-up {
                color: #dc3545 !important;
                font-weight: bold !important;
              }

              .trend-down {
                color: #28a745 !important;
                font-weight: bold !important;
              }

              .trend-neutral {
                color: #6c757d !important;
                font-weight: bold !important;
              }
            }
          }
        }
      }
    }
  }

  // 运营安全能力模块样式
  .operation-safety-module {
    .operation-row {
      display: flex !important;
      gap: 15px !important;
      margin-bottom: 15px !important;

      &:last-child {
        margin-bottom: 0 !important;
      }

      // 综合评分和描述块（复用历史安全事件的样式）
      .score-block {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border-radius: 8px !important;
        padding: 20px !important;
        text-align: center !important;
        color: white !important;

        .score-label {
          font-size: 14px !important;
          margin-bottom: 10px !important;
          opacity: 0.9 !important;
        }

        .score-number {
          font-size: 36px !important;
          font-weight: bold !important;
          color: #FFD700 !important;
        }
      }

      .description-block {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border-radius: 8px !important;
        padding: 20px !important;
        color: white !important;
        font-size: 14px !important;
        line-height: 1.6 !important;
      }

      // 运营安全能力卡片
      .operation-card {
        background: #F8FBFF !important;
        border-radius: 8px !important;
        padding: 15px !important;
        border: 1px solid #E0E0E0 !important;

        &.operation-card-40 {
          flex: 0 0 40% !important;
        }

        &.operation-card-60 {
          flex: 0 0 60% !important;
        }

        .operation-title {
          font-size: 14px !important;
          font-weight: bold !important;
          color: #333 !important;
          margin-bottom: 12px !important;
        }

        .operation-result {
          font-size: 13px !important;
          color: #666 !important;
          line-height: 1.5 !important;
        }

        // 水平排列的数据块
        .operation-data-blocks {
          display: flex !important;
          gap: 10px !important;

          .operation-data-block {
            flex: 1 !important;
            padding: 12px !important;
            border-radius: 6px !important;
            text-align: center !important;
            transition: transform 0.2s ease !important;

            &:hover {
              transform: translateY(-2px) !important;
            }

            &.light-red {
              background: #FFE5E5 !important;
              border: 1px solid #FFB3B3 !important;
            }

            &.light-blue {
              background: #E5F3FF !important;
              border: 1px solid #B3D9FF !important;
            }

            .data-name {
              font-size: 12px !important;
              color: #666 !important;
              margin-bottom: 6px !important;
            }

            .data-value {
              font-size: 16px !important;
              font-weight: bold !important;
              color: #333 !important;
            }
          }
        }

        // 垂直排列的数据块
        .operation-data-blocks-vertical {
          display: flex !important;
          flex-direction: column !important;
          gap: 10px !important;

          .operation-data-block {
            padding: 12px !important;
            border-radius: 6px !important;
            text-align: center !important;
            transition: transform 0.2s ease !important;

            &:hover {
              transform: translateY(-2px) !important;
            }

            &.light-red {
              background: #FFE5E5 !important;
              border: 1px solid #FFB3B3 !important;
            }

            &.light-blue {
              background: #E5F3FF !important;
              border: 1px solid #B3D9FF !important;
            }

            .data-name {
              font-size: 12px !important;
              color: #666 !important;
            }

            .data-value {
              font-size: 16px !important;
              font-weight: bold !important;
              color: #333 !important;
            }
          }
        }
      }
    }
  }
}

.history-intelligence-module {
  .history-row {
    margin-bottom: 15px !important;

    &:last-child {
      margin-bottom: 0 !important;
    }

    // 第一行：综合评分样式（与资质与合规情况相同）
    .score-block {
      background: #D6E4FF !important;
      color: #2A52D7 !important;
      padding: 15px !important;
      border-radius: 8px !important;
      text-align: center !important;

      .score-label {
        font-size: 12px !important;
        margin-bottom: 5px !important;
      }

      .score-number {
        font-size: 24px !important;
        font-weight: bold !important;
      }
    }

    .description-block {
      color: #333 !important;
      font-size: 14px !important;
      line-height: 1.6 !important;
    }

    // 四个色块宽度均分，确保并排展示
    &.four-cards-row {
      display: flex !important;
      gap: 15px !important;

      .info-card {
        // flex: 1 !important;
        min-width: 0 !important;
        background: #F8FBFF !important;
        border: 1px solid #E6F0FF !important;
        border-radius: 8px !important;
        padding: 15px !important;

        .card-title {
          font-size: 14px !important;
          font-weight: 500 !important;
          color: #333 !important;
          margin-bottom: 12px !important;
          text-align: center !important;
        }
      }
    }

    // 第二个色块：历史处罚情况
    .punishment-card {
      .punishment-header {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        margin-bottom: 15px !important;
        padding: 0 15px;
        .punishment-count {
          font-size: 19px !important;
          font-weight: bold !important;
          color: #666 !important;
        }

        .punishment-trends {
          display: flex !important;
          flex-direction: column !important;
          gap: 4px !important;

          .trend-summary {
            display: flex !important;
            align-items: center !important;
            gap: 6px !important;
            font-size: 12px !important;

            span:first-child {
              color: #666 !important;
            }

            .trend-up {
              color: #F56C6C !important;
              font-weight: 500 !important;
            }

            .trend-down {
              color: #67C23A !important;
              font-weight: 500 !important;
            }

            .trend-neutral {
              color: #909399 !important;
              font-weight: 500 !important;
            }
          }
        }
      }

      .punishment-item {
        display: flex !important;
        align-items: center !important;
        margin-bottom: 12px !important;
        gap: 8px !important;

        .punishment-chart {
          flex: 0 0 50px !important;

          .mini-donut {
            width: 66px !important;
            height: 66px !important;
            border-radius: 50% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            position: relative !important;

            &.green {
              background: conic-gradient(#67C23A 0deg 180deg, #E5E5E5 180deg 360deg) !important;

              &::before {
                content: '' !important;
                position: absolute !important;
                width: 45px !important;
                height: 45px !important;
                background: white !important;
                border-radius: 50% !important;
                z-index: 1 !important;
              }
            }

            &.red {
              background: conic-gradient(#F56C6C 0deg 180deg, #E5E5E5 180deg 360deg) !important;

              &::before {
                content: '' !important;
                position: absolute !important;
                width: 45px !important;
                height: 45px !important;
                background: white !important;
                border-radius: 50% !important;
                z-index: 1 !important;
              }
            }

            .percent {
              font-size: 10px !important;
              font-weight: bold !important;
              color: #333 !important;
              position: relative !important;
              z-index: 2 !important;
            }
          }

          // 放大版本的环状图
          .mini-donut-large {
            width: 66px !important;
            height: 66px !important;
            border-radius: 50% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            position: relative !important;

            &.green {
              background: conic-gradient(#67C23A 0deg 90deg, #E5E5E5 90deg 360deg) !important;

              &::before {
                content: '' !important;
                position: absolute !important;
                width: 45px !important;
                height: 45px !important;
                background: white !important;
                border-radius: 50% !important;
                z-index: 1 !important;
              }
            }

            &.red {
              background: conic-gradient(#F56C6C 0deg 270deg, #E5E5E5 270deg 360deg) !important;

              &::before {
                content: '' !important;
                position: absolute !important;
                width: 45px !important;
                height: 45px !important;
                background: white !important;
                border-radius: 50% !important;
                z-index: 1 !important;
              }
            }

            // 动态圆环样式
            &.dynamic-donut {
              .donut-inner {
                position: absolute !important;
                top: 50% !important;
                left: 50% !important;
                transform: translate(-50%, -50%) !important;
                width: 45px !important;
                height: 45px !important;
                background: white !important;
                border-radius: 50% !important;
                z-index: 1 !important;
              }
            }

            .percent {
              font-size: 12px !important;
              font-weight: bold !important;
              color: #333 !important;
              position: relative !important;
              z-index: 2 !important;
            }
          }
        }

        .punishment-info {
              flex: 1;
              padding: 0 8px; // 增加左右内边距

              .info-title {
                font-size: 12px;
                color: #666;
                margin-bottom: 6px;
                white-space: nowrap; // 防止换行
                overflow: hidden;
                text-overflow: ellipsis;
              }

              .info-value {
                font-size: 16px; // 稍微增大字体
                color: #333;
                white-space: nowrap; // 防止换行
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }

            .punishment-trend {
              flex: 0 0 80px;

              .trend-item {
                width:62px !important;
                display: flex;
                justify-content: space-between;
                font-size: 11px;
                margin-bottom: 6px;
                line-height:1.4;

                .trend-up {
                  color: #F56C6C;
                }

                .trend-down {
                  color: #67C23A;
                }

                .trend-neutral {
                  color: #909399;
                }
              }
            }
      }
    }

    // 第三个色块：安全检查
    .security-check-card {
      .check-stats {
        display: grid !important;
        grid-template-columns: 1fr 1fr !important;
        gap: 10px !important;
        margin-bottom: 15px !important;

        .stat-block {
          padding: 9px 10px !important;
          border-radius: 8px !important;
          text-align: center !important;
          // color: white !important;
          // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
          // transition: transform 0.2s ease !important;

          &:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
          }

          .stat-name {
            font-size: 12px !important;
            margin-bottom: 6px !important;
            opacity: 0.9 !important;
            line-height: 1.3 !important;
          }

          .stat-rate {
            font-size: 18px !important;
            font-weight: bold !important;
          }
        }
      }

      .frequent-issues {
        .issues-title {
          font-size: 13px !important;
          color: #666 !important;
          margin-bottom: 8px !important;
        }

        .issues-list {
          display: flex !important;
          gap: 10px !important;

          .issue-item {
            flex: 1 !important;
            padding: 5px 2px !important;
            border-radius: 6px !important;
            text-align: center !important;
            color: #555 !important;
            font-size: 12px !important;
            font-weight: 500 !important;
            border: 1px solid #D0D0D0 !important;
            transition: all 0.2s ease !important;

            &:hover {
              background-color: #DADADA !important;
              transform: translateY(-1px) !important;
            }
          }
        }
      }
    }

    // 第四个色块：重大安全事件
    .major-events-card {
      .total-count {
        font-size: 16px !important;
        color: #333 !important;
        text-align: center !important;
      }

      .event-bars {
        .event-bar {
          padding: 6px 12px 8px 10px;
          background-color: #E9F1FF;
          margin-bottom: 4px;
          &:last-child {
            margin-bottom: 0 !important;
          }

          .bar-info {
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            margin-bottom: 5px !important;
            font-size: 12px !important;

            .bar-name {
              color: #333 !important;
            }

            .bar-value {
              color: #333 !important;
            }

            .bar-trend {
              font-size: 11px !important;

              &.trend-up {
                color: #F56C6C !important;
              }

              &.trend-down {
                color: #67C23A !important;
              }

              &.trend-neutral {
                color: #909399 !important;
              }
            }
          }

          .bar-container {
            overflow: hidden !important;
            position: relative !important;
             width: 96%;
            margin-left: 2%;
            height: 12px;
            background: #C3CDD9;
            border-radius: 6px;
            .bar-fill {
              height: 100% !important;
              border-radius: 6px !important;
              transition: width 0.3s ease !important;
              position: relative !important;

              // 添加渐变效果使柱状图更明显
              &[style*="#FC6A7A"] {
                background: linear-gradient(90deg, #FC6A7A 0%, #FF8A9B 100%) !important;
                box-shadow: 0 2px 4px rgba(252, 106, 122, 0.3) !important;
              }

              &[style*="#58DBD3"] {
                background: linear-gradient(90deg, #58DBD3 0%, #7EE8DD 100%) !important;
                box-shadow: 0 2px 4px rgba(88, 219, 211, 0.3) !important;
              }

              &[style*="#434CBD"] {
                background: linear-gradient(90deg, #434CBD 0%, #6366F1 100%) !important;
                box-shadow: 0 2px 4px rgba(67, 76, 189, 0.3) !important;
              }
            }
          }
        }
      }
    }
  }
}

.score-block {
    background: #D6E4FF !important;
    color: #2A52D7 !important;
    padding: 15px !important;
    border-radius: 8px !important;
    text-align: center !important;
    box-shadow: 0 2px 8px rgba(42, 82, 215, 0.2) !important;
    transition: transform 0.2s ease !important;

    &:hover {
      transform: translateY(-2px) !important;
      box-shadow: 0 4px 12px rgba(42, 82, 215, 0.3) !important;
    }

    .score-label {
      font-size: 14px !important;
      margin-bottom: 8px !important;
      font-weight: 500 !important;
      color: #2A52D7 !important;
    }

    .score-number {
      font-size: 28px !important;
      font-weight: bold !important;
      color: #2A52D7 !important;
      line-height: 1 !important;
    }
}

.description-block {
    padding: 15px !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    box-shadow: 0 2px 8px rgba(42, 82, 215, 0.2) !important;
    transition: transform 0.2s ease !important;
    flex-grow: 1;
    &:hover {
      transform: translateY(-2px) !important;
      box-shadow: 0 4px 12px rgba(42, 82, 215, 0.3) !important;
    }
}
.total-count-header {
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            .total-count {
              font-size: 16px;
              color: #333;
              text-align: center;
            }

            .total-trends {
              display: flex;
              justify-content: center;
              gap: 20px;
              font-size: 12px;

              .trend-item {
                display: flex;
                align-items: center;
                gap: 4px;

                .trend-up {
                  color: #ff4444 !important;
                }

                .trend-down {
                  color: #00aa00 !important;
                }
              }
            }
}
.card-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 12px;
    text-align: center;
}

// 第二个色块：历史处罚情况
.punishment-card {
    .punishment-count {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    text-align: center;
    margin-bottom: 15px;
    }

    .punishment-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    gap: 8px;

    .punishment-chart {
        flex: 0 0 40px;

        .mini-donut {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        &.green {
            background: conic-gradient(#67C23A 0deg 180deg, #E5E5E5 180deg 360deg);

            &::before {
            content: '';
            position: absolute;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            z-index: 1;
            }
        }

        &.red {
            background: conic-gradient(#F56C6C 0deg 180deg, #E5E5E5 180deg 360deg);

            &::before {
            content: '';
            position: absolute;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            z-index: 1;
            }
        }



        .percent {
            font-size: 10px;
            font-weight: bold;
            color: #333;
            position: relative;
            z-index: 2;
        }
        }
    }

    
    }
}