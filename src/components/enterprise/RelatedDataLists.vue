<template>
  <div class="related-data-lists">


    <!-- 动态渲染各种关联列表 -->
    <div v-for="listConfig in activeListConfigs" :key="listConfig.field" class="list-section">
      <el-divider>
        <span class="divider-title">{{ listConfig.title }}</span>
      </el-divider>
      
      <div class="list-container">
       

        <!-- 正常表格 -->
        <el-table
          :data="listData[listConfig.field] || []"
          :loading="loadingStates[listConfig.field]"
          stripe
          border
          fit
          empty-text="暂无数据"
          :header-cell-style="{ backgroundColor: '#ebf1ff', color: '#333', textAlign: 'left' }"
          :cell-style="{ textAlign: 'left' }"
          class="auto-width-table"
        >
          <el-table-column
            v-for="column in getVisibleColumns(listConfig)"
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            :formatter="column.formatter"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <!-- 状态显示 -->
              <span v-if="column.slot === 'status'" :style="{ color: getStatusColor(scope.row[column.prop]) }">
                {{ scope.row[column.prop] || '未知' }}
              </span>

              <!-- 单张图片显示 -->
              <el-image
                v-else-if="column.slot === 'image'"
                :src="scope.row[column.prop]"
                :preview-src-list="scope.row[column.prop] ? [scope.row[column.prop]] : []"
                fit="cover"
                style="width: 50px; height: 50px;"
                :hide-on-click-modal="true"
              >
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>

              <!-- 多张图片显示 -->
              <div v-else-if="column.slot === 'multiImage'" class="multi-image-container">
                <image-viewer
                  v-if="scope.row[column.prop]"
                  :src="scope.row[column.prop]"
                  :width="50"
                  :height="50"
                  :title="getImageTitle(scope.row, column)"
                />
                <span v-else>-</span>
              </div>

              <!-- 电子屏名称/位置 -->
              <div v-else-if="column.slot === 'screenNameAddress'" class="multi-line-text">
                <div>
                  <span
                    v-if="isLinkColumn(listConfig, 'screenName')"
                    class="link-text"
                    @click="handleLinkClick(listConfig, scope.row)"
                  >
                    {{ scope.row.screenName || '-' }}
                  </span>
                  <span v-else>{{ scope.row.screenName || '-' }}</span>
                </div>
                <div class="sub-text">{{ scope.row.address || '-' }}</div>
              </div>

              <!-- 区域/派出所 -->
              <div v-else-if="column.slot === 'areaPolice'" class="multi-line-text">
                <div>{{ scope.row.areaName || '-' }}</div>
                <div class="sub-text">{{ scope.row.policeName || '-' }}</div>
              </div>

              <!-- 屏幕尺寸/类型 -->
              <div v-else-if="column.slot === 'sizeType'" class="multi-line-text">
                <div>{{ scope.row.size ? scope.row.size + ' m²' : '-' }}</div>
                <div class="sub-text">
                  <dict-tag :options="dict.type.screen_type" :value="scope.row.screenType"/>
                </div>
              </div>

              <!-- 责任单位 -->
              <div v-else-if="column.slot === 'enterprise'">
                {{ scope.row.enterprise ? scope.row.enterprise.enterpriseName : '-' }}
              </div>

              <!-- 责任单位联系人 -->
              <div v-else-if="column.slot === 'enterpriseContact'" class="multi-line-text">
                <div>{{ scope.row.enterprise ? scope.row.enterprise.principal : '-' }}</div>
                <div class="sub-text">{{ scope.row.enterprise ? scope.row.enterprise.principalPhone : '-' }}</div>
              </div>

              <!-- 重点区域标签列表 -->
              <div v-else-if="column.slot === 'keyareaList'">
                <template v-if="scope.row.keyareaList && scope.row.keyareaList.length > 0">
                  <el-tag
                    v-for="(item, index) in scope.row.keyareaList"
                    :key="index"
                    type="danger"
                    size="mini"
                    style="margin: 1px 2px;"
                    @click="showKeyarea(item.keyareaId)"
                  >
                    {{ item.keyareaName }}
                  </el-tag>
                </template>
                <span v-else>-</span>
              </div>

              <!-- 专项标签列表 -->
              <div v-else-if="column.slot === 'specialList'">
                <template v-if="scope.row.specialList && scope.row.specialList.length > 0">
                  <el-tag
                    v-for="(item, index) in scope.row.specialList"
                    :key="index"
                    type="danger"
                    size="mini"
                    style="margin: 1px 2px;"
                    @click="showSpecial(item.specialId)"
                  >
                    {{ item.specialName }}
                  </el-tag>
                </template>
                <span v-else>-</span>
              </div>

              <!-- 归属场所信息 -->
              <div v-else-if="column.slot === 'siteInfo'" class="multi-line-text">
                <template v-if="scope.row.site && scope.row.site.siteCode !== '0'">
                  <div>{{ scope.row.site.siteName || '-' }}</div>
                  <div class="sub-text">{{ scope.row.site.siteAddress || '-' }}</div>
                </template>
                <span v-else>-</span>
              </div>

              <!-- 连网系统信息 -->
              <div v-else-if="column.slot === 'netsystemInfo'">
                <template v-if="scope.row.netsystemId && scope.row.netsystemId !== '0'">
                  {{ scope.row.netsystem ? scope.row.netsystem.netsystemName : '-' }}
                </template>
                <span v-else>-</span>
              </div>

              <!-- 检查状态 -->
              <span v-else-if="column.slot === 'inspectionStatus'" :style="{ color: getInspectionStatusColor(scope.row[column.prop]) }">
                {{ scope.row[column.prop] || '-' }}
              </span>

              <!-- 等保备案 - 保护等级 -->
              <dict-tag v-else-if="column.slot === 'protectionLevel'" :options="dict.type.lp_protection_level" :value="scope.row.protectionLevel"/>

              <!-- 等保备案 - 数据来源 -->
              <dict-tag v-else-if="column.slot === 'dataSource'" :options="dict.type.lp_data_source" :value="scope.row.dataSource"/>

              <!-- 等保备案 - 专家评审状态 -->
              <dict-tag v-else-if="column.slot === 'expertReviewStatus'" :options="dict.type.lp_expert_review_status" :value="scope.row.expertReviewStatus"/>

              <!-- 等保备案 - 状态 -->
              <dict-tag v-else-if="column.slot === 'lpStatus'" :options="dict.type.lp_status" :value="scope.row.status"/>

              <!-- 网站备案 - 备案主体 -->
              <dict-tag v-else-if="column.slot === 'filingSubject'" :options="dict.type.ws_filing_subject" :value="scope.row.filingSubject"/>

              <!-- 网站备案 - 网站语种 -->
              <dict-tag v-else-if="column.slot === 'language'" :options="dict.type.ws_language" :value="scope.row.language"/>

              <!-- 网站备案 - 网站分级 -->
              <dict-tag v-else-if="column.slot === 'websiteRating'" :options="dict.type.ws_website_rating" :value="scope.row.websiteRating"/>

              <!-- APP - 运行平台 -->
              <dict-tag v-else-if="column.slot === 'runPlatform'" :options="dict.type.ws_run_platform" :value="scope.row.runPlatform"/>

              <!-- APP - APP分级 -->
              <dict-tag v-else-if="column.slot === 'appRating'" :options="dict.type.ws_website_rating" :value="scope.row.appRating"/>

              <!-- 小程序 - 托管服务器信息 -->
              <dict-tag v-else-if="column.slot === 'serverType'" :options="dict.type.ws_server_type" :value="scope.row.serverType"/>

              <!-- 小程序 - 小程序分级 -->
              <dict-tag v-else-if="column.slot === 'appletRating'" :options="dict.type.ws_website_rating" :value="scope.row.appletRating"/>

              <!-- APP状态 -->
              <dict-tag v-else-if="column.slot === 'appStatus'" :options="dict.type.ws_status" :value="scope.row.status"/>

              <!-- 小程序状态 -->
              <dict-tag v-else-if="column.slot === 'appletStatus'" :options="dict.type.ws_status" :value="scope.row.status"/>

              <!-- 日期时间格式化 -->
              <span v-else-if="column.slot === 'dateTime'">
                {{ formatDateTime(scope.row[column.prop]) }}
              </span>

              <!-- 安全设备提示 -->
              <el-tooltip
                v-else-if="column.slot === 'safetyEquipment'"
                :content="formatSafetyEquipmentTooltip(scope.row[column.prop])"
                placement="top"
                effect="dark"
              >
                <span class="equipment-text">{{ formatSafetyEquipment(scope.row[column.prop]) }}</span>
              </el-tooltip>

              <!-- 链接文本 -->
              <span
                v-else-if="isLinkColumn(listConfig, column.prop)"
                class="link-text"
                @click="handleLinkClick(listConfig, scope.row)"
              >
                {{ scope.row[column.prop] || '-' }}
              </span>

              <!-- 默认文本显示 -->
              <span v-else>{{ scope.row[column.prop] || '-' }}</span>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <el-pagination
          v-if="(listData[listConfig.field] || []).length > 0"
          @size-change="(size) => handleSizeChange(listConfig.field, size)"
          @current-change="(page) => handleCurrentChange(listConfig.field, page)"
          :current-page="(pagination[listConfig.field] && pagination[listConfig.field].currentPage) || 1"
          :page-sizes="[10, 20, 50]"
          :page-size="(pagination[listConfig.field] && pagination[listConfig.field].pageSize) || 10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="(pagination[listConfig.field] && pagination[listConfig.field].total) || 0"
          style="margin-top: 20px; text-align: center;"
        />
      </div>
    </div>

    
  </div>
</template>

<script>
import {
  getScreenList,
  getNetbarList,
  getLevelProtectList,
  getWebsiteList,
  getAppList,
  getAppletList,
  getOperatorList,
  getWifiSiteList
} from '@/api/enterprise'

import ImageViewer from '@/components/ImageViewer'

export default {
  name: 'RelatedDataLists',
  components: {
    ImageViewer
  },
  dicts: [
    'screen_type',
    'lp_protection_level',
    'lp_data_source',
    'lp_expert_review_status',
    'lp_status',
    'ws_filing_subject',
    'ws_language',
    'ws_website_rating',
    'ws_run_platform',
    'ws_server_type',
    'ws_status'
  ], // 引入字典数据
  props: {
    enterpriseData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      listData: {},
      loadingStates: {},
      pagination: {},

      // 网吧列表列可见性配置
      netbarColumnVisibility: {
        siteCode: true,
        siteName: true,
        businessStatusLabel: true,
        areaCodeLabel: true,
        policeStationLabel: true,
        legalPhone: true,
        address: true
      },
      
      // 列表配置映射
      listConfigs: {
        // 关联电子屏列表
        screenNum: {
          field: 'screenNum',
          title: '关联电子屏列表',
          icon: 'el-icon-monitor',
          apiMethod: getScreenList,
          linkKey: 'screenName',
          linkAddress: '/data-management/screen/detail/{screenId}', // "/data-management/screen/detail/"+row.screenId
          paramKey: 'creditCode',
          columns: [
            { prop: 'screenName', label: '电子屏名称/位置', minWidth: 220, slot: 'screenNameAddress' },
            { prop: 'policeName', label: '所属派出所', width: 180, slot: 'areaPolice' },
            { prop: 'images', label: '照片', width: 80, slot: 'multiImage' },
            { prop: 'screenType', label: '屏幕尺寸/类型', width: 120, slot: 'sizeType' },
            { prop: 'enterpriseName', label: '责任单位', width: 150, slot: 'enterprise' },
            { prop: 'legalPerson', label: '责任单位联系人', width: 140, slot: 'enterpriseContact' },
            { prop: 'keyareaId', label: '重点区域', width: 100, slot: 'keyareaList' },
            { prop: 'specialId', label: '专项', width: 240, slot: 'specialList' },
            { prop: 'siteName', label: '归属场所', width: 120, slot: 'siteInfo' },
            { prop: 'netsystemName', label: '连网系统', width: 120, slot: 'netsystemInfo' },
            { prop: 'inspectionNum', label: '近来三月检查次数', width: 120, slot: 'inspectionStatus' }
          ]
        },
        // 网吧列表
        netBarNum: {
          field: 'netBarNum',
          title: '网吧列表',
          icon: 'el-icon-monitor',
          apiMethod: getNetbarList,
          paramKey: 'creditCode',
          linkKey: 'siteName',
          linkAddress: '/data-management/netbar/detail/{siteCode}', //  "/data-management/netbar/detail/" + row.siteCode
          columns: [
            { prop: 'siteCode', label: '场所编号', width: 120,  },
            { prop: 'siteName', label: '场所名称', minWidth: 150},
            { prop: 'businessStatusLabel', label: '营业状态', width: 100},
            { prop: 'areaCodeLabel', label: '所属区域', width: 100 },
            { prop: 'policeStationLabel', label: '所属派出所', width: 120},
             { prop: 'legalPerson', label: '法人', width: 120 },
            { prop: 'legalPhone', label: '联系电话', width: 120},
            { prop: 'address', label: '地址', minWidth: 200}
          ]
        },
        // 等保备案列表
        levelProjectNum: {
          field: 'levelProjectNum',
          title: '等保备案列表',
          icon: 'el-icon-lock',
          apiMethod: getLevelProtectList,
          paramKey: 'creditCode',
          columns: [
            { prop: 'networkName', label: '网络名称', minWidth: 150 },
            { prop: 'unitName', label: '单位名称', minWidth: 150 },
            { prop: 'contactName', label: '联系人姓名', width: 120 },
            { prop: 'contactMobilePhone', label: '移动电话', width: 120 },
            { prop: 'recordCertificateNumber', label: '备案证明编号', width: 150 },
            { prop: 'protectionLevel', label: '保护等级', width: 100, slot: 'protectionLevel' },
            { prop: 'systemNumber', label: '系统编号', width: 120 },
            { prop: 'filingDate', label: '备案日期', width: 120 },
            { prop: 'businessType', label: '业务类型', width: 120 },
            { prop: 'dataSource', label: '数据来源', width: 120, slot: 'dataSource' },
            { prop: 'expertReviewStatus', label: '专家评审情况', width: 140, slot: 'expertReviewStatus' },
            { prop: 'statusLabel', label: '状态', width: 100, slot: 'lpStatus' }
          ]
        },
        // 网站备案列表
        websiteNum: {
          field: 'websiteNum',
          title: '网站备案列表',
          icon: 'el-icon-link',
          apiMethod: getWebsiteList,
          paramKey: 'creditCode',
          columns: [
            { prop: 'filingNumber', label: '工信部备案号', minWidth: 150 },
            { prop: 'websiteName', label: '网络名称', minWidth: 150 },
            { prop: 'masterDomain', label: '主域名', minWidth: 120 },
            { prop: 'filingSubject', label: '备案主体', minWidth: 120, slot: 'filingSubject' },
            { prop: 'accessServiceProvider', label: '接入服务商', minWidth: 120 },
            { prop: 'accessMethod', label: '接入方式', minWidth: 120 },
            { prop: 'domainServiceProvider', label: '域名服务商', minWidth: 120 },
            { prop: 'language', label: '网站语种', minWidth: 120, slot: 'language' },
            { prop: 'websiteRating', label: '网站分级', minWidth: 120, slot: 'websiteRating' },
            { prop: 'masterDomain', label: '网站开通日期', minWidth: 120 },
            { prop: 'certificateDate', label: '域名证书有效期', minWidth: 120 },
          ]
        },
        // APP列表
        appNum: {
          field: 'appNum',
          title: 'APP列表',
          icon: 'el-icon-mobile-phone',
          apiMethod: getAppList,
          paramKey: 'creditCode',
          columns: [
            { prop: 'registerNum', label: '注册登记号', width: 150 },
            { prop: 'appName', label: 'APP名称', minWidth: 150 },
            { prop: 'createTime', label: '创建时间', width: 120, slot: 'dateTime' },
            { prop: 'logo', label: 'LOGO', width: 80, slot: 'image' },
            { prop: 'runPlatform', label: '运行平台', width: 100, slot: 'runPlatform' },
            { prop: 'marketValue', label: '市值', width: 100 },
            { prop: 'monthUserNumber', label: '月活用户数量', width: 120 },
            { prop: 'registerUserNumber', label: '注册用户总量', width: 120 },
            { prop: 'updateDate', label: '更新时间', width: 120, slot: 'dateTime' },
            { prop: 'appRating', label: 'APP分级', width: 100, slot: 'appRating' },
            { prop: 'safeManagerName', label: '安全负责人姓名', width: 140 },
            { prop: 'safeManagerPhone', label: '安全负责人手机号', width: 150 },
             { prop: 'emergencyContactName', label: '应急联络人姓名', width: 140 },
            { prop: 'emergencyContactPhone', label: '应急联络人手机号', width: 150 },
            { prop: 'status', label: '状态', width: 100, slot: 'appStatus' } // 下一行
          //    <template slot-scope="scope">
          //   <dict-tag :options="dict.type.ws_status" :value="scope.row.status"/>
          // </template>
           
          ]
        },
        // 小程序列表
        appletNum: {
          field: 'appletNum',
          title: '小程序列表',
          icon: 'el-icon-mobile',
          apiMethod: getAppletList,
          paramKey: 'creditCode',
          columns: [
            { prop: 'registerNum', label: '注册登记号', width: 150 },
            { prop: 'appletName', label: '小程序名称', minWidth: 150 },
            { prop: 'openDate', label: '创建时间', width: 120, slot: 'dateTime' },
            { prop: 'ipAdipAddressdress', label: 'IP地址', width: 120 },
            { prop: 'filingNumber', label: '工信部备案', width: 120 },
            { prop: 'serverType', label: '托管服务器信息', width: 150, slot: 'serverType' },
            { prop: 'monthUserNumber', label: '月活用户数量', width: 120 },
            { prop: 'registerUserNumber', label: '注册用户总量', width: 120 },
            { prop: 'appletRating', label: '小程序分级', width: 120, slot: 'appletRating' },
            { prop: 'safeManagerName', label: '安全负责人姓名', width: 140 },
            { prop: 'safeManagerPhone', label: '安全负责人手机号', width: 150 },
            { prop: 'emergencyContactName', label: '应急联络人姓名', width: 140 },
            { prop: 'status', label: '状态', width: 100, slot: 'appletStatus' } //下一行
            //  <template slot-scope="scope">
            //   <dict-tag :options="dict.type.ws_status" :value="scope.row.status"/>
            // </template>
          ]
        },
        // 运营商管理列表
        operatorNum: {
          field: 'operatorNum',
          title: '运营商管理列表',
          icon: 'el-icon-connection',
          apiMethod: getOperatorList,
          paramKey: 'creditCode',
          columns: [
            // 提供服务类型这里是数组如下所示：
            { prop: 'provideServiceTypeLabel', label: '提供服务类型', width: 240},
            { prop: 'infoSecurityName', label: '信息安全负责人', width: 180 },
            { prop: 'infoSecurityPhone', label: '信息安全手机号码', width: 150 },
            { prop: 'netSecurityName', label: '网络安全负责人', width: 180 },
            { prop: 'netSecurityPhone', label: '网络安全手机号码', width: 180 },
            { prop: 'netSecurityWorkName', label: '网络安全工作负责人', width: 180 },
            { prop: 'netSecurityWorkPhone', label: '网络安全手机号码', width: 180 },
            { prop: 'dutyPhone', label: '值班电话', width: 180 }
          ]
        },
        // 非经营场所列表
        wifiNum: {
          field: 'wifiNum',
          title: '非经营场所列表',
          icon: 'el-icon-office-building',
          apiMethod: getWifiSiteList,
          paramKey: 'org_code',
          columns: [
            { prop: 'serviceCode', label: '场所编码', width: 120 },
            { prop: 'serviceName', label: '场所名称', minWidth: 150 },
            { prop: 'shecaiCode', label: '社采编码', width: 120 },
            { prop: 'principal', label: '法人姓名', width: 120 },
            { prop: 'principalTel', label: '法人电话', width: 120 },
            { prop: 'businessNature', label: '场所性质', width: 120 },// 下一行
        //      <template slot-scope="scope">
        //   <dict-tag :options="dict.type.wifi_business_nature" :value="scope.row.businessNature"/>
        // </template>
            { prop: 'provinceCode', label: '所属省', width: 100 },
            { prop: 'cityCode', label: '所属城市', width: 100 },
            { prop: 'areaName', label: '所属分区', width: 100 },
            { prop: 'policeName', label: '派出所', width: 120 },
            { prop: 'startTime', label: '营业开始时间', width: 140 },
            { prop: 'endTime', label: '营业结束时间', width: 140 }
          ]
        }
      }
    }
  },
  mounted() {
    console.log('RelatedDataLists mounted - enterpriseData:', this.enterpriseData)
    console.log('RelatedDataLists mounted - activeListConfigs:', this.activeListConfigs)
  },
  computed: {
    // 根据企业数据中的标签字段，筛选出需要显示的列表配置
    activeListConfigs() {
      const configs = []
      Object.keys(this.listConfigs).forEach(field => {
        const value = this.enterpriseData[field]
        if (value && value > 0) {
          configs.push(this.listConfigs[field])
        }
      })
      return configs
    }
  },
  watch: {
    enterpriseData: {
      handler(newData) {
        if (newData && newData.creditCode) {
          this.initializeData()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 初始化数据
    initializeData() {
      this.activeListConfigs.forEach(config => {
        this.initializePagination(config.field)
        this.fetchListData(config)
      })
    },

    // 初始化分页数据
    initializePagination(field) {
      this.$set(this.pagination, field, {
        currentPage: 1,
        pageSize: 10,
        total: 0
      })
      this.$set(this.loadingStates, field, false)
      this.$set(this.listData, field, [])
    },

    // 获取列表数据
    async fetchListData(config) {
      const { field, apiMethod, paramKey } = config
      
      this.$set(this.loadingStates, field, true)
      
      try {
        const params = {
          pageNum: this.pagination[field].currentPage,
          pageSize: this.pagination[field].pageSize
        }

        // 根据不同的参数键设置请求参数
        if (paramKey === 'org_code') {
          params[paramKey] = this.enterpriseData.creditCode
        } else {
          params[paramKey] = this.enterpriseData.creditCode
        }
        
        const response = await apiMethod(params)
        
        if (response.code === 200) {
          // 处理嵌套的数据结构
          let data = []
          if (response.data && response.data.rows) {
            // 运营商等接口返回的嵌套结构
            data = response.data.rows
          } else if (response.rows) {
            // 其他接口返回的直接结构
            data = response.rows
          } else if (response.data && Array.isArray(response.data)) {
            // 数据直接在data字段中
            data = response.data
          } else {
            data = []
          }



          this.$set(this.listData, field, data)
          if (this.pagination[field]) {
            // 处理嵌套结构的总数
            let total = 0
            if (response.data && response.data.total !== undefined) {
              total = response.data.total
            } else if (response.total !== undefined) {
              total = response.total
            } else {
              total = data.length
            }
            this.$set(this.pagination[field], 'total', total)
          }
        } else {
          this.$message.error(`获取${config.title}失败: ${response.msg || '未知错误'}`)
          this.$set(this.listData, field, [])
        }
      } catch (error) {
        console.error(`获取${config.title}失败:`, error)
        this.$message.error(`获取${config.title}失败，请稍后重试`)
        this.$set(this.listData, field, [])
      } finally {
        this.$set(this.loadingStates, field, false)
      }
    },

    // 处理页码变化
    handleCurrentChange(field, page) {
      if (this.pagination[field]) {
        this.$set(this.pagination[field], 'currentPage', page)
        const config = this.listConfigs[field]
        this.fetchListData(config)
      }
    },

    // 处理每页条数变化
    handleSizeChange(field, size) {
      if (this.pagination[field]) {
        this.$set(this.pagination[field], 'pageSize', size)
        this.$set(this.pagination[field], 'currentPage', 1)
        const config = this.listConfigs[field]
        this.fetchListData(config)
      }
    },

    // 获取状态颜色
    getStatusColor(status) {
      const statusColors = {
        '正常': '#67C23A',
        '营业': '#67C23A',
        '有效': '#67C23A',
        '通过': '#67C23A',
        '异常': '#F56C6C',
        '停业': '#F56C6C',
        '无效': '#F56C6C',
        '未通过': '#F56C6C',
        '待审核': '#E6A23C',
        '审核中': '#E6A23C'
      }
      return statusColors[status] || '#909399'
    },

    // 格式化安全设备列表显示
    formatSafetyEquipment(equipmentList) {
      // 严格检查数据类型
      if (!equipmentList) {
        return '暂无'
      }

      // 如果不是数组，尝试转换或返回默认值
      if (!Array.isArray(equipmentList)) {
        return '暂无'
      }

      if (equipmentList.length === 0) {
        return '暂无'
      }

      try {
        const providers = equipmentList
          .filter(item => item && typeof item === 'object')
          .map(item => item.deviceProvider)
          .filter(provider => provider && typeof provider === 'string')

        if (providers.length === 0) {
          return '暂无'
        }

        const displayText = providers.join('、')

        // 如果文本过长，截断并添加省略号
        if (displayText.length > 15) {
          return displayText.substring(0, 15) + '...'
        }

        return displayText
      } catch (error) {
        return '暂无'
      }
    },

    // 格式化安全设备列表悬浮提示
    formatSafetyEquipmentTooltip(equipmentList) {
      // 严格检查数据类型
      if (!equipmentList) {
        return '暂无安全设备信息'
      }

      if (!Array.isArray(equipmentList)) {
        return '暂无安全设备信息'
      }

      if (equipmentList.length === 0) {
        return '暂无安全设备信息'
      }

      try {
        const tooltipItems = equipmentList
          .filter(item => item && typeof item === 'object')
          .map(item => {
            const provider = (item.deviceProvider && typeof item.deviceProvider === 'string')
              ? item.deviceProvider : '未知厂商'
            const model = (item.deviceModel && typeof item.deviceModel === 'string')
              ? item.deviceModel : '未知型号'
            return `${provider} (${model})`
          })

        return tooltipItems.length > 0 ? tooltipItems.join('\n') : '暂无安全设备信息'
      } catch (error) {
        return '暂无安全设备信息'
      }
    },

    // 判断是否为链接列
    isLinkColumn(listConfig, columnProp) {
      return listConfig.linkKey && listConfig.linkKey === columnProp
    },

    // 处理链接点击
    handleLinkClick(listConfig, rowData) {
      if (listConfig.linkAddress) {
        // 如果有配置链接地址，则跳转
        let url = listConfig.linkAddress

        // 替换URL中的动态参数
        // 支持 {screenId}, {siteCode}, {id} 等格式
        url = url.replace(/\{(\w+)\}/g, (_, key) => {
          return rowData[key] || ''
        })

        // 使用路由跳转而不是新窗口打开
        this.$router.push(url)
      } else {
        // 如果没有配置链接地址，可以触发自定义事件
        this.$emit('link-click', {
          type: listConfig.field,
          data: rowData
        })
      }
    },



    // 获取检查状态颜色
    getInspectionStatusColor(status) {
      if (!status) return '#909399'

      // 如果是数字，转换为状态文字
      if (typeof status === 'number') {
        if (status === 1) return '#67C23A' // 正常 - 绿色
        if (status === 2) return '#F56C6C' // 异常 - 红色
      }

      // 如果是文字状态
      const statusColors = {
        '正常': '#67C23A',
        '异常': '#F56C6C',
        '待检查': '#E6A23C',
        '检查中': '#E6A23C'
      }

      return statusColors[status] || '#909399'
    },

    // 显示重点区域详情
    showKeyarea(keyareaId) {
      console.log('显示重点区域详情:', keyareaId)
      // 这里可以触发事件或跳转到详情页
      this.$emit('show-keyarea', keyareaId)
    },

    // 显示专项详情
    showSpecial(specialId) {
      console.log('显示专项详情:', specialId)
      // 这里可以触发事件或跳转到详情页
      this.$emit('show-special', specialId)
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'

      // 如果是时间戳
      if (typeof dateTime === 'number') {
        return this.parseTime(dateTime, '{y}-{m}-{d}')
      }

      // 如果是日期字符串
      if (typeof dateTime === 'string') {
        const date = new Date(dateTime)
        if (isNaN(date.getTime())) return dateTime // 如果无法解析，返回原值
        return this.parseTime(date, '{y}-{m}-{d}')
      }

      // 如果是Date对象
      if (dateTime instanceof Date) {
        return this.parseTime(dateTime, '{y}-{m}-{d}')
      }

      return dateTime
    },

    // 时间解析工具方法
    parseTime(time, pattern) {
      if (!time) return ''

      const date = new Date(time)
      if (isNaN(date.getTime())) return time

      const format = {
        y: date.getFullYear(),
        m: String(date.getMonth() + 1).padStart(2, '0'),
        d: String(date.getDate()).padStart(2, '0'),
        h: String(date.getHours()).padStart(2, '0'),
        i: String(date.getMinutes()).padStart(2, '0'),
        s: String(date.getSeconds()).padStart(2, '0')
      }

      return pattern.replace(/{([ymdhis])+}/g, (result, key) => {
        return format[key] || result
      })
    },

    // 获取可见的列配置
    getVisibleColumns(listConfig) {
      if (!listConfig || !listConfig.columns) return []

      // 如果是网吧列表，需要检查列可见性
      if (listConfig.field === 'netBarNum') {
        return listConfig.columns.filter(column => {
          if (column.visible) {
            return this.netbarColumnVisibility[column.visible] !== false
          }
          return true
        })
      }

      // 其他列表直接返回所有列
      return listConfig.columns
    },

    // 获取图片标题
    getImageTitle(row, column) {
      // 根据不同的列表类型返回不同的标题
      if (column.prop === 'images') {
        return row.screenName ? `${row.screenName} - 照片` : '电子屏照片'
      }
      return column.label || '图片预览'
    }
  }
}
</script>

<style lang="scss" scoped>
.related-data-lists {
  .list-section {
    margin-bottom: 30px;

    .divider-title {
      font-size: 16px;
      font-weight: 600;
    }

    // 调整分割线样式，使标题居左显示
    ::v-deep .el-divider {
      .el-divider__text {
        left: 7%;
        // transform: none;
        // background-color: #fff;
        // padding: 0 20px;
      }
    }
    
    .list-container {
      margin-top: 20px;

      // 表格自适应宽度
      .auto-width-table {
        width: auto !important;
        min-width: 100%;

        // 当列数较少时，表格不占满整个容器
        ::v-deep .el-table__body-wrapper {
          overflow-x: auto;
        }

        // 表格边框样式
        ::v-deep .el-table {
          border: 1px solid #EBEEF5;
          border-radius: 4px;
        }

        // 表头和单元格左对齐
        ::v-deep .el-table th,
        ::v-deep .el-table td {
          text-align: left !important;

          .cell {
            text-align: left !important;
          }
        }
      }

      .el-table {
        border-radius: 4px;
        overflow: hidden;
      }
    }
  }
  
  .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
    font-size: 20px;
  }

  .equipment-text {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;

    &:hover {
      color: #409EFF;
    }
  }

  .link-text {
    color: #409EFF;
    cursor: pointer;
    text-decoration: none;

    &:hover {
      color: #66b1ff;
      text-decoration: underline;
    }
  }

  // 多行文本样式
  .multi-line-text {
    line-height: 1.4;

    .sub-text {
      font-size: 12px;
      margin-top: 2px;
    }
  }

  // 多图片容器样式
  .multi-image-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    align-items: center;

    .el-image {
      border-radius: 4px;
      border: 1px solid #dcdfe6;
    }
  }

  // 标签样式调整
  .el-tag {
    margin: 1px 2px 1px 0;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
}
</style>
