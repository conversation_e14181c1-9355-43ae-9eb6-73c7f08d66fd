
<!-- 离线地图采集经纬度 -->
<template>
    <el-dialog :close-on-click-modal="false"  :title="title" :visible.sync="visible"  :width="modalObj.width" :before-close="modalClose" append-to-body   >
    <!-- <el-dialog :visible.sync="visible" :width="modalObj.width" :modal='modalObj.modal' append-to-body :before-close="modalClose"
        :close-on-click-modal ="false" :close-on-press-escape="false" :modal-append-to-body="false" class="dialogClass" >   -->
        <el-form ref="form" :model="form"  :rules="rules" label-width="100px">
            <el-form-item label="地址：" prop="address">
                <el-input v-model="form.address"   placeholder="请输入地址" ></el-input>
            </el-form-item>
            <el-form-item>
                <div id="divMapLocation"></div>
            </el-form-item>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="经度" prop="longitude">
                        <el-input v-model="form.longitude" autocomplete="off"  placeholder="经度"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="纬度" prop="latitude">
                        <el-input v-model="form.latitude" autocomplete="off"   placeholder="纬度"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
           
        </el-form>
        
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="modalClose">取 消</el-button>
        </div>
    </el-dialog>
</template>

<script>
// import { defineComponent } from '@vue/composition-api'

export default {
    name: 'MapPointDialog',
    props: { 
        visible: {
            type: Boolean,
            default: false
        },
        modalObj: { // 添加、修改时 
            type: Object,
            default: false
        } 
    }, 
    data() {

        return {
            // 弹出层标题
            title: '',  
            mapB: null,
            localSearch : null, // 
            mk: null,// 

            form:{
                address:'',
                longitude:'',// 经度
                latitude:'', // 纬度  
            },
            // 表单校验
            rules: {
                longitude: [
                    { required: true, message: "经度不能为空！", trigger: "blur" }
                ],
                latitude: [
                    { required: true, message: "纬度不能为空！", trigger: "blur" }
                ], 
                address:[
                    { required: true, message: "地址不能为空！", trigger: "blur" }
                ], 
            }
        }
    },

    /** */
    mounted() {
        
        // console.log(" MapPoint.vue--mounted()--this.modalObj == ",this.modalObj);
        this.title = this.modalObj.title;//

        if(this.modalObj.address != null  ) { // 修改的时候
            this.form.address = this.modalObj.address;//
            //  console.log('MapPoint.vue--mounted()--this.form.address = ', this.form.address);
        }
        if(this.modalObj.longitude != ''){
            this.form.longitude = this.modalObj.longitude;
        }
        if(this.modalObj.latitude != ''){
            this.form.latitude = this.modalObj.latitude;
        }
        // console.log('MapPiont --- mounted----this.form=',this.form); 

    },
    methods: {
        /** */
        open() { 
            // console.log('MapPoint.vue--methods--open()');
            if(this.modalObj.address != null  ) { // 修改的时候
                this.form.address = this.modalObj.address;// 
            }
            if(this.modalObj.longitude != ''){
                this.form.longitude = this.modalObj.longitude;
            }
            if(this.modalObj.latitude != ''){
                this.form.latitude = this.modalObj.latitude;
            }

            // console.log('MapPoint.vue--methods--this.form = ', this.form);
            // 
            this.initBaiduMap();// 

        },
        /** 关闭弹出窗口 */ 
        modalClose() { 
            // this.reset();
            this.$emit('update:visible', false); // 直接修改父组件的属性   
        },
        /** 确定 */
        submitForm() {
            // 
            this.$refs["form"].validate(valid => {
                if (valid) {
                    // 
                    this.$emit('value-changed', this.form.longitude, this.form.latitude,this.form.address); // 直接修改父组件的属性   
                    this.$emit('update:visible', false); // 直接修改父组件的属性   
                }
            });
            // 
        },
        /** 离线地图初始化 */
        initBaiduMap() {
            // 检查百度地图API是否加载
            if (typeof window.BMap === 'undefined') {
                console.error('百度地图API未加载，请检查bmap_offline_api_v3.0_min.js是否正确引入');
                this.$message.error('地图组件加载失败，请刷新页面重试');
                return;
            }

            // console.log('window =', window);
            // console.log('initBaiduMap.....this.form=', this.form);
            this.mapB = new window.BMap.Map("divMapLocation");
            console.log('initBaiduMap -->mapB..', this.mapB);
            // 设置中心经纬度  这里我们使用BMapGLGL命名空间下的Point类来创建一个坐标点。Point类描述了一个地理坐标点
            let point =null;
            if( this.form.longitude !=undefined && this.form.latitude !=undefined && this.form.longitude !='' && this.form.latitude !=''  ) {
                point = new window.BMap.Point(this.form.longitude, this.form.latitude);
            } //(114.28398, 30.601327);
            else {
                // 默认北京坐标
                const defaultPoint = window.vueConfig ? window.vueConfig.defaultPoint : { lng: 116.4177920000, lat: 39.9960840000 };
                console.log('defaultPoint==', defaultPoint);
                point = new window.BMap.Point(defaultPoint.lng, defaultPoint.lat); // 默认点位
            }
            var geolocation = new window.BMap.Geolocation();
            
            // console.log('...geolocation=',geolocation)
            // 开启SDK辅助定位
            geolocation.enableSDKLocation();
            let res = point;
             
            // 开始定位
            geolocation.getCurrentPosition(function (res) {
                // console.log('initBaiduMap--getCurrentPosition()--res=',res,' this=',this);
                //if (res) {
                if(this.getStatus() == window.BMAP_STATUS_SUCCESS){

                     // 获取经纬度
                    var mker = new window.BMap.Marker(res.point);
                    map.addOverlay(mker);
                    map.panTo(res.point);
                    // 这里可以调用getLocation(r.point)来获取地址
                    geolocation.getLocation(res.point, function(rs){
                        var addComp = rs.addressComponents;
                        //alert(addComp.province + ", " + addComp.city + ", " + addComp.district + ", " + addComp.street);
                        // console.log("...getLocation--",addComp.province + ", " + addComp.city + ", " + addComp.district + ", " + addComp.street);
                    });
                    // that.form.longitude = res.point.lng;
                    // that.form.latitude = res.point.lat; 
                    // this.mapB.centerAndZoom(res.point, 12); 
                    // that.getAddrByPoint(res.point);
                    // console.log('geolocation.getCurrentPosition --that.form=',that.form);
                } else {
                    // alert('failed'+this.getStatus());
                    // console.log('333...failed..getStatus=',this.getStatus() )
                }           
                            
            });

            // console.log('initBaiduMap--this.form=', this.form)
           
            var that =this;

            this.mapB.centerAndZoom(point, 12);

          
            this.mapB.addControl(new window.BMap.ScaleControl()); //显示比例尺在右下角 
            this.mapB.enableScrollWheelZoom();                  // 启用滚轮放大缩小。
 

            // // 3、设置图像标注并绑定拖拽标注结束后事件
            this.mk = new window.BMap.Marker(point, { enableDragging: true });
            this.mapB.addOverlay( this.mk);
            // this.local = new window.BMap.LocalSearch(this.mapB, {
            //     renderOptions: { map: that.mapB },
            // });

            this.localSearch = new window.BMap.LocalSearch(this.mapB);
            this.localSearch.enableAutoViewport(); //允许自动调节窗体大小

            // 7、绑定点击地图任意点事件 
            this.mapB.addEventListener("click", function(e) {
                // console.log(' mapB.click=>e.point.lng=', e.point.lng ,",e.point.lat=" ,e.point.lat, ', e.point =',e.point );
                //mapB.clearOverlays(); //清空原来的标注
                that.form.longitude = e.point.lng; // 经度
                that.form.latitude  = e.point.lat; // 纬度
                that.getAddrByPoint(e.point);// 
            });
 

        },  
        /**2、逆地址解析函数 根据坐标点获取详细地址  point   百度地图坐标点，必传 */ 
        getAddrByPoint(point) {
            var that = this;
            // console.log('.... getAddrByPoint =', point);
            var geco = new BMap.Geocoder();
            // console.log('...getAddrByPoint geco=', geco,',getAddrByPoint=',point);

            this.mapB.clearOverlays(); //清空原来的标注

            this.mk.setPosition(point); // 设置描点位置
            this.mapB.addOverlay( this.mk);
            
            geco.getLocation(point, function (rs) {
                // console.log('...getAddrByPoint--getLocation...rs =',rs);
                // that.mk.setPosition(point);
                // that.map.panTo(point);
                that.mk.setPosition(rs.point);
                that.form.address = rs.address + rs.content.poi_desc;
                that.form.longitude = rs.point.lng; // 经度
                that.form.latitude = rs.point.lat;  // 纬度

                // console.log('form.address=', that.form.address,',');
            });
        }, 

    }
}
</script>
<style>
    #divMapLocation {
        width: 100%;
        height: 360px;
        border: 1px solid rgb(204, 204, 204);
    }

</style>