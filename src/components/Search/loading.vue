<template>
  <div class="s-box">
    <div>
      <img class="divImg" src="../../assets/images/loading.gif" alt="">
    </div>
  </div>
</template>
<script> 

export default {
  name: 'Layout',
  props:{},
  data () {
    return {
      downUrl:process.env.VUE_APP_BASE_API,

    }
  },
  components: {

  },
  created () {

  },
  methods: {


    /****** ips 小区接入 end*******/
  }
}

</script>
<style lang="scss" scoped>
.s-box{
  width: 100%;
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  margin: auto;
  background: rgba(0,0,0,0.5);
  z-index: 99999;
}
.divImg{
  width: 150px;
  height: 132px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}
</style>
