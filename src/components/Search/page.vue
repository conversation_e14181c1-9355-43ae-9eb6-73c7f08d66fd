<template>
  <div class="baseDataSearch">
      <div class="comm-page">
        <el-pagination background  @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="pageNum"
          :page-sizes="[10, 50, 100, 200]" :page-size="form.pageSize" layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
  </div>
</template>
<script>
export default {
  name: 'Layout',
  props:['total','pageNum','pageSize'],
  data () {
    return {
      keyVlue: this.$route.query.keyVlue || '',
      form:{
        pageNum: this.pageNum,
        pageSize: 10,
      },
      
    }
  },
  watch: {
    pageSize: {
      handler(val) {
        this.form.pageSize = val;
      },
      deep: true
    }
  },
  components: {

  },
  created () {
    
  },
  methods: {
    //每页多少条
    handleSizeChange(val) {
      this.form.pageNum = 1
      this.form.pageSize = val
      this.$emit('page',this.form)
    },
    //当前页码
    handleCurrentChange(val) {
      this.form.pageNum = val;
      this.$emit('page',this.form)
      this.$emit('update:pageNum',this.form.pageNum)
    },

  }
}

</script>
<style lang="scss" scoped>
 
// .el-input {
//   margin-top:2px;
// }
.baseDataSearch{
  background: #f1f4fc;
  border: solid 1px #ebeef5;
  border-top: none;
}
.comm-page {
  text-align: right;
  min-height: 46px;
  padding-top: 6px;
  margin-right: 16px;
}

</style>
