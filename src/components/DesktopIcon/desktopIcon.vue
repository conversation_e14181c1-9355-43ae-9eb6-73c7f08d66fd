<template>
   
   <div class="el-card"   style=" height:300px;width:100%;margin-left: 0px; margin-top: 20px; padding: 20px;" >
        <span>常用应用</span>
        <div class="home-cen">
          <div
                  class="home-icon"  
                  @click="addApp()"
              >
                  <img src="../../assets/home/<USER>" />
                  <span>添加</span>
          </div>

          <div
            class="home-icon"
            v-for="(item, index) in desktopIconList"
            :key="index"
            @click="winIcon(item)"
          >
            <img :src="item.desktopIcon" />
            <span>{{ item.menuName }}</span>
          </div>

        </div>

 

   </div>  
   
</template>

<script>


import {getUserCurrentDesktopIconList,desktopIconClick} from '@/api/screen/deskicon';

export default {
  name: "desktopicon_manager", 
  props: {  
    // 添加 
    openAddIconMethed: {
      type: Function,
      require: true,
      default: null,
    },
  },
  data() {
    return {
      // isShowAddIconDlg:false, // 
      desktopIconList:[], // 当前用户工作台图标列表 

    }
  },
  mounted() {
    
     this.initDesktopIconList();
  },
  methods: {

    /** 日常应用数据加载 */
    initDesktopIconList() {

        getUserCurrentDesktopIconList().then(response=>{
          //
          console.log('getUserCurrentDesktopIconList---response' ,response);

          this.desktopIconList=[];
          //this.desktopIconList = response.data;// 
          response.data.forEach(item => {
            if(item.desktopIcon !=null ) {
              item.desktopIcon =require('../../assets/images/workbench/' + item.desktopIcon);
              this.desktopIconList.push(item);
            }
          });

        });

    },
    // 点击添加工作应用
    addApp() {
      // 调用父页面传递的添加方法
      this.openAddIconMethed();
    },
    //
    winIcon(itemVal) {
      console.log('---winIcon--click=',itemVal);

      if(itemVal.menuUrl ==null || itemVal.menuUrl=='') { 
        // 无效的菜单连接

      } else {
          let menuId = itemVal.menuId ;

          // 菜单点击连接后台记录
          desktopIconClick(menuId).then(response=>{
            if(response.code =='200') {
              let iconUrl = '/' + itemVal.menuPath;
              console.log('-----itemVal.menuUrl= ', itemVal.menuUrl,' iconUrl =',iconUrl) 
              // 跳转
              this.$router.push(iconUrl);
            }
          });
      }

    },
  }
};
</script>
<style scoped>


    .home-cen {
        width: 100%;
        margin-top: 10px;
        padding-left: 20px;
        padding-right: 20px;
        text-align: center;
        overflow: hidden;
        .home-icon {
            width: 94px;
            height: 90px;
            float: left;
            margin: 5px;
            cursor: pointer;
        }
        span {
            display: block;
            margin-top: 6px;
            font-size: 13px;
            font-family: Source Han Sans CN;
            font-weight: bold;
            color: rgb(110, 83, 207);
        }
        img {
            width: 65px;
            height: 65px;
        }
    }
     

</style>
