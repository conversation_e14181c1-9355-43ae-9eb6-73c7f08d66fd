<template>
   
   <el-dialog :visible.sync="visible" :width="addIconObj.width" :modal='addIconObj.modal' append-to-body :before-close="modalClose"
      :close-on-click-modal ="false" :close-on-press-escape="false" :modal-append-to-body="false" class="dialogClass" >  
      <div slot="title" class="dialog-title" ><!-- style=" padding:10px; background-color: #B3EBF5" --> 
        <i class="el-icon-menu"></i>
        <span class="title-text">{{addIconObj.title}}</span> 
      </div>
      <div class="desktopicon-box">
           
          <el-scrollbar class="page-scroll">
            <div
              class="appIocn"
              v-for="(item, index) in desktopIconList"
              :key="index"
              @click="clickApp(item)"
            >
              <img
                class="checkflag"
                :src="item.addedFlag == true ? checkUrl : noCheckUrl"
              />
              <img class="appIconImg"  :src="item.desktopIcon">
              <span>{{ item.label }}</span>
            </div>
          </el-scrollbar>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="save">保 存</el-button>
        <el-button @click="modalClose">退 出</el-button>
      </span>
   </el-dialog>
   
</template>

<script>

import {getMenuTree,addMenuDesktopIcon} from '@/api/screen/deskicon';

export default {
  name: "desktopicon_add_dialog",
  props: { 
    visible: {
      type: Boolean,
      default: false
    }, 
    // 父页面给子页面传递的对象 
    addIconObj: { //  
      type: Object,
      default: false
    } 
  }, 
  data() {
    return {
      loading:false,
      // unmatchArray: [], // 记录未匹配的项
      desktopIconList:[], //  自定义菜单图标列表

      checkUrl: require("../../assets/home/<USER>"),
      noCheckUrl: require("../../assets/home/<USER>"),

    }
  },
  mounted() {
    // 获取菜单列表
    getMenuTree().then(response => { 

      if(response.code=='200') {
          // console.log('----getMenuTree--response.data=',response.data);
          this.desktopIconList=[];
          // 过滤有些的查单显示 
          response.data.forEach(item=>{
            
              
              if(item.children !=null) {
                // 递归调用子菜单 
                this.getChildrenMenu(item.children);
              }
              if(item.desktopIcon !=null && item.url != null ) { 
                item.desktopIcon=require('../../assets/images/workbench/' + item.desktopIcon);
                this.desktopIconList.push(item);//
              } 
          });

          // this.desktopIconList = response.data;// 
          // console.log('getMenuTree---this.desktopIconList=',this.desktopIconList);

      }
      
    }).catch(() => {
        this.loading = false; 
        
    })
    // 
  },
  methods: {
    // 递归遍历子菜单  
    getChildrenMenu(childrenList) {

       childrenList.forEach(item=>{

              if(item.children !=null) {
                // 递归调用子菜单 
                this.getChildrenMenu(item.children);
              }
              if(item.desktopIcon !=null  && item.url != null ) { 
                item.desktopIcon=require('../../assets/images/workbench/' + item.desktopIcon);
                this.desktopIconList.push(item);//
              } 
       });
    },
    // 点击弹窗的图标 
    clickApp(item) {
      // console.log('-----before=',item.addedFlag)
      item.addedFlag = !item.addedFlag;
      // console.log('-----end =',item.addedFlag)
    }, 
    // 保存 
    save() {
      
      // let menuId='';
      let menuList=[];
      this.desktopIconList.forEach(item => {
        if (item.addedFlag) {
          menuList.push( item.id);
          // menuId = item.id;
        }
      }); 
      // console.log('-----menuList=',menuList);
      if(menuList.length<=0) {
        // 提示必须选择一个菜单 

      }
      else {
        addMenuDesktopIcon(menuList).then(response=>{
            // console.log('---addMenuDesktopIcon-response=',response);
            console.log(' addMenuDesktopIcon.....save... ')
            this.$emit('parentUpdateIcon'); // 调用父页面函数 刷新日常应用列表
            this.modalClose();
        });
      }


    },
    // 
    modalClose() {
      //this.reset();
      this.$emit('update:visible', false); // 直接修改父组件的属性  
    }
  }
};
</script>
<style scoped>


.desktopicon-box {
  overflow: hidden;
  height: 616px;
  .appIocn {
    width: 120px;
    height: 90px;
    text-align: center;
    position: relative;
    margin: 15px 23px;
    float: left;
    cursor: pointer;
    .appIconImg {
      width: 65px;
      height: auto;
    }
    span {
      display: block;
      margin-top: 6px;
      font-size: 12px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #777777;
    }
    .checkflag {
      width: 23px;
      height: 23px;
      position: absolute;
      right: 17px;
    }
  }
}
     

</style>
