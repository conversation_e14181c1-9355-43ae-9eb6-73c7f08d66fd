<template>
  <div class="image-viewer-wrapper">
    <!-- 缩略图 -->
    <div class="thumbnail-container" @click="openViewer">
      <img
        v-if="firstImage"
        :src="firstImage"
        :style="{ width: width + 'px', height: height + 'px' }"
        class="thumbnail-image"
        :alt="alt"
      />
      <div v-else class="no-image" :style="{ width: width + 'px', height: height + 'px' }">
        <i class="el-icon-picture-outline"></i>
        <span>暂无图片</span>
      </div>
      <!-- 放大镜图标 -->
      <div v-if="firstImage" class="zoom-overlay">
        <i class="el-icon-zoom-in"></i>
        <!-- 多图标识 -->
        <span v-if="imageList.length > 1" class="multi-image-badge">{{ imageList.length }}</span>
      </div>
    </div>

    <!-- 图片查看器对话框 -->
    <el-dialog
      :visible.sync="dialogVisible"
      :title="dialogTitle"
      width="80%"
      top="5vh"
      append-to-body
      class="image-dialog"
      @close="closeViewer"
    >
      <div class="image-container">
        <!-- 多图导航 -->
        <div v-if="imageList.length > 1" class="image-navigation">
          <el-button
            :disabled="currentIndex === 0"
            @click="prevImage"
            icon="el-icon-arrow-left"
            circle
            size="small"
          ></el-button>
          <span class="image-counter">{{ currentIndex + 1 }} / {{ imageList.length }}</span>
          <el-button
            :disabled="currentIndex === imageList.length - 1"
            @click="nextImage"
            icon="el-icon-arrow-right"
            circle
            size="small"
          ></el-button>
        </div>

        <!-- 当前图片 -->
        <img
          v-if="currentImage"
          :src="currentImage"
          class="preview-image"
          :alt="alt"
          @load="onImageLoad"
          @error="onImageError"
        />
        <div v-else class="no-image-large">
          <i class="el-icon-picture-outline"></i>
          <p>图片加载失败</p>
        </div>

        <!-- 缩略图列表 -->
        <div v-if="imageList.length > 1" class="thumbnail-list">
          <div
            v-for="(image, index) in imageList"
            :key="index"
            class="thumbnail-item"
            :class="{ active: index === currentIndex }"
            @click="setCurrentImage(index)"
          >
            <img :src="image" :alt="`图片${index + 1}`" />
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeViewer">关闭</el-button>
        <el-button v-if="currentImage" type="primary" @click="downloadImage">下载当前图片</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ImageViewer',
  props: {
    // 图片地址
    src: {
      type: String,
      default: ''
    },
    // 缩略图宽度
    width: {
      type: Number,
      default: 50
    },
    // 缩略图高度
    height: {
      type: Number,
      default: 50
    },
    // 图片描述
    alt: {
      type: String,
      default: '图片'
    },
    // 对话框标题
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false,
      imageLoaded: false,
      imageError: false,
      currentIndex: 0
    };
  },
  computed: {
    // 图片列表
    imageList() {
      if (!this.src) return [];

      // 检查是否包含英文逗号，如果有则分割
      if (this.src.includes(',')) {
        return this.src.split(',').map(url => url.trim()).filter(url => url);
      }

      return [this.src];
    },

    // 第一张图片（用于缩略图显示）
    firstImage() {
      return this.imageList.length > 0 ? this.imageList[0] : '';
    },

    // 当前显示的图片
    currentImage() {
      return this.imageList[this.currentIndex] || '';
    },

    // 对话框标题
    dialogTitle() {
      if (this.title) return this.title;

      if (this.imageList.length > 1) {
        return `图片预览 (${this.currentIndex + 1}/${this.imageList.length})`;
      }

      return '图片预览';
    }
  },
  methods: {
    // 打开图片查看器
    openViewer() {
      if (this.firstImage) {
        this.dialogVisible = true;
        this.imageLoaded = false;
        this.imageError = false;
        this.currentIndex = 0; // 重置到第一张图片
      }
    },

    // 关闭图片查看器
    closeViewer() {
      this.dialogVisible = false;
      this.currentIndex = 0;
    },

    // 上一张图片
    prevImage() {
      if (this.currentIndex > 0) {
        this.currentIndex--;
      }
    },

    // 下一张图片
    nextImage() {
      if (this.currentIndex < this.imageList.length - 1) {
        this.currentIndex++;
      }
    },

    // 设置当前图片
    setCurrentImage(index) {
      if (index >= 0 && index < this.imageList.length) {
        this.currentIndex = index;
      }
    },
    
    // 图片加载成功
    onImageLoad() {
      this.imageLoaded = true;
      this.imageError = false;
    },
    
    // 图片加载失败
    onImageError() {
      this.imageLoaded = false;
      this.imageError = true;
    },
    
    // 下载图片
    downloadImage() {
      if (!this.currentImage) return;

      const link = document.createElement('a');
      link.href = this.currentImage;
      link.download = this.getFileName(this.currentImage);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    // 获取文件名
    getFileName(imageUrl) {
      if (!imageUrl) return 'image.jpg';

      try {
        const url = new URL(imageUrl, window.location.origin);
        const pathname = url.pathname;
        const filename = pathname.split('/').pop();

        return filename || `image_${this.currentIndex + 1}.jpg`;
      } catch (error) {
        return `image_${this.currentIndex + 1}.jpg`;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.image-viewer-wrapper {
  display: inline-block;
  position: relative;
  
  .thumbnail-container {
    position: relative;
    cursor: pointer;
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid #dcdfe6;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #409EFF;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
      
      .zoom-overlay {
        opacity: 1;
      }
    }
    
    .thumbnail-image {
      display: block;
      object-fit: cover;
      transition: transform 0.3s ease;
    }
    
    .no-image {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: #f5f7fa;
      color: #909399;
      font-size: 12px;
      
      i {
        font-size: 20px;
        margin-bottom: 4px;
      }
    }
    
    .zoom-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;

      i {
        color: white;
        font-size: 20px;
      }

      .multi-image-badge {
        position: absolute;
        top: 4px;
        right: 4px;
        background-color: #409EFF;
        color: white;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 10px;
        min-width: 16px;
        text-align: center;
        line-height: 1;
      }
    }
  }
}

// 对话框样式
::v-deep .image-dialog {
  .el-dialog__body {
    padding: 20px;
    text-align: center;
  }
  
  .image-container {
    max-height: 70vh;
    overflow: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .image-navigation {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16px;
      gap: 16px;

      .image-counter {
        font-size: 14px;
        color: #606266;
        min-width: 60px;
        text-align: center;
      }
    }

    .preview-image {
      max-width: 100%;
      max-height: 50vh;
      object-fit: contain;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      margin-bottom: 16px;
    }

    .thumbnail-list {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
      justify-content: center;
      max-width: 100%;
      overflow-x: auto;
      padding: 8px 0;

      .thumbnail-item {
        width: 60px;
        height: 60px;
        border: 2px solid transparent;
        border-radius: 4px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
          border-color: #409EFF;
        }

        &:hover {
          border-color: #409EFF;
          opacity: 0.8;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    .no-image-large {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: #909399;

      i {
        font-size: 48px;
        margin-bottom: 16px;
      }

      p {
        margin: 0;
        font-size: 16px;
      }
    }
  }
  
  .dialog-footer {
    text-align: center;
    
    .el-button {
      margin: 0 8px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  ::v-deep .image-dialog {
    .el-dialog {
      width: 95% !important;
      margin: 0 auto;
    }
    
    .image-container {
      .preview-image {
        max-height: 60vh;
      }
    }
  }
}
</style>
