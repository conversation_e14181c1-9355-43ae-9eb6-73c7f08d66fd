<template>
  <div class="common-search">
    <!-- 基础搜索行 -->
    <div class="search-row">
      <div class="search-label">{{ searchConfig.label }}</div>
      <div class="search-input-wrapper">
        <el-input
          v-model="searchForm[searchConfig.key]"
          :placeholder="searchConfig.placeholder"
          class="search-input"
          clearable
          @keyup.enter="handleSearch"
          @clear="handleSearch"
        >
          <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
        </el-input>
      </div>
      <div v-if="advancedFields && advancedFields.length > 0" class="advanced-toggle" @click="toggleAdvanced">
        <span class="toggle-text">高级检索</span>
        <i class="el-icon-d-arrow-right toggle-icon" :class="{ 'expanded': showAdvanced }"></i>
      </div>
    </div>

    <!-- 高级搜索区域 -->
    <div v-show="showAdvanced" class="advanced-search">
      <el-row :gutter="20">
        <el-col
          v-for="field in advancedFields"
          :key="field.key"
          :span="field.span || 6"
        >
          <div class="search-field">
            <label class="field-label">{{ field.label }}：</label>
            <div class="field-input">
              <!-- 输入框 -->
              <el-input
                v-if="field.type === 'input'"
                v-model="searchForm[field.key]"
                :placeholder="field.placeholder"
                size="small"
                clearable
              />

              <!-- 选择框 -->
              <el-select
                v-else-if="field.type === 'select'"
                v-model="searchForm[field.key]"
                :placeholder="field.placeholder"
                size="small"
                clearable
                @change="handleFieldChange(field, $event)"
              >
                <el-option
                  v-for="option in field.options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>

              <!-- 日期选择器 -->
              <el-date-picker
                v-else-if="field.type === 'date'"
                v-model="searchForm[field.key]"
                type="date"
                :placeholder="field.placeholder"
                size="small"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              />

              <!-- 日期范围选择器 -->
              <el-date-picker
                v-else-if="field.type === 'daterange'"
                v-model="searchForm[field.key]"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              />
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 查询按钮区域 -->
      <div class="search-actions">
        <el-button type="primary" size="small" @click="handleSearch">查询</el-button>
        <!-- <el-button type="info" plain size="small" @click="resetAdvancedForm">重置高级搜索</el-button> -->
        <el-button type="info" plain size="small" @click="resetAllForm">重置</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CommonSearch',
  props: {
    // 搜索配置
    searchConfig: {
      type: Object,
      required: true,
      default: () => ({
        label: '名称或关键字',
        key: 'keyword',
        placeholder: '请输入名称或关键字'
      })
    },
    // 高级搜索字段配置
    advancedFields: {
      type: Array,
      default: () => []
    },
    // 初始搜索表单数据
    initialForm: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      showAdvanced: false,
      searchForm: {}
    }
  },
  watch: {
    initialForm: {
      handler(newVal) {
        this.searchForm = { ...newVal }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 切换高级搜索显示状态
    toggleAdvanced() {
      this.showAdvanced = !this.showAdvanced
    },
    
    // 处理搜索
    handleSearch() {
      // 清理空值
      const cleanForm = {}
      Object.keys(this.searchForm).forEach(key => {
        const value = this.searchForm[key]
        if (value !== null && value !== undefined && value !== '') {
          cleanForm[key] = value
        }
      })
      
      this.$emit('search', cleanForm)
    },
    
    // 重置搜索表单
    resetForm() {
      this.searchForm = { ...this.initialForm }
      this.handleSearch()

      // 发送重置事件给父组件，让父组件也更新其searchForm
      this.$emit('reset', { ...this.searchForm })
    },

    // 完全重置所有搜索条件
    resetAllForm() {
      // 重置为初始状态
      this.searchForm = { ...this.initialForm }

      // 通知父组件重置完成，并触发搜索
      this.handleSearch()

      // 发送重置事件给父组件，让父组件也更新其searchForm
      this.$emit('reset', { ...this.searchForm })
    },

    // 重置高级搜索表单
    resetAdvancedForm() {
      // 只重置高级搜索字段，保留基础搜索
      const basicKey = this.searchConfig.key
      const basicValue = this.searchForm[basicKey]

      // 重置所有高级搜索字段
      this.advancedFields.forEach(field => {
        this.searchForm[field.key] = null
      })

      // 保留基础搜索值
      this.searchForm[basicKey] = basicValue

      // 通知父组件重置完成，并触发搜索
      this.handleSearch()

      // 发送重置事件给父组件，让父组件也更新其searchForm
      this.$emit('reset', { ...this.searchForm })
    },

    // 处理字段变化（用于字段联动）
    handleFieldChange(field, value) {
      // 如果字段有onChange回调，执行回调
      if (field.onChange && typeof field.onChange === 'function') {
        field.onChange(value, this.searchForm)
      }

      // 触发字段变化事件，让父组件可以监听
      this.$emit('field-change', {
        field: field.key,
        value: value,
        searchForm: this.searchForm
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.common-search {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;

  .search-row {
    display: flex;
    align-items: center;
    gap: 16px;

    .search-label {
      font-size: 14px;
      color: #606266;
      white-space: nowrap;
      min-width: 120px;
      text-align:right;
    }

    .search-input-wrapper {
      flex: 1;
      max-width: 400px;

      .search-input {
        width: 100%;
      }
    }

    .advanced-toggle {
      display: flex;
      align-items: center;
      cursor: pointer;
      color: #4584FF;
      font-size: 14px;
      white-space: nowrap;
      user-select: none;

      .toggle-text {
        margin-right: 4px;
      }

      .toggle-icon {
        font-size: 12px;
        transform: rotate(90deg);
        transition: transform 0.3s ease;

        &.expanded {
          transform: rotate(-90deg);
        }
      }

      &:hover {
        color: #337ecc;
      }
    }
  }

  .advanced-search {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #EBEEF5;

    .search-field {
      margin-bottom: 16px;
      display: flex;
      align-items: center;

      .field-label {
        font-size: 14px;
        color: #606266;
        white-space: nowrap;
        min-width: 80px;
        margin-right: 8px;
        font-weight: 400 !important;
      }

      .field-input {
        flex: 1;

        .el-input,
        .el-select,
        .el-date-editor {
          width: 100%;
        }
      }
    }

    .search-actions {
      margin-top: 16px;
      text-align: right;

      .el-button {
        margin-left: 8px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .common-search {
    .search-row {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;

      .search-label {
        min-width: auto;
        text-align:right;
      }

      .search-input-wrapper {
        max-width: none;
      }

      .advanced-toggle {
        justify-content: center;
      }
       ::v-deep .el-input__inner {
        border-radius: 0;
      }
    }

    .advanced-search {
      .search-field {
        flex-direction: column;
        align-items: stretch;

        .field-label {
          min-width: auto;
          margin-bottom: 4px;
        }
      }
    }
  }
}
</style>
