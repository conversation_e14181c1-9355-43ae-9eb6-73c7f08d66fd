/**
 * Mock拦截器
 * 用于在开发环境中拦截API请求并返回mock数据
 * 在生产环境中可以通过注释掉main.js中的引用来禁用
 */
import MockAdapter from 'axios-mock-adapter'
import * as enterpriseMock from 'mock/enterprise'
import * as userMock from 'mock/user'
import * as dataManagementMock from 'mock/data-management'
import service from '@/utils/request'

// 打印一条消息，表明mock拦截器已启用
console.log('%c[Mock] Mock拦截器已启用，API请求将返回模拟数据', 'color: #44C991; font-weight: bold;');

// 打印所有可用的mock方法
console.log('%c[Mock] 可用的mock方法 (enterprise):', 'color: #44C991; font-weight: bold;', Object.keys(enterpriseMock));
console.log('%c[Mock] 可用的mock方法 (user):', 'color: #44C991; font-weight: bold;', Object.keys(userMock));

// 创建一个mock适配器实例，应用于service实例
console.log('%c[Mock] 创建mock适配器，应用于service实例', 'color: #44C991; font-weight: bold;');
console.log('%c[Mock] service.defaults.baseURL:', 'color: #44C991; font-weight: bold;', service.defaults.baseURL);

const mockAdapter = new MockAdapter(service, {
  delayResponse: 800, // 模拟网络延迟
  onNoMatch: (request) => {
    console.log('%c[Mock] 未匹配到请求:', 'color: #FF6B6B; font-weight: bold;', request.url);
    return 'passthrough';
  } // 未匹配到的请求将被传递给真实服务器
})

// 企业列表接口
mockAdapter.onGet(new RegExp('/enterprise/list.*')).reply(config => {
  console.log('Mock API: 拦截到 getEnterpriseList 请求');

  // 从URL中提取查询参数
  const url = new URL(config.url, window.location.origin)
  const categoryType = url.searchParams.get('categoryType') || 'all'
  const pageNum = parseInt(url.searchParams.get('pageNum')) || 1
  const pageSize = parseInt(url.searchParams.get('pageSize')) || 10
  const keyword = url.searchParams.get('keyword') || ''
  const searchType = url.searchParams.get('searchType') || 'all'

  console.log('Mock API: getEnterpriseList 参数:', { categoryType, pageNum, pageSize, keyword, searchType });

  // 获取mock数据
  const allData = enterpriseMock.getEnterpriseList(categoryType)

  // 筛选数据
  let filteredData = allData
  if (keyword) {
    filteredData = allData.filter(item => {
      switch (searchType) {
        case 'name':
          return item.name.toLowerCase().includes(keyword.toLowerCase())
        case 'creditCode':
          return item.creditCode.toLowerCase().includes(keyword.toLowerCase())
        case 'legalPerson':
          return (item.legalPerson || '').toLowerCase().includes(keyword.toLowerCase())
        case 'all':
        default:
          return item.name.toLowerCase().includes(keyword.toLowerCase()) ||
            item.creditCode.toLowerCase().includes(keyword.toLowerCase()) ||
            (item.legalPerson || '').toLowerCase().includes(keyword.toLowerCase())
      }
    })
  }

  // 计算分页
  const total = filteredData.length
  const startIndex = (pageNum - 1) * pageSize
  const endIndex = startIndex + pageSize
  const pagedData = filteredData.slice(startIndex, endIndex)

  console.log(`Mock API: getEnterpriseList 返回 ${pagedData.length} 条数据，总计 ${total} 条`);

  // 返回mock响应
  return [200, {
    code: 200,
    msg: '操作成功',
    data: {
      total,
      list: pagedData
    }
  }]
})

// 企业详情接口
mockAdapter.onGet(new RegExp('/enterprise/detail/\\d+')).reply(config => {
  console.log('Mock API: 拦截到 getEnterpriseDetail 请求');

  // 从URL中提取企业ID
  const id = parseInt(config.url.match(/\/detail\/(\d+)/)[1])
  console.log('Mock API: getEnterpriseDetail 企业ID:', id);

  // 获取所有企业数据
  const allEnterprises = enterpriseMock.getEnterpriseList('all')

  // 查找对应ID的企业
  const enterprise = allEnterprises.find(item => item.id === id) || allEnterprises[0]

  // 返回mock响应
  return [200, {
    code: 200,
    msg: '操作成功',
    data: enterprise
  }]
})

// 获取风险级别选项接口
mockAdapter.onGet(new RegExp('/enterprise/riskLevelOptions')).reply((config) => {
  console.log('Mock API: 拦截到 getRiskLevelOptions 请求', config.url);
  const riskLevelOptions = [
    { value: '1', label: '网吧单位', tagColor: '#FB6B2A' },
    { value: '2', label: '电子屏单位', tagColor: '#60B8FF' },
    { value: '3', label: '等保备案单位', tagColor: '#44C991' },
    { value: '4', label: '网站备案单位', tagColor: '#F5BC6C' },
    { value: '5', label: '运营商单位', tagColor: '#24A8BB' },
    { value: '6', label: '非经营单位', tagColor: '#D789D4' },
    { value: '7', label: '其他', tagColor: '#95ABD4' }
  ]

  return [200, {
    code: 200,
    msg: '操作成功',
    data: riskLevelOptions
  }]
})

// 获取企业分类统计数据接口
mockAdapter.onGet(new RegExp('/system/sysEnterprise/stat')).reply((config) => {
  console.log('Mock API: 拦截到 getEnterpriseCategoryStats 请求', config.url);

  // 返回与用户提供的数据结构一致的mock数据
  const statsData = {
    "totalNum": 610,
    "screenNum": 2,
    "levelProjectNum": 1,
    "websiteNum": 1,
    "operatorNum": 1,
    "netbarNum": 6,
    "wifiNum": 0,
    "appNum": 1,
    "appletNum": 1,
    "anyNum": 10,
    "otherNum": 600
  }

  return [200, {
    code: 200,
    msg: '操作成功',
    data: statsData
  }]
})

// 添加企业接口
mockAdapter.onPost(new RegExp('/enterprise/add.*')).reply(() => {
  console.log('Mock API: 拦截到 addEnterprise 请求');
  return [200, {
    code: 200,
    msg: '添加成功',
    data: null
  }]
})

// 更新企业接口
mockAdapter.onPut(new RegExp('/enterprise/update.*')).reply(() => {
  console.log('Mock API: 拦截到 updateEnterprise 请求');
  return [200, {
    code: 200,
    msg: '更新成功',
    data: null
  }]
})

// 删除企业接口
mockAdapter.onDelete(new RegExp('/enterprise/delete/.*')).reply(() => {
  console.log('Mock API: 拦截到 deleteEnterprise 请求');
  return [200, {
    code: 200,
    msg: '删除成功',
    data: null
  }]
})

// 批量删除企业接口
mockAdapter.onDelete(new RegExp('/enterprise/batchDelete.*')).reply(() => {
  console.log('Mock API: 拦截到 batchDeleteEnterprise 请求');
  return [200, {
    code: 200,
    msg: '批量删除成功',
    data: null
  }]
})

// ==================== 用户相关接口 ====================

// 登录接口
mockAdapter.onPost('/auth/login').reply((config) => {
  console.log('Mock API: 拦截到 login 请求');
  const { username, password } = JSON.parse(config.data);
  console.log('Mock API: login 参数:', { username, password });

  return [200, {
    code: 200,
    msg: null,
    data: {
      access_token: "eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6IjUyY2YwNTM5LWE1ODEtNDBlMy04OTdiLWI1YzY3NjM0MDkzZCIsInVzZXJuYW1lIjoiYWRtaW4ifQ.82EDxkwa6_t8EwLs15o-1i5g288BI1YvR-V2q1QZQEExypHFjslQZrxkpfhbzWNpzOuISw_PamuhnwjVqABUWQ",
      expires_in: 60
    }
  }]
})

// 登出接口
mockAdapter.onDelete('/auth/logout').reply(() => {
  console.log('Mock API: 拦截到 logout 请求');
  return [200, {
    code: 200,
    msg: null,
    data: {}
  }]
})

// 获取用户信息接口
mockAdapter.onGet('/system/user/getInfo').reply(() => {
  console.log('Mock API: 拦截到 getInfo 请求');
  return [200, {
    msg: "操作成功",
    code: 200,
    permissions: [
      "*:*:*"
    ],
    roles: [
      "admin"
    ],
    user: {
      createBy: "admin",
      createTime: "2024-06-30 11:27:11",
      updateBy: null,
      updateTime: null,
      remark: "管理员",
      userId: 1,
      deptId: 103,
      userName: "admin",
      nickName: "大王",
      email: "<EMAIL>",
      phonenumber: "15888888888",
      sex: "1",
      avatar: "",
      password: "$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2",
      status: "0",
      delFlag: "0",
      loginIp: "*************",
      loginDate: "2025-05-15T17:51:45.000+08:00",
      dept: {
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        deptId: 103,
        parentId: 101,
        ancestors: "0,100,101",
        deptName: "研发部门",
        orderNum: 1,
        leader: "大王",
        phone: null,
        email: null,
        status: "0",
        delFlag: null,
        parentName: null,
        children: []
      },
      roles: [
        {
          createBy: null,
          createTime: null,
          updateBy: null,
          updateTime: null,
          remark: null,
          roleId: 1,
          roleName: "超级管理员",
          roleKey: "admin",
          roleSort: 1,
          dataScope: "1",
          menuCheckStrictly: false,
          deptCheckStrictly: false,
          status: "0",
          delFlag: null,
          flag: false,
          menuIds: null,
          deptIds: null,
          permissions: null,
          admin: true
        }
      ],
      roleIds: null,
      postIds: null,
      roleId: null,
      admin: true
    }
  }]
})

// 获取路由接口
mockAdapter.onGet('/system/menu/getRouters').reply(() => {
  console.log('Mock API: 拦截到 getRouters 请求');
  return [200, {
    msg: "操作成功",
    code: 200,
    data: [
      {
        name: "System",
        path: "/system",
        hidden: false,
        redirect: "noRedirect",
        component: "Layout",
        alwaysShow: true,
        meta: {
          title: "系统管理",
          icon: "system",
          noCache: false,
          link: null
        },
        children: [
          {
            name: "User",
            path: "user",
            hidden: false,
            component: "system/user/index",
            meta: {
              title: "用户管理",
              icon: "user",
              noCache: false,
              link: null
            }
          },
          {
            name: "Role",
            path: "role",
            hidden: false,
            component: "system/role/index",
            meta: {
              title: "角色管理",
              icon: "peoples",
              noCache: false,
              link: null
            }
          }
        ]
      },
      {
        name: "Enterprise",
        path: "/enterprise",
        hidden: false,
        redirect: "noRedirect",
        component: "Layout",
        alwaysShow: true,
        meta: {
          title: "企业管理",
          icon: "tree",
          noCache: false,
          link: null
        },
        children: [
          {
            name: "EnterpriseList",
            path: "index",
            hidden: false,
            component: "enterprise/index",
            meta: {
              title: "企业列表",
              icon: "list",
              noCache: false,
              link: null
            }
          },
          {
            name: "EnterpriseDetail",
            path: "detail/:id",
            hidden: true,
            component: "enterprise/detail",
            meta: {
              title: "企业详情",
              icon: "form",
              noCache: false,
              link: null
            }
          }
        ]
      }
    ]
  }]
})

// 获取验证码接口
mockAdapter.onGet('/code').reply(() => {
  console.log('Mock API: 拦截到 getCodeImg 请求');
  return [200, {
    code: 200,
    msg: null,
    img: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAAAkCAIAAADNSmkJAAAFKklEQVR4Xu2aW0wUVxiAZ2Z32V12F3aRmyBesNgaq9KkpGlMxUbTpjVtGlOtJk1fTNM+mNg+tX0xfdAYY9K+mKZNm8ZL08ZL2/ShDcVUDQhSFSleqCjIrrjLLruzs7vTc+bsrMMyBMdddg7yfzzAzJk5/3z/f/5z5szA4P9lYtZ+wGwTWfsBs00E0TNPZNEzTwTRM09k0TNPBNHTT8Jh6NSpU9evX5/0qlQqPXz48J49e7Kzs6e8Kgj0er1SqQx/ZVn2+vXrV65cef/+/YRXIyMjDx06tGvXroyMjPBXlmWvXbt25cqVhw8fhr+Gw+H9+/fv3r07MzMzXLh169alS5f6+/vDhXA4vG/fvj179mRlZYULt2/fvnjxYl9fX7gQCoX27t27d+/enJyccOHOnTsXLlzo7e0NF4LB4Mcff7xv377c3NxwIRAIXLx48d69e+HCpk2bvvnmm7KyMsGFJpPp/PnzPT094YLP5/voo48OHDiQl5cXLnR3d587d667uztc8Hq9Bw4cOHjwYH5+frjQ1dV19uzZR48ehQsej2f//v2ffPJJQUFBuNDZ2Xnmh9M/P/4pXHC73fv27Tt06FBhYWG48PDhw9OnT3d0dIQLLpdr7969hw8fLioqChc6OjpOnTrV3t4eLjidzo8//viTTz8tLi4OF9rb20+ePNnW1hYuOByOjz766MiRI5s3bw4X2traWltbW1tbwwW73f7hhx9+/sUXJSUl4UJra2tLS0tra2u4YLPZDh48ePTo0dLS0nDh1q1bzc3NLS0t4YLVav3ggw+OHTu2ZcuWcKG5ubmpqampqSlcsFgsH3/+2fETX5aVlYULN27caGxsbGxsDBfMZvOHH3549OjR8vLycKGxsbGhoaGhoSFcMJlM77///vHjxysqKsKFhoaG+vr6+vr6cMFoNL733nvHjx+vrKwMF+rr6+vq6urq6sIFg8Hw7rvvnjhxoqqqKlyoq6urra2tra0NF/R6/TvvvPP1119XVVWFCzU1NdXV1dXV1eGCTqd7++23T548uXXr1nChurq6qqqqqqoqXNBqtW+99dbJb7/dtm1buFBVVVVZWVlZWRkuaLw+z+joqEQiEVwYGRlRq9WCCzabzev1qtVqwYXh4WGNRiO4YLVafT6fWq0WXBgaGtJqtYILFovF7/er1WrBhcHBQZ1OJ7hgNpsDgYBarRZcGBgY0Ov1ggtmszkYDKpUKsGF/v5+g8EguGAymUKhkEqlElzo6+szGo2CCyaTKRwOK5VKwYXe3l6TySS4MDAwgDFWKBSCC48fPzaZTIIL/f39GGOFQiG40NPTMzg4KLjQ19eHMZbL5YIL3d3dQ0NDggu9vb0YY7lcLrjQ1dU1PDwsuNDT04MxlslkggudnZ0jIyOCC93d3RhjmUwmuNDR0TEyMiK48OjRI4yxVCoVXGhvbx8dHRVc6OrqwhhLpVLBhba2trGxMcGFzs5OjLFEIhFcaG1tHR8fF1zo6OjAGEskEsGFlpaWYDAouNDe3o4xlkgkggvNzc0TExOCC21tbRhjsVgsuNDU1DQ5OSm48PDhQ4yxWCwWXGhsbJycnBRcaG1txRiLxWLBhYaGhkAgILjQ0tKCMRaJRIIL9fX1gUBAcKG5uRljLBKJBBdqa2sDgYDgQlNTE8ZYJBIJLtTU1Ph8PsGFxsZGjLFIJBJcuHnzps/nE1xoaGjAGIvFYsGF69ev+/1+wYX6+nqMsVgsFly4du2a3+8XXKirq8MYi8ViwYWrV68GAgHBhdraWoyxRCIRXLhy5crExITgQk1NDcZYIpEILly+fHliYkJw4ebNmxhjiUQiuHDp0qXJyUnBhfr6eoyxVCoVXLh48eLk5KTgQl1dHcZYKpUKLvz000+Tk5OCCzU1NRhjqVQquHDhwoWpqSmRSPQvptVNQcrXnqsAAAAASUVORK5CYII=",
    uuid: "7e8c0a00-0e7a-4c0e-a9b0-5c600a7a0000"
  }]
})

// ==================== 基础数据管理相关接口 ====================

// 获取基础数据台账统计
mockAdapter.onGet('/data-management/statistics').reply(() => {
  console.log('Mock API: 拦截到 getDataStatistics 请求');
  return [200, dataManagementMock.dataStatistics]
})

// 获取全量数据变化情况
mockAdapter.onGet('/data-management/change-history').reply(() => {
  console.log('Mock API: 拦截到 getDataChangeHistory 请求');
  return [200, dataManagementMock.dataChangeHistory]
})

// 获取数据分布情况
mockAdapter.onGet('/data-management/distribution').reply(() => {
  console.log('Mock API: 拦截到 getDataDistribution 请求');
  return [200, dataManagementMock.dataDistribution]
})

// 获取各派出所数据贡献情况
mockAdapter.onGet(new RegExp('/data-management/police-contribution.*')).reply(config => {
  console.log('Mock API: 拦截到 getPoliceStationContribution 请求');

  // 从URL中提取查询参数
  const url = new URL(config.url, window.location.origin)
  const type = url.searchParams.get('type') || 'total'

  console.log('Mock API: getPoliceStationContribution 参数:', { type });

  return [200, dataManagementMock.policeStationContribution[type] || dataManagementMock.policeStationContribution.total]
})

// 搜索数据
mockAdapter.onGet(new RegExp('/data-management/search.*')).reply(config => {
  console.log('Mock API: 拦截到 searchData 请求');

  // 从URL中提取查询参数
  const url = new URL(config.url, window.location.origin)
  const searchType = url.searchParams.get('searchType') || 'totalNum'
  const keyword = url.searchParams.get('keyword') || ''

  console.log('Mock API: searchData 参数:', { searchType, keyword });

  return [200, {
    code: 200,
    msg: '操作成功',
    data: {
      searchType,
      keyword,
      results: [
        { id: 1, name: '测试数据1', type: searchType, value: 100, updateTime: '2024-01-01 10:00:00' },
        { id: 2, name: '测试数据2', type: searchType, value: 200, updateTime: '2024-01-02 10:00:00' },
        { id: 3, name: '测试数据3', type: searchType, value: 300, updateTime: '2024-01-03 10:00:00' }
      ]
    }
  }]
})

// ==================== 电子屏管理相关接口 ====================

// 查询电子屏信息列表
mockAdapter.onGet(new RegExp('/screen/screen/list.*')).reply(config => {
  console.log('Mock API: 拦截到 listScreen 请求');

  // 从URL中提取查询参数
  const url = new URL(config.url, window.location.origin)
  const pageNum = parseInt(url.searchParams.get('pageNum')) || 1
  const pageSize = parseInt(url.searchParams.get('pageSize')) || 10
  const screenName = url.searchParams.get('screenName') || ''

  // 生成模拟数据
  const generateScreenData = () => {
    const list = []
    for (let i = 1; i <= 100; i++) {
      list.push({
        screenId: i,
        screenName: `电子屏${i.toString().padStart(3, '0')}`,
        screenNature: String((i % 3) + 1),
        screenType: String((i % 4) + 1),
        addressType: String((i % 3) + 1),
        showType: String((i % 2) + 1),
        areaCode: '110101',
        areaName: '东城区',
        policeCode: '110101001',
        policeName: '东华门派出所',
        address: `详细地址${i}`,
        size: (Math.random() * 100 + 10).toFixed(2),
        floorLevel: String((i % 5) + 1),
        images: '/profile/upload/2024/01/01/test.jpg',
        enterprise: {
          enterpriseId: i,
          enterpriseName: `责任单位${i}`,
          creditCode: `91110101${i.toString().padStart(10, '0')}`,
          principal: `负责人${i}`,
          principalPhone: `138${i.toString().padStart(8, '0')}`,
          enterpriseAddress: `企业地址${i}`
        },
        keyareaList: i % 3 === 0 ? [
          { keyareaId: 1, keyareaName: '重点区域1' }
        ] : [],
        specialList: i % 4 === 0 ? [
          { specialId: 1, specialName: '专项1' }
        ] : [],
        site: i % 5 === 0 ? {
          siteCode: i,
          siteName: `归属场所${i}`,
          siteAddress: `场所地址${i}`
        } : null,
        netsystem: i % 6 === 0 ? {
          netsystemId: i,
          netsystemName: `联网系统${i}`
        } : null,
        netsystemId: i % 6 === 0 ? i : '0',
        accessUrl: `http://example.com/${i}`,
        status: i % 3 === 0 ? '0' : '1',
        inspectionNum: Math.floor(Math.random() * 10),
        createTime: '2024-01-01 09:00:00',
        updateTime: '2024-01-01 10:00:00'
      })
    }
    return list
  }

  let allData = generateScreenData()

  // 筛选数据
  if (screenName) {
    allData = allData.filter(item =>
      item.screenName.includes(screenName) ||
      item.address.includes(screenName)
    )
  }

  // 分页
  const total = allData.length
  const startIndex = (pageNum - 1) * pageSize
  const endIndex = startIndex + pageSize
  const pagedData = allData.slice(startIndex, endIndex)

  return [200, {
    code: 200,
    msg: '操作成功',
    total,
    rows: pagedData
  }]
})

// 查询电子屏信息详细
mockAdapter.onGet(new RegExp('/screen/screen/\\d+')).reply(config => {
  console.log('Mock API: 拦截到 getScreen 请求');

  const screenId = parseInt(config.url.match(/\/screen\/(\d+)/)[1])

  const screenData = {
    screenId: screenId,
    screenName: `电子屏${screenId.toString().padStart(3, '0')}`,
    screenNature: String((screenId % 3) + 1),
    screenType: String((screenId % 4) + 1),
    addressType: String((screenId % 3) + 1),
    showType: String((screenId % 2) + 1),
    areaCode: '110101',
    areaName: '东城区',
    policeCode: '110101001',
    policeName: '东华门派出所',
    address: `详细地址${screenId}`,
    size: (Math.random() * 100 + 10).toFixed(2),
    floorLevel: String((screenId % 5) + 1),
    images: '/profile/upload/2024/01/01/test.jpg',
    enterpriseId: screenId,
    enterpriseName: `责任单位${screenId}`,
    enterprise: {
      enterpriseId: screenId,
      enterpriseName: `责任单位${screenId}`,
      creditCode: `91110101${screenId.toString().padStart(10, '0')}`,
      principal: `负责人${screenId}`,
      principalPhone: `138${screenId.toString().padStart(8, '0')}`,
      enterpriseAddress: `企业地址${screenId}`
    },
    keyareaId: screenId % 3 === 0 ? '1' : '0',
    keyareaName: screenId % 3 === 0 ? '重点区域1' : null,
    keyareaList: screenId % 3 === 0 ? [
      { keyareaId: 1, keyareaName: '重点区域1' }
    ] : [],
    specialId: screenId % 4 === 0 ? '1' : '0',
    specialName: screenId % 4 === 0 ? '专项1' : null,
    specialList: screenId % 4 === 0 ? [
      { specialId: 1, specialName: '专项1' }
    ] : [],
    siteCode: screenId % 5 === 0 ? screenId.toString() : '0',
    siteName: screenId % 5 === 0 ? `归属场所${screenId}` : null,
    site: screenId % 5 === 0 ? {
      siteCode: screenId,
      siteName: `归属场所${screenId}`,
      siteAddress: `场所地址${screenId}`
    } : null,
    netsystemId: screenId % 6 === 0 ? screenId.toString() : '0',
    netsystemName: screenId % 6 === 0 ? `联网系统${screenId}` : null,
    netsystem: screenId % 6 === 0 ? {
      netsystemId: screenId,
      netsystemName: `联网系统${screenId}`
    } : null,
    accessUrl: `http://example.com/${screenId}`,
    status: screenId % 3 === 0 ? '0' : '1',
    inspectionNum: Math.floor(Math.random() * 10),
    inspectionList: [
      {
        inspectionDate: '2024-01-01',
        inspectionType: '日常检查',
        inspectionResult: '1',
        inspectionContent: '检查内容描述',
        inspectionPerson: '检查员',
        remark: '检查备注'
      }
    ],
    createTime: '2024-01-01 09:00:00',
    updateTime: '2024-01-01 10:00:00'
  }

  return [200, {
    code: 200,
    msg: '操作成功',
    data: screenData
  }]
})

// 新增电子屏信息
mockAdapter.onPost('/screen/screen').reply(() => {
  console.log('Mock API: 拦截到 addScreen 请求');
  return [200, {
    code: 200,
    msg: '新增成功',
    data: null
  }]
})

// 修改电子屏信息
mockAdapter.onPut('/screen/screen').reply(() => {
  console.log('Mock API: 拦截到 updateScreen 请求');
  return [200, {
    code: 200,
    msg: '修改成功',
    data: null
  }]
})

// 删除电子屏信息
mockAdapter.onDelete(new RegExp('/screen/screen/\\d+')).reply(() => {
  console.log('Mock API: 拦截到 delScreen 请求');
  return [200, {
    code: 200,
    msg: '删除成功',
    data: null
  }]
})

// ==================== 相关数据接口 ====================

// 查询责任单位列表
mockAdapter.onGet(new RegExp('/screen/enterprise/list.*')).reply(config => {
  console.log('Mock API: 拦截到 listEnterprise 请求');

  const url = new URL(config.url, window.location.origin)
  const enterpriseName = url.searchParams.get('enterpriseName') || ''

  const enterprises = []
  for (let i = 1; i <= 20; i++) {
    enterprises.push({
      enterpriseId: i,
      enterpriseName: `责任单位${i}`,
      creditCode: `91110101${i.toString().padStart(10, '0')}`,
      principal: `负责人${i}`,
      principalPhone: `138${i.toString().padStart(8, '0')}`,
      enterpriseAddress: `企业地址${i}`
    })
  }

  let filteredData = enterprises
  if (enterpriseName) {
    filteredData = enterprises.filter(item => item.enterpriseName.includes(enterpriseName))
  }

  return [200, {
    code: 200,
    msg: '操作成功',
    total: filteredData.length,
    rows: filteredData
  }]
})

// 查询重点区域列表
mockAdapter.onGet(new RegExp('/screen/keyarea/list.*')).reply(() => {
  console.log('Mock API: 拦截到 listKeyarea 请求');

  const keyareas = [
    { keyareaId: 1, keyareaName: '重点区域1' },
    { keyareaId: 2, keyareaName: '重点区域2' },
    { keyareaId: 3, keyareaName: '重点区域3' }
  ]

  return [200, {
    code: 200,
    msg: '操作成功',
    total: keyareas.length,
    rows: keyareas
  }]
})

// 查询专项列表
mockAdapter.onGet(new RegExp('/screen/special/list.*')).reply(() => {
  console.log('Mock API: 拦截到 listSpecial 请求');

  const specials = [
    { specialId: 1, specialName: '专项1' },
    { specialId: 2, specialName: '专项2' },
    { specialId: 3, specialName: '专项3' }
  ]

  return [200, {
    code: 200,
    msg: '操作成功',
    total: specials.length,
    rows: specials
  }]
})

// 查询归属场所列表
mockAdapter.onGet(new RegExp('/screen/site/list.*')).reply(config => {
  console.log('Mock API: 拦截到 listSite 请求');

  const url = new URL(config.url, window.location.origin)
  const siteName = url.searchParams.get('siteName') || ''

  const sites = []
  for (let i = 1; i <= 20; i++) {
    sites.push({
      siteCode: i,
      siteName: `归属场所${i}`,
      siteAddress: `场所地址${i}`
    })
  }

  let filteredData = sites
  if (siteName) {
    filteredData = sites.filter(item => item.siteName.includes(siteName))
  }

  return [200, {
    code: 200,
    msg: '操作成功',
    total: filteredData.length,
    rows: filteredData
  }]
})

// 查询联网系统列表
mockAdapter.onGet(new RegExp('/screen/netsystem/list.*')).reply(config => {
  console.log('Mock API: 拦截到 listNetsystem 请求');

  const url = new URL(config.url, window.location.origin)
  const netsystemName = url.searchParams.get('netsystemName') || ''

  const netsystems = []
  for (let i = 1; i <= 20; i++) {
    netsystems.push({
      netsystemId: i,
      netsystemName: `联网系统${i}`
    })
  }

  let filteredData = netsystems
  if (netsystemName) {
    filteredData = netsystems.filter(item => item.netsystemName.includes(netsystemName))
  }

  return [200, {
    code: 200,
    msg: '操作成功',
    total: filteredData.length,
    rows: filteredData
  }]
})

// 查询用户区域列表
mockAdapter.onGet('/system/area/listUserArea').reply(() => {
  console.log('Mock API: 拦截到 listUserArea 请求');

  const areas = [
    { areaCode: '110101', areaName: '东城区' },
    { areaCode: '110102', areaName: '西城区' },
    { areaCode: '110105', areaName: '朝阳区' },
    { areaCode: '110106', areaName: '丰台区' }
  ]

  return [200, {
    code: 200,
    msg: '操作成功',
    data: areas
  }]
})

// 查询区域列表
mockAdapter.onGet(new RegExp('/system/area/list.*')).reply(config => {
  console.log('Mock API: 拦截到 listArea 请求');

  const url = new URL(config.url, window.location.origin)
  const parentCode = url.searchParams.get('parentCode')

  const polices = [
    { areaCode: '110101001', areaName: '东华门派出所', parentCode: '110101' },
    { areaCode: '110101002', areaName: '景山派出所', parentCode: '110101' },
    { areaCode: '110102001', areaName: '西长安街派出所', parentCode: '110102' },
    { areaCode: '110102002', areaName: '府右街派出所', parentCode: '110102' }
  ]

  const filteredData = polices.filter(item => item.parentCode === parentCode)

  return [200, {
    code: 200,
    msg: '操作成功',
    data: filteredData
  }]
})

export default mockAdapter
