/**
 * 页面通用工具方法
 */

/**
 * 关闭当前页面并返回上一页
 * 适用于新增/编辑页面的关闭操作
 * @param {Object} context - Vue组件实例 (this)
 */
export function handlePageClose(context) {
  // 关闭当前页签
  context.$store.dispatch('tagsView/delView', context.$route).then(() => {
    // 页签关闭后跳转到上一页
    context.$router.go(-1);
  });
}

/**
 * 页面滚动到顶部
 * 适用于路由切换时重置滚动位置
 */
export function scrollToTop() {
  // 滚动到页面顶部
  window.scrollTo({
    top: 0,
    left: 0,
    behavior: 'smooth'
  });
  
  // 同时处理可能存在的其他滚动容器
  const containers = document.querySelectorAll('.app-main, .main-container, .page-container');
  containers.forEach(container => {
    container.scrollTop = 0;
  });
}

/**
 * 路由跳转并重置滚动位置
 * @param {Object} router - Vue Router实例
 * @param {String|Object} to - 目标路由
 */
export function navigateAndScrollToTop(router, to) {
  router.push(to).then(() => {
    // 使用nextTick确保DOM更新后再滚动
    router.app.$nextTick(() => {
      scrollToTop();
    });
  });
}
