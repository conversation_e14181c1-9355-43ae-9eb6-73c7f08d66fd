<template>
   <div>测试页面


    <DesktopIcon   ref="desktopIcon" :openAddIconMethed="addIconClick" v-if="isShowDesktopIcon" ></DesktopIcon>

    <AccessIcon   ref="desktopIcon" :openAddIconMethed="addIconClick" v-if="isShowDesktopIcon" ></AccessIcon>


    <AddIconDialog   ref="addIconDlg" v-if="isShowAddIconDlg" :visible.sync="isShowAddIconDlg"  :addIconObj='addIconObj'></AddIconDialog>  
   </div>
</template>

<script>

import DesktopIcon from '@/components/DesktopIcon/desktopIcon.vue';
import AccessIcon from '@/components/DesktopIcon/accessIcon.vue';
import AddIconDialog from '@/components/DesktopIcon/addIconDialog.vue';

export default {
  name: "test_index",
  components: {DesktopIcon, AccessIcon,AddIconDialog },
  props: {

    options: {
      type: Array,
      default: null,
    },
    value: [Number, String, Array],
    // 当未找到匹配的数据时，显示value
    showValue: {
      type: Boolean,
      default: true,
    },
    separator: {
      type: String,
      default: ","
    }
  },
  data() {
    return {

        addIconObj:{}, 

        isShowDesktopIcon:true, 

        isShowAddIconDlg:false, // 

    }
  },
  computed: {
   
  },
  methods: {
    /** 添加 事件  */
    addIconClick() {
        this.isShowAddIconDlg =true; // 弹出框编辑页面显示 
 
        var sTitle ='【添加】工作台日常应用'  
        this.addIconObj = { title: sTitle,modal: true, width: '560px', }; 

    }

  }
};
</script>
<style scoped>

</style>
