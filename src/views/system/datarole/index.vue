<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="数据角色名称" prop="roleName">
        <el-input
          v-model="queryParams.roleName"
          placeholder="请输入数据角色名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="权限字符" prop="roleKey">
        <el-input
          v-model="queryParams.roleKey"
          placeholder="请输入权限字符"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="角色状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:datarole:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:datarole:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:datarole:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:datarole:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dataRoleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="数据角色编号" prop="roleId" width="120" /> -->
      <el-table-column label="数据角色名称" prop="roleName" :show-overflow-tooltip="true" width="150" />
      <el-table-column label="权限字符" prop="roleKey" :show-overflow-tooltip="true" width="150" />
      <el-table-column label="显示顺序" prop="roleSort" width="100" />
      <el-table-column label="状态" align="center" width="100">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope" v-if="scope.row.roleId !== 1">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:datarole:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:datarole:remove']"
          >删除</el-button>
          
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改数据角色配置对话框 -->
    <el-dialog :title="title" :visible.sync="openDataScope" width="600px" append-to-body @close="closeDialog">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="数据角色名称" prop="roleName">
          <el-input v-model="form.roleName" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item prop="roleKey">
          <span slot="label">
            <el-tooltip content="控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasRole('admin')`)" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip>
            权限字符
          </span>
          <el-input v-model="form.roleKey" placeholder="请输入权限字符" />
        </el-form-item>
        <el-form-item label="数据角色顺序" prop="roleSort">
          <el-input-number v-model="form.roleSort" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="数据权限" prop="permission"> 
           
          <el-tree
            class="tree-border"
            :data="dict.type.sys_data_permission"
            show-checkbox
            ref="dataTree"
            node-key="value"
            :default-checked-keys="dataDefaultSelect" 
            empty-text="加载中，请稍候"
            :props="defaultProps" 
          ></el-tree>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
 
  </div>
</template>

<script>
import { listDataRole, getDataRole, delDataRole, addOrUpdateDataRole, changeDataRoleStatus} from "@/api/system/datarole";
 

export default {
  name: "DataRole",
  dicts: ['sys_data_permission','sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据角色表格数据
      dataRoleList: [],
      // 弹出层标题
      title: "",
 
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      // 数据权限默认选择
      dataDefaultSelect:[],

      // 日期范围
      dateRange: [],  
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        roleName: undefined,
        roleKey: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 表单校验
      rules: {
        roleName: [
          { required: true, message: "数据角色名称不能为空", trigger: "blur" }
        ],
        roleKey: [
          { required: true, message: "权限字符不能为空", trigger: "blur" }
        ],
        roleSort: [
          { required: true, message: "角色顺序不能为空", trigger: "blur" }
        ],
        permission: [
          { required: true, message: "数据权限不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询数据角色列表 */
    getList() {
      this.loading = true;
      listDataRole(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
          this.dataRoleList = response.data.rows;
          // console.log('----------','11111=',response)
          this.total = response.data.total;
          this.loading = false;
        }
      );
    }, 
    // 角色状态修改
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.roleName + '"角色吗？').then(function() {
        return changeDataRoleStatus(row.id);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.status = row.status === "0" ? "1" : "0";
      });
    },
    // 取消按钮
    cancel() {
      this.openDataScope = false;
      this.reset();
    }, 
    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      
      this.form = {
        id: undefined,
        roleName: undefined,
        roleKey: undefined,
        roleSort: 0,
        status: '0',  
        permission: undefined,
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
 
  
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
     
      this.openDataScope = true;
      this.title = "添加数据角色";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      let roleId = row.id || this.ids

      //let _this = this;
      //this.dataDefaultSelect.splice(0, this.dataDefaultSelect.length);// 回显先清空 
      this.dataDefaultSelect.length =[];// 回显初始化处理  *** 
      
      //let queryParams={id:roleId};
      getDataRole(roleId).then(response => {
        
        this.form = response.data;
      
        if(this.form.permission !=undefined && this.form.permission.length>=1) { 
          
          // 切割选择数据权限 默认回显
          this.dataDefaultSelect =this.form.permission.split(',');  
          
          //console.log('----dataDefaultSelect=',this.dataDefaultSelect); 
        } 

        this.openDataScope = true;

        // console.log('---- get = ', this.form,'--dataDefaultSelect= ', this.dataDefaultSelect); 
        this.title = "修改角色"; 
      });
    }, 
    /** 修改添加关闭 */
    closeDialog() {
      
      this.$refs.dataTree.setCheckedKeys([]); // 清空初始化 

    },
    /** 获取树选择节点信息 */
    getSelectNodes() {
       // 获取选择的数据权限节点
       let checkedNodes = this.$refs.dataTree.getCheckedNodes();
          if(checkedNodes==undefined) {
            this.$message.error;("请选择数据权限节点！");
            return;
          }
          else {
            let dataIds ='';
            checkedNodes.forEach((item)=>{
              dataIds += item.value;
              dataIds +=','
            });
            // console.log('----dataIds=',dataIds);
            // 去掉最后一个，号
            if(dataIds.length>1) { 
              dataIds = dataIds.slice(0,-1);   
            }
            this.form.permission= dataIds; // 设置选择的数据权限节点权限集合,逗号分割
          }
          // console.log('----------this.form.permission = ', this.form.permission)
    },
    /** 提交按钮 */
    submitForm: function() {
      // 获取树选择节点信息
      this.getSelectNodes();
      // 
      this.$refs["form"].validate(valid => {
        if (valid) {
          

          addOrUpdateDataRole(this.form).then(response => {
            
            this.openDataScope = false;
            this.getList();
          });

          if (this.form.id != undefined) {
            this.$modal.msgSuccess("修改成功");
          } else {
            this.$modal.msgSuccess("新增成功"); 
          }
        }
      });
    }, 
    /** 删除按钮操作 */
    handleDelete(row) {
      const roleId = row.id ;//|| this.ids;
      this.$modal.confirm('是否确认删除角色编号为"' + roleId + '"的数据项？').then(function() {
        return delDataRole(roleId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/role/export', {
        ...this.queryParams
      }, `role_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>