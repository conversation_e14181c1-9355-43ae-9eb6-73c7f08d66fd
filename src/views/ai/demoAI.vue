<template>
  <div class="app-container ai-demo-chat-page">
    <!-- 左侧历史记录区域 -->
    <aside class="history-sidebar" v-show="!isFullscreen">
      <div class="sidebar-header">
        <h3 class="sidebar-title">历史记录</h3>
        <el-button
          type="text"
          icon="el-icon-refresh"
          @click="refreshChatHistory"
          title="刷新历史记录"
          class="refresh-btn"
          :loading="isRefreshingHistory"
        ></el-button>
      </div>

      <div class="sidebar-content">
        <!-- 历史记录列表 -->
        <div class="history-section">
          <div v-if="chatHistory.length === 0" class="empty-state">
            <i class="el-icon-chat-line-round"></i>
            <p>暂无历史记录</p>
          </div>

          <div class="history-list">
            <div
              v-for="(record, index) in chatHistory"
              :key="index"
              :class="['history-item', { 'active': selectedHistoryIndex === index }]"
              @click="selectHistoryRecord(record, index)"
            >
              <div class="history-content">
                <div class="history-question">
                  {{ record.problem_text || '无标题' }}
                </div>
                <div class="history-answer">
                  {{ getHistoryPreview(record.answer_text) }}
                </div>
                <div class="history-time">
                  {{ formatHistoryTime(record.create_time) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </aside>

    <!-- 聊天面板 -->
    <section class="chat-container">
      <!-- 聊天头部 -->
      <div class="chat-header">
        <div class="header-left">
          <span class="welcome-text">
            你好！我是{{ (applicationInfo && applicationInfo.name) || 'AI智能助手' }}
          </span>
        </div>
        <div class="header-right">
          <el-button
            class="new-chat-btn"
            @click="createNewConversation"
            icon="el-icon-plus"
            size="small"
            type="primary"
            plain
            :loading="isCreatingChat"
          >
            新建对话
          </el-button>
        </div>
      </div>

      <!-- 聊天内容区域 -->
      <div class="chat-content">
        <div class="chatBox">
          <div v-if="errorMessage" class="error-message">
            {{ errorMessage }}
          </div>
          <div class="chat-messages" ref="messagesContainer">
            <!-- 欢迎消息 -->
            <div v-if="currentMessages.length === 0" class="welcome-message">
              <div class="welcome-content">
                <img src="@/assets/images/tz/u117.png" alt="AI助手" class="welcome-icon" />
                <h3>{{ currentWelcomeTitle }}</h3>
                <p>{{ currentWelcomeText }}</p>
                <div class="quick-questions" v-if="currentQuickQuestions.length > 0">
                  <h4>快速提问：</h4>
                  <div class="question-buttons">
                    <el-button
                      v-for="(question, index) in currentQuickQuestions"
                      :key="index"
                      size="small"
                      type="primary"
                      plain
                      @click="sendQuickQuestion(question)"
                    >
                      {{ question }}
                    </el-button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 对话消息列表 -->
            <div v-for="(message, index) in currentMessages" :key="index" class="message">
              <div v-if="message.role === 'user'" class="user-message-container">
                <article class="message-content user-message">{{ message.content }}</article>
                <img :src="userAvatar" class="avatar user-avatar" alt="用户头像">
              </div>
              <div v-else-if="message.role !== 'user' && message.content && message.content.trim()" class="bot-message-container">
                <img src="@/assets/images/tz/u117.png" class="avatar bot-avatar" alt="AI助手头像">
                <article class="message-content bot-message">
                  <!-- Enterprise格式的消息内容渲染 -->
                  <div class="enterprise-message-content markdown-content" v-html="renderMarkdown(message.content)"></div>
                </article>
              </div>
            </div>

            <!-- 正在思考指示器 -->
            <div v-if="isTyping" class="thinking-indicator">
              <img src="@/assets/images/tz/u117.png" class="avatar bot-avatar" alt="AI助手头像">
              <div class="thinking-content">
                <div class="thinking-text">
                  <i class="el-icon-loading"></i>
                  <span>正在思考...</span>
                </div>
                <div class="thinking-actions">
                  <span @click="stopThinking" class="stop-text">停止回答</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 输入区域 -->
          <div class="chat-input">
            <div class="input-controls">
              <el-tooltip :content="`流式响应: ${useStream ? '已启用' : '已禁用'}`" placement="top">
                <el-button
                  @click="toggleStreamMode"
                  :type="useStream ? 'primary' : 'default'"
                  size="mini"
                  class="stream-toggle"
                >
                  <i class="el-icon-video-play"></i>
                  {{ useStream ? 'Stream' : 'Normal' }}
                </el-button>
              </el-tooltip>

              <el-tooltip content="测试流式响应" placement="top">
                <el-button
                  @click="testStreamResponse"
                  size="mini"
                  type="success"
                  class="test-button"
                >
                  <i class="el-icon-cpu"></i>
                  Stream
                </el-button>
              </el-tooltip>

              <el-tooltip content="测试JSON数据过滤" placement="top">
                <el-button
                  @click="testRawJsonResponse"
                  size="mini"
                  type="warning"
                  class="test-button"
                >
                  <i class="el-icon-document"></i>
                  JSON
                </el-button>
              </el-tooltip>
            </div>
            <div class="input-area">
              <textarea
                v-model="inputMessage"
                :placeholder="currentPlaceholder"
                @keydown.enter.exact="handleEnterKey"
                @keydown.enter.shift.exact="handleShiftEnter"
                rows="3"
                :disabled="isLoading"
                maxlength="2000"
              />
              <button
                @click="sendUserMessage"
                :disabled="!inputMessage.trim() || isLoading"
                class="send-btn"
              >
                <i v-if="isLoading" class="el-icon-loading"></i>
                <i v-else class="el-icon-s-promotion"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { getApplicationProfile, sendMessage, openChat, getChatHistory } from '@/api/maxkb'
import hljs from 'highlight.js'

export default {
  name: 'DemoAI',
  components: {},
  data() {
    return {
      chatMode: 'normal', // normal, knowledge, analysis
      isFullscreen: false,
      showSidebar: true, // 是否显示侧边栏
      useStream: true,
      maxMessages: 100,
      messageCount: 0,
      connectionStatus: '已连接',
      currentTime: '',
      timeInterval: null,
      // 应用信息 (步骤1获取的数据)
      applicationInfo: null,

      // 历史记录相关
      conversations: [], // 所有对话历史
      filteredHistory: [], // 过滤后的历史记录
      currentConversationId: null, // 当前对话ID
      searchKeyword: '', // 搜索关键词
      isCreatingChat: false, // 是否正在创建新对话
      isRefreshingHistory: false, // 是否正在刷新历史记录

      // 聊天历史记录相关
      chatHistory: [], // 聊天历史记录列表
      selectedHistoryIndex: -1, // 当前选中的历史记录索引

      // 聊天功能相关
      currentMessages: [], // 当前对话的消息列表
      inputMessage: '', // 输入框内容
      isLoading: false, // 是否正在发送消息
      isTyping: false, // AI是否正在输入
      inputRows: 1, // 输入框行数
      conversationId: null, // 当前会话ID
      errorMessage: '', // 错误消息

      // 不同模式的配置
      chatConfigs: {
        normal: {
          title: 'AI智能助手',
          welcomeTitle: '欢迎使用AI智能助手',
          welcomeText: '我是您的智能助手，可以回答各种问题，协助您完成工作。请输入您的问题开始对话。',
          placeholder: '请输入您的问题...',
          quickQuestions: [
            '你好，请介绍一下自己',
            '你能帮我做什么？',
            '如何使用这个系统？',
            '请推荐一些学习资源'
          ]
        },
        knowledge: {
          title: '知识库问答',
          welcomeTitle: '知识库智能问答',
          welcomeText: '我可以基于知识库为您提供准确的信息检索和问答服务。请输入您想了解的内容。',
          placeholder: '请输入您想查询的知识...',
          quickQuestions: [
            '系统有哪些功能模块？',
            '如何进行企业管理？',
            '数据安全如何保障？',
            '系统支持哪些操作？'
          ]
        },
        analysis: {
          title: '数据分析助手',
          welcomeTitle: '智能数据分析',
          welcomeText: '我可以帮助您分析数据、生成报告和提供业务洞察。请描述您的分析需求。',
          placeholder: '请描述您的数据分析需求...',
          quickQuestions: [
            '分析企业风险分布情况',
            '生成月度数据报告',
            '对比不同区域的数据',
            '预测未来发展趋势'
          ]
        }
      }
    }
  },
  computed: {
    currentChatTitle() {
      // 优先使用从接口获取的应用名称
      if (this.applicationInfo && this.applicationInfo.name) {
        return this.applicationInfo.name
      }
      // 降级使用配置的标题
      return this.chatConfigs[this.chatMode].title
    },
    currentWelcomeTitle() {
      return this.chatConfigs[this.chatMode].welcomeTitle
    },
    currentWelcomeText() {
      return this.chatConfigs[this.chatMode].welcomeText
    },
    currentPlaceholder() {
      return this.chatConfigs[this.chatMode].placeholder
    },
    currentQuickQuestions() {
      return this.chatConfigs[this.chatMode].quickQuestions
    },
    chatModeText() {
      const modeMap = {
        normal: '普通对话',
        knowledge: '知识库问答',
        analysis: '数据分析'
      }
      return modeMap[this.chatMode]
    },

    // 是否使用流式模式
    isStreamMode() {
      return this.useStream
    },
    // 获取当前登录用户头像
    userAvatar() {
      // 尝试从vuex store获取用户头像
      if (this.$store && this.$store.getters && this.$store.getters.avatar) {
        return this.$store.getters.avatar
      }
      // 尝试从localStorage获取用户信息
      try {
        const userInfo = localStorage.getItem('userInfo')
        if (userInfo) {
          const user = JSON.parse(userInfo)
          if (user.avatar) {
            return user.avatar
          }
        }
      } catch (error) {
        console.warn('获取用户头像失败:', error)
      }
      // 默认头像
      return require('@/assets/images/profile.jpg')
    }
  },
  mounted() {
    this.initTime()
    this.checkConnection().then(() => {
      // 连接检查完成后，初始化对话和历史记录
      this.initializeConversation()
    })
    this.loadConversationHistory()
  },

  methods: {
    // 初始化对话
    async initializeConversation() {
      // 检查是否有从首页传递过来的消息
      const messageFromHome = this.$route.query.message

      try {
        // 如果没有会话ID，创建新会话
        if (!this.conversationId) {
          await this.createMaxKBConversation()
        }

        // 获取聊天历史记录
        await this.refreshChatHistory()

        // 如果有传递的消息，自动发送
        if (messageFromHome && messageFromHome.trim()) {
          this.inputMessage = messageFromHome.trim()
          this.sendUserMessage()
        }
      } catch (error) {
        console.error('初始化对话失败:', error)
      }
    },
  },
  beforeDestroy() {
    if (this.timeInterval) {
      clearInterval(this.timeInterval)
    }
  },
  methods: {
    // 初始化对话
    async initializeConversation() {
      // 检查是否有从首页传递过来的消息
      const messageFromHome = this.$route.query.message

      try {
        // 如果没有会话ID，创建新会话
        if (!this.conversationId) {
          await this.createMaxKBConversation()
        }

        // 获取聊天历史记录
        await this.refreshChatHistory()

        // 如果有传递的消息，自动发送
        if (messageFromHome && messageFromHome.trim()) {
          this.inputMessage = messageFromHome.trim()
          this.sendUserMessage()
        }
      } catch (error) {
        console.error('初始化对话失败:', error)
      }
    },
    // 切换侧边栏显示
    toggleSidebar() {
      this.showSidebar = !this.showSidebar
    },

    // 停止思考/回答
    stopThinking() {
      console.log('[demoAI] 用户请求停止思考')

      // 停止加载状态
      this.isLoading = false
      this.isTyping = false

      // 如果有正在进行的请求，可以在这里取消
      // 注意：由于我们使用的是Promise-based的API，这里主要是更新UI状态

      // 给最后一条AI消息添加停止提示
      if (this.currentMessages.length > 0) {
        const lastMessage = this.currentMessages[this.currentMessages.length - 1]
        if (lastMessage.role === 'assistant' && !lastMessage.content.trim()) {
          lastMessage.content = '回答已停止。'
        }
      }

      this.$message.info('已停止回答')

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    // 刷新聊天历史记录
    async refreshChatHistory() {
      if (!this.conversationId || !this.applicationInfo?.id) {
        console.log('[demoAI] 缺少必要参数，无法获取历史记录')
        return
      }

      try {
        this.isRefreshingHistory = true
        console.log('[demoAI] 开始获取聊天历史记录:', {
          applicationId: this.applicationInfo.id,
          chatId: this.conversationId
        })

        // 调用历史记录接口
        const response = await getChatHistory(this.conversationId)
          console.log('获取到聊天历史记录-----',response)
        if (response && response.data) {
         
          // 处理历史记录数据，按照chat.json格式
          const processedHistory = this.processHistoryData(response.data)
          this.chatHistory = processedHistory.slice(0, 20) // 最多显示20条
          console.log('[demoAI] 获取到聊天历史记录:', this.chatHistory.length, '条')
        } else {
          this.chatHistory = []
          console.log('[demoAI] 未获取到聊天历史记录')
        }
      } catch (error) {
        console.error('[demoAI] 获取聊天历史记录失败:', error)
        this.$message.error('获取历史记录失败')
        this.chatHistory = []
      } finally {
        this.isRefreshingHistory = false
      }
    },



    // 处理历史记录数据 (按照data.json格式)
    processHistoryData(data) {
      // 根据接口返回的数据结构处理
      if (data && data.records && Array.isArray(data.records)) {
        return data.records.map(record => {
          // 按照data.json的数据结构处理
          let processedRecord = {
            id: record.id,
            chat_id: record.chat_id,
            problem_text: record.problem_text || record.padding_problem_text || '',
            answer_text: record.answer_text || '',
            create_time: record.create_time,
            index: record.index || 0
          }

          // 处理answer_text中的<think>标签
          if (processedRecord.answer_text) {
            // 移除<think>标签内容
            let cleanAnswer = processedRecord.answer_text.replace(/<think>[\s\S]*?<\/think>\n*/g, '')

            // 移除开头的换行符
            cleanAnswer = cleanAnswer.replace(/^\n+/, '')

            processedRecord.answer_text = cleanAnswer.trim()
          }

          return processedRecord
        }).sort((a, b) => {
          // 按创建时间倒序排列，最新的在前面
          return new Date(b.create_time) - new Date(a.create_time)
        })
      }

      // 如果是直接的数组格式（兼容旧格式）
      if (Array.isArray(data)) {
        return data.map(record => {
          let processedRecord = {
            id: record.id,
            chat_id: record.chat_id,
            problem_text: record.problem_text || '',
            answer_text: record.answer_text || '',
            create_time: record.create_time,
            index: record.index || 0
          }

          if (processedRecord.answer_text) {
            let cleanAnswer = processedRecord.answer_text.replace(/<think>[\s\S]*?<\/think>\n*/g, '')
            cleanAnswer = cleanAnswer.replace(/^\n+/, '')
            processedRecord.answer_text = cleanAnswer.trim()
          }

          return processedRecord
        })
      }

      return []
    },



    // 选择历史记录
    selectHistoryRecord(record, index) {
      this.selectedHistoryIndex = index
      console.log('[demoAI] 选择历史记录:', record)

      // 清空当前消息
      this.currentMessages = []

      // 添加历史记录的问题和回答到当前对话
      if (record.problem_text) {
        this.addMessage('user', record.problem_text)
      }

      if (record.answer_text) {
        this.addMessage('assistant', record.answer_text)
      }

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    // 格式化历史记录时间
    formatHistoryTime(timeStr) {
      if (!timeStr) return ''

      try {
        const date = new Date(timeStr)
        const now = new Date()
        const diff = now - date

        // 小于1小时显示分钟
        if (diff < 60 * 60 * 1000) {
          const minutes = Math.floor(diff / (60 * 1000))
          return `${minutes}分钟前`
        }

        // 小于24小时显示小时
        if (diff < 24 * 60 * 60 * 1000) {
          const hours = Math.floor(diff / (60 * 60 * 1000))
          return `${hours}小时前`
        }

        // 大于24小时显示日期
        return date.toLocaleDateString()
      } catch (error) {
        return timeStr
      }
    },

    // 获取历史记录预览文本
    getHistoryPreview(answerText) {
      if (!answerText) return '无回答'

      // 移除markdown格式和特殊字符
      let cleanText = answerText
        .replace(/\*\*/g, '') // 移除粗体标记
        .replace(/\*/g, '') // 移除斜体标记
        .replace(/\n/g, ' ') // 换行替换为空格
        .replace(/\s+/g, ' ') // 多个空格合并为一个
        .trim()

      // 截取前60个字符
      if (cleanText.length > 60) {
        return cleanText.substring(0, 60) + '...'
      }

      return cleanText
    },

    // 获取空响应时的友好提示
    getEmptyResponseMessage() {
      const messages = [
        '抱歉，我暂时没有收到完整的回复。请您再试一次，或者换个方式提问。',
        '看起来我的回答出现了问题。请您重新提问，我会尽力为您提供帮助。',
        '很抱歉，我的回复似乎不完整。建议您稍后再试，或者重新描述您的问题。',
        '抱歉，我遇到了一些技术问题。请您再次尝试，或者联系管理员获取帮助。'
      ]

      // 随机选择一个友好的提示
      const randomIndex = Math.floor(Math.random() * messages.length)
      return messages[randomIndex]
    },

    // 解析流式数据
    parseStreamData(data) {
      // 解析流式数据，基于data.json的格式
      const chunks = []

      if (typeof data === 'string') {
        // 处理SSE格式的数据
        const lines = data.split('\n')
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const parsed = JSON.parse(line.substring(6))
              chunks.push(parsed)
            } catch (e) {
              console.warn('解析流式数据行失败:', line)
            }
          }
        }
      } else if (Array.isArray(data)) {
        // 处理数组格式的数据
        chunks.push(...data)
      } else if (data && typeof data === 'object') {
        // 处理单个对象
        chunks.push(data)
      }

      return chunks
    },

    // 处理流式内容
    processStreamContent(content) {
      if (!content) return ''

      console.log('[demoAI] 处理流式内容，原始长度:', content.length)

      // 1. 移除JSON数据和HTML标签
      content = this.removeJsonData(content)

      // 2. 移除<think>标签
      content = this.removeThinkTags(content)

      // 3. Unicode解码
      content = this.decodeUnicode(content)

      // 4. 最终清理
      content = this.finalContentCleanup(content)

      console.log('[demoAI] 处理后流式内容，长度:', content.length)

      return content
    },

    // 移除JSON数据和无用的HTML标签
    removeJsonData(content) {
      if (!content) return ''

      console.log('[demoAI] 移除JSON数据前:', content.substring(0, 200))

      // 移除包含JSON数据的<p>标签（更全面的匹配）
      content = content.replace(/<p>data:\s*\{[^}]*(?:\{[^}]*\}[^}]*)*\}[^<]*<\/p>/g, '')

      // 移除其他包含data:的内容（处理嵌套JSON）
      content = content.replace(/data:\s*\{[^}]*(?:\{[^}]*\}[^}]*)*\}/g, '')

      // 移除单独的data:行
      content = content.replace(/^data:\s*.*$/gm, '')

      // 移除空的HTML标签
      content = content.replace(/<p>\s*<\/p>/g, '')
      content = content.replace(/<div>\s*<\/div>/g, '')
      content = content.replace(/<span>\s*<\/span>/g, '')

      // 移除多余的换行符和空白字符
      content = content.replace(/\n\s*\n/g, '\n')
      content = content.replace(/^\s+|\s+$/g, '')

      console.log('[demoAI] 移除JSON数据后:', content.substring(0, 200))

      return content
    },

    // 延迟函数
    sleep(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    },

    // 切换流式模式
    toggleStreamMode() {
      this.useStream = !this.useStream
      console.log(`[demoAI] 流式模式已${this.useStream ? '启用' : '禁用'}`)
      this.$message.info(`流式响应已${this.useStream ? '启用' : '禁用'}`)
    },

    // 测试流式响应（用于调试）
    async testStreamResponse() {
      console.log('[demoAI] 开始测试流式响应')

      // 模拟真实的流式数据（基于data.json格式）
      const testStreamData = `data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "64", "is_end": false}
data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "的", "is_end": false}
data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "平方根", "is_end": false}
data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "是", "is_end": false}
data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "8", "is_end": false}
data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "。", "is_end": false}
data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "\\n\\n", "is_end": false}
data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "这是", "is_end": false}
data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "一个", "is_end": false}
data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "简单的", "is_end": false}
data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "数学", "is_end": false}
data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "计算", "is_end": false}
data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "：", "is_end": false}
data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "\\n\\n", "is_end": false}
data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "\`\`\`python", "is_end": false}
data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "\\nimport math", "is_end": false}
data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "\\n\\ndef calculate_square_root(number):", "is_end": false}
data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "\\n    result = math.sqrt(number)", "is_end": false}
data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "\\n    return result", "is_end": false}
data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "\\n\\n# 计算64的平方根", "is_end": false}
data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "\\nresult = calculate_square_root(64)", "is_end": false}
data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "\\nprint(f\\"64的平方根是: {result}\\")", "is_end": false}
data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "\\n\`\`\`", "is_end": false}
data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "\\n\\n计算结果：8", "is_end": true}`

      console.log('[demoAI] 测试流式数据长度:', testStreamData.length)

      // 模拟流式效果
      const aiMessageIndex = this.currentMessages.length
      this.addMessage('assistant', '')
      this.isTyping = true

      // 使用真实的流式数据处理逻辑
      await this.processRealStreamData(testStreamData, aiMessageIndex)
      this.isTyping = false

      console.log('[demoAI] 测试流式响应完成')
    },

    // 测试包含原始JSON数据的情况
    async testRawJsonResponse() {
      console.log('[demoAI] 开始测试原始JSON数据处理')

      // 模拟包含原始JSON数据的响应
      const rawContent = `<p>data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "chat_record_id": "fbaabc40-6195-11f0-ab64-0242ac120003", "operate": true, "content": "", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}</p>

实际的回答内容：64的平方根是8。

<p>data: {"chat_id": "f6275468-6195-11f0-b28f-0242ac120003", "content": "完成", "is_end": true}</p>`

      // 处理内容
      const processedContent = this.processStreamContent(rawContent)
      console.log('[demoAI] 处理后的内容:', processedContent)

      // 显示处理结果
      this.addMessage('assistant', processedContent || '处理后内容为空')
    },

    // 自动调整输入框高度
    adjustTextareaHeight() {
      this.$nextTick(() => {
        const textarea = this.$refs.messageInput
        if (textarea) {
          textarea.style.height = 'auto'
          const scrollHeight = textarea.scrollHeight
          const maxHeight = 120 // 最大高度
          textarea.style.height = Math.min(scrollHeight, maxHeight) + 'px'
        }
      })
    },

    // 按照enterprise chat格式处理AI响应
    processEnterpriseAIResponse(response) {
      try {
        // 1. 基础验证
        if (!response) {
          return '抱歉，未收到有效回复。'
        }

        console.log('[demoAI] 开始处理enterprise格式响应:', response)

        // 2. 提取内容 (按照enterprise chat.json的格式)
        let content = ''

        if (response.data && response.data.content) {
          // 优先使用data.content字段
          content = response.data.content
        } else if (response.data && response.data.answer_list && response.data.answer_list[0]) {
          // 备选answer_list[0].content
          content = response.data.answer_list[0].content
        } else if (response.data && response.data.reasoning_content) {
          // 最后使用reasoning_content
          content = response.data.reasoning_content
        } else if (typeof response === 'string') {
          // 如果响应本身是字符串
          content = response
        } else if (response.content) {
          // 直接的content字段
          content = response.content
        } else {
          console.log('[demoAI] 未识别的响应格式，完整响应:', JSON.stringify(response, null, 2))
          return this.getEmptyResponseMessage()
        }

        console.log('[demoAI] 提取的原始内容:', content)

        // 3. 移除JSON数据和无用标签
        content = this.removeJsonData(content)

        // 4. 处理<think>标签 (移除AI思考过程)
        content = this.removeThinkTags(content)

        // 5. Unicode解码
        content = this.decodeUnicode(content)

        // 6. Markdown渲染
        content = this.convertMarkdownToHtml(content)

        // 7. 最终清理
        content = this.finalContentCleanup(content)

        // 8. 检查最终内容是否为空
        if (!content || !content.trim()) {
          console.log('[demoAI] 处理后内容为空，返回友好提示')
          return this.getEmptyResponseMessage()
        }

        console.log('[demoAI] 最终处理结果:', content)
        return content

      } catch (error) {
        console.error('[demoAI] 处理enterprise响应失败:', error)
        return '抱歉，处理回复时出现错误，请稍后再试。'
      }
    },

    // 移除<think>标签
    removeThinkTags(content) {
      if (!content) return content

      try {
        // 移除<think>标签及其内容
        let cleanedContent = content.replace(/<think>[\s\S]*?<\/think>\n*/g, '')

        // 清理多余的空白行
        cleanedContent = cleanedContent.replace(/\n\s*\n\s*\n/g, '\n\n')

        console.log('[demoAI] 移除think标签后:', cleanedContent.substring(0, 200) + '...')
        return cleanedContent.trim()
      } catch (error) {
        console.error('[demoAI] 移除think标签失败:', error)
        return content
      }
    },

    // 最终内容清理
    finalContentCleanup(content) {
      if (!content) return content

      try {
        // 清理多余的HTML标签
        let cleaned = content
          .replace(/<script[\s\S]*?<\/script>/gi, '') // 移除script标签
          .replace(/<style[\s\S]*?<\/style>/gi, '') // 移除style标签
          .replace(/\s+/g, ' ') // 合并多个空格
          .trim()

        return cleaned
      } catch (error) {
        console.error('[demoAI] 最终清理失败:', error)
        return content
      }
    },

    // 渲染Markdown内容
    renderMarkdown(content) {
      if (!content) return ''

      // 处理代码块高亮
      let processedContent = this.convertMarkdownToHtml(content)

      // 处理代码块高亮
      processedContent = this.processCodeBlocks(processedContent)

      return processedContent
    },

    // 处理代码块高亮
    processCodeBlocks(content) {
      // 处理行内代码
      content = content.replace(/<code class="language-(\w+)">(.*?)<\/code>/gs, (_, language, code) => {
        // 解码HTML实体
        const decodedCode = code
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .replace(/&amp;/g, '&')
          .replace(/&quot;/g, '"')
          .replace(/&#39;/g, "'")

        return `<pre><code class="hljs language-${language}">${this.highlightCode(decodedCode, language)}</code></pre>`
      })

      // 处理普通代码块
      content = content.replace(/<pre><code>(.*?)<\/code><\/pre>/gs, (_, code) => {
        const decodedCode = code
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .replace(/&amp;/g, '&')
          .replace(/&quot;/g, '"')
          .replace(/&#39;/g, "'")

        return `<pre><code class="hljs">${this.highlightCode(decodedCode, 'javascript')}</code></pre>`
      })

      return content
    },

    // 代码高亮
    highlightCode(code, language) {
      try {
        if (language && hljs.getLanguage(language)) {
          return hljs.highlight(language, code).value
        } else {
          return hljs.highlightAuto(code).value
        }
      } catch (error) {
        console.warn('代码高亮失败:', error)
        // 返回转义后的代码
        return code
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/"/g, '&quot;')
          .replace(/'/g, '&#39;')
      }
    },

    // 转换Markdown为HTML (增强版，支持更多Markdown语法)
    convertMarkdownToHtml(text) {
      if (!text) return ''

      let html = text

      try {
        // 1. 处理代码块 (支持语言标识)
        html = html.replace(/```(\w+)?\n?([\s\S]*?)```/g, (_, lang, code) => {
          const language = lang ? ` class="language-${lang}"` : ''
          return `<pre><code${language}>${this.escapeHtml(code.trim())}</code></pre>`
        })

        // 2. 处理行内代码
        html = html.replace(/`([^`\n]+)`/g, '<code>$1</code>')

        // 3. 处理粗体和斜体
        html = html.replace(/\*\*\*(.*?)\*\*\*/g, '<strong><em>$1</em></strong>') // 粗斜体
        html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // 粗体
        html = html.replace(/\*(.*?)\*/g, '<em>$1</em>') // 斜体

        // 4. 处理删除线
        html = html.replace(/~~(.*?)~~/g, '<del>$1</del>')

        // 5. 处理链接
        html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')

        // 6. 处理图片
        html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1" style="max-width: 100%; height: auto;">')

        // 7. 处理标题
        html = html.replace(/^#{6}\s+(.*$)/gm, '<h6>$1</h6>')
        html = html.replace(/^#{5}\s+(.*$)/gm, '<h5>$1</h5>')
        html = html.replace(/^#{4}\s+(.*$)/gm, '<h4>$1</h4>')
        html = html.replace(/^#{3}\s+(.*$)/gm, '<h3>$1</h3>')
        html = html.replace(/^#{2}\s+(.*$)/gm, '<h2>$1</h2>')
        html = html.replace(/^#{1}\s+(.*$)/gm, '<h1>$1</h1>')

        // 8. 处理水平分割线
        html = html.replace(/^---+$/gm, '<hr>')
        html = html.replace(/^\*\*\*+$/gm, '<hr>')

        // 9. 处理无序列表
        html = html.replace(/^[\s]*[-*+]\s+(.*)$/gm, '<li>$1</li>')
        html = html.replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>')

        // 10. 处理有序列表
        html = html.replace(/^[\s]*\d+\.\s+(.*)$/gm, '<li>$1</li>')
        // 注意：这里简化处理，实际应该区分有序和无序列表

        // 11. 处理引用
        html = html.replace(/^>\s+(.*)$/gm, '<blockquote>$1</blockquote>')
        html = html.replace(/(<blockquote>.*<\/blockquote>)/gs, '$1')

        // 12. 处理表格 (简化版)
        const tableRegex = /^\|(.+)\|\s*\n\|[-\s|:]+\|\s*\n((?:\|.+\|\s*\n?)*)/gm
        html = html.replace(tableRegex, (_, header, rows) => {
          const headerCells = header.split('|').map(cell => `<th>${cell.trim()}</th>`).join('')
          const rowCells = rows.trim().split('\n').map(row => {
            const cells = row.split('|').slice(1, -1).map(cell => `<td>${cell.trim()}</td>`).join('')
            return `<tr>${cells}</tr>`
          }).join('')
          return `<table><thead><tr>${headerCells}</tr></thead><tbody>${rowCells}</tbody></table>`
        })

        // 13. 处理段落 (在所有其他处理完成后)
        const lines = html.split('\n')
        const processedLines = []
        let inCodeBlock = false
        let inList = false
        let currentParagraph = []

        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim()

          // 检查是否在代码块中
          if (line.includes('<pre><code')) inCodeBlock = true
          if (line.includes('</code></pre>')) inCodeBlock = false

          // 检查是否在列表中
          if (line.includes('<ul>') || line.includes('<ol>')) inList = true
          if (line.includes('</ul>') || line.includes('</ol>')) inList = false

          if (inCodeBlock || inList ||
              line.startsWith('<h') || line.startsWith('<hr') ||
              line.startsWith('<blockquote') || line.startsWith('<table') ||
              line.includes('<pre>') || line.includes('<ul>') || line.includes('<ol>')) {
            // 结束当前段落
            if (currentParagraph.length > 0) {
              processedLines.push(`<p>${currentParagraph.join(' ')}</p>`)
              currentParagraph = []
            }
            processedLines.push(line)
          } else if (line === '') {
            // 空行结束段落
            if (currentParagraph.length > 0) {
              processedLines.push(`<p>${currentParagraph.join(' ')}</p>`)
              currentParagraph = []
            }
          } else {
            // 普通文本行
            currentParagraph.push(line)
          }
        }

        // 处理最后的段落
        if (currentParagraph.length > 0) {
          processedLines.push(`<p>${currentParagraph.join(' ')}</p>`)
        }

        html = processedLines.join('\n')

        // 14. 清理多余的空白
        html = html.replace(/\n\s*\n/g, '\n')

        return html
      } catch (error) {
        console.error('[demoAI] Markdown渲染失败:', error)
        return text.replace(/\n/g, '<br>')
      }
    },

    // HTML转义函数
    escapeHtml(text) {
      const div = document.createElement('div')
      div.textContent = text
      return div.innerHTML
    },

    // 设置聊天模式
    setChatMode(mode) {
      if (this.chatMode !== mode) {
        this.chatMode = mode
        this.$message.success(`已切换到${this.chatModeText}模式`)

        // 清空当前对话
        this.currentMessages = []
        this.inputMessage = ''
      }
    },

    // 处理全屏变化
    handleFullscreenChange(isFullscreen) {
      this.isFullscreen = isFullscreen
    },

    // 初始化时间
    initTime() {
      this.updateTime()
      this.timeInterval = setInterval(this.updateTime, 1000)
    },

    // 更新时间
    updateTime() {
      this.currentTime = new Date().toLocaleTimeString('zh-CN')
    },

    // 步骤1: 检查连接状态并获取应用信息
    async checkConnection() {
      try {
        console.log('[demoAI] 步骤1: 调用 /api/application/profile 获取应用信息...')
        const response = await getApplicationProfile()

        console.log('[demoAI] 步骤1 - 原始响应:', response)

        // 根据您提供的响应格式，应用信息在 response.data 中
        let appInfo = null
        if (response && response.data) {
          appInfo = response.data
        } else if (response && response.id) {
          // 兼容直接返回应用信息的格式
          appInfo = response
        }

        if (!appInfo || !appInfo.id) {
          throw new Error('响应中缺少应用信息或应用ID')
        }

        // 存储应用信息供后续使用
        this.applicationInfo = appInfo
        this.connectionStatus = '已连接'

        console.log('[demoAI] 步骤1 - 获取应用信息成功:', appInfo)
        console.log('[demoAI] 应用ID:', appInfo.id)
        console.log('[demoAI] 应用名称:', appInfo.name)

        // 显示连接成功消息和应用信息
        if (appInfo && appInfo.name) {
          this.$message.success(`AI助手服务连接成功: ${appInfo.name}`)
        } else {
          this.$message.success('AI助手服务连接成功')
        }
      } catch (error) {
        this.connectionStatus = '连接失败'
        this.applicationInfo = null
        console.error('[demoAI] 步骤1 失败 - 连接maxKB失败:', error)

        // 显示连接失败警告
        this.$message.warning('AI助手服务连接失败，将使用离线模式')
      }
    },

    // 创建新对话
    async createNewConversation() {
      this.isCreatingChat = true
      try {
        const newConversation = {
          id: this.generateId(),
          title: '',
          lastMessage: '',
          createdAt: new Date(),
          updatedAt: new Date(),
          mode: this.chatMode,
          messages: []
        }

        this.conversations.unshift(newConversation)
        this.currentConversationId = newConversation.id
        this.saveConversationHistory()
        this.filterHistory()

        // 清空当前消息
        this.currentMessages = []
        this.inputMessage = ''
        this.conversationId = null

        // 为新对话创建maxKB会话
        await this.createMaxKBConversation()

        this.$message.success('新对话已创建')
      } catch (error) {
        this.$message.error('创建对话失败')
        console.error('创建对话失败:', error)
      } finally {
        this.isCreatingChat = false
      }
    },

    // 加载对话历史
    loadConversationHistory() {
      try {
        const saved = localStorage.getItem('ai_conversations')
        if (saved) {
          this.conversations = JSON.parse(saved).map(conv => ({
            ...conv,
            createdAt: new Date(conv.createdAt),
            updatedAt: new Date(conv.updatedAt)
          }))
        }
        this.filterHistory()
      } catch (error) {
        console.error('加载对话历史失败:', error)
        this.conversations = []
      }
    },

    // 保存对话历史
    saveConversationHistory() {
      try {
        localStorage.setItem('ai_conversations', JSON.stringify(this.conversations))
      } catch (error) {
        console.error('保存对话历史失败:', error)
      }
    },

    // 刷新历史记录
    async refreshHistory() {
      this.isRefreshingHistory = true
      try {
        // 模拟刷新延迟
        await new Promise(resolve => setTimeout(resolve, 500))
        this.loadConversationHistory()
        this.$message.success('历史记录已刷新')
      } catch (error) {
        this.$message.error('刷新失败')
      } finally {
        this.isRefreshingHistory = false
      }
    },

    // 过滤历史记录
    filterHistory() {
      if (!this.searchKeyword.trim()) {
        this.filteredHistory = [...this.conversations]
      } else {
        const keyword = this.searchKeyword.toLowerCase()
        this.filteredHistory = this.conversations.filter(conv =>
          (conv.title && conv.title.toLowerCase().includes(keyword)) ||
          (conv.lastMessage && conv.lastMessage.toLowerCase().includes(keyword))
        )
      }
    },

    // 加载对话
    loadConversation(conversation) {
      if (!conversation || !conversation.id) {
        console.error('无效的对话对象:', conversation)
        return
      }

      this.currentConversationId = conversation.id
      this.chatMode = conversation.mode || 'normal'

      // 加载对话消息
      if (conversation.messages && Array.isArray(conversation.messages)) {
        this.currentMessages = [...conversation.messages]
      } else {
        this.currentMessages = []
      }

      this.$message.success(`已加载对话: ${conversation.title || '新对话'}`)
    },

    // 处理对话操作
    handleConversationAction(command) {
      if (!command || typeof command !== 'object') {
        console.error('无效的命令对象:', command)
        return
      }

      const { action, id } = command
      if (!action || !id) {
        console.error('缺少必要的参数:', { action, id })
        return
      }

      const conversation = this.conversations.find(conv => conv.id === id)
      if (!conversation) {
        console.error('未找到对话:', id)
        return
      }

      switch (action) {
        case 'rename':
          this.renameConversation(conversation)
          break
        case 'delete':
          this.deleteConversation(conversation)
          break
        default:
          console.error('未知的操作:', action)
      }
    },

    // 重命名对话
    renameConversation(conversation) {
      this.$prompt('请输入新的对话名称', '重命名对话', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: conversation.title || '新对话',
        inputValidator: (value) => {
          if (!value || !value.trim()) {
            return '对话名称不能为空'
          }
          return true
        }
      }).then(({ value }) => {
        conversation.title = value.trim()
        conversation.updatedAt = new Date()
        this.saveConversationHistory()
        this.filterHistory()
        this.$message.success('重命名成功')
      }).catch(() => {})
    },

    // 删除对话
    deleteConversation(conversation) {
      this.$confirm(`确定要删除对话"${conversation.title || '新对话'}"吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.conversations.findIndex(conv => conv.id === conversation.id)
        if (index > -1) {
          this.conversations.splice(index, 1)

          // 如果删除的是当前对话，创建新对话
          if (this.currentConversationId === conversation.id) {
            this.createNewConversation()
          }

          this.saveConversationHistory()
          this.filterHistory()
          this.$message.success('删除成功')
        }
      }).catch(() => {})
    },

    // 格式化时间
    formatTime(date) {
      if (!date) return ''

      const now = new Date()
      const time = new Date(date)
      const diff = now - time

      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return `${Math.floor(diff / 60000)}分钟前`
      } else if (diff < 86400000) { // 24小时内
        return time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      } else if (diff < 604800000) { // 7天内
        const days = Math.floor(diff / 86400000)
        return `${days}天前`
      } else {
        return time.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
      }
    },

    // 生成唯一ID
    generateId() {
      return 'conv_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
    },

    // 聊天功能方法
    // 发送消息
    async sendUserMessage() {
      if (!this.inputMessage.trim() || this.isLoading) return

      const userMessage = this.inputMessage.trim()
      this.inputMessage = ''
      this.adjustInputHeight()

      // 添加用户消息
      this.addMessage('user', userMessage)

      this.isLoading = true
      this.isTyping = true

      try {
        // 模拟AI回复
        await this.simulateAIResponse(userMessage)
      } catch (error) {
        console.error('发送消息失败:', error)
        this.addMessage('assistant', '抱歉，我遇到了一些问题，请稍后再试。')
        this.$message.error('发送消息失败')
      } finally {
        this.isLoading = false
        // 注意：isTyping 在流式响应完成后才设置为false，不在这里设置

        // 对话完成后更新历史记录
        if (this.conversationId) {
          this.$nextTick(() => {
            this.refreshChatHistory()
          })
        }
      }
    },

    // 调用maxKB AI回复 (支持流式和非流式)
    async simulateAIResponse(userMessage) {
      try {
        // 如果没有会话ID，先创建会话
        if (!this.conversationId) {
          await this.createMaxKBConversation()
        }

        console.log('[demoAI] 准备发送消息:', {
          message: userMessage,
          chat_id: this.conversationId,
          stream: this.isStreamMode
        })

        // 根据stream模式选择不同的处理方式
        if (this.isStreamMode) {
          await this.handleStreamResponse(userMessage)
        } else {
          await this.handleNormalResponse(userMessage)
        }
      } catch (error) {
        console.error('调用maxKB API失败:', error)
        this.handleAPIError(userMessage, error)
      }
    },

    // 处理流式响应
    async handleStreamResponse(userMessage) {
      console.log('[demoAI] 开始流式响应处理')

      // 创建一个空的AI消息用于流式更新
      const aiMessageIndex = this.currentMessages.length
      this.addMessage('assistant', '')

      try {
        // 调用流式API
        const response = await sendMessage({
          chat_id: this.conversationId,
          message: userMessage,
          re_chat: false,
          stream: true
        })

        console.log('[demoAI] 流式API响应类型:', typeof response)
        console.log('[demoAI] 流式API响应内容:', response)

        // 处理流式响应数据
        if (response) {
          // 检查是否是真正的流式响应数据
          if (this.isStreamResponseData(response)) {
            console.log('[demoAI] 检测到流式响应数据，开始实时处理')
            await this.processRealStreamData(response, aiMessageIndex)
          } else {
            // 如果API返回的是完整响应（非真正的流式），我们模拟流式效果
            const processedContent = this.processEnterpriseAIResponse(response)

            if (processedContent && processedContent.trim()) {
              console.log('[demoAI] 开始模拟流式效果，内容长度:', processedContent.length)
              // 模拟流式效果：逐字符显示
              await this.simulateStreamEffect(processedContent, aiMessageIndex)
            } else {
              console.log('[demoAI] 处理后内容为空')
              if (this.currentMessages[aiMessageIndex]) {
                this.currentMessages[aiMessageIndex].content = this.getEmptyResponseMessage()
              }
            }
          }

          // 流式响应完成，停止thinking指示器
          this.isTyping = false
        } else {
          console.log('[demoAI] API响应为空')
          if (this.currentMessages[aiMessageIndex]) {
            this.currentMessages[aiMessageIndex].content = this.getEmptyResponseMessage()
          }
          // 流式响应完成，停止thinking指示器
          this.isTyping = false
        }
      } catch (error) {
        console.error('[demoAI] 流式响应处理失败:', error)
        if (this.currentMessages[aiMessageIndex]) {
          this.currentMessages[aiMessageIndex].content = this.getEmptyResponseMessage()
        }
        // 出错时也要停止thinking指示器
        this.isTyping = false
      }
    },

    // 检查是否是流式响应数据
    isStreamResponseData(response) {
      // 检查是否包含流式数据的特征
      if (typeof response === 'string') {
        return response.includes('data: {') && response.includes('"content":')
      }

      // 检查是否是包含流式数据的对象
      if (response && response.data) {
        return response.data.hasOwnProperty('content') || response.data.hasOwnProperty('reasoning_content')
      }

      return false
    },

    // 处理真正的流式数据
    async processRealStreamData(response, aiMessageIndex) {
      let accumulatedContent = ''

      try {
        if (typeof response === 'string') {
          // 处理SSE格式的流式数据
          const lines = response.split('\n')

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const jsonStr = line.substring(6).trim()
                if (jsonStr === '[DONE]') {
                  break
                }

                const data = JSON.parse(jsonStr)
                console.log('[demoAI] 解析流式数据块:', data)

                // 提取有意义的内容
                let contentChunk = ''
                if (data.content && data.content.trim()) {
                  contentChunk = data.content
                } else if (data.reasoning_content && data.reasoning_content.trim()) {
                  contentChunk = data.reasoning_content
                }

                // 只有当内容不为空时才处理
                if (contentChunk) {
                  accumulatedContent += contentChunk

                  // 实时更新消息内容
                  if (this.currentMessages[aiMessageIndex]) {
                    // 处理内容（Unicode解码、清理等）
                    const processedContent = this.processStreamContent(accumulatedContent)
                    this.currentMessages[aiMessageIndex].content = processedContent
                  }

                  // 滚动到底部
                  this.$nextTick(() => {
                    this.scrollToBottom()
                  })

                  // 添加延迟模拟打字效果
                  await this.sleep(50)
                }

                // 检查是否结束
                if (data.is_end === true) {
                  console.log('[demoAI] 流式响应结束')
                  break
                }
              } catch (e) {
                console.warn('[demoAI] 解析流式数据行失败:', line, e)
              }
            }
          }
        } else if (response.data) {
          // 处理单个数据块
          let contentChunk = ''
          if (response.data.content && response.data.content.trim()) {
            contentChunk = response.data.content
          } else if (response.data.reasoning_content && response.data.reasoning_content.trim()) {
            contentChunk = response.data.reasoning_content
          }

          if (contentChunk) {
            accumulatedContent = contentChunk
            const processedContent = this.processStreamContent(accumulatedContent)

            if (this.currentMessages[aiMessageIndex]) {
              this.currentMessages[aiMessageIndex].content = processedContent
            }
          }
        }

        // 最终处理：如果没有内容，显示友好提示
        if (!accumulatedContent.trim()) {
          if (this.currentMessages[aiMessageIndex]) {
            this.currentMessages[aiMessageIndex].content = this.getEmptyResponseMessage()
          }
        }

      } catch (error) {
        console.error('[demoAI] 处理流式数据失败:', error)
        if (this.currentMessages[aiMessageIndex]) {
          this.currentMessages[aiMessageIndex].content = this.getEmptyResponseMessage()
        }
      }
    },

    // 判断是否是真正的流式响应
    isRealStreamResponse(response) {
      // 如果响应包含流式数据标识，则认为是真正的流式响应
      return response && (
        response.stream === true ||
        (typeof response === 'string' && response.includes('data:')) ||
        (response.data && Array.isArray(response.data))
      )
    },

    // 处理真正的流式响应
    async handleRealStreamResponse(response, aiMessageIndex) {
      let accumulatedContent = ''

      try {
        // 如果是字符串格式的SSE数据
        if (typeof response === 'string') {
          const lines = response.split('\n')
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.substring(6))
                if (data.content) {
                  accumulatedContent += data.content
                  // 实时更新消息内容
                  if (this.currentMessages[aiMessageIndex]) {
                    this.currentMessages[aiMessageIndex].content = accumulatedContent
                  }
                  // 滚动到底部
                  this.$nextTick(() => {
                    this.scrollToBottom()
                  })
                  // 添加延迟模拟打字效果
                  await this.sleep(30)
                }

                if (data.is_end) {
                  break
                }
              } catch (e) {
                console.warn('解析流式数据失败:', line)
              }
            }
          }
        }
        // 如果是数组格式的数据
        else if (response.data && Array.isArray(response.data)) {
          for (const chunk of response.data) {
            if (chunk.content) {
              accumulatedContent += chunk.content
              // 实时更新消息内容
              if (this.currentMessages[aiMessageIndex]) {
                this.currentMessages[aiMessageIndex].content = accumulatedContent
              }
              // 滚动到底部
              this.$nextTick(() => {
                this.scrollToBottom()
              })
              // 添加延迟模拟打字效果
              await this.sleep(30)
            }

            if (chunk.is_end) {
              break
            }
          }
        }

        // 最终处理内容
        const finalContent = this.processStreamContent(accumulatedContent)
        if (this.currentMessages[aiMessageIndex]) {
          this.currentMessages[aiMessageIndex].content = finalContent || this.getEmptyResponseMessage()
        }
      } catch (error) {
        console.error('处理真正的流式响应失败:', error)
        if (this.currentMessages[aiMessageIndex]) {
          this.currentMessages[aiMessageIndex].content = this.getEmptyResponseMessage()
        }
      }
    },

    // 模拟流式效果
    async simulateStreamEffect(content, aiMessageIndex) {
      console.log('[demoAI] 开始模拟流式效果')

      // 将内容按照合适的单位分割（考虑中文和英文）
      const chunks = this.splitContentForStream(content)
      let accumulatedContent = ''

      for (let i = 0; i < chunks.length; i++) {
        accumulatedContent += chunks[i]

        if (this.currentMessages[aiMessageIndex]) {
          // 实时更新消息内容，包含代码块处理
          this.currentMessages[aiMessageIndex].content = accumulatedContent
        }

        // 滚动到底部
        this.$nextTick(() => {
          this.scrollToBottom()
        })

        // 添加延迟模拟打字效果
        await this.sleep(30)
      }

      console.log('[demoAI] 流式效果完成')
    },

    // 将内容分割为适合流式显示的块
    splitContentForStream(content) {
      const chunks = []
      let currentChunk = ''

      for (let i = 0; i < content.length; i++) {
        const char = content[i]
        currentChunk += char

        // 中文字符或标点符号作为一个块
        if (/[\u4e00-\u9fff\u3000-\u303f\uff00-\uffef]/.test(char)) {
          chunks.push(currentChunk)
          currentChunk = ''
        }
        // 英文单词以空格或标点分割
        else if (/[\s\.,;:!?]/.test(char)) {
          chunks.push(currentChunk)
          currentChunk = ''
        }
        // 如果当前块太长，强制分割
        else if (currentChunk.length >= 3) {
          chunks.push(currentChunk)
          currentChunk = ''
        }
      }

      // 添加剩余内容
      if (currentChunk) {
        chunks.push(currentChunk)
      }

      return chunks
    },

    // 处理非流式响应
    async handleNormalResponse(userMessage) {
      try {
        const response = await sendMessage({
          chat_id: this.conversationId,
          message: userMessage,
          re_chat: false,
          stream: false
        })

        console.log('[demoAI] maxKB API响应:', response)

        // 按照enterprise chat格式处理API响应
        if (response) {
          const processedContent = this.processEnterpriseAIResponse(response)
          console.log('[demoAI] 处理后的AI回复内容:', processedContent)

          // 添加AI消息内容
          if (processedContent && processedContent.trim()) {
            this.addMessage('assistant', processedContent)
          } else {
            this.addMessage('assistant', this.getEmptyResponseMessage())
          }
        } else {
          this.addMessage('assistant', this.getEmptyResponseMessage())
        }
      } finally {
        // 非流式响应完成，停止thinking指示器
        this.isTyping = false
      }
    },

    // 处理API错误
    handleAPIError(userMessage, error) {
      console.error('调用maxKB API失败:', error)

      // API调用失败时的降级处理
      let fallbackResponse = ''

      if (userMessage.includes('你好') || userMessage.includes('hello')) {
        fallbackResponse = '你好！我是AI智能助手，很高兴为您服务。有什么我可以帮助您的吗？'
      } else if (userMessage.includes('介绍') || userMessage.includes('自己')) {
        fallbackResponse = '我是基于maxKB构建的AI智能助手，可以进行自然语言对话、知识库检索和智能推理。'
      } else if (userMessage.includes('功能') || userMessage.includes('能做什么')) {
        fallbackResponse = '我的主要功能包括：\n1. 智能对话交互\n2. 知识库检索\n3. 数据分析协助\n4. 问题解答\n5. 信息整理'
      } else {
        fallbackResponse = '抱歉，我遇到了一些技术问题，请稍后再试。如果问题持续存在，请联系管理员。'
      }

      this.addMessage('assistant', fallbackResponse)

      // 显示错误提示
      this.$message.warning('AI服务暂时不可用，已切换到离线模式')
    },

    // 步骤2: 创建maxKB会话 (使用已获取的应用信息)
    async createMaxKBConversation() {
      try {
        // 检查是否已获取应用信息
        if (!this.applicationInfo) {
          console.log('[demoAI] 应用信息未获取，先执行步骤1...')
          await this.checkConnection()
        }

        console.log('[demoAI] 当前应用信息:', this.applicationInfo)
        console.log('[demoAI] 应用信息类型:', typeof this.applicationInfo)
        console.log('[demoAI] 应用信息是否存在:', !!this.applicationInfo)
        console.log('[demoAI] 应用ID是否存在:', this.applicationInfo?.id)

        if (!this.applicationInfo || !this.applicationInfo.id) {
          console.error('[demoAI] 应用信息检查失败:')
          console.error('[demoAI] - applicationInfo:', this.applicationInfo)
          console.error('[demoAI] - applicationInfo.id:', this.applicationInfo?.id)
          throw new Error('无法获取应用信息，请检查网络连接')
        }

        console.log('[demoAI] 步骤2: 调用 openChat 创建会话...')
        console.log('[demoAI] 使用应用ID:', this.applicationInfo.id)

        // 直接调用步骤2的API
        const chatResponse = await openChat(this.applicationInfo.id)
        console.log('[demoAI] 步骤2 - 打开会话响应:', chatResponse)

        // 根据您提供的响应格式处理：{ "code": 200, "message": "成功", "data": "f2756af8-41dd-11f0-b546-0242ac120003" }
        let chatId = null
        if (chatResponse && chatResponse.data) {
          chatId = chatResponse.data
        } else if (chatResponse && typeof chatResponse === 'string') {
          chatId = chatResponse
        } else if (chatResponse && chatResponse.id) {
          chatId = chatResponse.id
        }

        if (!chatId) {
          throw new Error('会话响应中缺少 chat_id')
        }

        this.conversationId = chatId
        console.log('[demoAI] 步骤2 - 会话创建成功:', {
          chatId: chatId,
          applicationId: this.applicationInfo.id,
          applicationName: this.applicationInfo.name
        })

        // 显示应用信息
        if (this.applicationInfo.name) {
          this.$message.success(`已连接到: ${this.applicationInfo.name}`)
        }

      } catch (error) {
        console.error('[demoAI] 步骤2 失败 - 创建会话失败:', error)
        this.$message.error(`创建会话失败: ${error.message}`)

        // 生成本地会话ID作为降级方案
        this.conversationId = 'local_' + Date.now()
        console.log('[demoAI] 使用本地会话ID:', this.conversationId)
      }
    },

    // 添加消息
    addMessage(role, content) {
      // 确保内容正确处理和解码
      const processedContent = this.processMessageContent(String(content || ''))

      const message = {
        role,
        content: processedContent,
        timestamp: new Date()
      }

      console.log(`[demoAI] 添加消息 - ${role}:`, processedContent)
      this.currentMessages.push(message)

      // 更新当前对话的消息
      this.updateCurrentConversation(message)

      this.$nextTick(() => {
        this.scrollToBottom()
      })

      return this.currentMessages.length - 1
    },

    // 更新当前对话
    updateCurrentConversation(message) {
      if (this.currentConversationId) {
        const conversation = this.conversations.find(conv => conv.id === this.currentConversationId)
        if (conversation) {
          if (!conversation.messages) {
            conversation.messages = []
          }
          conversation.messages.push(message)
          conversation.lastMessage = message.content.substring(0, 50) + (message.content.length > 50 ? '...' : '')
          conversation.updatedAt = new Date()

          // 如果对话没有标题，使用第一条用户消息作为标题
          if (!conversation.title && message.role === 'user') {
            conversation.title = message.content.substring(0, 20) + (message.content.length > 20 ? '...' : '')
          }

          this.saveConversationHistory()
          this.filterHistory()
        }
      }
    },

    // 快速提问
    sendQuickQuestion(question) {
      this.inputMessage = question
      this.sendUserMessage()
    },

    // 清空当前聊天
    clearCurrentChat() {
      this.$confirm('确定要清空当前对话记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.currentMessages = []
        this.conversationId = null
        this.$message.success('对话已清空')
      }).catch(() => {})
    },

    // 切换全屏
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen
      this.handleFullscreenChange(this.isFullscreen)
    },

    // 处理Enter键
    handleEnterKey(event) {
      if (!event.shiftKey) {
        event.preventDefault()
        this.sendUserMessage()
      }
    },

    // 处理Shift+Enter
    handleShiftEnter() {
      // 允许换行
    },

    // 调整输入框高度
    adjustInputHeight() {
      this.$nextTick(() => {
        const lines = this.inputMessage.split('\n').length
        this.inputRows = Math.min(Math.max(lines, 1), 4)
      })
    },

    // 滚动到底部
    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.$refs.messagesContainer
        if (container) {
          container.scrollTop = container.scrollHeight
        }
      })
    },

    // 格式化消息
    formatMessage(content) {
      if (!content) return ''

      // 确保内容是字符串并处理
      let text = this.processMessageContent(String(content))

      // 语义化处理AI回答
      text = this.semanticizeAIResponse(text)

      // 文本格式化，将换行转换为<br>
      return text.replace(/\n/g, '<br>')
    },

    // 语义化AI回答，使其更加人性化和易读
    semanticizeAIResponse(content) {
      if (!content) return ''

      let text = String(content)

      try {
        console.log('[demoAI] 开始语义化处理，原始内容:', text.substring(0, 200) + '...')

        // 0. 特殊处理maxKB的响应格式
        text = this.processMaxKBResponse(text)

        // 1. 检查是否是JSON格式的结构化数据
        if (this.isStructuredData(text)) {
          return this.formatStructuredResponse(text)
        }

        // 2. 处理包含字段名的技术性回答
        text = this.humanizeFieldNames(text)

        // 3. 处理数据格式
        text = this.formatDataValues(text)

        // 4. 添加语义化的连接词和句式
        text = this.addSemanticConnectors(text)

        // 5. 优化段落结构
        text = this.optimizeParagraphStructure(text)

        console.log('[demoAI] 语义化处理完成，最终内容:', text.substring(0, 200) + '...')
        return text
      } catch (error) {
        console.error('[demoAI] 语义化处理失败:', error)
        return content
      }
    },

    // 处理maxKB特有的响应格式
    processMaxKBResponse(content) {
      if (!content) return content

      let text = content

      try {
        // 1. 处理<think>标签包裹的思考过程
        const thinkPattern = /<think>([\s\S]*?)<\/think>/g
        text = text.replace(thinkPattern, '')

        // 2. 提取<data>标签内的核心内容
        const dataPattern = /<data>([\s\S]*?)<\/data>/g
        const dataMatches = text.match(dataPattern)

        if (dataMatches && dataMatches.length > 0) {
          console.log('[demoAI] 找到data标签内容:', dataMatches)

          // 提取所有data标签内的内容
          const dataContents = []
          dataMatches.forEach(match => {
            const content = match.replace(/<\/?data>/g, '').trim()
            if (content && !dataContents.includes(content)) {
              dataContents.push(content)
            }
          })

          if (dataContents.length > 0) {
            // 如果有data标签内容，优先使用这些内容
            text = dataContents.join('\n\n')
            console.log('[demoAI] 使用data标签内容:', text)
          }
        }

        // 3. 清理HTML标签但保留内容
        text = text.replace(/<[^>]+>/g, ' ')
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .replace(/&amp;/g, '&')

        // 4. 清理多余的空白字符
        text = text.replace(/\s+/g, ' ').trim()

        // 5. 处理markdown格式的内容
        text = this.processMarkdownContent(text)

        return text
      } catch (error) {
        console.error('[demoAI] 处理maxKB响应格式失败:', error)
        return content
      }
    },

    // 处理Markdown格式内容
    processMarkdownContent(content) {
      if (!content) return content

      let text = content

      try {
        // 移除markdown的代码块标记
        text = text.replace(/```[\s\S]*?```/g, '')

        // 处理markdown的段落标记
        text = text.replace(/\*\*(.*?)\*\*/g, '$1') // 粗体
        text = text.replace(/\*(.*?)\*/g, '$1') // 斜体
        text = text.replace(/`(.*?)`/g, '$1') // 行内代码

        // 处理列表标记
        text = text.replace(/^\s*[-*+]\s+/gm, '• ') // 无序列表
        text = text.replace(/^\s*\d+\.\s+/gm, '') // 有序列表数字

        return text
      } catch (error) {
        console.error('[demoAI] 处理Markdown内容失败:', error)
        return content
      }
    },

    // 检查是否是结构化数据
    isStructuredData(text) {
      // 检查是否包含JSON对象、数组或明显的字段结构
      const jsonPattern = /^\s*[\{\[].*[\}\]]\s*$/
      const multiFieldPattern = /([\w\u4e00-\u9fa5]+\s*[:：]\s*[^\n]+\s*){2,}/

      return jsonPattern.test(text) || multiFieldPattern.test(text)
    },

    // 格式化结构化响应
    formatStructuredResponse(text) {
      try {
        // 尝试解析JSON
        if (text.trim().startsWith('{') || text.trim().startsWith('[')) {
          const data = JSON.parse(text)
          return this.formatJSONResponse(data)
        }

        // 处理字段:值格式的数据
        return this.formatFieldValueResponse(text)
      } catch (error) {
        return this.formatFieldValueResponse(text)
      }
    },

    // 格式化JSON响应
    formatJSONResponse(data) {
      if (Array.isArray(data)) {
        if (data.length === 0) return '没有找到相关数据。'

        let result = `找到了 ${data.length} 条相关信息：\n\n`
        data.forEach((item, index) => {
          result += `${index + 1}. ${this.formatObjectToText(item)}\n\n`
        })
        return result
      } else if (typeof data === 'object' && data !== null) {
        return `根据查询结果：\n\n${this.formatObjectToText(data)}`
      } else {
        return String(data)
      }
    },

    // 将对象格式化为自然语言
    formatObjectToText(obj) {
      if (!obj || typeof obj !== 'object') return String(obj)

      const fieldMappings = {
        // 企业相关字段
        'name': '企业名称',
        'creditCode': '统一社会信用代码',
        'legalPerson': '法定代表人',
        'corporationName': '法定代表人',
        'address': '注册地址',
        'regLocation': '注册地址',
        'property': '企业性质',
        'status': '状态',
        'handlerName': '负责人',
        'handlerPhoneNumber': '负责人电话',
        'corporationPhoneNumber': '法人电话',

        // 安全评估相关字段
        'generalScore': '综合评分',
        'riskLevel': '风险等级',
        'assessmentDate': '评估日期',
        'itemName': '评估项目',
        'itemKey': '项目代码',
        'generalDesp': '评估描述',

        // 通用字段
        'id': 'ID',
        'createTime': '创建时间',
        'updateTime': '更新时间',
        'phone': '联系电话',
        'email': '邮箱地址'
      }

      let result = []

      for (const [key, value] of Object.entries(obj)) {
        if (value === null || value === undefined || value === '') continue

        const fieldName = fieldMappings[key] || this.humanizeFieldName(key)
        const fieldValue = this.formatFieldValue(key, value)

        result.push(`${fieldName}：${fieldValue}`)
      }

      return result.join('，')
    },

    // 格式化字段:值格式的响应
    formatFieldValueResponse(text) {
      // 匹配字段:值的模式
      const fieldPattern = /([\w\u4e00-\u9fa5]+)\s*[:：]\s*([^\n,，]+)/g
      const matches = []
      let match

      while ((match = fieldPattern.exec(text)) !== null) {
        matches.push({
          field: match[1],
          value: match[2].trim()
        })
      }

      if (matches.length > 0) {
        let result = '根据查询结果：\n\n'
        matches.forEach(item => {
          const fieldName = this.humanizeFieldName(item.field)
          const fieldValue = this.formatFieldValue(item.field, item.value)
          result += `${fieldName}：${fieldValue}\n`
        })
        return result
      }

      return text
    },

    // 人性化字段名称
    humanizeFieldNames(text) {
      const fieldMappings = {
        'creditCode': '统一社会信用代码',
        'legalPerson': '法定代表人',
        'corporationName': '法定代表人',
        'handlerName': '负责人',
        'regLocation': '注册地址',
        'generalScore': '综合评分',
        'riskLevel': '风险等级',
        'itemName': '评估项目',
        'generalDesp': '评估描述'
      }

      let result = text
      for (const [key, value] of Object.entries(fieldMappings)) {
        const regex = new RegExp(`\\b${key}\\b`, 'gi')
        result = result.replace(regex, value)
      }

      return result
    },

    // 人性化单个字段名
    humanizeFieldName(fieldName) {
      const mappings = {
        'name': '企业名称',
        'creditCode': '统一社会信用代码',
        'legalPerson': '法定代表人',
        'corporationName': '法定代表人',
        'address': '注册地址',
        'regLocation': '注册地址',
        'property': '企业性质',
        'status': '状态',
        'handlerName': '负责人',
        'phone': '联系电话',
        'generalScore': '综合评分',
        'riskLevel': '风险等级'
      }

      return mappings[fieldName] || fieldName.replace(/([A-Z])/g, ' $1').trim()
    },

    // 格式化字段值
    formatFieldValue(fieldName, value) {
      if (value === null || value === undefined) return '未知'

      // 处理特殊字段
      if (fieldName.toLowerCase().includes('time') || fieldName.toLowerCase().includes('date')) {
        try {
          const date = new Date(value)
          if (!isNaN(date.getTime())) {
            return date.toLocaleString('zh-CN')
          }
        } catch (e) {
          // 忽略日期解析错误
        }
      }

      if (fieldName.toLowerCase().includes('score')) {
        const score = parseFloat(value)
        if (!isNaN(score)) {
          return `${score}分`
        }
      }

      if (fieldName.toLowerCase().includes('phone')) {
        return this.formatPhoneNumber(value)
      }

      return String(value)
    },

    // 格式化电话号码
    formatPhoneNumber(phone) {
      if (!phone) return '未提供'
      const phoneStr = String(phone).replace(/\D/g, '')
      if (phoneStr.length === 11) {
        return phoneStr.replace(/(\d{3})(\d{4})(\d{4})/, '$1-$2-$3')
      }
      return phone
    },

    // 格式化数据值
    formatDataValues(text) {
      // 格式化数字
      text = text.replace(/\b(\d+\.?\d*)\b/g, (match, number) => {
        const num = parseFloat(number)
        if (num > 1000000) {
          return `${(num / 10000).toFixed(1)}万`
        } else if (num > 1000) {
          return `${(num / 1000).toFixed(1)}千`
        }
        return match
      })

      return text
    },

    // 添加语义化连接词
    addSemanticConnectors(text) {
      // 如果文本很短或已经很自然，直接返回
      if (text.length < 50 || this.isNaturalLanguage(text)) {
        return text
      }

      // 添加适当的开头
      if (!text.match(/^(根据|查询|显示|结果|您好|关于)/)) {
        text = `根据查询结果，${text}`
      }

      return text
    },

    // 检查是否已经是自然语言
    isNaturalLanguage(text) {
      const naturalPatterns = [
        /[。！？]/,  // 包含中文标点
        /\b(是|的|了|在|有|为|和|与|或|但是|然而|因此|所以|由于)\b/,  // 包含常见连词
        /[，。！？；：]/  // 包含标点符号
      ]

      return naturalPatterns.some(pattern => pattern.test(text))
    },

    // 优化段落结构
    optimizeParagraphStructure(text) {
      // 确保句子以适当的标点结尾
      if (text && !text.match(/[。！？]$/)) {
        text += '。'
      }

      // 处理过长的句子，添加适当的分段
      if (text.length > 200) {
        text = text.replace(/([，；])\s*/g, '$1\n')
      }

      return text
    },

    // 清理响应内容，去除重复数据和无效内容
    cleanResponseContent(content) {
      if (!content || typeof content !== 'string') return content

      let cleanedContent = content

      try {
        console.log('[demoAI] 开始清理响应内容，原始长度:', content.length)

        // 1. 检测并处理重复的data字段模式
        const dataPattern = /data\s*[:：]\s*\{[^}]*\}/g
        const dataMatches = content.match(dataPattern)

        if (dataMatches && dataMatches.length > 5) {
          console.log('[demoAI] 检测到重复的data字段，数量:', dataMatches.length)

          // 如果有大量重复的data字段，只保留第一个或提取有用信息
          const firstMatch = dataMatches[0]
          console.log('[demoAI] 第一个data字段:', firstMatch)

          // 尝试从第一个data字段中提取有用信息
          try {
            const jsonMatch = firstMatch.match(/\{[^}]*\}/)
            if (jsonMatch) {
              const jsonStr = jsonMatch[0]
              const dataObj = JSON.parse(jsonStr)

              // 如果只是chat_id，说明这是无用的重复数据
              if (dataObj.chat_id && Object.keys(dataObj).length === 1) {
                console.log('[demoAI] 检测到无用的chat_id重复数据，将被清理')
                cleanedContent = '我已经收到您的消息，正在为您处理中。请稍等片刻。'
              } else {
                // 如果有其他有用信息，保留并格式化
                cleanedContent = this.formatObjectToText(dataObj)
              }
            }
          } catch (e) {
            console.log('[demoAI] 解析data字段失败，使用默认回复')
            cleanedContent = '我已经收到您的消息，正在为您处理中。请稍等片刻。'
          }
        }

        // 2. 清理其他重复模式
        // 检测重复的行（连续出现3次以上相同内容）
        const lines = cleanedContent.split('\n')
        const uniqueLines = []
        let lastLine = ''
        let repeatCount = 0

        for (const line of lines) {
          const trimmedLine = line.trim()

          if (trimmedLine === lastLine) {
            repeatCount++
            // 如果重复超过2次，跳过
            if (repeatCount <= 2) {
              uniqueLines.push(line)
            }
          } else {
            repeatCount = 0
            lastLine = trimmedLine
            if (trimmedLine) { // 只保留非空行
              uniqueLines.push(line)
            }
          }
        }

        if (uniqueLines.length < lines.length) {
          console.log('[demoAI] 清理了重复行，从', lines.length, '行减少到', uniqueLines.length, '行')
          cleanedContent = uniqueLines.join('\n')
        }

        // 3. 清理过长的重复内容
        if (cleanedContent.length > 1000) {
          const words = cleanedContent.split(/\s+/)
          const uniqueWords = []
          const wordCount = {}

          for (const word of words) {
            wordCount[word] = (wordCount[word] || 0) + 1

            // 如果某个词重复超过10次，只保留前几次
            if (wordCount[word] <= 10) {
              uniqueWords.push(word)
            }
          }

          if (uniqueWords.length < words.length) {
            console.log('[demoAI] 清理了重复词汇，从', words.length, '个词减少到', uniqueWords.length, '个词')
            cleanedContent = uniqueWords.join(' ')
          }
        }

        // 4. 最终检查：如果内容仍然过长且重复，使用智能摘要
        if (cleanedContent.length > 500 && this.isContentRepetitive(cleanedContent)) {
          console.log('[demoAI] 内容仍然重复，使用智能摘要')
          cleanedContent = this.generateContentSummary(cleanedContent)
        }

        console.log('[demoAI] 清理完成，最终长度:', cleanedContent.length)
        return cleanedContent

      } catch (error) {
        console.error('[demoAI] 清理响应内容失败:', error)
        return content
      }
    },

    // 检查内容是否重复
    isContentRepetitive(content) {
      if (!content || content.length < 100) return false

      // 检查是否有大量重复的短语
      const phrases = content.match(/.{10,50}/g) || []
      const phraseCount = {}
      let repetitiveCount = 0

      for (const phrase of phrases) {
        phraseCount[phrase] = (phraseCount[phrase] || 0) + 1
        if (phraseCount[phrase] > 3) {
          repetitiveCount++
        }
      }

      return repetitiveCount > phrases.length * 0.3
    },

    // 生成内容摘要
    generateContentSummary(content) {
      try {
        // 提取关键信息
        const keyInfo = []

        // 提取可能的有用信息
        const patterns = [
          /[\u4e00-\u9fa5]+(?:公司|企业|机构|医院|学校)/g, // 中文机构名
          /\d{4}-\d{2}-\d{2}/g, // 日期
          /\d+(?:\.\d+)?[万千百]/g, // 数字
          /<data>([^<]+)<\/data>/g, // data标签内容
        ]

        for (const pattern of patterns) {
          const matches = content.match(pattern)
          if (matches) {
            keyInfo.push(...matches.slice(0, 3)) // 最多取3个匹配
          }
        }

        if (keyInfo.length > 0) {
          return `根据您的查询，我找到了以下相关信息：${keyInfo.join('、')}。如需更详细的信息，请具体说明您想了解的内容。`
        } else {
          return '我已经收到您的查询请求。由于返回的数据较为复杂，建议您提供更具体的问题，我将为您提供更准确的答案。'
        }
      } catch (error) {
        return '我已经收到您的消息，正在为您处理中。请稍等片刻或重新描述您的问题。'
      }
    },

    // 解码Unicode转义序列
    decodeUnicode(str) {
      if (typeof str !== 'string') return str

      // 解码Unicode转义序列 (如 \u4e2d\u6587 -> 中文)
      return str.replace(/\\u[\dA-Fa-f]{4}/g, function(match) {
        return String.fromCharCode(parseInt(match.replace(/\\u/g, ''), 16))
      })
    },

    // 处理消息内容，确保中文正确显示
    processMessageContent(content) {
      if (!content) return ''

      let processedContent = String(content)

      try {
        // 1. 解码Unicode转义序列
        processedContent = this.decodeUnicode(processedContent)

        // 2. 处理可能的JSON字符串
        if (processedContent.startsWith('"') && processedContent.endsWith('"')) {
          try {
            processedContent = JSON.parse(processedContent)
          } catch (e) {
            // 如果JSON解析失败，去掉首尾引号
            processedContent = processedContent.slice(1, -1)
          }
        }

        // 3. 处理转义字符
        processedContent = processedContent
          .replace(/\\n/g, '\n')
          .replace(/\\r/g, '\r')
          .replace(/\\t/g, '\t')
          .replace(/\\"/g, '"')
          .replace(/\\'/g, "'")
          .replace(/\\\\/g, '\\')

        // 4. 再次解码Unicode（防止双重编码）
        processedContent = this.decodeUnicode(processedContent)

        // 5. 处理可能的URL编码
        try {
          if (processedContent.includes('%')) {
            processedContent = decodeURIComponent(processedContent)
          }
        } catch (e) {
          // URL解码失败，保持原内容
        }

        console.log('[demoAI] 内容处理步骤:')
        console.log('[demoAI] - 原始内容:', content)
        console.log('[demoAI] - 处理后内容:', processedContent)

        return processedContent
      } catch (error) {
        console.error('[demoAI] 处理消息内容失败:', error)
        return content
      }
    },

    // 格式化消息时间
    formatMessageTime(timestamp) {
      const now = new Date()
      const time = new Date(timestamp)
      const diff = now - time

      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return `${Math.floor(diff / 60000)}分钟前`
      } else if (diff < 86400000) { // 24小时内
        return time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      } else {
        return time.toLocaleString('zh-CN', {
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      }
    }
  },
  watch: {
    inputMessage() {
      this.adjustInputHeight()
    }
  }
}
</script>

<style lang="scss" scoped>
// AI Demo Chat Page - 参考enterprise/chat样式
.ai-demo-chat-page {
  height: calc(100vh - 84px);
  background-color: #f5f7fa;
  padding: 20px;
  display: flex;
  gap: 20px;
}

// 左侧历史记录区域
.history-sidebar {
  width: 280px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex-shrink: 0;

  .sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e0e6ed;
    background: white;

    .sidebar-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .refresh-btn {
      padding: 4px;
      color: #666;

      &:hover {
        color: #409eff;
      }
    }
  }

  .sidebar-content {
    flex: 1;
    padding: 16px;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .search-section {
      margin-bottom: 16px;
    }

    .history-section {
      flex: 1;
      overflow: hidden;

      .empty-state {
        text-align: center;
        padding: 40px 20px;
        color: #999;

        i {
          font-size: 32px;
          margin-bottom: 8px;
          display: block;
        }

        p {
          margin: 0;
          font-size: 14px;
        }
      }

      .history-list {
        height: 100%;
        overflow-y: auto;

        .history-item {
          padding: 12px;
          margin-bottom: 8px;
          border-radius: 8px;
          border: 1px solid #f0f0f0;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            border-color: #409eff;
            background: #f8f9ff;
          }

          &.active {
            border-color: #409eff;
            background: #f0f4ff;
          }

          .history-content {
            .history-question {
              font-size: 14px;
              font-weight: 500;
              color: #333;
              margin-bottom: 4px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .history-answer {
              font-size: 12px;
              color: #666;
              margin-bottom: 4px;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              -webkit-box-orient: vertical;
              line-height: 1.4;
              max-height: 2.8em;
            }

            .history-time {
              font-size: 11px;
              color: #999;
            }
          }
        }
      }
    }
  }
}

.chat-container {
  flex: 1;
  height: 100%;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e6ed;
  background: white;
  flex-shrink: 0;

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;

    .welcome-text {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }

    .new-chat-btn {
      font-size: 13px;
    }
  }

  .header-right {
    display: flex;
    gap: 8px;

    .tool-button {
      position: relative;
      border: 1px solid #d1d5db;
      background: #fff;
      color: #6b7280;
      border-radius: 16px;

      &:hover {
        border-color: #409eff;
        color: #409eff;
      }
    }
  }
}

.chat-content {
  flex: 1;
  padding: 0;
  display: flex;
  flex-direction: column;
  height: calc(100% - 20px);
}

.chatBox {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
  background-color: white;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px;
  margin: 12px;
  border-radius: 8px;
  text-align: center;
}

.welcome-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px;

  .welcome-content {
    text-align: center;
    color: #666;

    .welcome-icon {
      width: 64px;
      height: 64px;
      margin: 0 auto 16px auto;
      border-radius: 50%;
      object-fit: cover;
      display: block;
    }

    h3 {
      margin: 0 0 8px 0;
      font-size: 20px;
      color: #333;
    }

    p {
      margin: 0 0 24px 0;
      font-size: 14px;
      color: #666;
      line-height: 1.6;
    }

    .quick-questions {
      h4 {
        margin: 0 0 12px 0;
        color: #333;
        font-size: 16px;
      }

      .question-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        justify-content: center;

        .el-button {
          margin: 0;
        }
      }
    }
  }
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #F6F7FB;
}

.message {
  margin-bottom: 20px;
  display: flex;
  align-items: flex-start;
}

.user-message-container {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  align-items: flex-start;
  gap: 12px;
}

.bot-message-container {
  display: flex;
  justify-content: flex-start;
  width: 100%;
  align-items: flex-start;
  gap: 12px;
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.bot-avatar {
  border: 1px solid #4caf50;
}

.message-content {
  max-width: calc(95% - 50px);
  padding: 12px 16px;
  border-radius: 18px;
  line-height: 1.5;
  word-wrap: break-word;
  font-size: 15px;
}

.user-message {
  background-color: #e3f2fd;
  color: #0d47a1;
  border-top-right-radius: 4px;
}

.bot-message {
  background-color: white;
  color: #333;
  border-top-left-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

// 思考指示器
.thinking-indicator {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 20px;

  .avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0;
    border: 1px solid #4caf50;
  }

  .thinking-content {
    flex: 1;
    background-color: white;
    color: #333;
    border-top-left-radius: 4px;
    border-top-right-radius: 18px;
    border-bottom-left-radius: 18px;
    border-bottom-right-radius: 18px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    padding: 12px 16px;
    max-width: calc(95% - 50px);

    .thinking-text {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #6b7280;
      margin-bottom: 8px;

      i {
        animation: spin 1s linear infinite;
      }

      span {
        font-size: 14px;
      }
    }

    .thinking-actions {
      display: flex;
      justify-content: flex-start;

      .stop-text {
        font-size: 12px;
        color: #ef4444;
        cursor: pointer;
        transition: color 0.2s ease;

        &:hover {
          color: #dc2626;
          text-decoration: underline;
        }
      }
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.chat-input {
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 20px;
  border-top: 1px solid #e0e0e0;
  background-color: #fff;

  .input-controls {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-bottom: 10px;

    .stream-toggle, .test-button {
      font-size: 12px;
      padding: 4px 8px;
      height: 24px;
      line-height: 16px;
    }
  }

  .input-area {
    display: flex;
    align-items: flex-end;
    gap: 12px;
  }
}

.chat-input textarea {
  width: 100%;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 10px;
  resize: none;
  min-height: 60px;
  max-height: 150px;
  font-family: inherit;
  font-size: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  &:focus {
    outline: none;
    border-color: #2196f3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
  }
}

.send-btn {
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  position: absolute;
  right: 30px;
  top: 30px;

  &:hover {
    background-color: #1976d2;
  }

  &:disabled {
    background-color: #e0e0e0;
    color: #9e9e9e;
    cursor: not-allowed;
  }

  i {
    font-size: 18px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .ai-demo-chat-page {
    height: calc(100vh - 100px);
    padding: 10px;
  }

  .chat-header {
    padding: 12px 16px;

    .header-left {
      gap: 10px;

      .new-chat-btn {
        font-size: 12px;
        padding: 4px 8px;
      }
    }

    .header-right {
      gap: 6px;

      .tool-button {
        padding: 6px 10px;
        font-size: 11px;
      }
    }
  }

  .chat-messages {
    padding: 15px;
  }

  .message {
    margin-bottom: 15px;
  }

  .user-message-container,
  .bot-message-container {
    gap: 8px;
  }

  .avatar {
    width: 32px;
    height: 32px;
  }

  .message-content {
    max-width: calc(95% - 40px);
    padding: 10px 12px;
    font-size: 14px;
  }

  .chat-input {
    padding: 15px;

    textarea {
      padding: 12px;
      font-size: 14px;
      min-height: 50px;
    }
  }

  .send-btn {
    width: 36px;
    height: 36px;

    i {
      font-size: 16px;
    }
  }
}

// Enterprise消息内容样式
.enterprise-message-content {
  line-height: 1.7;
  word-wrap: break-word;
  font-size: 15px;
  color: #333;
}

// Markdown内容样式
.markdown-content {
  line-height: 1.6;
  word-wrap: break-word;

  // 标题样式
  h1, h2, h3, h4, h5, h6 {
    margin: 16px 0 8px 0;
    font-weight: 600;
    line-height: 1.25;
    color: #1f2937;
  }

  h1 { font-size: 1.5em; }
  h2 { font-size: 1.3em; }
  h3 { font-size: 1.2em; }
  h4 { font-size: 1.1em; }
  h5 { font-size: 1em; }
  h6 { font-size: 0.9em; }

  // 段落样式
  p {
    margin: 8px 0;
    line-height: 1.6;
  }

  // 行内代码样式
  code {
    background-color: #f3f4f6;
    color: #e11d48;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
  }

  // 代码块样式
  pre {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    margin: 12px 0;
    overflow-x: auto;
    position: relative;

    code {
      background: none;
      color: #333;
      padding: 0;
      border-radius: 0;
      font-size: 0.85em;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      line-height: 1.45;

      // highlight.js 语法高亮样式
      &.hljs {
        display: block;
        overflow-x: auto;
        padding: 0;
        background: transparent;
      }
    }
  }

  // 列表样式
  ul, ol {
    margin: 8px 0;
    padding-left: 20px;

    li {
      margin: 4px 0;
      line-height: 1.5;
    }
  }

  ul {
    list-style-type: disc;
  }

  ol {
    list-style-type: decimal;
  }

  // 引用样式
  blockquote {
    border-left: 4px solid #e5e7eb;
    margin: 12px 0;
    padding: 8px 16px;
    background-color: #f9fafb;
    color: #6b7280;
    font-style: italic;
  }

  // 链接样式
  a {
    color: #2563eb;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  // 表格样式
  table {
    border-collapse: collapse;
    width: 100%;
    margin: 12px 0;
    font-size: 0.9em;

    th, td {
      border: 1px solid #e5e7eb;
      padding: 8px 12px;
      text-align: left;
    }

    th {
      background-color: #f3f4f6;
      font-weight: 600;
    }

    tr:nth-child(even) {
      background-color: #f9fafb;
    }
  }

  // 水平分割线
  hr {
    border: none;
    border-top: 1px solid #e5e7eb;
    margin: 16px 0;
  }

  // 图片样式
  img {
    max-width: 100%;
    height: auto;
    border-radius: 6px;
    margin: 8px 0;
  }

  // 删除线
  del {
    color: #6b7280;
  }

  // 强调样式
  strong {
    font-weight: 600;
    color: #1f2937;
  }

  em {
    font-style: italic;
    color: #374151;
  }
}
</style>
