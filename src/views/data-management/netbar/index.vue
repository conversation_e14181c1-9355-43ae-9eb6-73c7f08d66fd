<template>
  <div class="app-container">
    <div class="screen-management-layout">
      <!-- 左侧导航 -->
      <basic-data-sidebar />

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <!-- 搜索区域 -->
        <common-search
          :search-config="searchConfig"
          :advanced-fields="advancedFields"
          :initial-form="searchForm"
          @search="handleSearch"
          @reset="handleReset"
          @field-change="handleFieldChange"
        />

        <!-- 操作按钮区域 -->
        <div class="action-bar">
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['netbar:netbar:add']"
              >新增</el-button>
            </el-col>
            <!-- <el-col :span="1.5">
              <el-button
                type="success"
                plain
                icon="el-icon-edit"
                size="mini"
                :disabled="single"
                @click="handleUpdate"
                v-hasPermi="['netbar:netbar:edit']"
              >修改</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="danger"
                plain
                icon="el-icon-delete"
                size="mini"
                :disabled="multiple"
                @click="handleDelete"
                v-hasPermi="['netbar:netbar:remove']"
              >删除</el-button>
            </el-col> -->
            <el-col :span="1.5">
              <el-button
                type="warning"
                plain
                icon="el-icon-download"
                size="mini"
                @click="handleExport"
                v-hasPermi="['netbar:netbar:export']"
              >导出</el-button>
            </el-col>
            <el-col :span="1.5">  
               <el-button
                type="success"
                icon="el-icon-plus"
                size="mini"
                @click="handleReminder"
                v-hasPermi="['netbar:netbar:add']"
              >设置到期提醒</el-button>
            </el-col>
            
            <div class="toolbar-right">
              <el-button
                type="text"
                icon="el-icon-setting"
                size="mini"
                class="display-fields-btn"
                @click="handleDisplayFieldsSettings"
              >设置显示字段</el-button>
              <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
            </div>
          </el-row>
        </div>

        <!-- 表格区域 -->
        <div class="table-container">

          <el-table
            v-loading="loading"
            :data="netbarList"
            @selection-change="handleSelectionChange"
            border
            style="width: 100%"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column v-if="displayFields.siteCode" label="场所编号" width="125" align="center" prop="siteCode" />
            <el-table-column v-if="displayFields.siteName" width="260" label="场所名称" align="center" prop="siteName">
              <template slot-scope="scope">
                <div class="site-name-container">
                  <div class="table-name-text-ellipsis">{{ scope.row.siteName }}</div>
                  <span
                    v-if="scope.row.dataUpdateStatus === 1"
                    class="update-badge pending"
                  >待更新</span>
                  <span
                    v-else-if="scope.row.dataUpdateStatus > 1"
                    class="update-badge overdue"
                  >已超期</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column v-if="displayFields.businessStatus" label="营业状态" align="center" prop="businessStatus">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.site_business_status" :value="scope.row.businessStatus"/>
              </template>
            </el-table-column>
            <el-table-column v-if="displayFields.areaCode" label="所属区域" align="center" prop="areaCodeLabel" />
            <el-table-column v-if="displayFields.policeStation" label="所属派出所" align="center" prop="policeStationLabel" />
            <el-table-column v-if="displayFields.legalPerson" label="法人" align="center" prop="legalPerson" />
            <el-table-column v-if="displayFields.legalPhone" label="联系电话" align="center" prop="legalPhone" />
            <el-table-column v-if="displayFields.address" label="地址" align="center" prop="address" width="200" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200" fixed="right">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-view"
                  @click="handleView(scope.row)"
                  v-hasPermi="['netbar:netbar:query']"
                >详情</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['netbar:netbar:edit']"
                >修改</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['netbar:netbar:remove']"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
    </div>

    <!-- 数据更新提醒组件 -->
    <data-update-reminder
      :visible.sync="reminderVisible"
      :biz-type="'SITE_BASE'"
    />

    <!-- 字段显示设置组件 -->
    <FieldDisplaySettings
      :visible.sync="displayFieldsDialogVisible"
      :available-fields="availableDisplayFields"
      :current-fields="currentSelectedFields"
      :api-url="'/netbar/siteBase/list'"
      @save-success="handleFieldSettingsSaveSuccess"
      @close="handleFieldSettingsClose"
    />
  </div>
</template>

<script>
import { listNetbar, delNetbar } from "@/api/netbar/netbar";
import { listArea } from '@/api/system/area';
import BasicDataSidebar from '@/components/BasicDataSidebar'
import CommonSearch from '@/components/CommonSearch'
import DataUpdateReminder from '@/components/DataUpdateReminder'
import FieldDisplaySettings from '@/components/FieldDisplaySettings'

export default {
  name: "NetbarIndex",
  dicts: ['site_business_status'],
  components: { BasicDataSidebar, CommonSearch, DataUpdateReminder, FieldDisplaySettings },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 网吧场所表格数据
      netbarList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        siteName: undefined,
        siteCode: undefined,
        areaCode: undefined,
        policeStation: undefined,
        businessStatus: undefined,
        legalPerson: undefined,
      },
      // 区域列表
      userAreaList: [],
      // 派出所列表
      policeList: [],
      // 搜索配置
      searchConfig: {
        label: '场所名称',
        key: 'siteName',
        placeholder: '请输入场所名称'
      },
      // 高级搜索字段
      advancedFields: [],
      // 搜索表单
      searchForm: {},
      // 提醒组件显示状态
      reminderVisible: false,

      // 字段显示设置
      displayFieldsDialogVisible: false,
      displayFields: {
        siteCode: true,
        siteName: true,
        businessStatus: true,
        areaCode: true,
        policeStation: true,
        legalPerson: true,
        legalPhone: true,
        address: true
      },
      // 可用的显示字段配置
      availableDisplayFields: [
        { key: 'siteCode', label: '场所编号', description: '网吧场所的唯一编号' },
        { key: 'siteName', label: '场所名称', description: '网吧场所的名称' },
        { key: 'businessStatus', label: '营业状态', description: '网吧的营业状态' },
        { key: 'areaCode', label: '所属区域', description: '网吧所在的行政区域' },
        { key: 'policeStation', label: '所属派出所', description: '网吧所属的派出所' },
        { key: 'legalPerson', label: '法人', description: '网吧的法人代表' },
        { key: 'legalPhone', label: '联系电话', description: '法人的联系电话' },
        { key: 'address', label: '地址', description: '网吧的详细地址' }
      ]
    };
  },
  computed: {
    // 当前选中的字段
    currentSelectedFields() {
      return Object.keys(this.displayFields).filter(key => this.displayFields[key]);
    }
  },
  created() {
    let overTimeQuery = this.$route.query && this.$route.query.overTimeQuery
    // 将字符串转换为数字
    if (overTimeQuery) {
      overTimeQuery = parseInt(overTimeQuery);
    }
    this.getList(overTimeQuery);
    this.getUserAreaList();
    this.initAdvancedFields();
  },
  methods: {
    // 初始化高级搜索字段
    initAdvancedFields() {
      this.advancedFields = [
        {
          label: '场所编码',
          key: 'siteCode',
          type: 'input',
          placeholder: '请输入场所编码',
          span: 6
        },
        {
          label: '区域',
          key: 'areaCode',
          type: 'select',
          placeholder: '请选择区域',
          span: 6,
          options: this.userAreaList.map(item => ({
            label: item.areaName,
            value: item.areaCode
          }))
        },
        {
          label: '所属派出所',
          key: 'policeStation',
          type: 'select',
          placeholder: '请选择所属派出所',
          span: 6,
          options: this.policeList.map(item => ({
            label: item.areaName,
            value: item.areaCode
          }))
        },
        {
          label: '法人',
          key: 'legalPerson',
          type: 'input',
          placeholder: '请输入法人',
          span: 6
        },
        {
          label: '营业状态',
          key: 'businessStatus',
          type: 'select',
          placeholder: '请选择营业状态',
          span: 6,
          options: (this.dict && this.dict.type && this.dict.type.site_business_status) ? this.dict.type.site_business_status.map(item => ({
            label: item.label,
            value: item.value
          })) : []
        }
      ];
    },

    // 处理搜索
    handleSearch(searchForm) {
      this.queryParams = {
        ...this.queryParams,
        ...searchForm,
        pageNum: 1
      };
      this.getList();
    },

    // 处理重置
    handleReset(searchForm) {
      this.searchForm = searchForm;
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        ...searchForm
      };
      this.getList();
    },

    // 处理字段变化（联动）
    handleFieldChange(event) {
      if (event.field === 'areaCode') {
        // 区域变化时，更新派出所列表
        this.getPoliceList(event.value);
        // 清空派出所选择
        event.searchForm.policeStation = null;
        // 更新高级搜索字段中派出所的选项
        this.updatePoliceOptions();
      }
    },
    // 设置提醒
    handleReminder() {
      this.reminderVisible = true;
    },
    /** 查询网吧场所列表 */
    getList(params) {
      this.loading = true;

      // 如果params是数字且为overTimeQuery的有效值，则设置overTimeQuery
      if (typeof params === 'number' && (params === 1 || params === 2)) {
        this.queryParams.overTimeQuery = params;
      } else if (typeof params === 'string' && (params === '1' || params === '2')) {
        // 处理字符串类型的overTimeQuery
        console.log('---设置overTimeQuery(字符串)----', params);
        this.queryParams.overTimeQuery = parseInt(params);
      } else if (typeof params === 'object' && params !== null) {
        // 如果是对象，可能是翻页等其他参数，不处理overTimeQuery
        console.log('---翻页或其他参数----', params);
      } else if (params === undefined || params === null) {
        // 如果没有参数，清除overTimeQuery（如果需要的话）
        // 注意：这里不自动清除，保持当前状态
      }
      listNetbar(this.queryParams).then(response => {
        this.netbarList = response.rows;
        this.total = response.total;

        // 处理API返回的fields字段
        if (response.fields) {
          this.handleApiFields(response.fields);
        }

        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        siteName: undefined,
        siteCode: undefined,
        areaCode: undefined,
        policeStation: undefined,
        businessStatus: undefined,
        legalPerson: undefined,
      };
      this.policeList = [];
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.siteCode)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push("/data-management/netbar/add");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const siteCode = row.siteCode || this.ids[0];
      this.$router.push("/data-management/netbar/edit/" + siteCode);
    },
    /** 详情按钮操作 */
    handleView(row) {
      this.$router.push("/data-management/netbar/detail/" + row.siteCode);
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const siteCodes = row.siteCode || this.ids;
      this.$modal.confirm('是否确认删除场所编号为"' + siteCodes + '"的数据项？').then(function() {
        return delNetbar(siteCodes);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/user/export', {
        ...this.queryParams
      }, `netbar_${new Date().getTime()}.xlsx`)
    },
    /** 设置显示字段 */
    handleDisplayFieldsSettings() {
      this.displayFieldsDialogVisible = true;
    },

    // 处理字段设置保存成功
    handleFieldSettingsSaveSuccess(selectedFields) {
      // 重置所有字段为false
      Object.keys(this.displayFields).forEach(key => {
        this.displayFields[key] = false;
      });

      // 设置选中的字段为true
      selectedFields.forEach(field => {
        if (this.displayFields.hasOwnProperty(field)) {
          this.displayFields[field] = true;
        }
      });

      // 保存到本地存储
      localStorage.setItem('netbarDisplayFields', JSON.stringify(this.displayFields));
      this.$message.success('显示字段设置已保存');
    },

    // 处理字段设置弹窗关闭
    handleFieldSettingsClose() {
      this.displayFieldsDialogVisible = false;
    },

    // 处理API返回的fields字段
    handleApiFields(fields) {
      console.log('API返回的fields字段:', fields);

      if (fields && typeof fields === 'string') {
        // 解析fields字符串，例如 "siteCode|siteName|businessStatus|areaCode|policeStation|legalPerson|legalPhone|address"
        const fieldsArray = fields.split('|');
        console.log('解析后的字段数组:', fieldsArray);

        // 重置所有字段为false
        Object.keys(this.displayFields).forEach(key => {
          this.displayFields[key] = false;
        });

        // 根据API返回的字段设置为true
        fieldsArray.forEach(field => {
          if (this.displayFields.hasOwnProperty(field)) {
            this.displayFields[field] = true;
          }
        });

        console.log('更新后的displayFields:', this.displayFields);
      } else if (!fields) {
        // 如果fields为null、空或undefined，显示所有字段
        console.log('fields为空，显示所有字段');
        Object.keys(this.displayFields).forEach(key => {
          this.displayFields[key] = true;
        });
      }
    },

    // 处理字段设置保存成功
    handleFieldSettingsSaveSuccess(selectedFields) {
      // 重置所有字段为false
      Object.keys(this.displayFields).forEach(key => {
        this.displayFields[key] = false;
      });

      // 设置选中的字段为true
      selectedFields.forEach(field => {
        if (this.displayFields.hasOwnProperty(field)) {
          this.displayFields[field] = true;
        }
      });

      // 保存到本地存储
      localStorage.setItem('netbarDisplayFields', JSON.stringify(this.displayFields));
      this.$message.success('显示字段设置已保存');
    },

    // 处理字段设置弹窗关闭
    handleFieldSettingsClose() {
      this.displayFieldsDialogVisible = false;
    },

    // 处理API返回的fields字段
    handleApiFields(fields) {
      console.log('API返回的fields字段:', fields);

      if (fields && typeof fields === 'string') {
        // 解析fields字符串，例如 "siteCode|siteName|businessStatus|areaCode|policeStation|legalPerson|legalPhone|address"
        const fieldsArray = fields.split('|');
        console.log('解析后的字段数组:', fieldsArray);

        // 重置所有字段为false
        Object.keys(this.displayFields).forEach(key => {
          this.displayFields[key] = false;
        });

        // 根据API返回的字段设置为true
        fieldsArray.forEach(field => {
          if (this.displayFields.hasOwnProperty(field)) {
            this.displayFields[field] = true;
          }
        });

        console.log('更新后的displayFields:', this.displayFields);
      } else if (!fields) {
        // 如果fields为null、空或undefined，显示所有字段
        console.log('fields为空，显示所有字段');
        Object.keys(this.displayFields).forEach(key => {
          this.displayFields[key] = true;
        });
      }
    },
    /** 获取区域列表 */
    getUserAreaList() {
      listArea().then(response => {
        this.userAreaList = response.data || [];
        this.initAdvancedFields();
      });
    },
    /** 获取派出所列表 */
    getPoliceList(areaCode) {
      const targetAreaCode = areaCode || this.queryParams.areaCode;
      if (targetAreaCode) {
        listArea({ parentId: targetAreaCode }).then(response => {
          this.policeList = response.data || [];
          this.updatePoliceOptions();
        });
      } else {
        this.policeList = [];
        this.updatePoliceOptions();
      }
    },

    // 更新高级搜索字段中派出所的选项
    updatePoliceOptions() {
      const policeField = this.advancedFields.find(field => field.key === 'policeStation');
      if (policeField) {
        policeField.options = this.policeList.map(item => ({
          label: item.areaName,
          value: item.areaCode
        }));
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/screen-management.scss';

.action-bar {
  background: white;
  padding: 16px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.toolbar-right {
  float: right;
  display: flex;
  align-items: center;
  gap: 12px;

  .display-fields-btn {
    color: #ACB0B3;
    padding: 0;
    border: none;
    background: none;

    &:hover {
      color: #4584FF;
    }
  }
}

// 修复表格头部背景色
::v-deep .el-table {
  .el-table__header-wrapper {
    .el-table__header {
      th {
        background-color: #EBF2FF !important;
        color: #333;
        font-weight: 500;
      }
    }
  }
}

// 名称角标样式
.site-name-container {
  position: relative;
  display: inline-block;
  padding: 12px 12px 0 0;
  .update-badge {
    position: absolute;
    top: -2px;
    right: -10px;
    font-size: 10px;
    padding: 1px 4px;
    border-radius: 2px;
    // color: white;
    white-space: nowrap;
    z-index: 1;

    &.pending {
      color: #FE9400;
    }

    &.overdue {
      color: #FF2A2A;
    }
  }
}
</style>