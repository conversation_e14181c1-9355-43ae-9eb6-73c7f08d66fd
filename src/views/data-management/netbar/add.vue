<template>
  <div class="app-container">
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">{{ form.id ? '编辑' : '新增' }}网吧场所</h2>
        <el-button icon="el-icon-arrow-left" size="small" @click="goBack">返回</el-button>
      </div>
    </div>
    <section class="form-container">
      <el-form ref="form" :model="form" :rules="rules" label-width="200px">
        <!-- 基础信息 -->
        <div class="form-section">
          <div class="section-header" @click="toggleSection('basic')">
            <div class="section-title">
              <div class="title-line"></div>
              <span class="title-text">基础信息</span>
            </div>
            <div class="section-toggle">
              <span class="toggle-text">{{ sectionCollapsed.basic ? '展开' : '收起' }}</span>
              <i :class="sectionCollapsed.basic ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            </div>
          </div>
          <div v-show="!sectionCollapsed.basic" class="section-content">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="场所编码" prop="siteCode">
                  <el-input v-model="form.siteCode" placeholder="请输入场所编码" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="统一社会信用代码" prop="creditCode">
                  <el-input v-model="form.creditCode" placeholder="请输入统一社会信用代码" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="16">
                <el-form-item label="场所名称" prop="siteName">
                  <el-input v-model="form.siteName" placeholder="请输入场所名称" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="审计厂商" prop="auditVendor">
                  <el-select v-model="form.auditVendor" placeholder="请选择审计厂商" clearable style="width: 100%">
                    <el-option
                      v-for="dict in dict.type.audit_vendor"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="所属区域" prop="areaCode">
                  <el-select v-model="form.areaCode" placeholder="请选择所属区域" clearable style="width: 100%" @change="getPoliceList">
                    <el-option
                      v-for="area in areaList"
                      :key="area.areaCode"
                      :label="area.areaName"
                      :value="area.areaCode"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="所属派出所" prop="policeStation">
                  <el-select v-model="form.policeStation" placeholder="请选择所属派出所" clearable style="width: 100%">
                    <el-option
                      v-for="police in policeList"
                      :key="police.areaCode"
                      :label="police.areaName"
                      :value="police.areaCode"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="分级分类" prop="classLevel">
                  <el-select v-model="form.classLevel" placeholder="请选择分级分类" clearable style="width: 100%">
                    <el-option
                      v-for="dict in dict.type.class_level"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="营业状态" prop="businessStatus">
                  <el-select v-model="form.businessStatus" placeholder="请选择营业状态" clearable style="width: 100%">
                    <el-option
                      v-for="dict in dict.type.site_business_status"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="临停截止日期" prop="stopBusinessDate">
                  <el-date-picker
                    v-model="form.stopBusinessDate"
                    type="date"
                    placeholder="临停截止日期"
                    style="width: 100%">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="临停备注" prop="stopBusinessRemark">
                  <el-input v-model="form.stopBusinessRemark" placeholder="请输入临停备注" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="场所地址" prop="address">
                  <el-input v-model="form.address" placeholder="请输入场所地址" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="经纬度" prop="longitude">
                  <el-row :gutter="10">
                    <el-col :span="4">
                      <el-input v-model="form.longitude" placeholder="经度" />
                    </el-col>
                    <el-col :span="4">
                      <el-input v-model="form.latitude" placeholder="纬度" />
                    </el-col>
                    <el-col :span="3">
                      <el-button type="text" @click="btnMapLocation" icon="el-icon-location">位置</el-button>
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="审计编号" prop="auditCode">
                  <el-input v-model="form.auditCode" placeholder="审计编号" style="width: 100px;" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="许可证编号" prop="licenceCode">
                  <el-input v-model="form.licenceCode" placeholder="许可证编号" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="消防证编号" prop="fireCode">
                  <el-input v-model="form.fireCode" placeholder="消防证编号" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="备案终端数" prop="baTerminalNum">
                  <el-input v-model="form.baTerminalNum" placeholder="备案终端数" style="width: 110px;" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="实际终端数" prop="realTerminalNum">
                  <el-input v-model="form.realTerminalNum" placeholder="实际终端数" style="width: 110px;" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="安全员数" prop="safetyNum">
                  <el-input v-model="form.safetyNum" placeholder="安全员数" style="width: 100px;" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="计费系统" prop="chargeSystem">
                  <el-input v-model="form.chargeSystem" placeholder="计费系统" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="计费系统版本" prop="chargeSysVersion">
                  <el-input v-model="form.chargeSysVersion" placeholder="计费系统版本" style="width: 120px;" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="营业面积" prop="areaSize">
                  <el-input v-model="form.areaSize" placeholder="营业面积" style="width: 110px;" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="是否连锁" prop="isChain">
                  <el-radio-group v-model="form.isChain" size="small">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="接入运营商" prop="accessOperator">
                  <el-select v-model="form.accessOperator" placeholder="请选择接入运营商" clearable style="width: 100%">
                    <el-option
                      v-for="dict in dict.type.wb_accesss_operator"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="接入方式" prop="connectMode">
                  <el-select v-model="form.connectMode" placeholder="请选择接入方式" clearable style="width: 100%">
                    <el-option
                      v-for="dict in dict.type.access_method"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="备案IP" prop="">
                  <div
                    style="display: flex; margin-bottom: 10px"
                    v-for="(item, i) in form.ipList"
                    :key="i"
                  >
                    <el-input
                      style="width: 120px"
                      v-model="item.ipStart"
                      placeholder="起始IP"
                    ></el-input>
                    <el-input
                      style="width: 120px; margin-left: 10px"
                      v-model="item.ipEnd"
                      placeholder="结束IP"
                    ></el-input>
                    <el-button
                      icon="el-icon-plus"
                      @click="addIp"
                      type="text"
                    ></el-button>
                    <el-button
                      v-if="i != 0"
                      icon="el-icon-minus"
                      @click="removeIp(i)"
                      type="text"
                    ></el-button>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="出口IP起始地址" prop="fromIp">
                  <el-input v-model="form.fromIp" placeholder="出口IP起始地址" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="出口IP截止地址" prop="toIp">
                  <el-input v-model="form.toIp" placeholder="出口IP截止地址" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="邮政编码" prop="zipCode">
                  <el-input v-model="form.zipCode" placeholder="请输入邮政编码" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 负责人信息 -->
        <div class="form-section">
          <div class="section-header" @click="toggleSection('principal')">
            <div class="section-title">
              <div class="title-line"></div>
              <span class="title-text">负责人信息</span>
            </div>
            <div class="section-toggle">
              <span class="toggle-text">{{ sectionCollapsed.principal ? '展开' : '收起' }}</span>
              <i :class="sectionCollapsed.principal ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            </div>
          </div>
          <div v-show="!sectionCollapsed.principal" class="section-content">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="负责人" prop="principal">
                  <el-input v-model="form.principal" placeholder="负责人" style="width: 100px;" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="负责人电话" prop="principalPhone">
                  <el-input v-model="form.principalPhone" placeholder="负责人电话" style="width: 120px;" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="负责人证件号码" prop="principalCertificateCode">
                  <el-input v-model="form.principalCertificateCode" placeholder="负责人证件号码" style="width: 180px;" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="负责人证件(人像)" prop="principalCertificateBackPhotoUrl">
                  <image-upload v-model="form.principalCertificateBackPhotoUrl" :limit="1" :data="{minioId}"/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="负责人证件照(国徽)" prop="principalCertificatePhotoUrl">
                  <image-upload v-model="form.principalCertificatePhotoUrl" :limit="1" :data="{minioId}" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="负责人证件(手持)" prop="principalHandCertificatePhotoUrl">
                  <image-upload v-model="form.principalHandCertificatePhotoUrl" :limit="1" :data="{minioId}" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 法人信息 -->
        <div class="form-section">
          <div class="section-header" @click="toggleSection('legal')">
            <div class="section-title">
              <div class="title-line"></div>
              <span class="title-text">法人信息</span>
            </div>
            <div class="section-toggle">
              <span class="toggle-text">{{ sectionCollapsed.legal ? '展开' : '收起' }}</span>
              <i :class="sectionCollapsed.legal ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            </div>
          </div>
          <div v-show="!sectionCollapsed.legal" class="section-content">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="法人" prop="legalPerson">
                  <el-input v-model="form.legalPerson" placeholder="法人" style="width: 100px;" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="法人电话" prop="legalPhone">
                  <el-input v-model="form.legalPhone" placeholder="法人电话" style="width: 120px;" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="法人证件号码" prop="legalCertificateCode">
                  <el-input v-model="form.legalCertificateCode" placeholder="法人证件号码" style="width: 180px;" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="法人证件(人像)" prop="legalCertificateBackPhotoUrl">
                  
                  <image-upload
                  :on-success="saveOk_back"
                  v-model="form.legalCertificateBackPhotoUrl"
                  :limit="1"
                   />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="法人证件照(国徽)" prop="legalCertificatePhotoUrl">
                  <image-upload v-model="form.legalCertificatePhotoUrl" :limit="1"  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="法人证件(手持)" prop="legalHandCertificatePhotoUrl">
                  <image-upload v-model="form.legalHandCertificatePhotoUrl" :limit="1"  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 场所门面图/拓扑图 -->
        <div class="form-section">
          <div class="section-header" @click="toggleSection('topology')">
            <div class="section-title">
              <div class="title-line"></div>
              <span class="title-text">场所门面图/拓扑图</span>
            </div>
            <div class="section-toggle">
              <span class="toggle-text">{{ sectionCollapsed.topology ? '展开' : '收起' }}</span>
              <i :class="sectionCollapsed.topology ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            </div>
          </div>
          <div v-show="!sectionCollapsed.topology" class="section-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="门面图" prop="facadePhotoUrl">
                  <image-upload v-model="form.facadePhotoUrl" :limit="1" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="拓扑图" prop="topologyPhotoUrl">
                  <image-upload v-model="form.topologyPhotoUrl" :limit="1"  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

      </el-form>

      <!-- 操作按钮 -->
      <div class="form-footer">
        <el-button type="primary" @click="submitForm" :loading="submitting">确 定</el-button>
        <el-button @click="goBack">取 消</el-button>
      </div>
    </section>

    <!-- 地图选择组件 -->
    <!-- 地图经纬度选择窗口 -->
    <MapPoint ref="PointMap1" :visible.sync="openMap" :modalObj='modalObj' @value-changed="getMapLocation"></MapPoint>
  </div>
</template>

<script>
import { getNetbar, addNetbar, updateNetbar } from "@/api/netbar/netbar";
import { listArea } from '@/api/system/area';
import ImageUpload from '@/components/ImageUpload';

export default {
  name: "NetbarAdd",
  dicts: ['site_business_status', 'wb_accesss_operator', 'audit_vendor', 'class_level', 'access_method', 'certificate_type'],
  components: {
    ImageUpload,
    MapPoint: (d) => import('@/components/MapPoint/index.vue')
  },
  data() {
    return {
      loading: false,
      submitting: false,
      siteCode: undefined,
       minioId:'system', 
      // 表单参数
      form: {
        ipList: [{ ipStart: "", ipEnd: "" }],
        isChain: 0
      },
      // 模块折叠状态
      sectionCollapsed: {
        basic: false,
        principal: false,
        legal: false,
        topology: false
      },

      // 区域列表
      areaList: [],
      // 派出所列表
      policeList: [],

      // 地图相关
      openMap: false,
      modalObj: {
        title: '网吧经纬度获取',
        modal: true,
        width: '1050px',
        address: '',
        longitude: 116.4177920000,
        latitude: 39.9042750000,
        optType: '0'
      },
      // 表单校验
      rules: {
        siteCode: [
          { required: true, message: "场所编码不能为空", trigger: "blur" }
        ],
        creditCode: [
          { required: true, message: "统一社会信用代码不能为空", trigger: "blur" }
        ],
        siteName: [
          { required: true, message: "场所名称不能为空", trigger: "blur" }
        ],
        areaCode: [
          { required: true, message: "所属区域不能为空", trigger: "blur" }
        ],
        policeStation: [
          { required: true, message: "所属派出所不能为空", trigger: "blur" }
        ],
        businessStatus: [
          { required: true, message: "营业状态不能为空", trigger: "blur" }
        ],
        address: [
          { required: true, message: "场所地址不能为空", trigger: "blur" }
        ],
        auditVendor: [
          { required: true, message: "审计厂商不能为空", trigger: "blur" }
        ],
        areaSize: [
          { required: true, message: "营业面积不能为空", trigger: "blur" }
        ],
        baTerminalNum: [
          { required: true, message: "备案终端数不能为空", trigger: "blur" }
        ],
        principal: [
          { required: true, message: "负责人不能为空", trigger: "blur" }
        ],
        principalPhone: [
          { required: true, message: "负责人电话不能为空", trigger: "blur" }
        ],
        legalPerson: [
          { required: true, message: "法人不能为空", trigger: "blur" }
        ],
        legalPhone: [
          { required: true, message: "法人电话不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    // 支持两种方式获取siteCode：路由参数和查询参数
    this.siteCode = this.$route.params && this.$route.params.id;
    if (!this.siteCode) {
      this.siteCode = this.$route.query && this.$route.query.siteCode;
    }
    this.getAreaList();
    this.getDetail();
  },
  methods: {
    /**【人像面】  ********************************************************************************3 uploadXc start  */
        saveOk_back(res, file) {
         
            //  this.form.corporationCertificateBack = URL.createObjectURL(file.raw); 
            if (res.code === 200) { 
                this.form.legalCertificateBackPhotoUrl = this.downloadFileUrl+res.data.url; 
                //  this.$modal.message('上传法人证件人像面照片成功!');
            } else { 
                this.form.legalCertificateBackPhotoUrl =''; 
                //this.$modal.msgError('上传法人证件人像面照片失败!');
            }  
             console.log('------***********-------',this.form.legalCertificateBackPhotoUrl)
            // this.$refs.upLegalBack.clearFiles(); // *** 解决只能选择一次文件上传问题

        }, 
    // 表单重置
    reset() {
      this.form = {
        id: null,
        siteCode: null,
        creditCode: null,
        siteName: null,
        areaCode: null,
        policeStation: null,
        classLevel: null,
        businessStatus: null,
        stopBusinessDate: null,
        stopBusinessRemark: null,
        address: null,
        longitude: null,
        latitude: null,
        auditCode: null,
        licenceCode: null,
        fireCode: null,
        baTerminalNum: null,
        realTerminalNum: null,
        safetyNum: null,
        chargeSystem: null,
        chargeSysVersion: null,
        areaSize: null,
        isChain: 0,
        accessOperator: null,
        connectMode: null,
        fromIp: null,
        toIp: null,
        principal: null,
        principalPhone: null,
        principalCertificateCode: null,
        principalCertificateBackPhotoUrl: null,
        principalCertificatePhotoUrl: null,
        principalHandCertificatePhotoUrl: null,
        legalPerson: null,
        legalPhone: null,
        legalCertificateCode: null,
        legalCertificateBackPhotoUrl: null,
        legalCertificatePhotoUrl: null,
        legalHandCertificatePhotoUrl: null,
        facadePhotoUrl: null,
        topologyPhotoUrl: null,
        ipList: [{ ipStart: "", ipEnd: "" }]
      };
      this.resetForm("form");
    },
    /** 获取详情 */
    getDetail() {
      this.reset();
      if (!this.siteCode) {
        return;
      }
      this.loading = true;
      getNetbar({ siteCode: this.siteCode }).then(response => {
        console.log('Netbar API Response:', response); // 调试日志
        if ( response.data) {
          this.form = response.data;
          console.log('Netbar Form data:', this.form); // 调试日志

          // 所属区域不为空的情况下 默认查询出 所属区域对应的所属派出所列表
          if (this.form.areaCode) {
            this.getPoliceList();
          }
          // 确保IP列表存在
          if (!this.form.ipList || this.form.ipList.length === 0) {
            this.form.ipList = [{ ipStart: "", ipEnd: "" }];
          }
        } else {
          console.log('No netbar data found, initializing default form'); // 调试日志
          // 如果没有数据，初始化默认表单
          this.form = {
            siteCode: this.siteCode || '',
            ipList: [{ ipStart: "", ipEnd: "" }],
            isChain: 0
          };
        }
        this.loading = false;
      }).catch(error => {
        console.error('Netbar API Error:', error); // 调试日志
        this.loading = false;
      });
    },
    /** 获取区域列表 */
    getAreaList() {
      listArea({ parentId: '110000' }).then(response => {
        this.areaList = response.data;
      });
    },
    /** 获取派出所列表 */
    getPoliceList() {
      if (!this.form.areaCode) return;
      listArea({ parentId: this.form.areaCode }).then(response => {
        this.policeList = response.data;
      });
    },
    /** 切换模块折叠状态 */
    toggleSection(section) {
      this.sectionCollapsed[section] = !this.sectionCollapsed[section];
    },
    /** 添加IP地址段 */
    addIp() {
      this.form.ipList.push({ ipStart: "", ipEnd: "" });
    },
    /** 删除IP地址段 */
    removeIp(index) {
      this.form.ipList.splice(index, 1);
    },
    /** 提交表单 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.submitting = true;
          if (this.form.id != null) {
            addNetbar(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.handleClose();
            }).finally(() => {
              this.submitting = false;
            });
          } else {
            addNetbar(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.handleClose();
            }).finally(() => {
              this.submitting = false;
            });
          }
        }
      });
    },
    /** 返回按钮 */
    goBack() {
      window.history.go(-1);
    },
    handleClose() {
      this.goBack();
    },

    /**经纬度 位置选择 */
    btnMapLocation() {
      // 检测是否已经输入了地址 没有输入地址 提示先输入地址
      if(this.form.address == undefined || this.form.address == null || this.form.address.trim() === '') {
        this.$message.warning('请先输入场所地址');
        return;
      }

      this.openMap = true; // 显示弹框
      var sTitle = '网吧经纬度获取';

      this.modalObj = {
        title: sTitle,
        modal: true,
        width: '1050px',
        address: this.form.address,
        longitude: this.form.longitude,
        latitude: this.form.latitude,
        optType: '0'
      };  // optType(0=新增、1=编辑、2=未分配编辑)。

      this.$nextTick(() => {
        console.log('this.$refs.PointMap1= ', this.$refs.PointMap1);
        this.$refs.PointMap1.open();
      });
    },

    /**选择点位 后更新经纬度信息  */
    getMapLocation(longitude, latitude, address) {
      console.log('--longitude=', longitude, ' latitude=', latitude, ' address=', address);
      // 更新地址和 经纬度信息字段
      this.form.longitude = longitude;
      this.form.latitude = latitude;
      this.form.address = address;
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .page-header {
    width: 94%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      justify-content: space-between;

      .page-title {
        margin: 0;
        font-size: 18px;
        color: #303133;
        font-weight: 600;
      }
    }
  }

  .form-container {
    width: 94%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
  }

  .form-section {
    margin-bottom: 24px;

    .section-header {
      background-color: #f6f7fb;
      padding: 12px 16px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 4px;
      margin-bottom: 16px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-line {
          width: 3px;
          height: 16px;
          background-color: #4584FF;
        }

        .title-text {
          font-size: 16px;
          font-weight: 600;
          color: #4584FF;
        }
      }

      .section-toggle {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #4584FF;
        font-size: 14px;

        .toggle-text {
          font-size: 14px;
        }

        i {
          font-size: 12px;
          transition: transform 0.3s;
        }
      }

      &:hover {
        background-color: #f0f2f5;
      }
    }

    .section-content {
      padding: 0 16px;
    }
  }
}

// 全局样式：确保表单标签不换行
::v-deep .el-form-item__label {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

// 上传组件样式
.avatar-uploader {
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 148px;
    height: 148px;

    &:hover {
      border-color: #409EFF;
    }
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 148px;
    height: 148px;
    line-height: 148px;
    text-align: center;
    display: block;
  }

  .avatar {
    width: 148px;
    height: 148px;
    display: block;
  }
}

.avatar-uploader-site {
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 200px;
    height: 150px;

    &:hover {
      border-color: #409EFF;
    }
  }

  .avatar-uploader-icon-site {
    font-size: 28px;
    color: #8c939d;
    width: 200px;
    height: 150px;
    line-height: 150px;
    text-align: center;
    display: block;
  }

  .avatar-site {
    width: 200px;
    height: 150px;
    display: block;
  }
}

.form-footer {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}
</style>