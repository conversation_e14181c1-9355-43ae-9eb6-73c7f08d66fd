<template>
<div style="display:flex;background: #f5f7fa;">
  <section class="app-container" :class="{ 'expanded': isRightCollapsed }">
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">网吧场所详情</h2>
        <div class="header-actions">
          <el-button type="primary" size="small" @click="handleEdit">编辑</el-button>
          <el-button icon="el-icon-arrow-left" size="small" @click="goBack">返回</el-button>
        </div>
      </div>
    </div>

    <div class="detail-content">
      <!-- 基础信息 -->
      <div id="basic-info" class="detail-section">
        <div class="section-header" @click="toggleSection('basic')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">基础信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.basic ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.basic ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.basic" class="section-content">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="场所编码">
              {{ form.siteCode }}
            </el-descriptions-item>
            <el-descriptions-item label="统一社会信用代码">
              {{ form.creditCode }}
            </el-descriptions-item>
            <el-descriptions-item label="场所名称" :span="2">
              {{ form.siteName }}
            </el-descriptions-item>
            <el-descriptions-item label="审计厂商">
              <dict-tag :options="dict.type.audit_vendor" :value="form.auditVendor"/>
            </el-descriptions-item>
            <el-descriptions-item label="所属区域">
              {{ form.areaCodeLabel }}
            </el-descriptions-item>
            <el-descriptions-item label="所属派出所">
              {{ form.policeStationLabel }}
            </el-descriptions-item>
            <el-descriptions-item label="分级分类">
              <dict-tag :options="dict.type.class_level" :value="form.classLevel"/>
            </el-descriptions-item>
            <el-descriptions-item label="营业状态">
              <dict-tag :options="dict.type.site_business_status" :value="form.businessStatus"/>
            </el-descriptions-item>
            <el-descriptions-item label="临停截止日期">
              {{ form.stopBusinessDate }}
            </el-descriptions-item>
            <el-descriptions-item label="临停备注">
              {{ form.stopBusinessRemark }}
            </el-descriptions-item>
            <el-descriptions-item label="场所地址" :span="2">
              {{ form.address }}
            </el-descriptions-item>
            <el-descriptions-item label="经纬度">
              经度 {{ form.longitude }} 纬度 {{ form.latitude }}
            </el-descriptions-item>
            <el-descriptions-item label="审计编号">
              {{ form.auditCode }}
            </el-descriptions-item>
            <el-descriptions-item label="许可证编号">
              {{ form.licenceCode }}
            </el-descriptions-item>
            <el-descriptions-item label="消防证编号">
              {{ form.fireCode }}
            </el-descriptions-item>
            <el-descriptions-item label="备案终端数">
              {{ form.baTerminalNum }}
            </el-descriptions-item>
            <el-descriptions-item label="实际终端数">
              {{ form.realTerminalNum }}
            </el-descriptions-item>
            <el-descriptions-item label="安全员数">
              {{ form.safetyNum }}
            </el-descriptions-item>
            <el-descriptions-item label="计费系统">
              {{ form.chargeSystem }}
            </el-descriptions-item>
            <el-descriptions-item label="计费系统版本">
              {{ form.chargeSysVersion }}
            </el-descriptions-item>
            <el-descriptions-item label="营业面积">
              {{ form.areaSize }}
            </el-descriptions-item>
            <el-descriptions-item label="是否连锁">
              {{ form.isChain === '1' ? '是' : '否' }}
            </el-descriptions-item>
            <el-descriptions-item label="接入运营商">
              <dict-tag :options="dict.type.wb_accesss_operator" :value="form.accessOperator"/>
            </el-descriptions-item>
            <el-descriptions-item label="接入方式">
              <dict-tag :options="dict.type.access_method" :value="form.connectMode"/>
            </el-descriptions-item>
            <el-descriptions-item label="备案IP" :span="3">
              <template v-for="(item, i) in form.ipList">
                <div v-if="item.ipStart" :key="i" style="margin-bottom: 5px;">
                  起始IP: {{ item.ipStart }} &nbsp;&nbsp; 结束IP: {{ item.ipEnd }}
                </div>
              </template>
            </el-descriptions-item>
            <el-descriptions-item label="出口IP起始地址">
              {{ form.fromIp }}
            </el-descriptions-item>
            <el-descriptions-item label="出口IP截止地址">
              {{ form.toIp }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 负责人信息 -->
      <div id="principal-info" class="detail-section">
        <div class="section-header" @click="toggleSection('principal')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">负责人信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.principal ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.principal ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.principal" class="section-content">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="负责人">
              {{ form.principal }}
            </el-descriptions-item>
            <el-descriptions-item label="负责人电话">
              {{ form.principalPhone }}
            </el-descriptions-item>
            <el-descriptions-item label="负责人证件号码">
              {{ form.principalCertificateCode }}
            </el-descriptions-item>
          </el-descriptions>

          <!-- 负责人证件照 -->
          <div class="certificate-images">
            <div class="image-item">
              <div class="image-label">负责人证件(人像)</div>
              <el-image
                v-if="form.principalCertificateBackPhotoUrl"
                :src="processedImageUrls.principalCertificateBackPhotoUrl"
                class="certificate-img"
                :preview-src-list="[processedImageUrls.principalCertificateBackPhotoUrl]">
                <div slot="placeholder" class="image-slot">
                  加载中<span class="dot">...</span>
                </div>
              </el-image>
              <div v-else class="no-image">暂无图片</div>
            </div>
            <div class="image-item">
              <div class="image-label">负责人证件照(国徽)</div>
              <el-image
                v-if="form.principalCertificatePhotoUrl"
                :src="processedImageUrls.principalCertificatePhotoUrl"
                class="certificate-img"
                :preview-src-list="[processedImageUrls.principalCertificatePhotoUrl]">
                <div slot="placeholder" class="image-slot">
                  加载中<span class="dot">...</span>
                </div>
              </el-image>
              <div v-else class="no-image">暂无图片</div>
            </div>
            <div class="image-item">
              <div class="image-label">负责人证件(手持)</div>
              <el-image
                v-if="form.principalHandCertificatePhotoUrl"
                :src="processedImageUrls.principalHandCertificatePhotoUrl"
                class="certificate-img"
                :preview-src-list="[processedImageUrls.principalHandCertificatePhotoUrl]">
                <div slot="placeholder" class="image-slot">
                  加载中<span class="dot">...</span>
                </div>
              </el-image>
              <div v-else class="no-image">暂无图片</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 法人信息 -->
      <div id="legal-info" class="detail-section">
        <div class="section-header" @click="toggleSection('legal')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">法人信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.legal ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.legal ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.legal" class="section-content">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="法人">
              {{ form.legalPerson }}
            </el-descriptions-item>
            <el-descriptions-item label="法人电话">
              {{ form.legalPhone }}
            </el-descriptions-item>
            <el-descriptions-item label="法人证件号码">
              {{ form.legalCertificateCode }}
            </el-descriptions-item>
          </el-descriptions>

          <!-- 法人证件照 -->
          <div class="certificate-images">
            <div class="image-item">
              <div class="image-label">法人证件(人像)</div>
              <el-image
                v-if="form.legalCertificateBackPhotoUrl"
                :src="processedImageUrls.legalCertificateBackPhotoUrl"
                class="certificate-img"
                :preview-src-list="[processedImageUrls.legalCertificateBackPhotoUrl]">
                <div slot="placeholder" class="image-slot">
                  加载中<span class="dot">...</span>
                </div>
              </el-image>
              <div v-else class="no-image">暂无图片</div>
            </div>
            <div class="image-item">
              <div class="image-label">法人证件照(国徽)</div>
              <el-image
                v-if="form.legalCertificatePhotoUrl"
                :src="processedImageUrls.legalCertificatePhotoUrl"
                class="certificate-img"
                :preview-src-list="[processedImageUrls.legalCertificatePhotoUrl]">
                <div slot="placeholder" class="image-slot">
                  加载中<span class="dot">...</span>
                </div>
              </el-image>
              <div v-else class="no-image">暂无图片</div>
            </div>
            <div class="image-item">
              <div class="image-label">法人证件(手持)</div>
              <el-image
                v-if="form.legalHandCertificatePhotoUrl"
                :src="processedImageUrls.legalHandCertificatePhotoUrl"
                class="certificate-img"
                :preview-src-list="[processedImageUrls.legalHandCertificatePhotoUrl]">
                <div slot="placeholder" class="image-slot">
                  加载中<span class="dot">...</span>
                </div>
              </el-image>
              <div v-else class="no-image">暂无图片</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 场所门面图/拓扑图 -->
      <div id="topology-info" class="detail-section">
        <div class="section-header" @click="toggleSection('topology')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">场所门面图/拓扑图</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.topology ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.topology ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.topology" class="section-content">
          <div class="site-images">
            <div class="image-item">
              <div class="image-label">门面图</div>
              <el-image
                v-if="form.facadePhotoUrl"
                :src="processedImageUrls.facadePhotoUrl"
                class="site-img"
                :preview-src-list="[processedImageUrls.facadePhotoUrl]">
                <div slot="placeholder" class="image-slot">
                  加载中<span class="dot">...</span>
                </div>
              </el-image>
              <div v-else class="no-image">暂无图片</div>
            </div>
            <div class="image-item">
              <div class="image-label">拓扑图</div>
              <el-image
                v-if="form.topologyPhotoUrl"
                :src="processedImageUrls.topologyPhotoUrl"
                class="site-img"
                :preview-src-list="[processedImageUrls.topologyPhotoUrl]">
                <div slot="placeholder" class="image-slot">
                  加载中<span class="dot">...</span>
                </div>
              </el-image>
              <div v-else class="no-image">暂无图片</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 右侧数据维护记录区域 -->
  <collapsible-detail-right @collapse-change="handleRightCollapseChange">
    <!-- 锚点导航组件 -->
    <template #anchor>
      <anchor-navigation
        :anchor-items="anchorItems"
        @anchor-change="handleAnchorChange"
      />
    </template>

    <!-- 数据维护时间线 -->
    <template #timeline>
      <data-maintenance-timeline
        :biz-type="'SITE_BASE'"
        :biz-id="form.siteCode"
        v-if="form.siteCode"
      />
    </template>
  </collapsible-detail-right>
</div>
</template>

<script>
import { getNetbar } from "@/api/netbar/netbar";
import DataMaintenanceTimeline from '@/components/DataMaintenanceTimeline'
import AnchorNavigation from '@/components/AnchorNavigation'
import CollapsibleDetailRight from '@/components/CollapsibleDetailRight'

export default {
  name: "NetbarDetail",
  dicts: ['site_business_status', 'wb_accesss_operator', 'audit_vendor', 'class_level', 'access_method', 'certificate_type'],
  components: {
    DataMaintenanceTimeline,
    AnchorNavigation,
    CollapsibleDetailRight
  },
  data() {
    return {
      loading: false,
      siteCode: undefined,
      // 表单参数
      form: {},
      // 模块折叠状态
      sectionCollapsed: {
        basic: false,
        principal: false,
        legal: false,
        topology: false
      },

      // 锚点导航配置
      anchorItems: [
        { id: 'basic-info', label: '基础信息' },
        { id: 'principal-info', label: '负责人信息' },
        { id: 'legal-info', label: '法人信息' },
        { id: 'topology-info', label: '场所门面图/拓扑图' }
      ],

      // 右侧面板收起状态
      isRightCollapsed: false
    };
  },
  computed: {
    // 处理图片URL，确保包含正确的前缀
    processedImageUrls() {
      const baseUrl = process.env.VUE_APP_BASE_API + "/file/";

      const processUrl = (url) => {
        console.log(baseUrl,'---------urlurlurl---------',url)
        if (!url) return '';
        if (url.includes(baseUrl)) return url;
        console.log(url.includes(baseUrl),'---------urlurlurl----222-----',baseUrl + url)
        // return baseUrl + url;
         return  url;
      };

      return {
        principalCertificateBackPhotoUrl: processUrl(this.form.principalCertificateBackPhotoUrl),
        principalCertificatePhotoUrl: processUrl(this.form.principalCertificatePhotoUrl),
        principalHandCertificatePhotoUrl: processUrl(this.form.principalHandCertificatePhotoUrl),
        legalCertificateBackPhotoUrl: processUrl(this.form.legalCertificateBackPhotoUrl),
        legalCertificatePhotoUrl: processUrl(this.form.legalCertificatePhotoUrl),
        legalHandCertificatePhotoUrl: processUrl(this.form.legalHandCertificatePhotoUrl),
        facadePhotoUrl: processUrl(this.form.facadePhotoUrl),
        topologyPhotoUrl: processUrl(this.form.topologyPhotoUrl)
      };
      
    }
  },
  created() {
    this.siteCode = this.$route.params && this.$route.params.id;
    this.getDetail();
  },
  methods: {
    /** 获取详情 */
    getDetail() {
      if (!this.siteCode) {
        return;
      }
      this.loading = true;
      getNetbar({ siteCode: this.siteCode }).then(response => {
        console.log('Netbar detail API Response:', response);
        if ( response.data) {
          this.form = response.data;
          if (!this.form.ipList || this.form.ipList.length === 0) {
            this.form.ipList = [];
          }
        } else {
          this.form = {};
        }
        this.loading = false;
      }).catch(error => {
        console.error('Netbar detail API Error:', error);
        this.loading = false;
      });
    },
    /** 切换模块折叠状态 */
    toggleSection(section) {
      this.sectionCollapsed[section] = !this.sectionCollapsed[section];
    },
    /** 编辑按钮 */
    handleEdit() {
      this.$router.push("/data-management/netbar/edit/" + this.siteCode);
    },
    /** 返回按钮 */
    goBack() {
      window.history.go(-1);
    },
    handleClose() {
      this.goBack();
    },

    // 处理锚点变化
    handleAnchorChange(anchorId) {
      console.log('当前激活的锚点:', anchorId);
    },

    // 处理右侧面板收起展开
    handleRightCollapseChange(isCollapsed) {
      this.isRightCollapsed = isCollapsed;
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
  width: calc(100% - 380px);
  transition: width 0.3s ease;

  &.expanded {
    width: calc(100% - 60px);
  }

  .page-header {
    width: 99%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      justify-content: space-between;

      .page-title {
        margin: 0;
        font-size: 18px;
        color: #303133;
        font-weight: 600;
      }

      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
  }

  .detail-content {
    width: 99%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
  }

  .detail-section {
    margin-bottom: 24px;

    .section-header {
      background-color: #f6f7fb;
      padding: 12px 16px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 4px;
      margin-bottom: 16px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-line {
          width: 3px;
          height: 16px;
          background-color: #4584FF;
        }

        .title-text {
          font-size: 16px;
          font-weight: 600;
          color: #4584FF;
        }
      }

      .section-toggle {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #4584FF;
        font-size: 14px;

        .toggle-text {
          font-size: 14px;
        }

        i {
          font-size: 12px;
          transition: transform 0.3s;
        }
      }

      &:hover {
        background-color: #f0f2f5;
      }
    }

    .section-content {
      padding: 0 16px;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

::v-deep .el-descriptions__label {
  font-weight: 500;
  color: #666;
}

::v-deep .el-descriptions__content {
  color: #333;
}

// 证件照样式
.certificate-images {
  display: flex;
  gap: 20px;
  margin-top: 20px;
  flex-wrap: wrap;

  .image-item {
    text-align: center;

    .image-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .certificate-img {
      width: 148px;
      height: 148px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }

    .no-image {
      width: 148px;
      height: 148px;
      border: 1px dashed #ddd;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #999;
      font-size: 14px;
      background-color: #f9f9f9;
    }
  }
}

// 场所图片样式
.site-images {
  display: flex;
  gap: 30px;
  margin-top: 20px;
  flex-wrap: wrap;

  .image-item {
    text-align: center;

    .image-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .site-img {
      width: 200px;
      height: 150px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }

    .no-image {
      width: 200px;
      height: 150px;
      border: 1px dashed #ddd;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #999;
      font-size: 14px;
      background-color: #f9f9f9;
    }
  }
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 14px;
}


</style>