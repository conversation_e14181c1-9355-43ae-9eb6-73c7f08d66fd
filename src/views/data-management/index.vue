<template>
  <div class="data-management-home">
    <!-- 第一个模块：顶部搜索框 -->
    <section class="top-container">
      <div class="search-section">
        <div class="search-container">
          <el-select
            v-model="selectDocument"
            placeholder="请选择"
            class="search-select"
            @change="handleSearch"
          >
            <el-option
              v-for="option in searchOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
          <el-input
            v-model="searchKeys"
            placeholder="请输入搜索内容"
            class="search-input"
            clearable
            @keyup.enter.native="handleSearch"
            @clear="handleSearch"
          />
        </div>
      </div>
      <el-button class="switch-map-mode" size="mini" @click="switchToMapMode">
        <div class="switch-map-mode-content"><img src="@/assets/images/tz/seitch.svg" alt="切换" class="switch-icon" /> <span>切换地图模式</span></div>
      </el-button>
    </section>
    

    <!-- 第二个模块：基础数据台账 -->
    <div class="data-cards-section">
      <h2 class="section-title">基础数据台账</h2>
      <div class="cards-container">
        <div
          v-for="card in dataCards"
          :key="card.key"
          class="data-card"
          @click="navigateToList(card.key)"
        >
          <div class="card-header">
            <span class="card-title">{{ card.name }}</span>
            <el-button
              type="primary"
              plain
              size="mini"
              icon="el-icon-plus"
              @click.stop="navigateToAdd(card.key)"
            >
              新增
            </el-button>
          </div>
          <div class="card-count">{{ card.totalCount }}</div>
          <div class="card-pending">
            <span class="label">本月待更新数据：</span>
            <span class="value pending">{{ card.pendingCount }}</span>
          </div>
          <div class="card-overdue">
            <span class="label">已超期未更新数量：</span>
            <span class="value overdue">{{ card.overdueCount }}</span>
            <span class="trend" :class="card.trendType">
              {{ card.trendIcon }} {{ formatPercent(card.trendPercent) }}% 环比上月
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 第三个模块：基础数据更新情况 -->
    <div class="charts-section">
      <h2 class="section-title">基础数据更新情况</h2>
      <el-row :gutter="20">
        <!-- 左侧：全量数据变化情况 -->
        <el-col :span="8" :lg="8" :md="24" :sm="24" :xs="24">
          <div class="chart-card">
            <div class="chart-title">全量数据变化情况</div>
            <div id="dataChangeChart" class="chart-container"></div>
          </div>
        </el-col>
        
        <!-- 中间：数据分布 -->
        <el-col :span="8" :lg="8" :md="24" :sm="24" :xs="24">
          <div class="chart-card">
            <div class="chart-title">数据分布</div>
            <div class="distribution-container">
              <div class="chart-section">
                <div id="dataDistributionChart" class="chart-container"></div>
              </div>
              <div class="legend-section">
                <div class="custom-legend">
                  <div
                    v-for="(item, index) in distributionData"
                    :key="index"
                    class="legend-item"
                    :class="{ 'legend-row-1': index < 4, 'legend-row-2': index >= 4 }"
                  >
                    <span class="legend-icon" :style="{ backgroundColor: item.itemStyle.color }"></span>
                    <span class="legend-name">{{ item.name }}</span>
                    <span class="legend-value">{{ item.value }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        
        <!-- 右侧：各派出所数据贡献情况 -->
        <el-col :span="8" :lg="8" :md="24" :sm="24" :xs="24">
          <div class="chart-card">
            <div class="chart-title">各派出所数据贡献情况</div>
            <div id="contributionChart" class="chart-container"></div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getDataStatistics, getDataUpdateReminderStat, getDataChangeHistory, getPoliceStationContribution, searchData } from '@/api/data-management'
import { levelprotectSearchType } from '@/api/home'

export default {
  name: 'DataManagementHome',
  data() {
    return {
      // 搜索相关
      selectDocument: '',
      searchKeys: '',
      searchOptions: [],
      // 原始搜索类型数据
      searchTypesData: {},
      // 过滤后的搜索类型数据（用于checkbox）
      checkedSearchTypes: [],
      
      // 数据卡片
      dataCards: [],

      // 数据更新提醒统计
      reminderStatData: [],
      
      // 图表实例
      dataChangeChart: null,
      dataDistributionChart: null,
      contributionChart: null,
      
      // 图表数据
      changeData: {
        dates: [],
        updateData: [],
        addData: []
      },

      distributionData: [],

      contributionType: 'total',
      contributionData: []
    }
  },
  mounted() {
    this.loadData()
    this.loadSearchTypes()
    // 添加窗口大小变化监听
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    // 销毁图表实例
    this.dataChangeChart && this.dataChangeChart.dispose()
    this.dataDistributionChart && this.dataDistributionChart.dispose()
    this.contributionChart && this.contributionChart.dispose()
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    // 切换到地图模式
    switchToMapMode() {
      this.$router.push('/data-management/screenmap');
    },

    // 加载所有数据
    async loadData() {
      try {
        await Promise.all([
          this.loadDataStatistics(),
          this.loadDataUpdateReminderStat(),
          this.loadDataChangeHistory(),
          this.loadPoliceStationContribution()
        ])

        this.$nextTick(() => {
          this.initCharts()
        })
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      }
    },

    // 加载搜索类型数据
    async loadSearchTypes() {
      try {
        const response = await levelprotectSearchType()
        if (response.code === 200 && response.data) {
          this.searchTypesData = response.data

          // 过滤掉不需要展示的字段
          const excludeLabels = ['单位主体信息表', '检查任务表', '检查任务结果表', '行政处罚表', '行政处置表']
          const filteredData = {}

          Object.keys(this.searchTypesData).forEach(category => {
            filteredData[category] = this.searchTypesData[category].filter(item =>
              !excludeLabels.includes(item.label)
            )
          })

          // 重新组织搜索选项
          this.searchOptions = [
            {
              label: '全部',
              value: this.getAllCodes(filteredData)
            }
          ]

          // 添加等保备案
          if (filteredData['等保备案'] && filteredData['等保备案'].length > 0) {
            filteredData['等保备案'].forEach(item => {
              this.searchOptions.push({
                label: item.label,
                value: item.code
              })
            })
          }

          // 添加网吧
          if (filteredData['网吧'] && filteredData['网吧'].length > 0) {
            filteredData['网吧'].forEach(item => {
              this.searchOptions.push({
                label: item.label,
                value: item.code
              })
            })
          }

          // 添加运营商信息表（从单位主体中提取）
          if (filteredData['单位主体'] && filteredData['单位主体'].length > 0) {
            const operatorInfo = filteredData['单位主体'].find(item => item.code === 'operator_info')
            if (operatorInfo) {
              this.searchOptions.push({
                label: operatorInfo.label,
                value: operatorInfo.code
              })
            }
          }

          // 添加网站信息表、app表、小程序表（分别显示）
          if (filteredData['网站'] && filteredData['网站'].length > 0) {
            filteredData['网站'].forEach(item => {
              this.searchOptions.push({
                label: item.label,
                value: item.code
              })
            })
          }

          // 添加电子屏分类（保持原有逻辑）
          if (filteredData['电子屏'] && filteredData['电子屏'].length > 0) {
            const codes = filteredData['电子屏'].map(item => item.code).join(',')
            this.searchOptions.push({
              label: '电子屏',
              value: codes
            })
          }

          // 设置默认选中的搜索类型
          this.selectDocument = this.getAllCodes(filteredData)

          // 设置过滤后的数据用于checkbox
          this.checkedSearchTypes = this.getAllCodes(filteredData).split(',')
        }
      } catch (error) {
        console.error('加载搜索类型失败:', error)
      }
    },

    // 获取所有过滤后的code
    getAllCodes(filteredData) {
      const allCodes = []
      Object.keys(filteredData).forEach(category => {
        filteredData[category].forEach(item => {
          allCodes.push(item.code)
        })
      })
      return allCodes.join(',')
    },

    // 加载基础数据台账统计
    async loadDataStatistics() {
      try {
        const response = await getDataStatistics()
        if (response.code === 200 && response.data) {
          // 创建名称到key的映射
          const nameToKeyMap = {
            '电子屏': 'screenNum',
            '等保备案': 'levelprotectNum',
            '网站': 'websiteNum',
            'APP': 'appNum',
            '小程序': 'appletNum',
            '网吧': 'netbarNum',
            '运营商': 'operatorNum',
            '非经营': 'wifiNum'
          }

          this.dataCards = response.data.map(item => ({
            key: nameToKeyMap[item.name] || item.name,
            name: item.name,
            totalCount: item.num,
            pendingCount: 0, // 将在loadDataUpdateReminderStat中更新
            overdueCount: 0, // 将在loadDataUpdateReminderStat中更新
            trendPercent: 0, // 将在loadDataUpdateReminderStat中更新
            trendType: 'up',
            trendIcon: '↑'
          }))

          // 同时更新distributionData用于环状图
          const colors = ['#0081ff', '#1abb78', '#ffc53d', '#ff7a45', '#722ed1', '#eb2f96', '#52c41a', '#13c2c2']
          this.distributionData = response.data.map((item, index) => ({
            name: item.name,
            value: item.num,
            itemStyle: { color: colors[index % colors.length] }
          }))
        }
      } catch (error) {
        console.error('加载数据统计失败:', error)
      }
    },

    // 加载数据更新提醒统计
    async loadDataUpdateReminderStat() {
      try {
        const response = await getDataUpdateReminderStat()
        if (response.code === 200 && response.data) {
          this.reminderStatData = response.data

          // 更新dataCards中的待更新和超期数据
          this.dataCards.forEach(card => {
            const reminderItem = this.reminderStatData.find(item => item.name === card.name)
            if (reminderItem) {
              card.pendingCount = reminderItem.pendingUpdateNum || 0
              card.overdueCount = reminderItem.overdueNum || 0
              card.trendPercent = Math.abs(reminderItem.qoqRate || 0)
              card.trendType = (reminderItem.qoqRate || 0) >= 0 ? 'up' : 'down'
              card.trendIcon = (reminderItem.qoqRate || 0) >= 0 ? '↑' : '↓'
            }
          })
        }
      } catch (error) {
        console.error('加载数据更新提醒统计失败:', error)
      }
    },

    // 加载全量数据变化情况
    async loadDataChangeHistory() {
      try {
        const response = await getDataChangeHistory('day')
        if (response.code === 200 && response.data) {
          this.changeData.dates = response.data.map(item => item.statisticsDate)
          this.changeData.updateData = response.data.map(item => item.UPDATE || 0)
          this.changeData.addData = response.data.map(item => item.ADD || 0)
        }
      } catch (error) {
        console.error('加载数据变化历史失败:', error)
      }
    },

    // 加载各派出所数据贡献情况
    async loadPoliceStationContribution() {
      try {
        let response = await getPoliceStationContribution()
        if (response.code === 200 && response.data) {
          // 按数值降序排序，并只取前5个
           this.contributionData = response.data
            // .map(item => ({
            //   name: item.name  || '未知派出所',
            //   value: item.num || 0
            // }))
             // 过滤掉无效数据
          // 数据加载完成后，如果图表已初始化，立即更新图表
          if (this.contributionChart) {
            this.updateContributionChart()
          }
        }
      } catch (error) {
        console.error('加载派出所贡献数据失败:', error)
      }
    },

    // 处理搜索
    handleSearch() {
      // console.log('搜索类型:', this.selectDocument, '关键词:', this.searchKeys)
      if (!this.searchKeys.trim()) return
      // 验证搜索关键词
      // if (!this.searchKeys || !this.searchKeys.trim()) {
      //   this.$message.warning('请输入关键词才能搜索')
      //   return
      // }

      // 构建查询参数
      const query = {
        selectDocument: this.selectDocument,
        keyWord: this.searchKeys.trim()
      }

      // 跳转到搜索详情页面
      this.$router.push({
        path: '/data-management/searchDetail',
        query: query
      })
    },

    // 导航到列表页
    navigateToList(key) {
      // console.log('导航到列表页:', key)
      const routeMap = {
        'screenNum': '/data-management/screen',
        'levelprotectNum': '/data-management/levelprotect',
        'websiteNum': '/data-management/website',
        'appNum': '/data-management/website/app',
        'appletNum': '/data-management/website/applet',
        'netbarNum': '/data-management/netbar',
        'operatorNum': '/data-management/operator/baseinfo',
        'wifiNum': '/data-management/wifi/site'
      }

      if (routeMap[key]) {
        this.$router.push(routeMap[key])
      } else {
        this.$message.info(`${key}列表页面开发中`)
      }
    },

    // 导航到新增页
    navigateToAdd(key) {
      // console.log('导航到新增页:', key)
      const routeMap = {
        'screenNum': '/data-management/screen/add',
        'levelprotectNum': '/data-management/levelprotect/add',
        'websiteNum': '/data-management/website/add',
        'appNum': '/data-management/website/app/add',
        'appletNum': '/data-management/website/applet/add',
        'netbarNum': '/data-management/netbar/add',
        'operatorNum': '/data-management/operator/baseinfo/add',
        'wifiNum': '/data-management/wifi/site/add'
      }

      if (routeMap[key]) {
        this.$router.push(routeMap[key])
      } else {
        this.$message.info(`${key}新增页面开发中`)
      }
    },

    // 初始化所有图表
    initCharts() {
      this.$nextTick(() => {
        this.initDataChangeChart()
        this.initDataDistributionChart()
        this.initContributionChart()
      })
    },

    // 初始化全量数据变化情况图表
    initDataChangeChart() {
      const chartDom = document.getElementById('dataChangeChart')
      if (!chartDom) return

      this.dataChangeChart = echarts.init(chartDom)

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: ['变更', '新增'],
          bottom: 0
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.changeData.dates
        },
        yAxis: {
          type: 'value',
          min: -1,
          // max: 500
        },
        series: [
          {
            name: '变更',
            type: 'line',
            smooth: true,
            lineStyle: {
              width: 3,
              color: '#0081ff'
            },
            itemStyle: {
              color: '#0081ff'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(0, 129, 255, 0.3)' },
                { offset: 1, color: 'rgba(0, 129, 255, 0.1)' }
              ])
            },
            data: this.changeData.updateData
          },
          {
            name: '新增',
            type: 'line',
            smooth: true,
            lineStyle: {
              width: 3,
              color: '#1abb78'
            },
            itemStyle: {
              color: '#1abb78'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(26, 187, 120, 0.3)' },
                { offset: 1, color: 'rgba(26, 187, 120, 0.1)' }
              ])
            },
            data: this.changeData.addData
          }
        ]
      }

      this.dataChangeChart.setOption(option)
    },

    // 初始化数据分布图表
    initDataDistributionChart() {
      const chartDom = document.getElementById('dataDistributionChart')
      if (!chartDom) return

      this.dataDistributionChart = echarts.init(chartDom)

      const totalValue = this.distributionData.reduce((sum, item) => sum + item.value, 0)

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        legend: {
          show: false
        },
        graphic: {
          type: 'text',
          left: 'center',
          top: 'center',
          style: {
            text: `${totalValue}\n总量`,
            textAlign: 'center',
            fill: '#333',
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        series: [
          {
            name: '数据分布',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '50%'],
            data: this.distributionData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }

      this.dataDistributionChart.setOption(option)
    },

    // 初始化贡献情况图表
    initContributionChart() {
      const chartDom = document.getElementById('contributionChart')
      if (!chartDom) {
        console.error('找不到贡献图表容器')
        return
      }

      this.contributionChart = echarts.init(chartDom)
      this.updateContributionChart()
    },

    // 更新贡献情况图表
    async updateContributionChart() {
      if (!this.contributionChart) {
        console.log('贡献图表未初始化')
        return
      }

      const data = this.contributionData || []
      if (data.length === 0) {
        console.log('贡献数据为空')
        return
      }
      const topData = data
      // const maxValue = Math.max(...topData.map(item => item.value))

      // 计算图表高度，超过5个时启用滚动
      const chartHeight = Math.max(topData.length * 40, 200); // 每个柱子40px高度，最小200px
      const maxValue = Math.max(...topData.map(item => item.value));

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '20%',
          right: '15%', // 增加右边距为数值标签留空间
          top: '3%',
          bottom: '3%',
          containLabel: true
        },
        // 当数据超过5个时启用滚动
        dataZoom: topData.length > 10 ? [
          {
            type: 'slider',
            yAxisIndex: 0,
            width: 10,
            right: 5,
            start: 0,
            end: Math.min(100, (10 / topData.length) * 100), // 显示前5个
            handleSize: 8,
            showDetail: false
          }
        ] : [],
        xAxis: {
          type: 'value',
          max: maxValue * 1.1, // 设置最大值，为右侧数值标签留空间
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'category',
          data: topData.map(item => item.name),
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#333',
            fontSize: 12
          }
        },
        series: [
          {
            type: 'bar',
            data: topData.map(item => ({
              value: item.value,
              itemStyle: {
                color: '#0099ff',
                borderRadius: [0, 8, 8, 0]
              }
            })),
            barWidth: 14, // 柱子宽度
            borderRadius: 6,
            backgroundStyle: {
              color: '#e9eef4',
              borderRadius: [0, 8, 8, 0]
            },
            showBackground: true,
            label: {
              show: false // 隐藏柱子上的标签
            }
          },
          {
            // 添加一个隐藏的系列用于在最右端显示数值
            type: 'bar',
            data: topData.map(() => maxValue * 1.05), // 设置为最大值的105%，确保在最右端
            barWidth: 0, // 隐藏柱子
            itemStyle: {
              color: 'transparent' // 透明色
            },
            label: {
              show: true,
              position: 'right',
              color: '#333',
              fontSize: 12,
              fontWeight: 'bold',
              formatter: function(params) {
                // 显示对应的真实数值
                return topData[params.dataIndex].value;
              },
              offset: [10, -10] // 向左偏移一点，避免贴边
            },
            silent: true, // 不响应鼠标事件
            tooltip: {
              show: false // 不显示tooltip
            }
          }
        ]
      }

      this.contributionChart.setOption(option)
    },

    // 格式化百分比，最多保留3位小数
    formatPercent(value) {
      if (typeof value !== 'number') return '0'

      // 保留3位小数，去掉末尾的0
      const formatted = parseFloat(value.toFixed(3))
      return formatted.toString()
    },

    // 处理窗口大小变化
    handleResize() {
      // 重新调整所有图表大小
      this.dataChangeChart && this.dataChangeChart.resize()
      this.dataDistributionChart && this.dataDistributionChart.resize()
      this.contributionChart && this.contributionChart.resize()
    }
  }
}
</script>

<style lang="scss" scoped>
.data-management-home {
  // padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
  margin-top: -8px;

  

  // 顶部容器
  .top-container {
    position: relative;
    padding-top: 40px;
    padding-bottom: 30px;
     background-image: url('../../assets/images/tz/usearch.png');
  background-size: cover;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  // 搜索区域
  .search-section {
    display: flex;
    justify-content: center;
    
    .search-container {
      display: flex;
      width: 600px;
      border: 1px solid #4584FF;
      border-radius: 8px;
      overflow: hidden;
      height: 46px;
      ::v-deep .el-input__inner {
        border: none;
      }
       ::v-deep .el-input--medium .el-input__inner {
        height: 42px;
        line-height: 42px;
      }
      .search-select {
        width: 150px;

        ::v-deep .el-input {
          height: 42px;

          .el-input__inner {
            height: 42px;
            line-height: 42px;
            border: none;
            border-radius: 0;
            border-right: 1px solid #4584FF;
            background-color: #fff;
          }
        }
      }

      .search-input {
        flex: 1;

        ::v-deep .el-input {
          height: 42px;

          .el-input__inner {
            height: 42px;
            line-height: 42px;
            border: none;
            border-radius: 0;
            background-color: #fff;
          }
        }
      }
    }
  }

  // 切换地图模式按钮
  .switch-map-mode {
    position: absolute;
    top: 18px;
    right: 18px;
    background: #4584FF;
    border-color: #4584FF;
    color: white;
    height: 36px;
    padding: 0 10px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    font-size: 12px;
    line-height: 36px;
    .switch-map-mode-content {
      display: flex !important;
      align-items: center !important;
    }
    &:hover {
      background: #3a73e6;
      border-color: #3a73e6;
    }
    
    .switch-icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
  }

  // 数据卡片区域
  .data-cards-section {
    margin-bottom: 40px;
    background: white;
    padding: 20px;
    border-radius: 8px;

    .section-title {
      font-size: 20px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 20px;
      padding-left: 10px;
      border-left: 4px solid #409EFF;
    }

    .cards-container {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;

      .data-card {
        background-color: #F4F8FF;
        border-radius: 8px;
        padding: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid #e4e7ed;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;

          .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }

          .el-button {
            border-radius: 4px;
          }
        }

        .card-count {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 10px;
        }

        .card-pending {
          margin-bottom: 8px;
          font-size: 14px;

          .label {
            color: #606266;
          }

          .value.pending {
            color: #17B26D;
            font-weight: 600;
          }
        }

        .card-overdue {
          font-size: 14px;
          display: flex;
          align-items: center;
          flex-wrap: wrap;

          .label {
            color: #606266;
          }

          .value.overdue {
            color: #F56C6C;
            font-weight: 600;
            margin-right: 10px;
          }

          .trend {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;

            &.up {
              background-color: #f56c6c2a;
              color: #F56C6C;
            }

            &.down {
              background-color: #17b26d2d;
              color: #17B26D;
            }
          }
        }
      }
    }
  }

  // 图表区域
  .charts-section {
    background: white;
    padding: 20px;
    border-radius: 8px;

    .section-title {
      font-size: 20px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 20px;
      padding-left: 10px;
      border-left: 4px solid #409EFF;
    }

    .chart-card {
      background: transparent; // 去掉背景色
      border: none; // 去掉边框
      padding: 20px;
      box-shadow: none; // 去掉阴影
      height: 400px;

      .chart-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 20px;
        text-align: center;
      }

      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .chart-title {
          margin-bottom: 0;
          text-align: left;
        }
      }

      .chart-container {
        height: 320px;
      }

      // 数据分布图表特殊布局
      .distribution-container {
        display: flex;
        height: 320px;

        .chart-section {
          flex: 0 0 60%;

          .chart-container {
            height: 100%;
          }
        }

        .legend-section {
          flex: 1;
          padding-left: 20px;
          display: flex;
          align-items: center;

          .custom-legend {
            width: 100%;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;

            .legend-item {
              display: flex;
              align-items: center;
              font-size: 12px;
              margin-bottom: 8px;

              .legend-icon {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                margin-right: 8px;
                flex-shrink: 0;
              }

              .legend-name {
                flex: 1;
                color: #606266;
                margin-right: 4px;
              }

              .legend-value {
                color: #303133;
                font-weight: 600;
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .data-management-home {
    .data-cards-section {
      .cards-container {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }
}

@media (max-width: 768px) {
  .data-management-home {
    // padding: 15px;

    .search-section {
      .search-container {
        width: 100%;
        flex-direction: column;

        .search-select {
          width: 100%;
        }
      }
    }

    .data-cards-section {
      .cards-container {
        grid-template-columns: 1fr;
      }
    }

    .charts-section {
      .el-row {
        .el-col {
          margin-bottom: 20px;
        }
      }

      .chart-card {
        .distribution-container {
          flex-direction: column;

          .chart-section {
            flex: 0 0 60%;
          }

          .legend-section {
            flex: 1;
            padding-left: 0;
            padding-top: 20px;

            .custom-legend {
              grid-template-columns: repeat(4, 1fr);
            }
          }
        }
      }
    }
  }
}
</style>
