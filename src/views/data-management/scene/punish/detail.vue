<template>
  <div class="app-container">
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">行政处罚详情</h2>
        <div class="header-actions">
          <el-button
            type="primary"
            size="small"
            @click="handleEdit"
            :disabled="!punishId || punishId === 'undefined' || punishId === 'null'"
          >
            编辑
          </el-button>
          <el-button icon="el-icon-arrow-left" size="small" @click="goBack">返回</el-button>
        </div>
      </div>
    </div>

    <div class="detail-content">
      <!-- 空状态提示 -->
      <div v-if="!punishId || punishId === 'undefined' || punishId === 'null' || (!form.punishNum && !loading)" class="empty-state">
        <el-empty description="未找到处罚记录数据">
          <el-button type="primary" @click="goBack">返回列表</el-button>
        </el-empty>
      </div>

      <!-- 基础信息 -->
      <div v-else class="detail-section">
        <div class="section-header" @click="toggleSection('basic')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">基础信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.basic ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.basic ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.basic" class="section-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="处罚决定书编号">
              {{ form.punishNum }}
            </el-descriptions-item>
            <el-descriptions-item label="公司名称">
              {{ form.corporateName }}
            </el-descriptions-item>
            <el-descriptions-item label="统一社会信用代码">
              {{ form.creditCode }}
            </el-descriptions-item>
            <el-descriptions-item label="处罚决定书时间">
              {{ form.punishDate }}
            </el-descriptions-item>
            <el-descriptions-item label="处罚结果">
              <dict-tag :options="dict.type.si_punish_status" :value="form.punishStatus"/>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ parseTime(form.createTime) }}
            </el-descriptions-item>
          </el-descriptions>
          
          <div class="detail-item">
            <div class="item-label">简要案情</div>
            <div class="item-content">{{ form.problem || '暂无' }}</div>
          </div>
          
          <div class="detail-item">
            <div class="item-label">备注</div>
            <div class="item-content">{{ form.remark || '暂无' }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getPunish } from "@/api/scene/punish";

export default {
  name: "ScenePunishDetail",
  dicts: ['si_punish_status'],
  data() {
    return {
      loading: false,
      punishId: undefined,
      // 表单参数
      form: {},
      // 模块折叠状态
      sectionCollapsed: {
        basic: false
      }
    };
  },
  created() {
    this.punishId = this.$route.params && this.$route.params.id;
    console.log('Punish detail punishId:', this.punishId);

    // 容错处理：即使ID无效也要初始化页面
    if (!this.punishId || this.punishId === 'undefined' || this.punishId === 'null') {
      console.warn('行政处罚详情页面：接收到无效的ID参数', this.punishId);
      this.$message.warning('未找到有效的处罚记录ID，将显示空白详情页面');
      // 设置空的表单数据
      this.form = {};
    } else {
      this.getDetail();
    }
  },
  methods: {
    /** 获取详情 */
    getDetail() {
      if (!this.punishId) {
        console.warn('行政处罚详情：缺少punishId参数');
        this.form = {};
        return;
      }

      this.loading = true;
      getPunish(this.punishId).then(response => {
        console.log('Punish detail API Response:', response);

        if (response.code === 200 && response.data) {
          this.form = response.data;
          console.log('Punish detail form data:', this.form);
        } else {
          console.warn('行政处罚详情：API返回空数据或错误', response);
          this.form = {};
          this.$message.warning('未找到对应的处罚记录，请检查ID是否正确');
        }
        this.loading = false;
      }).catch(error => {
        console.error('Punish detail API Error:', error);
        this.loading = false;

        // 容错处理：API调用失败时显示友好提示，但不跳转到404
        this.form = {};
        this.$message.error('获取处罚记录详情失败：' + (error.message || '网络错误'));
      });
    },
    /** 切换模块折叠状态 */
    toggleSection(section) {
      this.sectionCollapsed[section] = !this.sectionCollapsed[section];
    },
    /** 编辑按钮 */
    handleEdit() {
      if (!this.punishId || this.punishId === 'undefined' || this.punishId === 'null') {
        this.$message.warning('无法编辑：缺少有效的处罚记录ID');
        return;
      }
      this.$router.push("/data-management/scene/punish/edit/" + this.punishId);
    },
    /** 返回按钮 */
    goBack() {
      this.$router.push('/data-management/scene');
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .page-header {
    width: 94%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      justify-content: space-between;

      .page-title {
        margin: 0;
        font-size: 18px;
        color: #303133;
        font-weight: 600;
      }

      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
  }

  .detail-content {
    width: 94%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;

    .empty-state {
      padding: 40px 20px;
      text-align: center;
    }
  }

  .detail-section {
    margin-bottom: 24px;

    .section-header {
      background-color: #f6f7fb;
      padding: 12px 16px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 4px;
      margin-bottom: 16px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-line {
          width: 3px;
          height: 16px;
          background-color: #4584FF;
        }

        .title-text {
          font-size: 16px;
          font-weight: 600;
          color: #4584FF;
        }
      }

      .section-toggle {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #4584FF;
        font-size: 14px;

        .toggle-text {
          font-size: 14px;
        }

        i {
          font-size: 12px;
          transition: transform 0.3s;
        }
      }

      &:hover {
        background-color: #f0f2f5;
      }
    }

    .section-content {
      padding: 0 16px;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.detail-item {
  margin-top: 20px;
  
  .item-label {
    font-weight: 500;
    color: #666;
    margin-bottom: 8px;
    font-size: 14px;
  }
  
  .item-content {
    color: #333;
    line-height: 1.6;
    padding: 12px;
    background-color: #f9f9f9;
    border-radius: 4px;
    min-height: 40px;
  }
}

::v-deep .el-descriptions__label {
  font-weight: 500;
  color: #666;
}

::v-deep .el-descriptions__content {
  color: #333;
}
</style>
