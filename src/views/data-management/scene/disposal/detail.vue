<template>
  <div class="app-container">
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">行政处置详情</h2>
        <div class="header-actions">
          <el-button type="primary" size="small" @click="handleEdit">编辑</el-button>
          <el-button icon="el-icon-arrow-left" size="small" @click="goBack">返回</el-button>
        </div>
      </div>
    </div>

    <div class="detail-content">
      <!-- 基础信息 -->
      <div class="detail-section">
        <div class="section-header" @click="toggleSection('basic')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">基础信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.basic ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.basic ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.basic" class="section-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="单位名称">
              {{ form.corporateName }}
            </el-descriptions-item>
            <el-descriptions-item label="统一社会信用代码">
              {{ form.creditCode }}
            </el-descriptions-item>
            <el-descriptions-item label="日期">
              {{ form.disposalDate }}
            </el-descriptions-item>
            <el-descriptions-item label="是否本辖区">
              <dict-tag :options="dict.type.sys_yes_no" :value="form.jurisdictionFlag"/>
            </el-descriptions-item>
            <el-descriptions-item label="网站/系统">
              {{ form.platform }}
            </el-descriptions-item>
            <el-descriptions-item label="网站地址">
              {{ form.platformUrl }}
            </el-descriptions-item>
            <el-descriptions-item label="联系人">
              {{ form.contactName }}
            </el-descriptions-item>
            <el-descriptions-item label="电话">
              {{ form.contactPhone }}
            </el-descriptions-item>
            <el-descriptions-item label="邮箱">
              {{ form.contactEmail }}
            </el-descriptions-item>
            <el-descriptions-item label="核查情况">
              <dict-tag :options="dict.type.si_disposal_status" :value="form.disposalStatus"/>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ parseTime(form.createTime) }}
            </el-descriptions-item>
          </el-descriptions>
          
          <div class="detail-item">
            <div class="item-label">地址</div>
            <div class="item-content">{{ form.address || '暂无' }}</div>
          </div>
          
          <div class="detail-item">
            <div class="item-label">通报问题</div>
            <div class="item-content">{{ form.problem || '暂无' }}</div>
          </div>
          
          <div class="detail-item" v-if="form.reportUrl">
            <div class="item-label">整改报告</div>
            <div class="item-content">
              <el-link type="primary" :href="form.reportUrl" target="_blank">
                <i class="el-icon-download"></i> 下载整改报告
              </el-link>
            </div>
          </div>
          
          <div class="detail-item">
            <div class="item-label">备注</div>
            <div class="item-content">{{ form.remark || '暂无' }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getDisposal } from "@/api/scene/disposal";

export default {
  name: "SceneDisposalDetail",
  dicts: ['si_disposal_status', 'sys_yes_no'],
  data() {
    return {
      loading: false,
      disposalId: undefined,
      // 表单参数
      form: {},
      // 模块折叠状态
      sectionCollapsed: {
        basic: false
      }
    };
  },
  created() {
    this.disposalId = this.$route.params && this.$route.params.id;
    console.log('Disposal detail disposalId:', this.disposalId);
    this.getDetail();
  },
  methods: {
    /** 获取详情 */
    getDetail() {
      if (!this.disposalId) {
        return;
      }
      this.loading = true;
      getDisposal(this.disposalId).then(response => {
        console.log('Disposal detail API Response:', response);
        if (response.code === 200 && response.data) {
          this.form = response.data;
          console.log('Disposal detail form data:', this.form);
        } else {
          this.form = {};
        }
        this.loading = false;
      }).catch(error => {
        console.error('Disposal detail API Error:', error);
        this.loading = false;
      });
    },
    /** 切换模块折叠状态 */
    toggleSection(section) {
      this.sectionCollapsed[section] = !this.sectionCollapsed[section];
    },
    /** 编辑按钮 */
    handleEdit() {
      this.$router.push("/data-management/scene/disposal/edit/" + this.disposalId);
    },
    /** 返回按钮 */
    goBack() {
      this.$router.push('/data-management/scene');
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .page-header {
    width: 94%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      justify-content: space-between;

      .page-title {
        margin: 0;
        font-size: 18px;
        color: #303133;
        font-weight: 600;
      }

      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
  }

  .detail-content {
    width: 94%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
  }

  .detail-section {
    margin-bottom: 24px;

    .section-header {
      background-color: #f6f7fb;
      padding: 12px 16px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 4px;
      margin-bottom: 16px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-line {
          width: 3px;
          height: 16px;
          background-color: #4584FF;
        }

        .title-text {
          font-size: 16px;
          font-weight: 600;
          color: #4584FF;
        }
      }

      .section-toggle {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #4584FF;
        font-size: 14px;

        .toggle-text {
          font-size: 14px;
        }

        i {
          font-size: 12px;
          transition: transform 0.3s;
        }
      }

      &:hover {
        background-color: #f0f2f5;
      }
    }

    .section-content {
      padding: 0 16px;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.detail-item {
  margin-top: 20px;
  
  .item-label {
    font-weight: 500;
    color: #666;
    margin-bottom: 8px;
    font-size: 14px;
  }
  
  .item-content {
    color: #333;
    line-height: 1.6;
    padding: 12px;
    background-color: #f9f9f9;
    border-radius: 4px;
    min-height: 40px;
  }
}

::v-deep .el-descriptions__label {
  font-weight: 500;
  color: #666;
}

::v-deep .el-descriptions__content {
  color: #333;
}
</style>
