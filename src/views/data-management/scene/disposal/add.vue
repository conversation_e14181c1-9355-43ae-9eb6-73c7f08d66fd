<template>
  <div class="app-container">
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">{{ form.disposalId ? '编辑' : '新增' }}行政处置</h2>
        <el-button icon="el-icon-arrow-left" size="small" @click="goBack">返回</el-button>
      </div>
    </div>
    <section class="form-container">

    <el-form ref="form" :model="form" :rules="rules" label-width="200px">
      <!-- 基础信息 -->
      <div class="form-section">
        <div class="section-header" @click="toggleSection('basic')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">基础信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.basic ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.basic ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.basic" class="section-content">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="单位名称" prop="corporateName">
                <el-autocomplete
                  style="width: 100%;"
                  v-model="form.corporateName"
                  :fetch-suggestions="queryEnterpriseByName"
                  placeholder="请输入单位名称"
                  @select="handleSelectEnterprise"
                >
                  <template slot-scope="{ item }" style="width: 100%;">
                    <span>{{ item.name }}</span>
                  </template>
                </el-autocomplete>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="统一社会信用代码" prop="creditCode">
                <el-autocomplete
                  style="width: 100%;"
                  v-model="form.creditCode"
                  :fetch-suggestions="queryEnterpriseByCode"
                  placeholder="请输入统一社会信用代码"
                  @select="handleSelectEnterprise"
                >
                  <template slot-scope="{ item }" style="width: 100%;">
                    <span>{{ item.creditCode }}</span>
                  </template>
                </el-autocomplete>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="日期" prop="disposalDate">
                <el-date-picker clearable
                                style="width: 100%"
                                v-model="form.disposalDate"
                                type="date"
                                value-format="yyyy-MM-dd"
                                placeholder="请选择日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="是否本辖区" prop="jurisdictionFlag">
                <el-select v-model="form.jurisdictionFlag" style="width: 100%" placeholder="请选择是否本辖区">
                  <el-option
                    v-for="dict in dict.type.sys_yes_no"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="网站/系统" prop="platform">
                <el-input v-model="form.platform" placeholder="请输入网站/系统" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="网站地址" prop="platformUrl">
                <el-input v-model="form.platformUrl" placeholder="请输入网站地址" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="联系人" prop="contactName">
                <el-input v-model="form.contactName" placeholder="请输入联系人" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="电话" prop="contactPhone">
                <el-input v-model="form.contactPhone" placeholder="请输入电话" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="邮箱" prop="contactEmail">
                <el-input v-model="form.contactEmail" placeholder="请输入邮箱" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="核查情况" prop="disposalStatus">
                <el-select v-model="form.disposalStatus" style="width: 100%" placeholder="请选择核查情况">
                  <el-option
                    v-for="dict in dict.type.si_disposal_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="地址" prop="address">
                <el-input type="textarea" v-model="form.address" rows="2" placeholder="请输入地址" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="通报问题" prop="problem">
                <el-input type="textarea" v-model="form.problem" rows="4" placeholder="请输入通报问题" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="整改报告" prop="reportUrl">
                <file-upload v-model="form.reportUrl" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" type="textarea" rows="3" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="form-footer">
        <el-button type="primary" @click="submitForm" :loading="submitting">确 定</el-button>
        <el-button @click="goBack">取 消</el-button>
      </div>

    </el-form>
    </section>
  </div>
</template>

<script>
import { getDisposal, addDisposal, updateDisposal } from "@/api/scene/disposal";
import { getEnterpriseList } from "@/api/enterprise/erp";

export default {
  name: "SceneDisposalAdd",
  dicts: ['si_disposal_status', 'sys_yes_no'],
  data() {
    return {
      loading: false,
      submitting: false,
      disposalId: undefined,
      // 表单参数
      form: {},
      // 模块折叠状态
      sectionCollapsed: {
        basic: false
      },
      // 表单校验
      rules: {
        corporateName: [
          { required: true, message: "单位名称不能为空", trigger: "blur" }
        ],
        creditCode: [
          { required: true, message: "统一社会信用代码不能为空", trigger: "blur" }
        ],
        disposalDate: [
          { required: true, message: "日期不能为空", trigger: "blur" }
        ],
        problem: [
          { required: true, message: "通报问题不能为空", trigger: "blur" }
        ],
        platform: [
          { required: true, message: "网站/系统不能为空", trigger: "blur" }
        ],
        jurisdictionFlag: [
          { required: true, message: "是否本辖区不能为空", trigger: "change" }
        ],
        address: [
          { required: true, message: "地址不能为空", trigger: "blur" }
        ],
        contactName: [
          { required: true, message: "联系人不能为空", trigger: "blur" }
        ],
        contactPhone: [
          { required: true, message: "电话不能为空", trigger: "blur" }
        ],
        disposalStatus: [
          { required: true, message: "核查情况不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.disposalId = this.$route.params && this.$route.params.id;
    this.getDetail();
  },
  methods: {
    // 表单重置
    reset() {
      this.form = {
        id: null,
        creditCode: null,
        corporateName: null,
        disposalDate: null,
        problem: null,
        platform: null,
        platformUrl: null,
        jurisdictionFlag: null,
        address: null,
        contactName: null,
        contactPhone: null,
        contactEmail: null,
        disposalStatus: null,
        reportUrl: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 获取详情 */
    getDetail() {
      this.reset();
      if (!this.disposalId) {
        return;
      }
      this.loading = true;
      getDisposal(this.disposalId).then(response => {
        this.form = response.data;
        this.loading = false;
      });
    },
    /** 切换模块折叠状态 */
    toggleSection(section) {
      this.sectionCollapsed[section] = !this.sectionCollapsed[section];
    },
    /** 提交表单 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.submitting = true;
          if (this.form.disposalId!= null) {
            updateDisposal(this.form).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.goBack();
            }).finally(() => {
              this.submitting = false;
            });
          } else {
            addDisposal(this.form).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.goBack();
            }).finally(() => {
              this.submitting = false;
            });
          }
        }
      });
    },
    /** 返回按钮 */
    goBack() {
      this.$router.push('/data-management/scene');
    },
    // 企业名称自动完成
    queryEnterpriseByName(queryString, cb) {
      if (!queryString) {
        cb([]);
        return;
      }
      getEnterpriseList({ name: queryString }).then(response => {
        cb(response.rows || []);
      });
    },
    // 企业代码自动完成
    queryEnterpriseByCode(queryString, cb) {
      if (!queryString) {
        cb([]);
        return;
      }
      getEnterpriseList({ creditCode: queryString }).then(response => {
        cb(response.rows || []);
      });
    },
    // 选择企业
    handleSelectEnterprise(item) {
      this.form.creditCode = item.creditCode;
      this.form.corporateName = item.name;
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .page-header {
    width: 94%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      justify-content: space-between;

      .page-title {
        margin: 0;
        font-size: 18px;
        color: #303133;
        font-weight: 600;
      }
    }
  }

  .form-container {
    width: 94%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
  }

  .form-section {
    margin-bottom: 24px;

    .section-header {
      background-color: #f6f7fb;
      padding: 12px 16px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 4px;
      margin-bottom: 16px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-line {
          width: 3px;
          height: 16px;
          background-color: #4584FF;
        }

        .title-text {
          font-size: 16px;
          font-weight: 600;
          color: #4584FF;
        }
      }

      .section-toggle {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #4584FF;
        font-size: 14px;

        .toggle-text {
          font-size: 14px;
        }

        i {
          font-size: 12px;
          transition: transform 0.3s;
        }
      }

      &:hover {
        background-color: #f0f2f5;
      }
    }

    .section-content {
      padding: 0 16px;
    }
  }
}

// 全局样式：确保表单标签不换行
::v-deep .el-form-item__label {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.form-footer {
  text-align: right;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}
</style>
