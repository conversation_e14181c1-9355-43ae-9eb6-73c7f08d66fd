<template>
  <div class="punish-component">
    <!-- 搜索区域 -->
    <common-search
      :search-config="searchConfig"
      :advanced-fields="advancedFields"
      :initial-form="searchForm"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['scene:punish:add']"
          >新增</el-button>
        </el-col>
        <!-- <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['scene:punish:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['scene:punish:remove']"
          >删除</el-button>
        </el-col> -->
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-upload2"
            size="mini"
            @click="handleImport"
            v-hasPermi="['scene:punish:import']"
          >导入</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['scene:punish:export']"
          >导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="punishList"
        @selection-change="handleSelectionChange"
        border
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="统一社会信用代码" align="center" prop="creditCode" />
        <el-table-column label="公司名称" align="center" prop="corporateName" />
        <el-table-column label="处罚决定书编号" align="center" prop="punishNum" />
        <el-table-column label="简要案情" align="left" :show-overflow-tooltip="true" prop="problem">
        <template slot-scope="scope" >
          <span class="text-line-three">{{ scope.row.problem }}</span>
        </template>
      </el-table-column>
        <el-table-column label="处罚结果" align="center" prop="punishStatus">
          <template slot-scope="scope">
            <dict-tag :options="(dict && dict.type && dict.type.si_punish_status) || []" :value="scope.row.punishStatus"/>
          </template>
        </el-table-column>
        <el-table-column label="处罚日期" align="center" prop="punishDate" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleDetail(scope.row)"
              v-hasPermi="['scene:punish:query']"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['scene:punish:edit']"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['scene:punish:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xlsx格式文件。</span>
          <el-link type="danger" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPunish, delPunish } from "@/api/scene/punish";
import CommonSearch from '@/components/CommonSearch'
import { getToken } from "@/utils/auth";
export default {
  name: "PunishComponent",
  components: { CommonSearch },
  props: {
    dict: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 行政处罚表格数据
      punishList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        creditCode: null,
        corporateName: null,
        punishNum: null,
        punishStatus: null,
      },
      // 搜索配置
      searchConfig: {
        label: '公司名称',
        key: 'corporateName',
        placeholder: '请输入公司名称'
      },
      // 高级搜索字段
      advancedFields: [],
      // 搜索表单
      searchForm: {},
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      
      // 导入参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        previewUrl: process.env.VUE_APP_BASE_API + "/file/download/",
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/scene/punish/importData"
      },
      // 导入日志
      importLog: ""
    };
  },
  created() {
    this.getList();
    this.initAdvancedFields();
  },
  watch: {
    // 监听字典数据变化
    'dict.type.si_punish_status': {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.initAdvancedFields();
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 初始化高级搜索字段
    initAdvancedFields() {
      console.log('PunishComponent - 初始化高级搜索字段');
      console.log('PunishComponent - dict:', this.dict);
      console.log('PunishComponent - si_punish_status:', this.dict && this.dict.type && this.dict.type.si_punish_status);

      this.advancedFields = [
        {
          label: '统一社会信用代码',
          key: 'creditCode',
          type: 'input',
          placeholder: '请输入统一社会信用代码',
          span: 6
        },
        {
          label: '处罚决定书编号',
          key: 'punishNum',
          type: 'input',
          placeholder: '请输入处罚决定书编号',
          span: 6
        },
        {
          label: '处罚结果',
          key: 'punishStatus',
          type: 'select',
          placeholder: '请选择处罚结果',
          span: 6,
          options: (this.dict && this.dict.type && this.dict.type.si_punish_status) ? this.dict.type.si_punish_status.map(item => ({
            label: item.label,
            value: item.value
          })) : []
        },
        {
          label: '日期',
          key: 'disposalDateRange',
          type: 'daterange',
          placeholder: ['开始日期', '结束日期'],
          span: 6,
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd'
        }
      ];

      console.log('PunishComponent - 处罚结果选项:', this.advancedFields[2].options);
    },

    // 处理搜索
    handleSearch(searchForm) {
      this.queryParams = {
        ...this.queryParams,
        ...searchForm,
        pageNum: 1
      };

      // 确保params对象存在
      if (!this.queryParams.params) {
        this.queryParams.params = {};
      }

      // 处理日期范围参数，放在params中
      if (searchForm.disposalDateRange && searchForm.disposalDateRange.length === 2) {
        this.queryParams.params.beginPunishDate = searchForm.disposalDateRange[0];
        this.queryParams.params.endPunishDate = searchForm.disposalDateRange[1];
        delete this.queryParams.disposalDateRange;
      } else {
        this.queryParams.params.beginPunishDate = null;
        this.queryParams.params.endPunishDate = null;
      }

      this.getList();
    },

    // 处理重置
    handleReset(searchForm) {
      this.searchForm = searchForm;
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        creditCode: null,
        corporateName: null,
        punishNum: null,
        punishStatus: null,
        ...searchForm
      };
      this.getList();
    },

    /** 查询行政处罚列表 */
    getList() {
      this.loading = true;
      listPunish(this.queryParams).then(response => {
        this.punishList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.punishId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
     this.$router.push("/data-management/scene/punish/add");
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      const id = row.punishId || row.id || '';
      this.$router.push("/data-management/scene/punish/detail/" + id);
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const id = row.punishId || this.ids[0];
      this.$router.push("/data-management/scene/punish/edit/" + id);
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.punishId || this.ids;
      this.$modal.confirm('是否确认删除行政处罚编号为"' + ids + '"的数据项？').then(function() {
        return delPunish(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
   /** 导出按钮操作 */
    handleExport() {
      this.download('scene/punish/export', {
        ...this.queryParams
      }, `行政处罚_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "数据导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    async importTemplate() {
      const url = process.env.VUE_APP_BASE_API+'/file/download/'+vueConfig.template_path_punish; // 替换成你要下载的文件的URL
      const fileName = '行政处罚导入模板.xlsx'; // 自定义文件名

      const response = await fetch(url);
      const blob = await response.blob();
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      let that = this;
      that.loading = false;
      that.upload.open = false;
      that.upload.isUploading = false;
      that.$refs.upload.clearFiles();
      that.importLog = response.msg;

      that.$confirm(
        "<div style=' width: 700px; overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"
        + response.msg
        + "</div>",
        '导入结果',
        {
          confirmButtonText: '下载导入日志',
          cancelButtonText: '取消',
          // type: 'info',
          dangerouslyUseHTMLString: true,
          showCancelButton: true,  //是否显示取消按钮
          showClose: false, //是否显示右上角的x
          closeOnClickModal: false, //是否可以点击空白处关闭弹窗
        })
        .then(() => {
          if(that.importLog){
            that.downloadImportLog('log.txt', that.importLog)
          }else{
            that.$modal.msgError("暂无日志");
          }
        })
        .catch(() => {

        });

      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.upload.open = false;
      this.$refs.upload.submit();
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.loading = true;
      this.upload.isUploading = true;
    },
    downloadImportLog(fileName, data){
      //创建一个a链接，用于触发下载事件的载体
      let aLink = document.createElement('a')
      //将实参字符串转二进制对象，如果不是文本可以通过添加第二个参数指定编码
      let blob = new Blob([this.htmlToString(data)]);
      //指定要下载的文件名(浏览器下载时，会根据文件后缀名指定解码)
      aLink.download = fileName
      //给a链接配置href指向刚才的二进制对象
      aLink.href = URL.createObjectURL(blob)
      //触发事件
      aLink.click()
    },
    htmlToString(html) {
      html = html.replaceAll('<br/>','\n');
      return html;
    },
  }
};
</script>

<style lang="scss" scoped>
.punish-component {
  .action-bar {
    background: white;
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
  }

  .table-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
  }
}
</style>
