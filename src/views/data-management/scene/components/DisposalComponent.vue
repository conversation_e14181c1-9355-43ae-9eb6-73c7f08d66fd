<template>
  <div class="disposal-component">
    <!-- 搜索区域 -->
    <common-search
      :search-config="searchConfig"
      :advanced-fields="advancedFields"
      :initial-form="searchForm"
      @search="handleSearch"
    @reset="handleReset"
    />

    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['scene:disposal:add']"
          >新增</el-button>
        </el-col>
        <!-- <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['scene:disposal:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['scene:disposal:remove']"
          >删除</el-button>
        </el-col> -->
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-upload2"
            size="mini"
            @click="handleImport"
            v-hasPermi="['scene:disposal:import']"
          >导入</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['scene:disposal:export']"
          >导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="disposalList"
        @selection-change="handleSelectionChange"
        border
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column label="统一社会信用代码" align="center" prop="creditCode" />
        <el-table-column label="公司名称" align="center" prop="corporateName" /> -->
        <el-table-column label="公司名称" align="center" :show-overflow-tooltip="true" prop="corporateName" width="180">
          <template slot-scope="scope">
            <span>{{ scope.row.creditCode }}</span>
            <br/>
            <span>{{ scope.row.corporateName }}</span>
          </template>
      </el-table-column>
       <el-table-column label="日期" align="center" prop="disposalDate" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.disposalDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
         <el-table-column label="通报问题" align="left" :show-overflow-tooltip="true" prop="problem">
        <template slot-scope="scope" >
          <span class="text-line-three">{{ scope.row.problem }}</span>
        </template>
      </el-table-column>
        <el-table-column label="网站/系统" align="center" prop="platform" />
        <el-table-column label="核查情况" align="center" width="120" prop="disposalStatus">
          <template slot-scope="scope">
            <dict-tag :options="(dict && dict.type && dict.type.si_disposal_status) || []" :value="scope.row.disposalStatus"/>
          </template>
        </el-table-column>
        <el-table-column label="网站地址" align="center" prop="platformUrl"  width="200"/>
      <el-table-column label="是否本辖区" align="center" prop="jurisdictionFlag" width="90">
        <template slot-scope="scope">
          <dict-tag :options="(dict && dict.type && dict.type.sys_yes_no) || []" :value="scope.row.jurisdictionFlag"/>
        </template>
      </el-table-column>
      <el-table-column label="地址" align="left" :show-overflow-tooltip="true" prop="problem">
        <template slot-scope="scope" >
          <span class="text-line-three">{{ scope.row.address }}</span>
        </template>
      </el-table-column>
      <el-table-column label="联系人" align="center" prop="contactName"  width="120"/>
      <el-table-column label="电话" align="center" prop="contactPhone"  width="120"/>
      <el-table-column label="邮箱" align="center" prop="contactEmail"  width="200"/>
        <el-table-column label="核查日期" align="center" prop="disposalDate" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleDetail(scope.row)"
              v-hasPermi="['scene:disposal:query']"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['scene:disposal:edit']"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['scene:disposal:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".zip"
        :headers="upload.headers"
        :action="upload.url + '?previewUrl=' + upload.previewUrl"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xlsx格式文件。</span>
          <el-link type="danger" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDisposal, delDisposal } from "@/api/scene/disposal";
import CommonSearch from '@/components/CommonSearch'
import { getToken } from "@/utils/auth";
export default {
  name: "DisposalComponent",
  components: { CommonSearch },
  props: {
    dict: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 行政处置表格数据
      disposalList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        creditCode: null,
        corporateName: null,
        platform: null,
        disposalStatus: null,
      },
      // 搜索配置
      searchConfig: {
        label: '公司名称',
        key: 'corporateName',
        placeholder: '请输入公司名称'
      },
      // 高级搜索字段
      advancedFields: [],
      // 搜索表单
      searchForm: {},
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 导入参数
       // 导入参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        previewUrl: process.env.VUE_APP_BASE_API + "/file/download/",
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/scene/disposal/importData"
      },
      // 导入日志
      importLog: ""
    };
  },
  created() {
    this.getList();
    this.initAdvancedFields();
  },
  watch: {
    // 监听字典数据变化
    'dict.type.si_disposal_status': {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.initAdvancedFields();
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 初始化高级搜索字段
    initAdvancedFields() {
      console.log('DisposalComponent - 初始化高级搜索字段');
      console.log('DisposalComponent - dict:', this.dict);
      console.log('DisposalComponent - si_disposal_status:', this.dict && this.dict.type && this.dict.type.si_disposal_status);

      this.advancedFields = [
        {
          label: '统一社会信用代码',
          key: 'creditCode',
          type: 'input',
          placeholder: '请输入统一社会信用代码',
          span: 6
        },
        {
          label: '网站/系统',
          key: 'platform',
          type: 'input',
          placeholder: '请输入网站/系统',
          span: 6
        },
        {
          label: '核查情况',
          key: 'disposalStatus',
          type: 'select',
          placeholder: '请选择核查情况',
          span: 6,
          options: (this.dict && this.dict.type && this.dict.type.si_disposal_status) ? this.dict.type.si_disposal_status.map(item => ({
            label: item.label,
            value: item.value
          })) : []
        },
        {
          label: '处罚决定书时间',
          key: 'punishDateRange',
          type: 'daterange',
          placeholder: ['开始日期', '结束日期'],
          span: 6,
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd'
        }
      ];

      console.log('DisposalComponent - 核查情况选项:', this.advancedFields[2].options);
    },

    // 处理搜索
    handleSearch(searchForm) {
      // 分离日期范围字段
      const { punishDateRange, ...otherFields } = searchForm;

      this.queryParams = {
        ...this.queryParams,
        ...otherFields,
        pageNum: 1
      };

      // 确保params对象存在
      if (!this.queryParams.params) {
        this.queryParams.params = {};
      }

      // 处理日期范围参数，放在params中
      if (punishDateRange && punishDateRange.length === 2) {
        this.queryParams.params.beginDisposalDate = punishDateRange[0];
        this.queryParams.params.endDisposalDate = punishDateRange[1];
      } else {
        this.queryParams.params.beginDisposalDate = null;
        this.queryParams.params.endDisposalDate = null;
      }

      this.getList();
    },

    // 处理重置
    handleReset(searchForm) {
      this.searchForm = searchForm;
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        creditCode: null,
        corporateName: null,
        platform: null,
        disposalStatus: null,
        ...searchForm
      };
      this.getList();
    },

    /** 查询行政处置列表 */
    getList() {
      this.loading = true;
      listDisposal(this.queryParams).then(response => {
        this.disposalList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.disposalId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push("/data-management/scene/disposal/add");
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      const id = row.disposalId;
      this.$router.push("/data-management/scene/disposal/detail/" + id);
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const id = row.disposalId;
      this.$router.push("/data-management/scene/disposal/edit/" + id);
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.disposalId || this.ids;
      this.$modal.confirm('是否确认删除行政处置编号为"' + ids + '"的数据项？').then(function() {
        return delDisposal(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('scene/disposal/export', {
        ...this.queryParams
      }, `disposal_${new Date().getTime()}.xlsx`)
    },

    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "数据导入";
      this.upload.open = true;
    },

    /** 下载模板操作 */
   /** 下载模板操作 */
    async importTemplate() {
      const url = process.env.VUE_APP_BASE_API+'/file/download/'+vueConfig.template_path_disposal; // 替换成你要下载的文件的URL
      const fileName = '行政处置导入模板.xlsx'; // 自定义文件名

      const response = await fetch(url);
      const blob = await response.blob();
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.importLog = response.msg;

      this.$confirm(
        `<div style='width: 700px; overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>${response.msg}</div>`,
        '导入结果',
        {
          confirmButtonText: '下载导入日志',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          showCancelButton: true,
          showClose: false,
          closeOnClickModal: false,
        }
      ).then(() => {
        if (this.importLog) {
          this.downloadImportLog('log.txt', this.importLog);
        } else {
          this.$modal.msgError("暂无日志");
        }
      }).catch(() => {});

      this.getList();
    },

    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },

    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },

    // 下载导入日志
    downloadImportLog(fileName, data) {
      const aLink = document.createElement('a');
      const blob = new Blob([data]);
      aLink.href = window.URL.createObjectURL(blob);
      aLink.download = fileName;
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
    }
  }
};
</script>

<style lang="scss" scoped>
.disposal-component {
  .action-bar {
    background: white;
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
  }

  .table-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
  }
}
</style>
