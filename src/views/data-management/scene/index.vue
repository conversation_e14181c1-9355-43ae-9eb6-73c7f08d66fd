<template>
  <div class="app-container">
    <div class="screen-management-layout">
      <!-- 左侧导航 -->
      <!-- <basic-data-sidebar /> -->

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <!-- Tab切换区域 -->
        <div class="tab-container">
          <el-tabs v-model="activeTab" @tab-click="handleTabClick">
            <el-tab-pane label="行政处罚" name="punish">
              <punish-component :dict="dict" />
            </el-tab-pane>
            <el-tab-pane label="行政处置" name="disposal">
              <disposal-component :dict="dict" />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>

    <!-- 数据更新提醒组件 -->
    <data-update-reminder
      :visible.sync="reminderVisible"
      :biz-type="'SCENE'"
    />
  </div>
</template>

<script>
// import BasicDataSidebar from '@/components/BasicDataSidebar'
import PunishComponent from './components/PunishComponent'
import DisposalComponent from './components/DisposalComponent'
import DataUpdateReminder from '@/components/DataUpdateReminder'

export default {
  name: "SceneIndex",
  dicts: ['si_punish_status', 'si_disposal_status'],
  components: {
    // BasicDataSidebar,
    PunishComponent,
    DisposalComponent,
    DataUpdateReminder
  },
  data() {
    return {
      // 当前激活的tab
      activeTab: 'punish',
      // 数据更新提醒弹窗显示状态
      reminderVisible: false,
    };
  },
  methods: {
    // 处理tab切换
    handleTabClick(tab) {
      this.activeTab = tab.name;
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/screen-management.scss';

.tab-container {
  padding: 20px;
  margin-bottom: 20px;

  ::v-deep .el-tabs {
    .el-tabs__header {
      background: transparent;
        margin: 0px;
      .el-tabs__nav-wrap {
        &::after {
          display: none;
        }
      }

      .el-tabs__nav {
        border: none;

        .el-tabs__item {
             font-size: 18px;
                font-weight: 500;
                width: 140px;
                text-align: center;
            
                padding: 0px;
                height: 46px;
                line-height: 46px;
                border-inline-end-width: 140px;
                border: none;
                background-color: #e9f0ff;
                color: #44515b;
                transition: all .3s ease;

          &.is-active {
            background-color: #4584FF;
            color: white;
            font-weight: 600;
          }

          &:hover {
            background-color: #4584FF;
            color: white;
          }
        }
      }

      .el-tabs__active-bar {
        display: none;
      }
    }

    .el-tabs__content {
      .el-tab-pane {
        padding: 0;
      }
    }
  }
}
</style>