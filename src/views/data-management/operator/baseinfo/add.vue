<template>
  <div class="app-container">
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">{{ form.id ? '编辑' : '新增' }}运营商基础信息</h2>
        <el-button icon="el-icon-arrow-left" size="small" @click="goBack">返回</el-button>
      </div>
    </div>
    <section class="form-container">

    <el-form ref="form" :model="form" :rules="rules" label-width="200px">
      <!-- 基础信息 -->
      <div class="form-section">
        <div class="section-header" @click="toggleSection('basic')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">基础信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.basic ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.basic ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.basic" class="section-content">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="统一社会信用代码" prop="creditCode">
                <el-input v-model="form.creditCode" placeholder="请输入统一社会信用代码" />
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item label="企业名称" prop="enterpriseName">
                <el-input v-model="form.enterpriseName" placeholder="请输入企业名称" />
              </el-form-item>
            </el-col> -->
          </el-row>
        </div>
      </div>

      <!-- 增值电信业务许可信息 -->
      <div class="form-section">
        <div class="section-header" @click="toggleSection('license')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">增值电信业务许可信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.license ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.license ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.license" class="section-content">
          <div v-for="(item, index) in form.licenseList" :key="'license.'+index" class="license-item">
            <div class="license-header">
              <div class="license-number">{{ index + 1 }}</div>
              <div class="license-actions">
                <el-button
                  icon="el-icon-plus"
                  @click="handleLicense('add', item, index)"
                  type="text"
                  size="small"
                >增加许可</el-button>
                <el-button
                  v-if="index != 0"
                  icon="el-icon-minus"
                  @click="handleLicense('delete', item, index)"
                  type="text"
                  size="small"
                >删除</el-button>
              </div>
            </div>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item
                  label="经营许可证编号"
                  :prop="'licenseList.'+index +'.licenceCode'"
                  :rules="{ required: true, message: '请输入经营许可证编号', trigger: 'blur' }"
                >
                  <el-input v-model="item.licenceCode" placeholder="请输入经营许可证编号" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="发证日期"
                  :prop="'licenseList.'+index +'.publishTime'"
                  :rules="{ required: true, message: '请输入发证日期', trigger: 'blur' }"
                >
                  <el-date-picker
                    v-model="item.publishTime"
                    type="date"
                    placeholder="发证日期"
                    style="width: 100%">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="有效期至"
                  :prop="'licenseList.'+index + '.expirationTime'"
                  :rules="{ required: true, message: '请输入有效期至', trigger: 'blur' }"
                >
                  <el-date-picker
                    v-model="item.expirationTime"
                    type="date"
                    placeholder="有效期至"
                    style="width: 100%">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 业务种类 -->
            <div v-for="(itemBT, indexBT) in item.buzTypeList" :key="'buzType.' + indexBT" class="business-type-item">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item
                    label="业务种类"
                    :prop="'licenseList['+ index +'].buzTypeList.'+ indexBT +'.businessType'"
                    :rules="rules.businessType"
                  >
                    <el-input v-model="itemBT.businessType" placeholder="请输入业务种类" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item
                    label="服务项目"
                    :prop="'licenseList['+ index +'].buzTypeList.'+ indexBT +'.serviceItems'"
                  >
                    <el-input v-model="itemBT.serviceItems" placeholder="请输入服务项目" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item
                    label="业务覆盖范围"
                    :prop="'licenseList['+index +'].buzTypeList.' + indexBT +'.businessCoverage'"
                    :rules="rules.businessCoverage"
                  >
                    <el-input v-model="itemBT.businessCoverage" placeholder="请输入业务覆盖范围" />
                  </el-form-item>
                </el-col>
                <el-col :span="2" class="business-type-actions">
                  <el-button
                    icon="el-icon-plus"
                    @click="handleBusinessType('add', item, indexBT)"
                    type="text"
                    size="small"
                  ></el-button>
                  <el-button
                    v-if="indexBT != 0"
                    icon="el-icon-minus"
                    @click="handleBusinessType('delete', item, indexBT)"
                    type="text"
                    size="small"
                  ></el-button>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </div>

      <!-- 安全审计设备信息 -->
      <div class="form-section">
        <div class="section-header" @click="toggleSection('equipment')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">安全审计设备信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.equipment ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.equipment ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.equipment" class="section-content">
          <div v-for="(item, index) in form.safetyEquipmentList" :key="'equipment.' + index">
            <el-row :gutter="20">
              <el-col :span="10">
                <el-form-item
                  label="安全审计设备厂商"
                  :prop="'safetyEquipmentList.'+index + '.deviceProvider'"
                  :rules="rules.deviceProvider"
                >
                  <el-input v-model="item.deviceProvider" placeholder="请输入设备厂商" />
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item
                  label="安全审计设备型号"
                  :prop="'safetyEquipmentList.'+index +'.deviceModel'"
                  :rules="rules.deviceModel"
                >
                  <el-input v-model="item.deviceModel" placeholder="请输入设备型号" />
                </el-form-item>
              </el-col>
              <el-col :span="4" class="equipment-actions">
                <el-button
                  icon="el-icon-plus"
                  @click="handleEquipment('add', item, index)"
                  type="text"
                  size="small"
                ></el-button>
                <el-button
                  v-if="index != 0"
                  icon="el-icon-minus"
                  @click="handleEquipment('delete', item, index)"
                  type="text"
                  size="small"
                ></el-button>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>

      <!-- 提供服务类型 -->
      <div class="form-section">
        <div class="section-header" @click="toggleSection('service')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">提供服务类型</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.service ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.service ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.service" class="section-content">
          <el-form-item label="提供服务类型" prop="provideServiceType1">
            <el-checkbox-group v-model="form.provideServiceType1" @change="handleServiceChange">
              <el-checkbox
                v-for="dict in dict.type.service_type_operator"
                :label="dict.value"
                :key="dict.value"
              >
                {{ dict.label }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>
      </div>

      <!-- 信息安全负责人 -->
      <div class="form-section">
        <div class="section-header" @click="toggleSection('infoSecurity')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">信息安全负责人</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.infoSecurity ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.infoSecurity ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.infoSecurity" class="section-content">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="负责人姓名" prop="infoSecurityName">
                <el-input v-model="form.infoSecurityName" placeholder="请输入负责人姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="负责人职务" prop="infoSecurityPosition">
                <el-input v-model="form.infoSecurityPosition" placeholder="请输入负责人职务" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="手机号" prop="infoSecurityPhone">
                <el-input v-model="form.infoSecurityPhone" placeholder="请输入手机号" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="证件类型" prop="infoSecurityCertificateType">
                <el-select v-model="form.infoSecurityCertificateType" placeholder="请选择证件类型" clearable style="width: 100%">
                  <el-option
                    v-for="dict in dict.type.certificate_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="证件号码" prop="infoSecurityCertificateCode">
                <el-input v-model="form.infoSecurityCertificateCode" placeholder="请输入证件号码" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 网安对口信息安全部门 -->
      <div class="form-section">
        <div class="section-header" @click="toggleSection('netSecurity')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">网安对口信息安全部门</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.netSecurity ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.netSecurity ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.netSecurity" class="section-content">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="部门名称" prop="netSecurityDept">
                <el-input v-model="form.netSecurityDept" placeholder="请输入部门名称" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="负责人" prop="netSecurityName">
                <el-input v-model="form.netSecurityName" placeholder="请输入负责人" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="职务" prop="netSecurityPosition">
                <el-input v-model="form.netSecurityPosition" placeholder="请输入职务" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="手机号" prop="netSecurityPhone">
                <el-input v-model="form.netSecurityPhone" placeholder="请输入手机号" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="证件类型" prop="netSecurityCertificateType">
                <el-select v-model="form.netSecurityCertificateType" placeholder="请选择证件类型" clearable style="width: 100%">
                  <el-option
                    v-for="dict in dict.type.certificate_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="证件号码" prop="netSecurityCertificateCode">
                <el-input v-model="form.netSecurityCertificateCode" placeholder="请输入证件号码" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 网安工作对口联系人 -->
      <div class="form-section">
        <div class="section-header" @click="toggleSection('netSecurityWork')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">网安工作对口联系人</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.netSecurityWork ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.netSecurityWork ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.netSecurityWork" class="section-content">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="部门名称" prop="netSecurityWorkDept">
                <el-input v-model="form.netSecurityWorkDept" placeholder="请输入部门名称" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="联系人" prop="netSecurityWorkName">
                <el-input v-model="form.netSecurityWorkName" placeholder="请输入联系人" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="联系人职务" prop="netSecurityWorkPosition">
                <el-input v-model="form.netSecurityWorkPosition" placeholder="请输入联系人职务" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="手机号" prop="netSecurityWorkPhone">
                <el-input v-model="form.netSecurityWorkPhone" placeholder="请输入手机号" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="证件类型" prop="netSecurityWorkCertificateType">
                <el-select v-model="form.netSecurityWorkCertificateType" placeholder="请选择证件类型" clearable style="width: 100%">
                  <el-option
                    v-for="dict in dict.type.certificate_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="证件号码" prop="netSecurityWorkCertificateCode">
                <el-input v-model="form.netSecurityWorkCertificateCode" placeholder="请输入证件号码" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="证件(人像)" prop="netSecurityWorkCertificateBack">
                <image-upload v-model="form.netSecurityWorkCertificateBack" :limit="1"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="证件照(国徽)" prop="netSecurityWorkCertificateFront">
                <image-upload v-model="form.netSecurityWorkCertificateFront" :limit="1"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="证件(手持)" prop="netSecurityWorkCertificateHand">
                <image-upload v-model="form.netSecurityWorkCertificateHand" :limit="1"/>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 7*24小时值班电话 -->
      <div class="form-section">
        <div class="section-header" @click="toggleSection('duty')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">7*24小时值班电话</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.duty ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.duty ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.duty" class="section-content">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="值班电话" prop="dutyPhone">
                <el-input v-model="form.dutyPhone" placeholder="请输入值班电话" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

    </el-form>

    <!-- 操作按钮 -->
    <div class="form-footer">
      <el-button type="primary" @click="submitForm" :loading="submitting">确 定</el-button>
      <el-button @click="handleClose">取 消</el-button>
    </div>
    </section>
  </div>
</template>

<script>
import { getOperator, addOperator, updateOperator } from "@/api/operator/operator";
import ImageUpload from '@/components/ImageUpload';

export default {
  name: "OperatorBaseinfoAdd",
  dicts: ['service_type_operator', 'certificate_type'],
  components: {
    ImageUpload
  },
  data() {
    return {
      loading: false,
      submitting: false,
      creditCode: undefined,
      // 表单参数
      form: {
        licenseList: [
          {
            licenceCode: "",
            publishTime: "",
            expirationTime: "",
            buzTypeList: [{ businessType: "", serviceItems: "", businessCoverage: "" }]
          }
        ],
        safetyEquipmentList: [{ deviceProvider: "", deviceModel: "" }],
        provideServiceType1: [],
        provideServiceType: ""
      },
      // 模块折叠状态
      sectionCollapsed: {
        basic: false,
        license: false,
        equipment: false,
        service: false,
        infoSecurity: false,
        netSecurity: false,
        netSecurityWork: false,
        duty: false
      },
      // 表单校验
      rules: {
        creditCode: [
          { required: true, message: "统一社会信用代码不能为空", trigger: "blur" }
        ],
        // enterpriseName: [
        //   { required: true, message: "企业名称不能为空", trigger: "blur" }
        // ],
        deviceProvider: [
          { required: true, message: "安全审计设备厂商不能为空", trigger: "blur" }
        ],
        deviceModel: [
          { required: true, message: "安全审计设备型号不能为空", trigger: "blur" }
        ],
        businessType: [
          { required: true, message: "业务种类不能为空", trigger: "blur" }
        ],
        businessCoverage: [
          { required: true, message: "业务覆盖范围不能为空", trigger: "blur" }
        ],
        provideServiceType: [
          { required: true, message: "提供服务类型不能为空", trigger: "blur" }
        ],
        infoSecurityName: [
          { required: true, message: "信息安全负责人姓名不能为空", trigger: "blur" }
        ],
        infoSecurityPhone: [
          { required: true, message: "信息安全负责人手机号不能为空", trigger: "blur" }
        ],
        infoSecurityPosition: [
          { required: true, message: "信息安全负责人职务不能为空", trigger: "blur" }
        ],
        infoSecurityCertificateType: [
          { required: true, message: "信息安全负责人证件类型不能为空", trigger: "blur" }
        ],
        infoSecurityCertificateCode: [
          { required: true, message: "信息安全负责人证件号码不能为空", trigger: "blur" }
        ],
        netSecurityDept: [
          { required: true, message: "网安对口信息安全部门名称不能为空", trigger: "blur" }
        ],
        netSecurityName: [
          { required: true, message: "网安对口信息安全部门负责人不能为空", trigger: "blur" }
        ],
        netSecurityPosition: [
          { required: true, message: "网安对口信息安全部门负责人职务不能为空", trigger: "blur" }
        ],
        netSecurityPhone: [
          { required: true, message: "网安对口信息安全部门负责人手机号不能为空", trigger: "blur" }
        ],
        netSecurityCertificateType: [
          { required: true, message: "网安对口信息安全部门负责人证件类型不能为空", trigger: "blur" }
        ],
        netSecurityCertificateCode: [
          { required: true, message: "网安对口信息安全部门负责人证件号码不能为空", trigger: "blur" }
        ],
        netSecurityWorkDept: [
          { required: true, message: "网安工作对口部门名称不能为空", trigger: "blur" }
        ],
        netSecurityWorkName: [
          { required: true, message: "网安工作对口联系人不能为空", trigger: "blur" }
        ],
        netSecurityWorkPosition: [
          { required: true, message: "网安工作对口联系人职务不能为空", trigger: "blur" }
        ],
        netSecurityWorkPhone: [
          { required: true, message: "网安工作对口联系人手机号不能为空", trigger: "blur" }
        ],
        netSecurityWorkCertificateType: [
          { required: true, message: "网安工作对口联系人证件类型不能为空", trigger: "blur" }
        ],
        netSecurityWorkCertificateCode: [
          { required: true, message: "网安工作对口联系人证件号码不能为空", trigger: "blur" }
        ],
        netSecurityWorkCertificateFront: [
          { required: true, message: "网安工作对口联系人证件(国徽)不能为空", trigger: "blur" }
        ],
        netSecurityWorkCertificateBack: [
          { required: true, message: "网安工作对口联系人证件(人像)不能为空", trigger: "blur" }
        ],
        netSecurityWorkCertificateHand: [
          { required: true, message: "网安工作对口联系人证件(手持)不能为空", trigger: "blur" }
        ],
        dutyPhone: [
          { required: true, message: "值班电话不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    // 支持两种方式获取creditCode：路由参数和查询参数
    this.creditCode = this.$route.params && this.$route.params.id;
    if (!this.creditCode) {
      this.creditCode = this.$route.query && this.$route.query.creditCode;
    }
    this.getDetail();
  },
  methods: {
    // 表单重置
    reset() {
      this.form = {
        id: null,
        creditCode: null,
        enterpriseName: null,
        licenseList: [
          {
            licenceCode: "",
            publishTime: "",
            expirationTime: "",
            buzTypeList: [{ businessType: "", serviceItems: "", businessCoverage: "" }]
          }
        ],
        safetyEquipmentList: [{ deviceProvider: "", deviceModel: "" }],
        provideServiceType1: [],
        provideServiceType: "",
        infoSecurityName: null,
        infoSecurityPosition: null,
        infoSecurityPhone: null,
        infoSecurityCertificateType: null,
        infoSecurityCertificateCode: null,
        netSecurityDept: null,
        netSecurityName: null,
        netSecurityPosition: null,
        netSecurityPhone: null,
        netSecurityCertificateType: null,
        netSecurityCertificateCode: null,
        netSecurityWorkDept: null,
        netSecurityWorkName: null,
        netSecurityWorkPosition: null,
        netSecurityWorkPhone: null,
        netSecurityWorkCertificateType: null,
        netSecurityWorkCertificateCode: null,
        dutyPhone: null
      };
      this.resetForm("form");
    },
    /** 获取详情 */
    getDetail() {
      this.reset();
      if (!this.creditCode) {
        return;
      }
      this.loading = true;
      getOperator({creditCode:this.creditCode}).then(response => {
                if(response.code=='200') {
                    this.form = response.data; 
                    if(this.form.provideServiceType.length>0) {
                        this.form.provideServiceType1=this.form.provideServiceType.split(","); // 分割成以逗号为分隔符的数组
                        this.form.provideServiceType1 = this.form.provideServiceType1.map(Number);
                       
                    }
                    // console.log('----this.form.provideServiceType=',this.form.provideServiceType);
                    // console.log('----this.form.provideServiceType1=',this.form.provideServiceType1);
                }
                else {
                    this.form={
                        creditCode:'',
                        netSecurityWorkCertificateFront:'',
                        netSecurityWorkCertificateBack:'',
                        netSecurityWorkCertificateHand:'',  
                        // 增值业务许可列表 
                        licenseList:[
                            {
                                "licenceCode":"","publishTime":"","expirationTime":"",
                                "buzTypeList":[{"businessType":"","serviceItems":"","businessCoverage":""}],
                                "licenceImageList":[{"url":""}]
                            }
                        ], 
                        safetyEquipmentList:[{"deviceProvider":"","deviceModel":""}],  // 安全审计设备信息列表
                        provideServiceType1:[], // 提供服务类型数组
                        provideServiceType:'',  // 提供服务类型字符
                    };
                }
                // console.log('----getNetbarInfo--this.form=', this.form);
                this.loading = false;
            } );
    },
    /** 切换模块折叠状态 */
    toggleSection(section) {
      this.sectionCollapsed[section] = !this.sectionCollapsed[section];
    },
    /** 处理许可证操作 */
    handleLicense(type, item, index) {
      if (type === 'add') {
        this.form.licenseList.push({
          licenceCode: "",
          publishTime: "",
          expirationTime: "",
          buzTypeList: [{ businessType: "", serviceItems: "", businessCoverage: "" }]
        });
      } else if (type === 'delete') {
        this.form.licenseList.splice(index, 1);
      }
    },
    /** 处理业务类型操作 */
    handleBusinessType(type, licenseItem, index) {
      if (type === 'add') {
        licenseItem.buzTypeList.push({ businessType: "", serviceItems: "", businessCoverage: "" });
      } else if (type === 'delete') {
        licenseItem.buzTypeList.splice(index, 1);
      }
    },
    /** 处理设备操作 */
    handleEquipment(type, item, index) {
      if (type === 'add') {
        this.form.safetyEquipmentList.push({ deviceProvider: "", deviceModel: "" });
      } else if (type === 'delete') {
        this.form.safetyEquipmentList.splice(index, 1);
      }
    },
    /** 处理服务类型变化 */
    handleServiceChange(value) {
      this.form.provideServiceType = value.join(",");
    },
    /** 提交表单 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        let that = this
        if (valid) {
          this.submitting = true;
           let pServiceType ='';
                    that.form.provideServiceType1.forEach(function(itemT) {
                        console.log("provideServiceType1.itemT=",itemT);
                        pServiceType += itemT;
                        pServiceType +=",";
                    });
                    // 去掉最后一个，号
                    if (pServiceType.charAt(pServiceType.length - 1) === ",") { 
                        pServiceType = pServiceType.slice(0, -1);
                    }

                    that.form.provideServiceType = pServiceType;// 
          if (this.form.id != null) {
            addOperator(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.handleClose();
            }).finally(() => {
              this.submitting = false;
            });
          } else {
            addOperator(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.handleClose();
            }).finally(() => {
              this.submitting = false;
            });
          }
        }
      });
    },
    /** 返回按钮 */
    goBack() {
      window.history.go(-1);
    },
    handleClose() {
      this.goBack();
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .page-header {
    width: 94%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      justify-content: space-between;

      .page-title {
        margin: 0;
        font-size: 18px;
        color: #303133;
        font-weight: 600;
      }
    }
  }

  .form-container {
    width: 94%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
  }

  .form-section {
    margin-bottom: 24px;

    .section-header {
      background-color: #f6f7fb;
      padding: 12px 16px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 4px;
      margin-bottom: 16px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-line {
          width: 3px;
          height: 16px;
          background-color: #4584FF;
        }

        .title-text {
          font-size: 16px;
          font-weight: 600;
          color: #4584FF;
        }
      }

      .section-toggle {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #4584FF;
        font-size: 14px;

        .toggle-text {
          font-size: 14px;
        }

        i {
          font-size: 12px;
          transition: transform 0.3s;
        }
      }

      &:hover {
        background-color: #f0f2f5;
      }
    }

    .section-content {
      padding: 0 16px;
    }
  }
}

.license-item {
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
  background-color: #fafafa;

  .license-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .license-number {
      font-weight: 600;
      color: #4584ff;
      font-size: 16px;
    }

    .license-actions {
      display: flex;
      gap: 8px;
    }
  }
}

.business-type-item {
    border-left: 3px solid #eeeff2;
  padding-left: 12px;
  margin-bottom: 12px;
}

.business-type-actions,
.equipment-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  
  
//   padding-top: 30px;
}

// 全局样式：确保表单标签不换行
::v-deep .el-form-item__label {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

::v-deep .el-checkbox-group {
  .el-checkbox {
    margin-right: 20px;
    margin-bottom: 10px;
  }
}

.business-type-actions,.equipment-actions,.license-actions {
::v-deep i {
    font-size: 18px !important;
    font-weight: 600 !important;
  }
}

.form-footer {
  text-align: right;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

</style>