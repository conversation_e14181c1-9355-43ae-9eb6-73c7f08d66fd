<template>
<div style="display:flex;background: #f5f7fa;width:100%;">
  <section class="app-container" :class="{ 'expanded': isRightCollapsed }">
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">运营商基础信息详情</h2>
        <div class="header-actions">
          <el-button type="primary" size="small" @click="handleEdit">编辑</el-button>
          <el-button icon="el-icon-arrow-left" size="small" @click="goBack">返回</el-button>
        </div>
      </div>
    </div>

    <div class="detail-content">
      <!-- 基础信息 -->
      <div id="basic-info" class="detail-section">
        <div class="section-header" @click="toggleSection('basic')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">基础信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.basic ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.basic ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.basic" class="section-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="统一社会信用代码">
              {{ form.creditCode }}
            </el-descriptions-item>
            <el-descriptions-item label="企业名称">
              {{ form.enterpriseName }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 增值电信业务许可信息 -->
      <div id="license-info" class="detail-section">
        <div class="section-header" @click="toggleSection('license')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">增值电信业务许可信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.license ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.license ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.license" class="section-content">
          <div v-for="(item, index) in form.licenseList" :key="'license.'+index" class="license-item">
            <div class="license-header">
              <div class="license-number">许可证 {{ index + 1 }}</div>
            </div>
            <el-descriptions :column="3" border>
              <el-descriptions-item label="经营许可证编号">
                {{ item.licenceCode }}
              </el-descriptions-item>
              <el-descriptions-item label="发证日期">
                {{ parseTime(item.publishTime, '{y}-{m}-{d}') }}
              </el-descriptions-item>
              <el-descriptions-item label="有效期至">
                {{ parseTime(item.expirationTime, '{y}-{m}-{d}') }}
              </el-descriptions-item>
            </el-descriptions>

            <!-- 业务种类 -->
            <div class="business-types">
              <h4>业务种类</h4>
              <el-table :data="item.buzTypeList" border style="width: 100%">
                <el-table-column prop="businessType" label="业务种类" align="center" />
                <el-table-column prop="serviceItems" label="服务项目" align="center" />
                <el-table-column prop="businessCoverage" label="业务覆盖范围" align="center" />
              </el-table>
            </div>
          </div>
        </div>
      </div>

      <!-- 安全审计设备信息 -->
      <div id="equipment-info" class="detail-section">
        <div class="section-header" @click="toggleSection('equipment')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">安全审计设备信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.equipment ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.equipment ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.equipment" class="section-content">
          <el-table :data="form.safetyEquipmentList" border style="width: 100%">
            <el-table-column prop="deviceProvider" label="安全审计设备厂商" align="center" />
            <el-table-column prop="deviceModel" label="安全审计设备型号" align="center" />
          </el-table>
        </div>
      </div>

      <!-- 提供服务类型 -->
      <div id="service-info" class="detail-section">
        <div class="section-header" @click="toggleSection('service')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">提供服务类型</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.service ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.service ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.service" class="section-content">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="提供服务类型">
              <span v-for="(type, index) in serviceTypeLabels" :key="index">
                <el-tag type="primary" style="margin-right: 8px;">{{ type }}</el-tag>
              </span>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 信息安全负责人 -->
      <div id="info-security" class="detail-section">
        <div class="section-header" @click="toggleSection('infoSecurity')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">信息安全负责人</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.infoSecurity ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.infoSecurity ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.infoSecurity" class="section-content">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="负责人姓名">
              {{ form.infoSecurityName }}
            </el-descriptions-item>
            <el-descriptions-item label="负责人职务">
              {{ form.infoSecurityPosition }}
            </el-descriptions-item>
            <el-descriptions-item label="手机号">
              {{ form.infoSecurityPhone }}
            </el-descriptions-item>
            <el-descriptions-item label="证件类型">
              <dict-tag :options="dict.type.certificate_type" :value="form.infoSecurityCertificateType"/>
            </el-descriptions-item>
            <el-descriptions-item label="证件号码">
              {{ form.infoSecurityCertificateCode }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 网安对口信息安全部门 -->
      <div id="net-security" class="detail-section">
        <div class="section-header" @click="toggleSection('netSecurity')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">网安对口信息安全部门</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.netSecurity ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.netSecurity ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.netSecurity" class="section-content">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="部门名称">
              {{ form.netSecurityDept }}
            </el-descriptions-item>
            <el-descriptions-item label="负责人">
              {{ form.netSecurityName }}
            </el-descriptions-item>
            <el-descriptions-item label="职务">
              {{ form.netSecurityPosition }}
            </el-descriptions-item>
            <el-descriptions-item label="手机号">
              {{ form.netSecurityPhone }}
            </el-descriptions-item>
            <el-descriptions-item label="证件类型">
              <dict-tag :options="dict.type.certificate_type" :value="form.netSecurityCertificateType"/>
            </el-descriptions-item>
            <el-descriptions-item label="证件号码">
              {{ form.netSecurityCertificateCode }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 网安工作对口联系人 -->
      <div id="net-security-work" class="detail-section">
        <div class="section-header" @click="toggleSection('netSecurityWork')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">网安工作对口联系人</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.netSecurityWork ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.netSecurityWork ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.netSecurityWork" class="section-content">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="部门名称">
              {{ form.netSecurityWorkDept }}
            </el-descriptions-item>
            <el-descriptions-item label="联系人">
              {{ form.netSecurityWorkName }}
            </el-descriptions-item>
            <el-descriptions-item label="联系人职务">
              {{ form.netSecurityWorkPosition }}
            </el-descriptions-item>
            <el-descriptions-item label="手机号">
              {{ form.netSecurityWorkPhone }}
            </el-descriptions-item>
            <el-descriptions-item label="证件类型">
              <dict-tag :options="dict.type.certificate_type" :value="form.netSecurityWorkCertificateType"/>
            </el-descriptions-item>
            <el-descriptions-item label="证件号码">
              {{ form.netSecurityWorkCertificateCode }}
            </el-descriptions-item>
          </el-descriptions>

          <!-- 网安工作对口联系人证件照 -->
          <div class="certificate-images">
            <div class="image-item">
              <div class="image-label">证件(人像)</div>
              <el-image
                v-if="form.netSecurityWorkCertificateBack"
                :src="form.netSecurityWorkCertificateBack"
                class="certificate-img"
                :preview-src-list="[form.netSecurityWorkCertificateBack]">
                <div slot="placeholder" class="image-slot">
                  加载中<span class="dot">...</span>
                </div>
              </el-image>
              <div v-else class="no-image">暂无图片</div>
            </div>
            <div class="image-item">
              <div class="image-label">证件照(国徽)</div>
              <el-image
                v-if="form.netSecurityWorkCertificateFront"
                :src="form.netSecurityWorkCertificateFront"
                class="certificate-img"
                :preview-src-list="[form.netSecurityWorkCertificateFront]">
                <div slot="placeholder" class="image-slot">
                  加载中<span class="dot">...</span>
                </div>
              </el-image>
              <div v-else class="no-image">暂无图片</div>
            </div>
            <div class="image-item">
              <div class="image-label">证件(手持)</div>
              <el-image
                v-if="form.netSecurityWorkCertificateHand"
                :src="form.netSecurityWorkCertificateHand"
                class="certificate-img"
                :preview-src-list="[form.netSecurityWorkCertificateHand]">
                <div slot="placeholder" class="image-slot">
                  加载中<span class="dot">...</span>
                </div>
              </el-image>
              <div v-else class="no-image">暂无图片</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 7*24小时值班电话 -->
      <div id="duty-info" class="detail-section">
        <div class="section-header" @click="toggleSection('duty')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">7*24小时值班电话</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.duty ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.duty ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.duty" class="section-content">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="值班电话">
              {{ form.dutyPhone }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </div>
  </section>

  <!-- 右侧数据维护记录区域 -->
  <collapsible-detail-right
    @collapse-change="handleRightCollapseChange"
    @width-change="handleRightWidthChange"
  >
    <!-- 锚点导航组件 -->
    <template #anchor>
      <anchor-navigation
        :anchor-items="anchorItems"
        @anchor-change="handleAnchorChange"
      />
    </template>

    <!-- 数据维护时间线 -->
    <template #timeline>
      <data-maintenance-timeline
        :biz-type="'OPERATOR_INFO'"
        :biz-id="form.id"
        v-if="form.id"
      />
    </template>
  </collapsible-detail-right>
</div>
</template>

<script>
import { getOperator } from "@/api/operator/operator";
import DataMaintenanceTimeline from '@/components/DataMaintenanceTimeline'
import AnchorNavigation from '@/components/AnchorNavigation'
import CollapsibleDetailRight from '@/components/CollapsibleDetailRight'

export default {
  name: "OperatorBaseinfoDetail",
  dicts: ['service_type_operator', 'certificate_type'],
  components: {
    DataMaintenanceTimeline,
    AnchorNavigation,
    CollapsibleDetailRight
  },
  data() {
    return {
      loading: false,
      creditCode: undefined,
      // 表单参数
      form: {
        licenseList: [],
        safetyEquipmentList: []
      },
      // 模块折叠状态
      sectionCollapsed: {
        basic: false,
        license: false,
        equipment: false,
        service: false,
        infoSecurity: false,
        netSecurity: false,
        netSecurityWork: false,
        duty: false
      },

      // 锚点导航配置
      anchorItems: [
        { id: 'basic-info', label: '基础信息' },
        { id: 'license-info', label: '增值电信业务许可信息' },
        { id: 'equipment-info', label: '安全审计设备信息' },
        { id: 'service-info', label: '提供服务类型' },
        { id: 'info-security', label: '信息安全负责人' },
        { id: 'net-security', label: '网安对口信息安全部门' },
        { id: 'net-security-work', label: '网安工作对口联系人' },
        { id: 'duty-info', label: '7*24小时值班电话' }
      ],

      // 右侧面板收起状态
      isRightCollapsed: false,
      // 右侧面板宽度
      rightPanelWidth: 380
    };
  },
  computed: {
    serviceTypeLabels() {
      if (!this.form.provideServiceType) return [];
      const serviceTypes = this.form.provideServiceType.split(',');
      const labels = [];
      serviceTypes.forEach(type => {
        const dict = this.dict.type.service_type_operator.find(item => item.value === type);
        if (dict) {
          labels.push(dict.label);
        }
      });
      return labels;
    }
  },
  created() {
    this.$router.onReady(() => {
      this.creditCode = this.$route.params && this.$route.params.id;
      console.log('Operator detail creditCode:', this.creditCode);
      if (this.creditCode != null) {
        this.getDetail();
      }
    });
  },
  methods: {
    /** 获取详情 */
    getDetail() {
      if (!this.creditCode) {
        return;
      }
      this.loading = true;
      getOperator({ creditCode: this.creditCode }).then(response => {
        console.log('Operator detail API Response:', response);
        if (response.code === 200 && response.data) {
          this.form = response.data;
          console.log('Operator detail form data:', this.form);

          // 初始化数组字段
          if (!this.form.licenseList || this.form.licenseList.length === 0) {
            this.form.licenseList = [];
          }
          if (!this.form.safetyEquipmentList || this.form.safetyEquipmentList.length === 0) {
            this.form.safetyEquipmentList = [];
          }

          // 处理服务类型数组
          if (this.form.provideServiceType1 && Array.isArray(this.form.provideServiceType1)) {
            this.form.provideServiceType = this.form.provideServiceType1.join(',');
          }
        } else {
          this.form = {
            licenseList: [],
            safetyEquipmentList: []
          };
        }
        this.loading = false;
      }).catch(error => {
        console.error('Operator detail API Error:', error);
        this.loading = false;
      });
    },
    /** 切换模块折叠状态 */
    toggleSection(section) {
      this.sectionCollapsed[section] = !this.sectionCollapsed[section];
    },
    /** 编辑按钮 */
    handleEdit() {
      this.$router.push("/data-management/operator/baseinfo/edit/" + this.creditCode);
    },
    /** 返回按钮 */
    goBack() {
      window.history.go(-1);
    },
    handleClose() {
      this.goBack();
    },

    // 处理锚点变化
    handleAnchorChange(anchorId) {
      console.log('当前激活的锚点:', anchorId);
    },

    // 处理右侧面板收起展开
    handleRightCollapseChange(isCollapsed) {
      this.isRightCollapsed = isCollapsed;
    },

    // 处理右侧面板宽度变化
    handleRightWidthChange(width) {
      this.rightPanelWidth = width;
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
      width: calc(100% - 380px);
  transition: width 0.3s ease;

  &.expanded {
    width: calc(100% - 60px);
  }

  .page-header {
    width: 99%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      justify-content: space-between;

      .page-title {
        margin: 0;
        font-size: 18px;
        color: #303133;
        font-weight: 600;
      }

      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
  }

  .detail-content {
    width: 99%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
  }

  .detail-section {
    margin-bottom: 24px;

    .section-header {
      background-color: #f6f7fb;
      padding: 12px 16px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 4px;
      margin-bottom: 16px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-line {
          width: 3px;
          height: 16px;
          background-color: #4584FF;
        }

        .title-text {
          font-size: 16px;
          font-weight: 600;
          color: #4584FF;
        }
      }

      .section-toggle {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #4584FF;
        font-size: 14px;

        .toggle-text {
          font-size: 14px;
        }

        i {
          font-size: 12px;
          transition: transform 0.3s;
        }
      }

      &:hover {
        background-color: #f0f2f5;
      }
    }

    .section-content {
      padding: 0 16px;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.license-item {
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
  background-color: #fafafa;

  .license-header {
    margin-bottom: 16px;

    .license-number {
      font-weight: 600;
      color: #4584ff;
      font-size: 16px;
    }
  }

  .business-types {
    margin-top: 16px;

    h4 {
      margin-bottom: 12px;
      color: #4584ff;
      font-size: 14px;
    }
  }
}

::v-deep .el-descriptions__label {
  font-weight: 500;
  color: #666;
}

::v-deep .el-descriptions__content {
  color: #333;
}

::v-deep .el-tag {
  margin-bottom: 4px;
}

// 证件照样式
.certificate-images {
  display: flex;
  gap: 40px;
  margin-top: 20px;
  flex-wrap: wrap;

  .image-item {
    text-align: center;

    .image-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .certificate-img {
      width: 148px;
      height: 148px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }

    .no-image {
      width: 148px;
      height: 148px;
      border: 1px dashed #ddd;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #999;
      font-size: 14px;
      background-color: #f9f9f9;
    }
  }
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 14px;
}


</style>