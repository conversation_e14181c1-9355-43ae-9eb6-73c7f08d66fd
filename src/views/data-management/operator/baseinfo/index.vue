<template>
  <div class="app-container">
    <div class="screen-management-layout">
      <!-- 左侧导航 -->
      <basic-data-sidebar />

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <!-- 搜索区域 -->
        <common-search
          :search-config="searchConfig"
          :advanced-fields="advancedFields"
          :initial-form="queryParams"
          @search="handleSearch"
        />

        <!-- 操作按钮区域 -->
        <div class="action-bar">
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['operator:baseinfo:add']"
              >新增</el-button>
            </el-col>
            <!-- <el-col :span="1.5">
              <el-button
                type="success"
                plain
                icon="el-icon-edit"
                size="mini"
                :disabled="single"
                @click="handleUpdate"
                v-hasPermi="['operator:baseinfo:edit']"
              >修改</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="danger"
                plain
                icon="el-icon-delete"
                size="mini"
                :disabled="multiple"
                @click="handleDelete"
                v-hasPermi="['operator:baseinfo:remove']"
              >删除</el-button>
            </el-col> -->
            <el-col :span="1.5">
              <el-button
                type="warning"
                plain
                icon="el-icon-download"
                size="mini"
                @click="handleExport"
                v-hasPermi="['operator:baseinfo:export']"
              >导出</el-button>
            </el-col>
            <el-col :span="1.5">
               <el-button
                type="success"
                icon="el-icon-plus"
                size="mini"
                @click="handleReminder"
                v-hasPermi="['operator:baseinfo:add']"
              >设置到期提醒</el-button>
            </el-col>
            <div class="toolbar-right">
              <el-button type="text" icon="el-icon-setting" @click="handleDisplayFields" class="display-fields-btn">
                设置显示字段
              </el-button>
              <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
            </div>
          </el-row>
        </div>

        <!-- 表格区域 -->
        <div class="table-container">
          <el-table
            v-loading="loading"
            :data="operatorList"
            @selection-change="handleSelectionChange"
            style="width: 100%"
            border
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column
              label="统一社会信用代码"
              prop="creditCode"
              show-overflow-tooltip
              min-width="180"
              width="180"
            >
              <template slot-scope="scope">
                <div class="credit-code-container">
                  <span>{{ scope.row.creditCode }}</span>
                  <span
                    v-if="scope.row.dataUpdateStatus === 1"
                    class="update-badge pending"
                  >待更新</span>
                  <span
                    v-else-if="scope.row.dataUpdateStatus > 1"
                    class="update-badge overdue"
                  >已超期</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="provideServiceTypeLabel"
              label="提供服务类型"
              min-width="100"
              width="100"
              show-overflow-tooltip
            />
            <el-table-column
              prop="infoSecurityName"
              label="信息安全负责人"
              min-width="120"
              width="120"
            />
            <el-table-column
              prop="infoSecurityPhone"
              label="信息安全手机号码"
              min-width="100"
               width="140"
              show-overflow-tooltip
            />
            <el-table-column
              prop="netSecurityName"
              label="网络安全负责人"
              min-width="120"
              width="120"
              show-overflow-tooltip
            />
            <el-table-column
              prop="netSecurityPhone"
              label="网络安全手机号码"
              min-width="180"
              show-overflow-tooltip
            />
            <el-table-column
              prop="netSecurityWorkName"
              label="网络安全工作负责人"
             min-width="180"
              show-overflow-tooltip
            />
            <el-table-column
              prop="netSecurityWorkPhone"
              label="网络安全手机号码"
              min-width="180"
              show-overflow-tooltip
            />
            <el-table-column
              prop="dutyPhone"
              label="值班电话"
              min-width="100"
               width="140"
              show-overflow-tooltip
            />
            <el-table-column
              label="操作"
              align="center"
              min-width="150"
              width="200"
              class-name="small-padding fixed-width"
              fixed="right"
            >
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-view"
                  @click="handleView(scope.row)"
                  v-hasPermi="['operator:operator:query']"
                >详情</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['operator:operator:edit']"
                >修改</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['operator:operator:remove']"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
    </div>

    <!-- 数据更新提醒组件 -->
    <data-update-reminder
      :visible.sync="reminderVisible"
      :biz-type="'OPERATOR_INFO'"
    />
  </div>
</template>

<script>
import { listOperator, delOperator } from "@/api/operator/operator";
import BasicDataSidebar from '@/components/BasicDataSidebar'
import CommonSearch from '@/components/CommonSearch'
import DataUpdateReminder from '@/components/DataUpdateReminder'

export default {
  name: "OperatorBaseInfo",
  dicts: ['service_type_operator'],
  components: { BasicDataSidebar, CommonSearch, DataUpdateReminder },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 运营商基础信息表格数据
      operatorList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        creditCode: undefined,
        infoSecurityName: undefined,
        netSecurityName: undefined,
        netSecurityWorkName: undefined,
      },
      // 搜索配置
      searchConfig: {
        label: '统一社会信用代码',
        key: 'creditCode',
        placeholder: '请输入统一社会信用代码'
      },
      // 高级搜索字段
      advancedFields: [],
      // 数据更新提醒弹窗显示状态
      reminderVisible: false,
    };
  },
  created() {
    this.getList();
    this.initAdvancedFields();
  },
  methods: {
    // 设置到期提醒
    handleReminder() {
      this.reminderVisible = true;
    },

    // 初始化高级搜索字段
    initAdvancedFields() {
      this.advancedFields = [
        {
          label: '信息安全负责人',
          key: 'infoSecurityName',
          type: 'input',
          placeholder: '请输入信息安全负责人',
          span: 6
        },
        {
          label: '网络安全负责人',
          key: 'netSecurityName',
          type: 'input',
          placeholder: '请输入网络安全负责人',
          span: 6
        },
        {
          label: '网络安全工作负责人',
          key: 'netSecurityWorkName',
          type: 'input',
          placeholder: '请输入网络安全工作负责人',
          span: 6
        }
      ];
    },

    // 处理搜索
    handleSearch(searchForm) {
      this.queryParams = {
        ...this.queryParams,
        ...searchForm,
        pageNum: 1
      };
      this.getList();
    },
    /** 查询运营商基础信息列表 */
    getList() {
      this.loading = true;
      listOperator(this.queryParams).then(response => {
        this.operatorList = response.data.rows;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        creditCode: undefined,
        infoSecurityName: undefined,
        netSecurityName: undefined,
        netSecurityWorkName: undefined,
      };
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.creditCode)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push("/data-management/operator/baseinfo/add");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const creditCode = row.creditCode || this.ids[0];
      this.$router.push("/data-management/operator/baseinfo/edit/" + creditCode);
    },
    /** 详情按钮操作 */
    handleView(row) {
      this.$router.push("/data-management/operator/baseinfo/detail/" + row.creditCode);
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const creditCodes = row.creditCode || this.ids;
      this.$modal.confirm('是否确认删除统一社会信用代码为"' + creditCodes + '"的数据项？').then(function() {
        return delOperator(creditCodes);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('operator/operator/export', {
        ...this.queryParams
      }, `operator_${new Date().getTime()}.xlsx`)
    },
    /** 设置显示字段 */
    handleDisplayFields() {
      this.$modal.msgInfo("设置显示字段功能开发中");
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/screen-management.scss';

.action-bar {
  background: white;
  padding: 16px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.toolbar-right {
  float: right;
  display: flex;
  align-items: center;
  gap: 12px;

  .display-fields-btn {
    color: #ACB0B3;
    padding: 0;
    border: none;
    background: none;

    &:hover {
      color: #4584FF;
    }
  }
}

// 修复表格头部背景色
::v-deep .el-table {
  .el-table__header-wrapper {
    .el-table__header {
      th {
        background-color: #EBF2FF !important;
        color: #333;
        font-weight: 500;
      }
    }
  }
}

// 统一社会信用代码角标样式
.credit-code-container {
  position: relative;
  display: inline-block;
  padding: 12px 12px 0 0;

  .update-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    color: white;
    font-weight: bold;
    z-index: 10;

    &.pending {
      background-color: #FE9400;
    }

    &.overdue {
      background-color: #FF2A2A;
    }
  }
}
</style>