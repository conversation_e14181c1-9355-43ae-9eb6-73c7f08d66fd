
<!-- -->
<template>
    <div class="app-container" style="padding:0px;"> 
      <div class="box" style="margin-top:-5px;">
          <div class="top-div">
            <div class="page-box">
              <el-input
                v-model="searchKeys"
                size="large"
                placeholder="请输入想要搜索的信息..."
                 @keyup.enter.native="searchClick"
                 @clear="searchClick"
              ></el-input>
              <el-button
                type="text"
                class="search-btn"
                size="large"
                style="margin: 10px 0 0 20px;"
                @click="toggleSearchConditions"
              >
                选择业务类型
                <i :class="showSearchConditions ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
              </el-button>
            </div>
            
            <section class="choose">
              <div>
                <el-checkbox v-model="splitSearchFlag" style="margin-left:20px;margin-right:10px;">分词搜索</el-checkbox>
                <span style="margin-left:10px;color: #6BA5DB;font-size: 13px;">条件说明：查询条件输入各个表的全字段条件，条件可以使用”&“进行与组合查询，也可以使用”|“进行或组合查询。</span>
              </div>
              <div class="checkbox-content" style="width: 620px;margin: 20px auto 0;" v-show="showSearchConditions">
                <el-button class="del" type="primary" plain size="mini" @click="selectAll">全选</el-button>
                <el-button class="del" type="text" @click="clearEmpty">清空</el-button>
                <el-button style="margin-left: 5px;float:right;" type="primary" size="mini" @click="searchClick"><i class="el-icon-search"></i> 一键搜</el-button>
                <el-checkbox-group v-model="checkedSearchTypes">
                  <div v-for="(itemLabel,key) in searchTypes" :key="key"  >
                    {{key}}
                    <el-checkbox v-for="item in itemLabel" :label="item.code" :key="item.code">{{item.label}}</el-checkbox>
                  </div>

                </el-checkbox-group>
              </div>
              
            </section>
          </div>
          
          <div class="table" v-if="dataSearchShow=='1'" >
            <div class="search-total" style="margin-left:6%;font-size: 14px;letter-spacing: 2px;">搜索记录总数：{{ searchTotal }}</div>
            <div class="search-detail-line" v-for="(item,index) in lineDataList" :key="index" @click="handleDetailClick(item)">
              <!-- 数据类型标签 -->
              <div class="data-type-tag">
                {{ item.命中表 }}
              </div>
                <div v-if="item.命中表=='电子屏信息表'">
                  <div class="search-detail-title"> 
                    <span    v-html="getJsonDataItem(item,'名称')"></span>
                    <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'区域编码')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'派出所')"></span>
                  </div>

                  <div class="cen">
                    <span v-html="getDataItemTitle(item,'更新时间')"></span> 
                    <span style="margin-left:10px;" v-html="getDataItemTitle(item,'地址')"></span> 
                    <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'位置类型')"></span>
                    <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'场所编码')"></span> 
                    <!-- <image-preview style="margin-right: 10px;" v-for="imgItem in item.照片.split(',')" :src="imgItem" :width="100" :height="100"/> -->
                  </div>

                  <!-- 电子屏信息 -->
                  <div class="newAdded"  >
                    <div>  
                      <span v-html="getDataItemTitle(item,'所在楼层')"></span>
                      <span v-html="getDataItemTitle(item,'电子屏性质')"></span>

                      <span v-html="getDataItemTitle(item,'屏幕类型')"></span>
                      <span v-html="getDataItemTitle(item,'显示类型：')"></span> 
                      <span>电子屏尺寸：<span v-html="getJsonDataItem(item,'电子屏尺寸（m²）')"></span></span> 
                      <span v-html="getDataItemTitle(item,'民警电话：')"></span>  
                      <span v-html="getDataItemTitle(item,'负责民警')"></span>  
                      <!-- <span v-html="getDataItemTitle(item,'责任单位')"></span>  -->
                    </div>
                    <div class="clear"></div>
                  </div>
                </div>
                <div v-if="item.命中表=='责任单位表'">
                  <div class="search-detail-title"> 
                    <span   v-html="getJsonDataItem(item,'单位名称')"></span>
                    <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'统一社会信用代码')"></span> 
                  </div>

                  <div class="cen">
                    <span v-html="getDataItemTitle(item,'更新时间')"></span> 
                    <span style="margin-left:10px;" v-html="getDataItemTitle(item,'单位性质')"></span> 
                    <span style="margin-left:10px;" v-html="getDataItemTitle(item,'单位地址')"></span>  
                  </div>

                  <div class="newAdded"  >
                    <div>  
                      <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'联系人姓名')"></span>
                      <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'联系人电话')"></span> 
                      <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'联系人部门')"></span> 
                      
                    </div>
                    <div class="clear"></div>
                  </div>
                </div>

                <div v-if="item.命中表=='归属场所'">
                  <div class="search-detail-title"> 
                    <span  v-html="getJsonDataItem(item,'场所名称')"></span>
                    <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'场所状态')"></span> 
                  </div>

                  <div class="cen">
                    <span v-html="getDataItemTitle(item,'更新时间')"></span> 
                    <span style="margin-left:10px;" v-html="getDataItemTitle(item,'场所类型')"></span> 
                    <span style="margin-left:10px;" v-html="getDataItemTitle(item,'地址')"></span>  
                  </div>

                  <div class="newAdded"  >
                    <div>  
                      <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'负责人姓名')"></span>
                      <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'负责人电话')"></span>  
                    </div>
                    <div class="clear"></div>
                  </div>
                </div>


                <div v-if="item.命中表=='联网系统表'">
                  <div class="search-detail-title"> 
                    <span  v-html="getJsonDataItem(item,'名称')"></span>
                    <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'开发厂商单位统一社会信用代码')"></span> 
                    <span style="margin-left:10px;" v-html="getDataItemTitle(item,'控制系统运行状态')"></span> 
                  </div>

                  <div class="cen">
                    <span v-html="getDataItemTitle(item,'更新时间')"></span>  
                    <span style="margin-left:10px;" v-html="getDataItemTitle(item,'等级保护备案机关')"></span> 
                    <span style="margin-left:10px;" v-html="getDataItemTitle(item,'等级保护级别')"></span>
                    <span style="margin-left:10px;" v-html="getDataItemTitle(item,'系统开发/制造商')"></span>
                    <span style="margin-left:10px;" v-html="getDataItemTitle(item,'联网系统域名/地址')"></span>
                    <span style="margin-left:10px;" v-html="getDataItemTitle(item,'联网系统物理位置')"></span>
                  </div> 
                  <div class="newAdded"  >
                    <div>  
                      <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'负责人姓名')"></span>
                      <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'负责人电话')"></span> 
                      <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'运维厂商单位统一社会信用代码')"></span> 
                      <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'运维厂商名称')"></span>
                      <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'运维厂商负责人')"></span> 
                      <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'运维厂商负责人电话')"></span> 
                      <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'连接控制系统方式')"></span> 
                    </div>
                    <div class="clear"></div>
                  </div>
                </div>

                <div v-if="item.命中表=='重点区域表'">
                  <div class="search-detail-title"> 
                    <span  v-html="getJsonDataItem(item,'名称')"></span>  
                  </div>

                  <div class="cen">
                    <span v-html="getDataItemTitle(item,'更新时间')"></span>  
                    <span style="margin-left:10px;" v-html="getDataItemTitle(item,'检查次数')"></span>  
                    <span style="margin-left:10px;" v-html="getDataItemTitle(item,'电子屏数')"></span>  
                  </div>

                  <div class="newAdded"  >
                    <div>  
                      <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'类型')"></span> 
                    </div>
                    <div class="clear"></div>
                  </div>
                </div>

                <div v-if="item.命中表=='专项表'">
                  <div class="search-detail-title"> 
                    <span  v-html="getJsonDataItem(item,'专项名称')"></span>
                    <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'专项周期')"></span>  
                  </div>

                  <div class="cen">
                    <span v-html="getDataItemTitle(item,'更新时间')"></span>  
                    <span style="margin-left:10px;" v-html="getDataItemTitle(item,'专项启动时间')"></span>  
                    <span style="margin-left:10px;" v-html="getDataItemTitle(item,'专项结束时间')"></span> 
                  </div>

                  <div class="newAdded"  >
                    <div>  
                      <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'说明')"></span>
                    </div>
                    <div class="clear"></div>
                  </div>
                </div>

                <div v-if="item.命中表=='检查任务表'">
                  <div class="search-detail-title"> 
                    <span   v-html="getJsonDataItem(item,'名称')"></span>
                    <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'检查数量')"></span>  
                  </div>

                  <div class="cen">
                    <span v-html="getDataItemTitle(item,'更新时间')"></span>  
                    <span style="margin-left:10px;" v-html="getDataItemTitle(item,'开始时间')"></span>  
                    <span style="margin-left:10px;" v-html="getDataItemTitle(item,'结束时间')"></span> 
                  </div>

                  <div class="newAdded"  >
                    <div>  
                      <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'任务内容')"></span> 
                      <span >检查文件附件：</span><el-link type="primary" :underline="false" :href="item.检查文件路径" target="_blank"><i class="el-icon-document"></i></el-link> 
                    </div>
                    <div class="clear"></div>
                  </div>
                </div>

                <div v-if="item.命中表=='检查任务结果表'">
                  <div class="search-detail-title"> 
                    <span  v-html="getJsonDataItem(item,'检查人')"></span>
                    <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'异常类型')"></span>  
                  </div>

                  <div class="cen">
                    <span v-html="getDataItemTitle(item,'更新时间')"></span>   
                  </div>

                  <div class="newAdded"  >
                    <div>  
                      <span style="margin-left:10px;" v-html="getDataItemTitle(item,'检查结果')"></span>  
                      <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'异常描述')"></span> 
                      <span >检查文件附件：</span>
                      <el-link type="primary" :underline="false" :href="item.检查文件路径" target="_blank"><i class="el-icon-document"></i></el-link>  
                    </div>
                    <div class="clear"></div>
                  </div>
                </div>

                <div v-if="item.命中表=='等保备案表'">
                  <div class="search-detail-title"> 
                    <span  v-html="getJsonDataItem(item,'网络名称')"></span>
                    <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'保护等级')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'业务类型')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'专家评审情况')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'定级日期')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'备案日期')"></span>
                  </div>

                  <div class="cen">
                    <span v-html="getDataItemTitle(item,'更新时间')"></span> 
                    <span style="margin-left:10px;" v-html="getDataItemTitle(item,'单位名称')"></span> 
                    <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'上级系统所属单位名称')"></span>
                    <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'上级系统名称')"></span> 
                    <!-- <image-preview style="margin-right: 10px;" v-for="imgItem in item.照片.split(',')" :src="imgItem" :width="100" :height="100"/> -->
                  </div>
                  <div  style=" margin-top:4px;"> 
                    <p style="padding-left:10px; padding-top: 0px; margin:0px; " v-html="getDataItemTitle(item,'业务描述')"></p> 
                  </div>

                  <!--   -->
                  <div class="newAdded"  >
                    <div>  
                      <span v-html="getDataItemTitle(item,'备案证明编号')"></span>
                      <span v-html="getDataItemTitle(item,'备案机关')"></span> 
                      <span v-html="getDataItemTitle(item,'服务对象')"></span>
                      <span v-html="getDataItemTitle(item,'服务范围')"></span> 
                      <span v-html="getDataItemTitle(item,'状态')"></span> 
                      <span v-html="getDataItemTitle(item,'IPV6')"></span> 

                      <span v-html="getDataItemTitle(item,'主管部门名称')"></span>
                      <span v-html="getDataItemTitle(item,'互联网IP地址')"></span>
                      <span v-html="getDataItemTitle(item,'投入运行使用日期')"></span>
                      <span v-html="getDataItemTitle(item,'数据来源')"></span>
                      <span v-html="getDataItemTitle(item,'测评时间')"></span>
                      <span v-html="getDataItemTitle(item,'系统互联情况')"></span>
                      <span v-html="getDataItemTitle(item,'系统类型')"></span>
                      <span v-html="getDataItemTitle(item,'统一社会信用代码')"></span>
                      <span v-html="getDataItemTitle(item,'网址/域名')"></span>

                      <span v-html="getDataItemTitle(item,'网络性质')"></span>
                      <span v-html="getDataItemTitle(item,'联系人办公电话')"></span>
                      <span v-html="getDataItemTitle(item,'联系人姓名')"></span>
                      <span v-html="getDataItemTitle(item,'联系人移动电话')"></span>
                      <span v-html="getDataItemTitle(item,'行业类别')"></span>
                      <span v-html="getDataItemTitle(item,'覆盖范围')"></span>

                      <span v-html="getDataItemTitle(item,'等级测评单位名称')"></span>  
                      <span v-html="getDataItemTitle(item,'网络名称')"></span>  
                    </div>
                    <div class="clear"></div>
                  </div>
                </div>

                <div v-if="item.命中表=='网站信息表'">
                  <div class="search-detail-title"> 
                    <span  v-html="getJsonDataItem(item,'网站名称')"></span>
                    <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'工信部备案号')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'网站分级')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'备案主体')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'社会信用代码')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'主域名')"></span>
                  </div>

                  <div class="cen">
                    <span v-html="getDataItemTitle(item,'更新时间')"></span> 
                    
                    <span style="margin-left:10px;" v-html="getDataItemTitle(item,'域名服务商')"></span> 
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'ip')"></span>
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'从域名')"></span>
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'域名证书')"></span> 
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'域名证书有效期')"></span> 
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'网站语种')"></span> 
                  </div> 

                  

                  <!--   -->
                  <div class="newAdded"  >
                    <div  > 
                      <span    v-html="getDataItemTitle(item,'市值')"></span> 
                      <span    v-html="getDataItemTitle(item,'月活用户数量')"></span> 
                      <span   v-html="getDataItemTitle(item,'接入方式')"></span> 
                      <span   v-html="getDataItemTitle(item,'网站开通日期')"></span> 
                      <span   v-html="getDataItemTitle(item,'是否提供管制物品服务')"></span> 
                      <span   v-html="getDataItemTitle(item,'是否涉及前置审核内容')"></span> 
                    </div>

                    <div>  
                      <span v-html="getDataItemTitle(item,'安全负责人姓名')"></span>
                      <span v-html="getDataItemTitle(item,'安全负责人手机号')"></span> 
                      <span v-html="getDataItemTitle(item,'安全负责人电子邮箱')"></span>
                      <span v-html="getDataItemTitle(item,'安全负责人证件号码')"></span> 
                      <span v-html="getDataItemTitle(item,'安全负责人证件类型')"></span>  

                      <span v-html="getDataItemTitle(item,'应急联络人姓名')"></span>
                      <span v-html="getDataItemTitle(item,'应急联络人手机号')"></span>
                      <span v-html="getDataItemTitle(item,'应急联络人电子邮箱')"></span>
                      <span v-html="getDataItemTitle(item,'应急联络人证件号码')"></span>
                      <span v-html="getDataItemTitle(item,'应急联络人证件类型')"></span>
                      
                       
                    </div>
                    <div class="clear"></div>
                  </div>
                </div>

                <div v-if="item.命中表=='app表'">
                  <div class="search-detail-title"> 
                    <span  v-html="getJsonDataItem(item,'APP名称')"></span>
                    <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'注册登记号')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'注册用户总量')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'月活用户数量')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'状态')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'统一社会信用代码')"></span>
                  </div>

                  <div class="cen">
                    <span v-html="getDataItemTitle(item,'更新时间')"></span>  
                    <span style="margin-left:10px;" v-html="getDataItemTitle(item,'功能描述')"></span> 
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'市值')"></span>   
                  </div>  

                  <!--   -->
                  <div class="newAdded"  >
                    <div  > 
                      <span    v-html="getDataItemTitle(item,'运行平台')"></span> 
                      <span    v-html="getDataItemTitle(item,'月活用户数量')"></span> 
                      <span   v-html="getDataItemTitle(item,'应用包名')"></span>  
                    </div>

                    <div>  
                      <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'应急联络人姓名')"></span>
                      <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'应急联络人联系电话')"></span> 
                      <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'应急联络人证件号码')"></span>
                      <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'应急联络人证件类型')"></span>

                      <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'安全责任人姓名')"></span>  
                      <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'安全责任人联系电话')"></span>  
                      <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'安全负责人证件号码')"></span> 
                      <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'安全责任人证件类型')"></span>  

                    </div>
                    <div class="clear"></div>
                  </div>
                </div>

                <div v-if="item.命中表=='小程序表'">
                  <div class="search-detail-title"> 
                    <span   v-html="getJsonDataItem(item,'小程序名称')"></span>
                    <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'注册登记号')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'注册用户总量')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'月活用户数量')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'状态')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'统一社会信用代码')"></span>
                  </div>

                  <div class="cen">
                    <span v-html="getDataItemTitle(item,'更新时间')"></span>  
                    <span style="margin-left:10px;" v-html="getDataItemTitle(item,'小程序分级')"></span> 
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'市值')"></span>   
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'IP地址')"></span> 
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'标签')"></span> 
                  </div>  

                  <!--   -->
                  <div class="newAdded"  >
                    <div  > 
                      <span    v-html="getDataItemTitle(item,'创建时间')"></span> 
                      <span    v-html="getDataItemTitle(item,'月活用户数量')"></span> 
                      <span   v-html="getDataItemTitle(item,'托管服务器信息')"></span>  
                    </div>

                    <div>  
                      <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'应急联络人姓名')"></span>
                      <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'应急联络人联系电话')"></span> 
                      <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'应急联络人证件号码')"></span>
                      <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'应急联络人证件类型')"></span>

                      <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'安全责任人姓名')"></span>  
                      <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'安全责任人联系电话')"></span>  
                      <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'安全负责人证件号码')"></span> 
                      <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'安全责任人证件类型')"></span>  
                      <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'安全责任人电子邮件地址')"></span>

                    </div>
                    <div class="clear"></div>
                  </div>
                </div>

                <div v-if="item.命中表=='网吧信息表'">
                  <div class="search-detail-title"> 
                    <span   v-html="getJsonDataItem(item,'场所名称')"></span>
                    <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'场所编码')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'审核编号')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'备案终端数')"></span>
                    
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'营业状态')"></span>
                  </div>

                  <div class="cen">
                    <span v-html="getDataItemTitle(item,'更新时间')"></span> 
                    
                    <span style="margin-left:10px;" v-html="getDataItemTitle(item,'法人')"></span> 
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'法人电话')"></span>
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'法人证件号码')"></span>
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'接入运营商')"></span> 
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'营业场所接入方式')"></span> 
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'消防证编号')"></span> 
                  </div> 

                  

                  <div class="newAdded"  >
                    <div  > 
                      <span    v-html="getDataItemTitle(item,'营业面积(平米)')"></span> 
                      <span   v-html="getDataItemTitle(item,'审计厂商')"></span>
                      <span    v-html="getDataItemTitle(item,'计费系统')"></span> 
                      <span   v-html="getDataItemTitle(item,'计费系统版本')"></span> 
                      <span   v-html="getDataItemTitle(item,'许可证编号')"></span> 
                      <span   v-html="getDataItemTitle(item,'负责人')"></span> 
                      <span   v-html="getDataItemTitle(item,'负责人电话')"></span> 
                    </div>

                    <div>  
                      <span v-html="getDataItemTitle(item,'地址')"></span>
                      <span v-html="getDataItemTitle(item,'纬度')"></span> 
                      <span v-html="getDataItemTitle(item,'经度')"></span> 
                       
                    </div>
                    <div class="clear"></div>
                  </div>
                </div>

                <div v-if="item.命中表=='单位主体信息表'">
                  <div class="search-detail-title"> 
                    <span   v-html="getJsonDataItem(item,'单位名称')"></span>
                    <span  style="margin-left:10px;"  v-html="getDataItemTitle(item,'单位性质')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'统一社会信用代码')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'所属辖区')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'人员规模')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'状态')"></span>
                  </div>

                  <div class="cen">
                    <span v-html="getDataItemTitle(item,'更新时间')"></span> 
                    
                    <span style="margin-left:10px;" v-html="getDataItemTitle(item,'法人姓名')"></span> 
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'法人手机号码')"></span>
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'法人证件类型')"></span>
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'法人证件号码')"></span> 
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'法人证件有效期')"></span> 
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'消防证编号')"></span> 
                  </div> 

                  

                  <div class="newAdded"  >
                    <div  > 
                      <span    v-html="getDataItemTitle(item,'负责人姓名')"></span> 
                      <span   v-html="getDataItemTitle(item,'负责人手机号码')"></span>
                      <span   v-html="getDataItemTitle(item,'负责人办公室电话')"></span>
                      <span    v-html="getDataItemTitle(item,'负责人证件类型')"></span> 
                      <span   v-html="getDataItemTitle(item,'负责人证件号码')"></span> 
                      <span   v-html="getDataItemTitle(item,'负责人证件有效期')"></span> 
                      <span   v-html="getDataItemTitle(item,'负责人电子邮箱')"></span>  
                    </div>

                    <div>  
                      <span v-html="getDataItemTitle(item,'单位注册地址')"></span>
                      <span v-html="getDataItemTitle(item,'实际办公地址')"></span>  
                       
                    </div>
                    <div class="clear"></div>
                  </div>
                </div>

                <div v-if="item.命中表=='行政处罚表'">
                  <div class="search-detail-title"> 
                    <span   @click="punishDetailClick( item.ID)" v-html="getJsonDataItem(item,'公司名称')"></span> 
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'统一社会信用代码')"></span>
                    
                  </div>

                  <div class="cen">
                    <span v-html="getDataItemTitle(item,'更新时间')"></span> 
                     
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'简要案情')"></span> 
                  </div>  

                  <div class="newAdded"  >
                    <div  > 
                      <span style="margin-left:10px;" v-html="getDataItemTitle(item,'处罚决定书时间')"></span>  
                      <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'处罚结果')"></span> 
                    </div> 
                    <div class="clear"></div>
                  </div>
                </div>

                <div v-if="item.命中表=='行政处置表'">
                  <div class="search-detail-title"> 
                    <span   @click="disposalDetailClick( item.ID)" v-html="getJsonDataItem(item,'具体地址')"></span> 
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'公司名称')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'统一社会信用代码')"></span> 
                    
                  </div>

                  <div class="cen">
                    <span v-html="getDataItemTitle(item,'更新时间')"></span> 
                     
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'通报问题')"></span>
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'备注')"></span> 
                    <!-- <span >整改报告：</span><el-link type="primary" :underline="false" :href="item.整改报告" target="_blank"><i class="el-icon-document"></i></el-link> -->
                  </div>  
                  
                  <!--   -->
                  <div class="newAdded"  >
                    <div  > 
                      <span v-html="getDataItemTitle(item,'网站/系统')"></span>
                      <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'核查情况')"></span>  
                      <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'网站地址')"></span> 
                      <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'联系人')"></span> 
                      <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'电话')"></span> 
                      <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'邮箱')"></span> 
                    </div> 
                    <div class="clear"></div>
                  </div>
                </div>
                
                <div v-if="item.命中表=='运营商信息表'">
                  <div class="search-detail-title"> 
                    <span   v-html="getJsonDataItem(item,'统一社会信用代码')"></span> 
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'信息安全负责人姓名')"></span>
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'信息安全负责人手机号码')"></span> 
                    
                  </div>

                  <div class="cen">
                    <span v-html="getDataItemTitle(item,'更新时间')"></span> 
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'信息安全负责人职务')"></span> 
                    <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'信息安全负责人证件号码')"></span> 

                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'值班电话')"></span>
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'网络安全工作负责人姓名')"></span> 
                    <span style="margin-left:10px;"  v-html="getDataItemTitle(item,'网络安全工作负责人手机号码')"></span> 
                  </div>  
                  
                  <div class="newAdded"  >
                    <div  >  
                      <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'网络安全工作负责人职务')"></span>  
                      <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'网络安全工作负责人证件号码')"></span> 
                      <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'网络安全工作负责部门')"></span> 
                      <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'网络安全负责人姓名')"></span> 
                      <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'网络安全负责人手机号码')"></span> 
                      <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'网络安全负责人职务')"></span> 
                      <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'网络安全负责人证件号码')"></span> 
                      <span   style="margin-left:10px;"  v-html="getDataItemTitle(item,'网络安全负责部门')"></span> 

                    </div> 
                    <div class="clear"></div>
                  </div>
                </div>

            </div>

          </div>
        </div>    
          
        <div class="noData" v-if="dataSearchShow == '2'">
            <img src="../../assets/images/zanwu-search.png" alt="">
        </div> 
        <!-- 分页 -->
        <Page v-if="dataSearchShow == '1'" v-on:page="pageFun" :pageNum.sync="searchParams.pageNum" :total="searchTotal"></Page> 
        
       
    </div>    

      
     
</template>

<script>

import {levelprotectSearchType,levelprotectSearchOneClick} from "@/api/home";
import Page from "@/components//Search/page";
import Loading from '@/components/Search/loading';
import { stripHtmlTags } from '@/utils/index'


export default ({
  name: "searchDetail", 
  components:{Page, Loading },
  data() {
    return {
      
      openPunishDetails:false,
      punishNum:'',

      openDisposalDetails:false,
      disposalId:'',

      loading:false,

      checkedSearchTypes:[],  //    一键搜选择类型
      // keyValues:'',  // 搜索关键字
      splitSearchFlag: false, // 是否分词搜索 1分词 0 不分词
      searchTotal:0,
      searchKeys: this.$route.query.keyWord || '', // 一键搜关键字

      // searchTypes:this.$route.query.searchTypes,    // 一键搜索资源类型
      searchTypes:[],
      dataSearchShow: '1',

      // 搜索条件展开/收起控制
      showSearchConditions: false,

      //
      lineDataList:[],     // 搜索结果

      searchParams: {
        pageSize: 10,
        pageNum: 1,

      }
    };
  }, 
  created() {
    console.log('--created..... ');
    console.log('路由参数:', this.$route.query);
    this.loading = true;

    // 获取一键搜类型
    levelprotectSearchType({}).then(response=>{
        if(response.code=='200') {
          // 过滤掉不需要展示的字段
          const excludeLabels = ['单位主体信息表', '检查任务表', '检查任务结果表', '行政处罚表', '行政处置表']
          const filteredData = {}

          Object.keys(response.data).forEach(category => {
            filteredData[category] = response.data[category].filter(item =>
              !excludeLabels.includes(item.label)
            )
          })

          this.searchTypes = filteredData;

          // 如果有路由参数中的selectDocument，使用它；否则默认全选
          if (this.$route.query.selectDocument) {
            this.checkedSearchTypes = this.$route.query.selectDocument.split(',');
          } else {
            // 默认全部搜索类型选择
            for (var key in this.searchTypes) {
              if (this.searchTypes.hasOwnProperty(key)) {
                this.searchTypes[key].forEach(item=>{
                  this.checkedSearchTypes.push(item.code);
                });
              }
            }
          }

          // 如果有搜索关键词，自动执行搜索
          if (this.searchKeys) {
            this.search();
          } else {
            this.loading = false;
          }
        }
    }).catch((e) => {
        this.loading = false;
        console.log('--screenSearchType-- error =',e);
    });
  },
  mounted() {
    
    if(this.searchTypes.length<=0) {
      console.log('---', 'mounted...searchTypes=',this.searchTypes);    
    }
    // this.loading = true;
    // // 获取一键搜类型 
    // screenSearchType().then(response=>{
        
    //     if(response.code=='200') {
    //       this.searchTypes = response.data;    // 

    //       this.searchTypes.forEach(item=>{
    //         this.checkedSearchTypes.push(item.code);// 默认全部搜索类型选择 
    //       });

    //       // 搜索
    //       this.search();

    //     } 
    //     console.log('--screenSearchType== ',  response,'--searchTypes=',this.searchTypes);
    // }).catch(() => {
    //     this.loading = false;  
    // });
  },
  methods: {

    // 切换搜索条件显示/隐藏
    toggleSearchConditions() {
      this.showSearchConditions = !this.showSearchConditions;
    },
    

    // 处理详情页跳转
    handleDetailClick(item) {
      const tableType = item.命中表;
      // const id = item.ID || item['统一社会信用代码'];
      // if (!id) {
      //   console.warn('缺少ID字段，无法跳转到详情页id=', id);
      //   return;
      // }
      console.log('item====', item);
      let detailPath = '';
      switch (tableType) {
        case '电子屏信息表':
          detailPath = `/data-management/screen/detail/${item.ID}`;
          break;
        case '等保备案表':
          detailPath = `/data-management/levelprotect/detail/${item.ID}`;
          break;
        case '网站信息表':
          detailPath = `/data-management/website/detail/${item.ID}`;
          break;
        case 'app表':
          detailPath = `/data-management/website/app/detail/${stripHtmlTags(item['注册登记号'])}`;
          break;
        case '小程序表':
          detailPath = `/data-management/website/applet/detail/${stripHtmlTags(item['注册登记号'])}`;
          break;
        case '网吧信息表': 
          detailPath = `/data-management/netbar/detail/${stripHtmlTags(item['场所编码'])}`;
          break;
        case '运营商信息表':
          detailPath = `/data-management/operator/baseinfo/detail/${stripHtmlTags(item['统一社会信用代码'])}`;
          break;
        case '归属场所表':
          detailPath = `/data-management/screen/site/detail/${item.ID}`;
          break;
        case '责任单位表':
          detailPath = `/data-management/screen/enterprise/detail/${item.ID}`;
          break;
        case '联网系统表':
          detailPath = `/data-management/screen/netsystem/detail/${item.ID}`;
          break;
        case '重点区域表':
          detailPath = `/data-management/screen/keyarea/detail/${item.ID}`;
          break;
        case '专项表':
          detailPath = `/data-management/screen/special/detail/${item.ID}`;
          break;
        default:
          console.warn(`未知的表类型: ${tableType}`);
          return;
      }
      console.log('detailPath---------', detailPath)
      // 跳转到详情页
      this.$router.push(detailPath);
    },

    /** 行政处罚详情 */
    punishDetailClick(dataId) {
      // console.log('----punishDetailClick.dataId=', dataId);
      this.punishNum = dataId;// 
      this.openPunishDetails =true;// 显示

    },
    /** 行政处罚取消按钮 */
    punishCancel() {
       
      this.openPunishDetails = false;
    },
    /** 行政处置详情 */
    disposalDetailClick(dataId) {
      console.log('----disposalDetailClick.dataId=', dataId);
      this.disposalId = dataId;// 
      this.openDisposalDetails =true;// 显示

    },
    /** 行政处置取消按钮 */
    disposalCancel() {
       
      this.openDisposalDetails = false;
    },



    /** 根据关键字查询数据 加标题返回  */
    getDataItemTitle(jsonObj,keyName) {

      let strData = '';
      for (const key in jsonObj) {
        // console.log('----key, value ---', key, jsonObj[key]);
        if(keyName == key){
          strData = keyName+'：' + jsonObj[key];
        }
      }
      return strData;//

    }, 
    /** 根据关键字查询数据 返回*/
    getJsonDataItem(jsonObj,keyName) {

      let strData = '';
      for (const key in jsonObj) {
        // console.log('----key, value ---', key, jsonObj[key]);
        if(keyName == key){
					strData = jsonObj[key];
				}
      }
      return strData;//

    },
    /** 搜索记录点击处理 */
    searchItemDetailClick(newRoute) {
      console.log('----newRoute=',newRoute);
      // let targetRoute = this.$router.resolve({path:newRoute});
      // window.open(targetRoute.href,"_blank");
      // this.$router.push({path:newRoute});
      
    },
    //接收子组件分页值
    pageFun(data){
      // this.form.pageNum = data.pageNum
      // this.form.pageSize = data.pageSize
			// this.keyVlue = localStorage.getItem('vlue')
      this.searchParams.pageNum = data.pageNum,
      this.searchParams.pageSize = data.pageSize;

      this.search () ; // 获取列表
    },
    /** 搜索按钮 */
    searchClick() {
      if (!this.searchKeys.trim()) return
      this.searchParams.pageNum = 1;
      this.search () ; // 获取列表
    },
    /** 搜索 */
    search() {

      let strKeyWOrd = this.searchKeys; // 一键搜索关键字
      let searchIndexs ='';
      let splitFlag = 0;
      if(this.splitSearchFlag) {
        splitFlag =1;
      }

      // 允许没有关键词的搜索，注释掉这个限制
      // if( strKeyWOrd == '' || strKeyWOrd==null){
      //       this.$message({
      //               message:'请输入要搜索的关键字！',
      //               type:'warning'
      //           });
      //       return;
      // }

      if(this.checkedSearchTypes.length<=0) {
        this.$message({
                    message:'请要查询的范围！',
                    type:'warning'
                }); 
            return;
      }
      else {
        this.checkedSearchTypes.forEach(item=>{
          // console.log('----checkedSearchTypes--item =',item)
          searchIndexs +=item;
          searchIndexs +=",";
        });
        searchIndexs= searchIndexs.slice(0, -1);

      }

        let query ={
            pageSize:this.searchParams.pageSize,
            pageNum: this.searchParams.pageNum,
            selectDocument: searchIndexs,
            keyWord: strKeyWOrd,
            splitFlag : splitFlag
        };

        levelprotectSearchOneClick(query).then(response =>{
          // console.log('---screenSearchOneClick = ', response)
          this.lineDataList = []; // 初始化 
          this.searchTotal =0; 

          if(response.code=='200') { 
            this.lineDataList = response.rows;
            this.searchTotal = response.total;   // 记录总数
            // console.log('------this.lineDataList=', this.lineDataList); 
          }
          if(this.searchTotal>0) {
            this.dataSearchShow = '1'; // 有数据
          } else { 
            this.dataSearchShow = '2';//  没数据 
          }

          // console.log('------dataSearchShow= ', this.dataSearchShow);
        });

    },
    /** 全选 */
    selectAll() {
      // this.searchTypes.forEach(item => {
      //   this.checkedSearchTypes.push(item.code);
      // })
      for (var key in this.searchTypes) {
        if (this.searchTypes.hasOwnProperty(key)) { // 确保key是对象自身的属性 
            this.searchTypes[key].forEach(item=>{
              this.checkedSearchTypes.push(item.code);// 默认全部搜索类型选择 
            });
        }
      }

    },
    /** 清空 */
    clearEmpty() {
      this.checkedSearchTypes = [];
    },

  }
});

</script>

<style lang="scss" >
.app-container {
  background-color: #F6F7FB;
  padding: 0;
}
  em {
    font-style: normal;
    color: #FF7437;
  }

  .box{
    width: 100%;

  }
  .s-box{
    top: 212px;
    left: 54px;
    background: rgba(0, 0, 0, 0);
  }
  .top-div{
    position: relative;
    padding-top: 20px;
    padding-bottom: 20px;
    margin-bottom: 30px;
     background-image: url('../../assets/images/tz/usearch.png');
  background-size: cover;
    .page-box{
      width: 640px;
      display: flex;
      margin: 0 auto;
      align-items: center;
      ::v-deep .el-input--large .el-input__inner{
        height: 50px;
        line-height: 50px;
      } 
      .search-btn.el-button--text {
        color: #4584FF;
        font-size: 14px;
        line-height: 52px;
        margin-left: 20px;
      }
    }
    .search-total{
      position: absolute;
      top: 20px;
      right: 30px;
      font-size: 16px;
      color: #1890ff;
    }
  }

  .choose{
    width: 55%;
    overflow: hidden;
    margin: 0 auto;
    margin-top: 10px;
    margin-bottom: 5px;
    span{
      display: inline-block; 
      /* margin-right: 12px; */
      /* width: 77px; */
      font-size: 14px;
      text-align: center;
    }
  }

.el-checkbox{
  margin: 6px 5px;
}
/* .el-input{
  width: 612px;
  margin-top: 20px; 
} */

  /** 一键搜索没有数据的样式 */
  .noData{
    background: #f2f7ff;
    overflow: hidden;
    img{
      display: block;
      width: 269px;
      height: 226px;
      margin: 0 auto;
      margin-top: 143px;
    }
  }

/** 搜索结果表格样式 */
.table{
  width: 100%;
  margin: 0 auto;

  .search-detail-line {
    width: 95%;
    min-height: 110px;
    margin: 20px auto;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
    position: relative;
    overflow: hidden;
    font-size: 14px;
    line-height: 30px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }
    /* 数据类型标签样式 */
    .data-type-tag {
      position: absolute;
    top: 0px;
    right: 0;
    background: #4584FF;
    color: #fff;
    padding: 2px 18px;
    font-size: 14px;
    border-radius: 20px 0 0 20px;
    z-index: 10;
    letter-spacing: 1px;
    
    }
    .search-detail-title{
      margin-top: 18px;
      font-style: normal;
      span {
        &:nth-of-type(1) {
          display: inline-block;
          font-size: 18px;
          font-weight: 700;
          color: #376FFF; 
          cursor: pointer;
        }
        &:nth-of-type(2) {
          position: relative;
          top: -1px;
          margin-left: 20px;
          font-size: 14px;
        }
      }
    }
    .creditCode{
      position: absolute;
      top: 20px;
      left: 300px;
			span{
				margin-right: 10px;
				font-size: 15px;
			}
    }
    .cen{
      margin-top: 14px;
			width: 84%;
      span{
        font-size: 16px;
        font-weight: 400;
        color: #333333; 
        vertical-align: middle;
        &.mingzhongziduanneirong {
          color: #FF7437;
        }
      }
    }
    .cen span:nth-of-type(1){
       font-size: 13px;
            font-weight: 400;
            position: absolute;
            bottom: 10px;
            right: 14px;
            color: #999;

    }
 
    .cont{
      font-size: 16px;
      font-weight: 400;
      color: #333333;
      margin-top: 14px;
      margin-bottom: 0px;
    }
    .detail{
      position: absolute;
      width: 89px;
      background: #1890ff;
      border-radius: 3px;
      text-align: center;
      padding: 4px;
      color: #fff;
      right: 7px;
      top: 49px;
      font-size: 14px;
      cursor: pointer;
    }
    .xinxi{
      position: absolute;
      font-size: 16px;
      font-weight: 400;
      color: #333333;
      right: 4px;
      top: 18px;
    }
  }
}
  /* .comm-page{
    text-align: center; margin:0 auto; min-height:50px;
    margin-top: 20px;
  } */

.newAdded{
  padding: 3px 0;
  color: #333333;
  font-size: 15px;
  overflow: hidden;
	width: 84%;
  div{ 
    margin-right: 28px;
    margin-bottom: 5px;
    float: left;
		span{
			margin-right: 15px;
			margin-top: 6px;
			display: inline-block;
		}
  }
	/* .suoshufenju{
		position: absolute;
		top: 20px;
		left: 800px;
	} */
}
.newAdded div:nth-of-type(1){
  margin-left: 0;
}
.clear{
  clear: both;
}
.zzdx{
  margin-bottom: 10px;
  float: left;
  margin-right: 31px;
  color: #333333;
      font-size: 15px;
}

.Loading{
  border: solid 1px red;
}

.mingzhongziduanneirong{
  margin-left: 10px;
  width: 620px;
  display: inline-block;
  white-space:nowrap; overflow:hidden; text-overflow:ellipsis
}
@media screen and (min-width:1281px){
  .mingzhongziduanneirong{
    width: 64%;
    display: inline-block;
    white-space:nowrap; overflow:hidden; text-overflow:ellipsis;
  }

}


</style>