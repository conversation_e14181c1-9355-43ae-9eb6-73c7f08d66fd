<template>
  <div class="app-container">
    <div class="screen-management-layout">
      <!-- 左侧导航菜单 -->
      <basic-data-sidebar />

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <!-- 搜索组件 -->
        <common-search
          :search-config="searchConfig"
          :advanced-fields="advancedFields"
          :initial-form="searchForm"
          @search="handleSearch"
          @reset="handleReset"
        />

    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['website:app:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            icon="el-icon-upload2"
            size="mini"
            @click="handleImport"
            v-hasPermi="['website:app:import']"
          >导入</el-button>
        </el-col>
        <!-- <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['website:app:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['website:app:remove']"
          >删除</el-button>
        </el-col> -->
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['website:app:export']"
          >导出</el-button>
        </el-col>
        <el-col :span="1.5">
           <el-button
            type="success"
            icon="el-icon-plus"
            size="mini"
            @click="handleReminder"
            v-hasPermi="['website:app:add']"
          >设置到期提醒</el-button>
        </el-col>

        <div class="toolbar-right">
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </div>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="appList"
        @selection-change="handleSelectionChange"
        :header-cell-style="{ backgroundColor: '#EBF2FF' }"
        border
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="注册登记号" align="center" prop="registerNum" />
        <el-table-column label="APP名称" align="center"  prop="appName" width="120">
          <template slot-scope="scope">
            <div class="site-name-container">
              <span>{{ scope.row.appName }}</span>
              <span
                v-if="scope.row.dataUpdateStatus === 1"
                class="update-badge pending"
              >待更新</span>
              <span
                v-else-if="scope.row.dataUpdateStatus > 1"
                class="update-badge overdue"
              >已超期</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="统一社会信用代码" align="center" prop="creditCode" />
        <el-table-column label="创建时间" align="center" prop="openDate" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.openDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="LOGO" align="center" prop="logo" width="100">
          <template slot-scope="scope">
            <image-preview :src="scope.row.logo" :width="50" :height="50"/>
          </template>
        </el-table-column>
        <el-table-column label="运行平台" align="center" prop="runPlatform">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.ws_run_platform" :value="scope.row.runPlatform"/>
          </template>
        </el-table-column>
        <el-table-column label="市值" align="center" prop="marketValue" width="120"/>
        <el-table-column label="月活用户数量" align="center" prop="monthUserNumber" />
        <el-table-column label="注册用户总量" align="center" prop="registerUserNumber" />
        <el-table-column label="更新时间" align="center" prop="updateDate" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.updateDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="APP分级" align="center" prop="appRating">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.ws_website_rating" :value="scope.row.appRating"/>
          </template>
        </el-table-column>
        <el-table-column label="安全负责人姓名" align="center" prop="safeManagerName" />
        <el-table-column label="安全负责人手机号" align="center" prop="safeManagerPhone" />
        <el-table-column label="应急联络人姓名" align="center" prop="emergencyContactName" />
        <el-table-column label="应急联络人手机号" align="center" prop="emergencyContactPhone" />
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.ws_status" :value="scope.row.status"/>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleDetail(scope.row)"
              v-hasPermi="['website:app:query']"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleEdit(scope.row)"
              v-hasPermi="['website:app:edit']"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['website:app:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".zip"
        :headers="upload.headers"
        :action="upload.url + '?previewUrl=' + upload.previewUrl"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入zip格式文件。</span>
          <el-link type="danger" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
      </div>
    </div>

          <!-- 导入对话框 -->
          <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
            <el-upload
              ref="upload"
              :limit="1"
              accept=".zip"
              :headers="upload.headers"
              :action="upload.url + '?previewUrl=' + upload.previewUrl"
              :disabled="upload.isUploading"
              :on-progress="handleFileUploadProgress"
              :on-success="handleFileSuccess"
              :auto-upload="false"
              drag
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <div class="el-upload__tip text-center" slot="tip">
                <span>仅允许导入zip格式文件。</span>
                <el-link type="danger" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
              </div>
            </el-upload>
            <div slot="footer" class="dialog-footer">
              <el-button type="primary" @click="submitFileForm">确 定</el-button>
              <el-button @click="upload.open = false">取 消</el-button>
            </div>
          </el-dialog>

    <!-- 数据更新提醒组件 -->
    <data-update-reminder
      :visible.sync="reminderVisible"
      :biz-type="'APP'"
    />
    </div>
  </div>
</template>

<script>
import { listApp, delApp } from "@/api/website/app";
import { getToken } from "@/utils/auth";
import BasicDataSidebar from '@/components/BasicDataSidebar'
import CommonSearch from '@/components/CommonSearch'
import DataUpdateReminder from '@/components/DataUpdateReminder'

export default {
  name: "AppIndex",
  dicts: ['ws_website_rating', 'ws_internet_service_a', 'ws_internet_service_c', 'ws_internet_service_b', 'ws_run_platform', 'sys_yes_no', 'ws_status', 'ws_internet_service_z', 'certificate_type', 'ws_internet_service_e', 'ws_internet_service_d', 'ws_internet_service_g', 'ws_internet_service_f', 'ws_internet_service_i', 'ws_internet_service_h'],
  components: { BasicDataSidebar, CommonSearch, DataUpdateReminder },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // APP信息管理表格数据
      appList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 提醒组件显示状态
      reminderVisible: false,

      // 搜索配置
      searchConfig: {
        label: '注册登记号',
        key: 'registerNum',
        placeholder: '请输入注册登记号'
      },

      // 高级搜索字段
      advancedFields: [],

      // 搜索表单
      searchForm: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        registerNum: null,
        appName: null,
        creditCode: null,
        runPlatform: null,
        monthUserNumber: null,
        registerUserNumber: null,
        updateDate: null,
        appRating: null,
        safeManagerName: null,
        safeManagerPhone: null,
        emergencyContactName: null,
        emergencyContactPhone: null,
        status: null,
      },
      // 导入参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        previewUrl: process.env.VUE_APP_BASE_API + "/file/download/",
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/website/app/importData"
      }
    };
  },
   watch: {
    // 监听字典数据变化
    'dict.type': {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.initAdvancedFields();
        }
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.getList();
    this.initAdvancedFields();
  },
   mounted() {
    // 确保字典数据加载完成后再初始化高级搜索字段
    this.$nextTick(() => {
      this.initAdvancedFields();
    });
  },
  methods: {
    // 设置提醒
    handleReminder() {
      this.reminderVisible = true;
    },
    // 初始化高级搜索字段
    initAdvancedFields() {
     // 检查关键字典数据是否已加载 ws_run_platform    ws_website_rating   ws_status
      const hasRequiredDicts = this.dict && this.dict.type &&
                              this.dict.type.ws_website_rating &&
                               this.dict.type.ws_run_platform &&
                               this.dict.type.ws_status;
                               
      if(hasRequiredDicts) {
        this.advancedFields = [
        {
          label: 'APP名称',
          key: 'appName',
          type: 'input',
          placeholder: '请输入APP名称',
          span: 6
        },
        {
          label: '统一社会信用代码',
          key: 'creditCode',
          type: 'input',
          placeholder: '请输入统一社会信用代码',
          span: 6
        },
        {
          label: '运行平台',
          key: 'runPlatform',
          type: 'select',
          placeholder: '请选择运行平台',
          span: 6,
          options: (this.dict && this.dict.type && this.dict.type.ws_run_platform) ? this.dict.type.ws_run_platform.map(item => ({
            label: item.label,
            value: item.value
          })) : []
        },
        {
          label: '月活用户数量',
          key: 'monthUserNumber',
          type: 'input',
          placeholder: '请输入月活用户数量',
          span: 6
        },
        {
          label: '注册用户总量',
          key: 'registerUserNumber',
          type: 'input',
          placeholder: '请输入注册用户总量',
          span: 6
        },
        {
          label: 'APP分级',
          key: 'appRating',
          type: 'select',
          placeholder: '请选择APP分级',
          span: 6,
          options: (this.dict && this.dict.type && this.dict.type.ws_website_rating) ? this.dict.type.ws_website_rating.map(item => ({
            label: item.label,
            value: item.value
          })) : []
        },
        {
          label: '安全负责人姓名',
          key: 'safeManagerName',
          type: 'input',
          placeholder: '请输入安全负责人姓名',
          span: 6
        },
        {
          label: '安全负责人手机号',
          key: 'safeManagerPhone',
          type: 'input',
          placeholder: '请输入安全负责人手机号',
          span: 6
        },
        {
          label: '应急联络人姓名',
          key: 'emergencyContactName',
          type: 'input',
          placeholder: '请输入应急联络人姓名',
          span: 6
        },
        {
          label: '应急联络人手机号',
          key: 'emergencyContactPhone',
          type: 'input',
          placeholder: '请输入应急联络人手机号',
          span: 6
        },
        {
          label: '状态',
          key: 'status',
          type: 'select',
          placeholder: '请选择状态',
          span: 6,
          options: (this.dict && this.dict.type && this.dict.type.ws_status) ? this.dict.type.ws_status.map(item => ({
            label: item.label,
            value: item.value
          })) : []
        }
      ];
      }else {
        setTimeout(() => {
          this.initAdvancedFields();
        }, 100);
      }
      
    },
    /** 查询APP信息管理列表 */
    getList() {
      this.loading = true;
      listApp(this.queryParams).then(response => {
        this.appList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    // 处理搜索
    handleSearch(searchForm) {
      this.queryParams = {
        ...this.queryParams,
        ...searchForm,
        pageNum: 1
      };
      this.getList();
    },

    // 处理重置
    handleReset(searchForm) {
      this.searchForm = searchForm;
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        ...searchForm
      };
      this.getList();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.registerNum)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push("/data-management/website/app/add");
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.$router.push(`/data-management/website/app/detail/${row.registerNum}`);
    },
    /** 修改按钮操作 */
    handleEdit(row) {
      this.$router.push(`/data-management/website/app/edit/${row.registerNum}`);
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const registerNum = row.registerNum || this.ids
      this.$router.push(`/data-management/website/app/edit/${registerNum}`);
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const registerNums = row.registerNum || this.ids;
      this.$modal.confirm('是否确认删除APP信息管理编号为"' + registerNums + '"的数据项？').then(function() {
        return delApp(registerNums);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('website/app/export', {
        ...this.queryParams
      }, `APP信息管理_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "数据导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
   async importTemplate() {
      const url = process.env.VUE_APP_BASE_API+'/file/download/'+vueConfig.template_path_app; // 替换成你要下载的文件的URL
      const fileName = 'APP导入模板.xlsx'; // 自定义文件名

      const response = await fetch(url);
      const blob = await response.blob();
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .screen-management-layout {
    display: flex;
    gap: 20px;
    height: 100%;

    .content-area {
      flex: 1;
      min-width: 0;
    }
  }

  .action-bar {
    background: white;
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
  }

  .table-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
  }

  .toolbar-right {
    float: right;
    display: flex;
    align-items: center;
    gap: 12px;
  }
}

// 全局样式：确保表单标签不换行
::v-deep .el-form-item__label {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

// APP名称容器样式


// 名称角标样式
.site-name-container {
  position: relative;
  display: inline-block;
  padding: 12px 12px 0 0;
  .update-badge {
    position: absolute;
    top: -2px;
    right: -10px;
    font-size: 10px;
    padding: 1px 4px;
    border-radius: 2px;
    // color: white;
    white-space: nowrap;
    z-index: 1;

    &.pending {
      color: #FE9400;
    }

    &.overdue {
      color: #FF2A2A;
    }
  }
}
</style>
