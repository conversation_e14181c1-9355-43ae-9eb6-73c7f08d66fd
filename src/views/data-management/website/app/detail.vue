<template>
<div style="display:flex;background: #f5f7fa;">
  <section class="app-container" :class="{ 'expanded': isRightCollapsed }" :style="{ width: `calc(100% - ${rightPanelWidth}px)` }">
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">APP详情</h2>
        <div class="header-actions">
          <el-button icon="el-icon-arrow-left" @click="goBack" size="small">返回</el-button>
        </div>
      </div>
    </div>

    <div class="detail-container" v-loading="loading">
      <!-- 主体信息模块 -->
      <div id="basic-info" class="detail-section">
        <div class="section-header" @click="toggleSection('basic')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">主体信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.basic ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.basic ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>

        <div v-show="!sectionCollapsed.basic" class="section-content">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="APP名称">
              {{ form.appName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="统一社会信用代码">
              {{ form.creditCode || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="注册登记号">
              {{ form.registerNum || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="运行平台">
              <dict-tag :options="dict.type.ws_run_platform" :value="form.runPlatform"/>
            </el-descriptions-item>
            <el-descriptions-item label="应用包名">
              {{ form.packageName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="是否涉及前置审核内容">
              <dict-tag :options="dict.type.sys_yes_no" :value="form.preAuditContentFlag"/>
            </el-descriptions-item>
            <el-descriptions-item label="注册用户总量">
              {{ form.registerUserNumber || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="月活用户数量">
              {{ form.monthUserNumber || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="市值">
              {{ form.marketValue || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="APP分级">
              <dict-tag :options="dict.type.ws_website_rating" :value="form.appRating"/>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ form.openDate || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="更新时间">
              {{ form.updateDate || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <dict-tag :options="dict.type.ws_status" :value="form.status"/>
            </el-descriptions-item>
            <el-descriptions-item label="功能描述" :span="2">
              {{ form.description || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="标签">
              {{ form.label || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="LOGO">
              <div v-if="!form.logo">-</div>
              <div v-else>
                <image-preview :src="form.logo" :width="50" :height="50"/>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="运行截图">
              <div v-if="!form.screenshot">-</div>
              <div v-else>
                <image-preview
                  style="margin-right: 10px;"
                  v-for="item in form.screenshot.split(',')"
                  :key="item"
                  :src="item"
                  :width="100"
                  :height="100"
                />
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 互联网交互服务模块 -->
      <div id="internet-info" class="detail-section">
        <div class="section-header" @click="toggleSection('internet')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">互联网交互服务</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.internet ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.internet ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>

        <div v-show="!sectionCollapsed.internet" class="section-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="提供互联网交互服务">
              <dict-tag :options="dict.type.sys_yes_no" :value="form.provideInternetServiceFlag"/>
            </el-descriptions-item>
            <template v-if="form.provideInternetServiceFlag == 'Y'">
              <el-descriptions-item label="网络基础类A">
                <dict-tag :options="dict.type.ws_internet_service_a" :value="form.internetServiceA"/>
              </el-descriptions-item>
              <el-descriptions-item label="网络销售类B">
                <!-- 仍然没有换行超出当前列宽了 -->
                <dict-tag :options="dict.type.ws_internet_service_b" :value="form.internetServiceB"/>
              </el-descriptions-item>
              <el-descriptions-item label="生活服务类C">
                <dict-tag :options="dict.type.ws_internet_service_c" :value="form.internetServiceC"/>
              </el-descriptions-item>
              <el-descriptions-item label="社交文娱类D">
                <!-- 仍然没有换行超出当前列宽了 -->
                <dict-tag :options="dict.type.ws_internet_service_d" :value="form.internetServiceD"/>
              </el-descriptions-item>
              <el-descriptions-item label="信息咨询类E">
                <dict-tag :options="dict.type.ws_internet_service_e" :value="form.internetServiceE"/>
              </el-descriptions-item>
              <el-descriptions-item label="金融服务类F">
                <!-- 仍然没有换行超出当前列宽了 -->
                <dict-tag :options="dict.type.ws_internet_service_f" :value="form.internetServiceF"/>
              </el-descriptions-item>
              <el-descriptions-item label="计算机应用类G">
                <dict-tag :options="dict.type.ws_internet_service_g" :value="form.internetServiceG"/>
              </el-descriptions-item>
              <el-descriptions-item label="上网场所类H">
                <dict-tag :options="dict.type.ws_internet_service_h" :value="form.internetServiceH"/>
              </el-descriptions-item>
              <el-descriptions-item label="联网单位类I">
                <dict-tag :options="dict.type.ws_internet_service_i" :value="form.internetServiceI"/>
              </el-descriptions-item>
              <el-descriptions-item label="其他Z">
                <dict-tag :options="dict.type.ws_internet_service_z" :value="form.internetServiceZ"/>
              </el-descriptions-item>
            </template>
          </el-descriptions>
        </div>
      </div>

      <!-- 安全负责人模块 -->
      <div id="safety-info" class="detail-section">
        <div class="section-header" @click="toggleSection('safety')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">安全负责人</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.safety ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.safety ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>

        <div v-show="!sectionCollapsed.safety" class="section-content">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="安全负责人姓名">
              {{ form.safeManagerName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="安全负责人证件类型">
              <dict-tag :options="dict.type.certificate_type" :value="form.safeManagerIdType"/>
            </el-descriptions-item>
            <el-descriptions-item label="安全负责人证件号码">
              {{ form.safeManagerIdNumber || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="安全负责人证件有效期">
              {{ form.safeManagerIdDate || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="安全负责人手机号">
              {{ form.safeManagerPhone || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="安全负责人电子邮箱">
              {{ form.safeManagerEmail || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 应急联络人模块 -->
      <div id="emergency-info" class="detail-section">
        <div class="section-header" @click="toggleSection('emergency')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">应急联络人</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.emergency ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.emergency ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>

        <div v-show="!sectionCollapsed.emergency" class="section-content">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="应急联络人姓名">
              {{ form.emergencyContactName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="应急联络人证件类型">
              <dict-tag :options="dict.type.certificate_type" :value="form.emergencyContactIdType"/>
            </el-descriptions-item>
            <el-descriptions-item label="应急联络人证件号码">
              {{ form.emergencyContactIdNumber || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="应急联络人证件有效期">
              {{ form.emergencyContactIdDate || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="应急联络人手机号">
              {{ form.emergencyContactPhone || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="应急联络人电子邮箱">
              {{ form.emergencyContactEmail || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </div>
  </section>

  <!-- 右侧数据维护记录区域 -->
  <collapsible-detail-right
    @collapse-change="handleRightCollapseChange"
    @width-change="handleRightWidthChange"
  >
    <!-- 锚点导航组件 -->
    <template #anchor>
      <anchor-navigation
        :anchor-items="anchorItems"
        @anchor-change="handleAnchorChange"
      />
    </template>

    <!-- 数据维护时间线 -->
    <template #timeline>
      <data-maintenance-timeline
        :biz-type="'APP'"
        :biz-id="form.registerNum"
        v-if="form.registerNum"
      />
    </template>
  </collapsible-detail-right>
</div>
</template>

<script>
import { getApp } from "@/api/website/app";
import BasicDataSidebar from '@/components/BasicDataSidebar'
import DataMaintenanceTimeline from '@/components/DataMaintenanceTimeline'
import AnchorNavigation from '@/components/AnchorNavigation'
import CollapsibleDetailRight from '@/components/CollapsibleDetailRight'

export default {
  name: 'AppDetail',
  dicts: ['ws_website_rating', 'ws_internet_service_a', 'ws_internet_service_c', 'ws_internet_service_b', 'ws_run_platform', 'sys_yes_no', 'ws_status', 'ws_internet_service_z', 'certificate_type', 'ws_internet_service_e', 'ws_internet_service_d', 'ws_internet_service_g', 'ws_internet_service_f', 'ws_internet_service_i', 'ws_internet_service_h'],
  components: {
    BasicDataSidebar,
    DataMaintenanceTimeline,
    AnchorNavigation,
    CollapsibleDetailRight
  },
  data() {
    return {
      loading: false,
      registerNum: undefined,
      // 表单参数
      form: {},

      // 模块折叠状态
      sectionCollapsed: {
        basic: false,
        internet: false,
        safety: false,
        emergency: false
      },

      // 锚点导航配置
      anchorItems: [
        { id: 'basic-info', label: '主体信息' },
        { id: 'internet-info', label: '互联网交互服务' },
        { id: 'safety-info', label: '安全负责人' },
        { id: 'emergency-info', label: '应急联络人' }
      ],

      // 右侧面板收起状态
      isRightCollapsed: false,
      // 右侧面板宽度
      rightPanelWidth: 360
    };
  },
  created() {
    this.registerNum = this.$route.params && this.$route.params.id;
    this.getDetail();
  },
  methods: {
    // 表单重置
    reset() {
      this.form = {
        registerNum: null,
        appName: null,
        creditCode: null,
        openDate: null,
        logo: null,
        runPlatform: null,
        packageName: null,
        marketValue: null,
        monthUserNumber: null,
        registerUserNumber: null,
        updateDate: null,
        appRating: null,
        preAuditContentFlag: null,
        screenshot: null,
        description: null,
        label: null,
        provideInternetServiceFlag: null,
        internetServiceA: null,
        internetServiceB: null,
        internetServiceC: null,
        internetServiceD: null,
        internetServiceE: null,
        internetServiceF: null,
        internetServiceG: null,
        internetServiceH: null,
        internetServiceI: null,
        internetServiceZ: null,
        safeManagerName: null,
        safeManagerIdType: null,
        safeManagerIdNumber: null,
        safeManagerIdDate: null,
        safeManagerPhone: null,
        safeManagerEmail: null,
        emergencyContactName: null,
        emergencyContactIdType: null,
        emergencyContactIdNumber: null,
        emergencyContactIdDate: null,
        emergencyContactPhone: null,
        emergencyContactEmail: null,
        status: null
      };
    },
    /** 获取详情 */
    getDetail() {
      this.reset();
      if (!this.registerNum) {
        return;
      }
      getApp(this.registerNum).then(response => {
        this.form = response.data;
      });
    },
    /** 返回按钮 */
    handleClose() {
      this.$router.push("/data-management/website/app");
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 切换模块折叠状态
    toggleSection(section) {
      this.sectionCollapsed[section] = !this.sectionCollapsed[section];
    },

    // 处理锚点变化
    handleAnchorChange(anchorId) {
      console.log('当前激活的锚点:', anchorId);
    },

    // 处理右侧面板收起展开
    handleRightCollapseChange(isCollapsed) {
      this.isRightCollapsed = isCollapsed;
    },

    // 处理右侧面板宽度变化
    handleRightWidthChange(width) {
      this.rightPanelWidth = width;
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
  transition: width 0.3s ease;

  &.expanded {
    width: calc(100% - 60px) !important;
  }

   .page-header {
     width: 99%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      justify-content: space-between;
      .page-title {
        margin: 0;
        font-size: 18px;
        color: #303133;
      }
    }
  }

  .detail-container {
    width: 99%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;

    .detail-section {
      margin-bottom: 24px;

      .section-header {
        background-color: #f6f7fb;
        padding: 12px 16px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 4px;
        margin-bottom: 16px;

        .section-title {
          display: flex;
          align-items: center;
          gap: 8px;

          .title-line {
            width: 3px;
            height: 16px;
            background-color: #4584FF;
          }

          .title-text {
            font-size: 16px;
            font-weight: 600;
            color: #4584FF;
          }
        }

        .section-toggle {
          display: flex;
          align-items: center;
          gap: 4px;
          color: #4584FF;
          font-size: 14px;

          .toggle-text {
            font-size: 14px;
          }

          i {
            font-size: 12px;
          }
        }

        &:hover {
          background-color: #f0f2f5;
        }
      }

      .section-content {
        padding: 0 16px;
      }
    }
  }
}



// 描述列表样式优化 - 解决内容溢出问题
// 使用多层选择器确保样式生效
::v-deep .el-descriptions {
  table-layout: fixed !important;
  width: 100% !important;

  // Element UI 2.x 版本的类名结构
  .el-descriptions__body .el-descriptions__table,
  .el-descriptions-item__cell {
    table-layout: fixed !important;
    width: 100% !important;
  }

  // 标签列样式 - 兼容不同版本的类名
  .el-descriptions__cell.is-bordered-label,
  .el-descriptions-item__cell.is-bordered-label {
    white-space: nowrap !important;
    width: 150px !important;
    min-width: 150px !important;
    max-width: 150px !important;
    padding: 12px 8px !important;
    background-color: #FAFAFA !important;
    font-weight: 500 !important;
    color: #606266 !important;
    text-align: right !important;
    vertical-align: top !important;
  }

  // 内容列样式 - 兼容不同版本的类名
  .el-descriptions__cell.is-bordered-content,
  .el-descriptions-item__cell:not(.is-bordered-label) {
    word-break: break-all !important;
    word-wrap: break-word !important;
    white-space: normal !important;
    padding: 12px 8px !important;
    background-color: #FFFFFF !important;
    color: #303133 !important;
    vertical-align: top !important;
    max-width: calc(100% - 150px) !important;
  }
}

// 专门针对dict-tag组件的样式
::v-deep .el-descriptions .el-descriptions-item__cell:not(.is-bordered-label) {
  // dict-tag组件的直接容器
  > div {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 4px 8px !important;
    align-items: flex-start !important;
    line-height: 1.5 !important;
    max-width: 100% !important;
    width: 100% !important;
  }
}

// 最高优先级的el-tag样式
::v-deep .el-descriptions .el-tag {
  display: inline-block !important;
  margin: 0 4px 4px 0 !important;
  white-space: normal !important;
  word-break: break-all !important;
  word-wrap: break-word !important;
  max-width: 100% !important;
  width: auto !important;
  height: auto !important;
  min-height: 24px !important;
  line-height: 1.4 !important;
  padding: 6px 10px !important;
  vertical-align: top !important;
  box-sizing: border-box !important;

  // 标签内容换行处理
  span {
    display: inline-block !important;
    word-break: break-all !important;
    word-wrap: break-word !important;
    white-space: normal !important;
  }
}

// 针对互联网交互服务模块的特殊优化
#internet-info ::v-deep .el-descriptions {
  .el-descriptions-item__cell:not(.is-bordered-label) {
    min-height: 40px !important;

    // 确保多个标签能够正确换行显示
    > div {
      max-width: 100% !important;
      width: 100% !important;
    }

    .el-tag {
      margin: 0 4px 6px 0 !important;
      max-width: calc(100% - 8px) !important;

      // 长文本标签的特殊处理
      &.el-tag--info,
      &.el-tag--success,
      &.el-tag--warning,
      &.el-tag--danger,
      &.el-tag--primary,
      &:not([class*="el-tag--"]) {
        white-space: normal !important;
        height: auto !important;
        padding: 8px 12px !important;
        line-height: 1.3 !important;
        word-break: break-all !important;
        word-wrap: break-word !important;
        display: inline-block !important;
        vertical-align: top !important;

        span {
          white-space: normal !important;
          word-break: break-all !important;
          word-wrap: break-word !important;
        }
      }
    }
  }
}

// 额外的强制样式，确保长文本标签换行
.detail-section ::v-deep .el-descriptions .el-tag {
  white-space: normal !important;
  word-break: break-all !important;
  word-wrap: break-word !important;
  height: auto !important;
  line-height: 1.4 !important;
  padding: 6px 10px !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

// 最终的强制样式 - 确保所有情况下都能换行
::v-deep .el-tag {
  &[class*="el-tag"] {
    white-space: normal !important;
    word-break: break-all !important;
    word-wrap: break-word !important;
    height: auto !important;
    min-height: 24px !important;
    line-height: 1.4 !important;
    display: inline-block !important;
    vertical-align: top !important;
    max-width: 100% !important;

    // 内部文本也要换行
    * {
      white-space: normal !important;
      word-break: break-all !important;
      word-wrap: break-word !important;
    }
  }
}
</style>
