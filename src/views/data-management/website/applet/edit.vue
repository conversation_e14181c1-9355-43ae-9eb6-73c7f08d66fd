<template>
  <div class="app-container">
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">编辑小程序信息</h2>
        <el-button icon="el-icon-arrow-left" size="small" @click="goBack">返回</el-button>
      </div>
    </div>

    <div class="form-container">
      <el-form ref="form" :model="form" :rules="rules" label-width="165px">
        <!-- 主体信息模块 -->
        <div class="form-section">
          <div class="section-header" @click="toggleSection('basic')">
            <div class="section-title">
              <div class="title-line"></div>
              <span class="title-text">主体信息</span>
            </div>
            <div class="section-toggle">
              <span class="toggle-text">{{ sectionCollapsed.basic ? '展开' : '收起' }}</span>
              <i :class="sectionCollapsed.basic ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            </div>
          </div>

          <div v-show="!sectionCollapsed.basic" class="section-content">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="小程序名称" prop="appletName">
                      <el-input v-model="form.appletName" placeholder="请输入小程序名称" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="统一社会信用代码" prop="creditCode">
                      <el-input v-model="form.creditCode" placeholder="请输入统一社会信用代码" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="注册登记号" prop="registerNum">
                      <el-input v-model="form.registerNum" placeholder="请输入注册登记号" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="工信部备案号" prop="filingNumber">
                      <el-input v-model="form.filingNumber" placeholder="请输入工信部备案号" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="IP地址" prop="ipAddress">
                      <el-input v-model="form.ipAddress" placeholder="请输入IP地址" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="托管服务器信息" prop="serverType">
                      <el-select v-model="form.serverType" placeholder="请选择托管服务器信息" style="width: 100%">
                        <el-option
                          v-for="dict in dict.type.ws_server_type"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="市值" prop="marketValue">
                      <el-input v-model="form.marketValue" placeholder="请输入市值" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="月活用户数量" prop="monthUserNumber">
                      <el-input v-model="form.monthUserNumber" placeholder="请输入月活用户数量" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="注册用户总量" prop="registerUserNumber">
                      <el-input v-model="form.registerUserNumber" placeholder="请输入注册用户总量" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="小程序分级" prop="appletRating">
                      <el-select v-model="form.appletRating" style="width: 100%;" placeholder="请选择小程序分级">
                        <el-option
                          v-for="dict in dict.type.ws_website_rating"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="是否涉及前置审核内容" prop="preAuditContentFlag">
                      <el-select v-model="form.preAuditContentFlag" style="width: 100%;" placeholder="请选择是否涉及前置审核内容">
                        <el-option
                          v-for="dict in dict.type.sys_yes_no"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="标签" prop="label">
                      <el-input v-model="form.label" placeholder="请输入标签" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="创建时间" prop="openDate">
                      <el-date-picker
                        v-model="form.openDate"
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="请选择创建时间"
                        style="width: 100%"
                        clearable
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="状态" prop="status">
                      <el-select v-model="form.status" style="width: 100%;" placeholder="请选择状态">
                        <el-option
                          v-for="dict in dict.type.ws_status"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="运行截图" prop="screenshot">
                      <image-upload v-model="form.screenshot" :limit="5"/>
                    </el-form-item>
                  </el-col>
                </el-row>
                
          </div>
        </div>

        <!-- 安全负责人模块 -->
        <div class="form-section">
          <div class="section-header" @click="toggleSection('safety')">
            <div class="section-title">
              <div class="title-line"></div>
              <span class="title-text">安全负责人</span>
            </div>
            <div class="section-toggle">
              <span class="toggle-text">{{ sectionCollapsed.safety ? '展开' : '收起' }}</span>
              <i :class="sectionCollapsed.safety ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            </div>
          </div>

          <div v-show="!sectionCollapsed.safety" class="section-content">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="安全负责人姓名" prop="safeManagerName">
                      <el-input v-model="form.safeManagerName" placeholder="安全负责人姓名" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="安全负责人证件类型" prop="safeManagerIdType">
                      <el-select v-model="form.safeManagerIdType" style="width: 100%;" placeholder="请选择安全负责人证件类型">
                        <el-option
                          v-for="dict in dict.type.certificate_type"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="安全负责人证件号码" prop="safeManagerIdNumber">
                      <el-input v-model="form.safeManagerIdNumber" placeholder="安全负责人证件号码" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="安全负责人证件有效期" prop="safeManagerIdDate">
                      <el-input v-model="form.safeManagerIdDate" placeholder="安全负责人证件有效期"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="安全负责人手机号" prop="safeManagerPhone">
                      <el-input v-model="form.safeManagerPhone" placeholder="安全负责人手机号" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="安全负责人电子邮箱" prop="safeManagerEmail">
                      <el-input v-model="form.safeManagerEmail" placeholder="请输入安全负责人电子邮箱" />
                    </el-form-item>
                  </el-col>
                </el-row>
          </div>
        </div>

        <!-- 应急联络人模块 -->
        <div class="form-section">
          <div class="section-header" @click="toggleSection('emergency')">
            <div class="section-title">
              <div class="title-line"></div>
              <span class="title-text">应急联络人</span>
            </div>
            <div class="section-toggle">
              <span class="toggle-text">{{ sectionCollapsed.emergency ? '展开' : '收起' }}</span>
              <i :class="sectionCollapsed.emergency ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            </div>
          </div>

          <div v-show="!sectionCollapsed.emergency" class="section-content">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="应急联络人姓名" prop="emergencyContactName">
                      <el-input v-model="form.emergencyContactName" placeholder="应急联络人姓名" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="应急联络人证件类型" prop="emergencyContactIdType">
                      <el-select v-model="form.emergencyContactIdType" style="width: 100%;" placeholder="请选择应急联络人证件类型">
                        <el-option
                          v-for="dict in dict.type.certificate_type"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="应急联络人证件号码" prop="emergencyContactIdNumber">
                      <el-input v-model="form.emergencyContactIdNumber" placeholder="应急联络人证件号码" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="应急联络人证件有效期" prop="emergencyContactIdDate">
                      <el-input v-model="form.emergencyContactIdDate" placeholder="应急联络人证件有效期"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="应急联络人手机号" prop="emergencyContactPhone">
                      <el-input v-model="form.emergencyContactPhone" placeholder="应急联络人手机号" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="应急联络人电子邮箱" prop="emergencyContactEmail">
                      <el-input v-model="form.emergencyContactEmail" placeholder="请输入应急联络人电子邮箱" />
                    </el-form-item>
                  </el-col>
                </el-row>
          </div>
        </div>

        <div class="form-actions">
          <el-button @click="goBack">取消</el-button>
          <el-button type="primary" :loading="submitting" @click="submitForm">保存</el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
import { getApplet, addApplet, updateApplet } from "@/api/website/applet";
import BasicDataSidebar from '@/components/BasicDataSidebar'

export default {
  name: 'AppletEdit',
  dicts: ['ws_website_rating', 'certificate_type', 'sys_yes_no', 'ws_server_type', 'ws_status'],
  components: { BasicDataSidebar },
  data() {
    return {
      submitting: false,
      registerNum: undefined,
      // 表单参数
      form: {},

      // 模块折叠状态
      sectionCollapsed: {
        basic: false,
        safety: false,
        emergency: false
      },
      // 表单校验
      rules: {
        registerNum: [
          { required: true, message: "注册登记号不能为空", trigger: "blur" }
        ],
        appletName: [
          { required: true, message: "小程序名称不能为空", trigger: "blur" }
        ],
        creditCode: [
          { required: true, message: "统一社会信用代码不能为空", trigger: "blur" }
        ],
        openDate: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        ipAddress: [
          { required: true, message: "IP地址不能为空", trigger: "blur" }
        ],
        filingNumber: [
          { required: true, message: "工信部备案号不能为空", trigger: "blur" }
        ],
        serverType: [
          { required: true, message: "托管服务器信息不能为空", trigger: "change" }
        ],
        registerUserNumber: [
          { required: true, message: "注册用户总量不能为空", trigger: "blur" }
        ],
        appletRating: [
          { required: true, message: "小程序分级不能为空", trigger: "change" }
        ],
        preAuditContentFlag: [
          { required: true, message: "是否涉及前置审核内容不能为空", trigger: "change" }
        ],
        safeManagerName: [
          { required: true, message: "安全负责人姓名不能为空", trigger: "blur" }
        ],
        safeManagerIdType: [
          { required: true, message: "安全负责人证件类型不能为空", trigger: "change" }
        ],
        safeManagerIdNumber: [
          { required: true, message: "安全负责人证件号码不能为空", trigger: "blur" }
        ],
        safeManagerPhone: [
          { required: true, message: "安全负责人手机号不能为空", trigger: "blur" }
        ],
        emergencyContactName: [
          { required: true, message: "应急联络人姓名不能为空", trigger: "blur" }
        ],
        emergencyContactIdType: [
          { required: true, message: "应急联络人证件类型不能为空", trigger: "change" }
        ],
        emergencyContactIdNumber: [
          { required: true, message: "应急联络人证件号码不能为空", trigger: "blur" }
        ],
        emergencyContactPhone: [
          { required: true, message: "应急联络人手机号不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.registerNum = this.$route.params && this.$route.params.id;
    if (this.registerNum) {
      this.getInfo();
    } else {
      this.reset();
    }
  },
  methods: {
    // 表单重置
    reset() {
      this.form = {
        registerNum: null,
        appletName: null,
        creditCode: null,
        openDate: null,
        ipAddress: null,
        filingNumber: null,
        serverType: null,
        marketValue: null,
        monthUserNumber: null,
        registerUserNumber: null,
        appletRating: null,
        preAuditContentFlag: null,
        screenshot: null,
        label: null,
        safeManagerName: null,
        safeManagerIdType: null,
        safeManagerIdNumber: null,
        safeManagerIdDate: null,
        safeManagerPhone: null,
        safeManagerEmail: null,
        emergencyContactName: null,
        emergencyContactIdType: null,
        emergencyContactIdNumber: null,
        emergencyContactIdDate: null,
        emergencyContactPhone: null,
        emergencyContactEmail: null,
        status: null
      };
      this.resetForm("form");
    },
    /** 获取详情 */
    getInfo() {
      getApplet(this.registerNum).then(response => {
        this.form = response.data;
      });
    },
    /** 提交按钮 */
    submitForm() {
      console.log('applet edit submitForm called');
      this.$refs["form"].validate((valid, invalidFields) => {
        console.log('Validation result:', valid);
        if (invalidFields) {
          console.log('Invalid fields:', invalidFields);
        }

        if (valid) {
          this.submitting = true;
          if (this.form.registerNum != null) {
            updateApplet(this.form).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.$router.push("/data-management/website/applet");
            }).catch((error) => {
              console.error('Update error:', error);
              this.$modal.msgError("修改失败：" + (error.message || error));
            }).finally(() => {
              this.submitting = false;
            });
          } else {
            addApplet(this.form).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.$router.push("/data-management/website/applet");
            }).catch((error) => {
              console.error('Add error:', error);
              this.$modal.msgError("新增失败：" + (error.message || error));
            }).finally(() => {
              this.submitting = false;
            });
          }
        } else {
          console.log('Form validation failed');
          this.$modal.msgError("表单验证失败，请检查必填项");
        }
      });
    },
    /** 返回按钮 */
    handleClose() {
      this.$router.push("/data-management/website/applet");
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 切换模块折叠状态
    toggleSection(section) {
      this.sectionCollapsed[section] = !this.sectionCollapsed[section];
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .page-header {
     width: 94%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      justify-content: space-between;
      .page-title {
        margin: 0;
        font-size: 18px;
        color: #303133;
      }
    }
  }

  .form-container {
    width: 94%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
  }

  .form-section {
    margin-bottom: 24px;

    .section-header {
      background-color: #f6f7fb;
      padding: 12px 16px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 4px;
      margin-bottom: 16px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-line {
          width: 3px;
          height: 16px;
          background-color: #4584FF;
        }

        .title-text {
          font-size: 16px;
          font-weight: 600;
          color: #4584FF;
        }
      }

      .section-toggle {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #4584FF;
        font-size: 14px;

        .toggle-text {
          font-size: 14px;
        }

        i {
          font-size: 12px;
        }
      }

      &:hover {
        background-color: #f0f2f5;
      }
    }

    .section-content {
      padding: 0 16px;
    }
  }

  .form-actions {
    background: white;
    padding: 20px;
    border-radius: 8px;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    border-top: 1px solid #f0f0f0;
  }
}

// 全局样式：确保表单标签不换行
::v-deep .el-form-item__label {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}
</style>
