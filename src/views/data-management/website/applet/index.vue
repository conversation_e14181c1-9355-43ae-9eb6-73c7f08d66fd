<template>
  <div class="app-container">
    <div class="screen-management-layout">
      <!-- 左侧导航菜单 -->
      <basic-data-sidebar />

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <!-- 搜索组件 -->
        <common-search
          :search-config="searchConfig"
          :advanced-fields="advancedFields"
          :initial-form="searchForm"
          @search="handleSearch"
          @reset="handleReset"
        />

      <!-- 操作按钮区域 -->
      <div class="action-bar">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['website:applet:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-upload2"
              size="mini"
              @click="handleImport"
              v-hasPermi="['website:applet:import']"
            >导入</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['website:applet:edit']"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['website:applet:remove']"
            >删除</el-button>
          </el-col> -->
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['website:applet:export']"
            >导出</el-button>
          </el-col>
          <el-col :span="1.5">
             <el-button
              type="success"
              icon="el-icon-plus"
              size="mini"
              @click="handleReminder"
              v-hasPermi="['website:applet:add']"
            >设置到期提醒</el-button>
          </el-col>

          <div class="toolbar-right">
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
          </div>
        </el-row>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="appletList"
          @selection-change="handleSelectionChange"
          :header-cell-style="{ backgroundColor: '#EBF2FF' }"
          border
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="注册登记号" align="center" prop="registerNum" width="180"/>
          <el-table-column label="小程序名称" align="center" prop="appletName" width="180">
            <template slot-scope="scope">
              <div class="site-name-container">
                <span>{{ scope.row.appletName }}</span>
                <span
                  v-if="scope.row.dataUpdateStatus === 1"
                  class="update-badge pending"
                >待更新</span>
                <span
                  v-else-if="scope.row.dataUpdateStatus > 1"
                  class="update-badge overdue"
                >已超期</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="统一社会信用代码" align="center" prop="creditCode" width="180" />
          <el-table-column label="创建时间" align="center" prop="openDate" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.openDate, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="IP地址" align="center" prop="ipAddress" />
          <el-table-column label="工信部备案号" align="center" prop="filingNumber" />
          <el-table-column label="托管服务器信息" align="center" prop="serverType">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.ws_server_type" :value="scope.row.serverType"/>
            </template>
          </el-table-column>
          <el-table-column label="月活用户数量" align="center" prop="monthUserNumber" />
          <el-table-column label="注册用户总量" align="center" prop="registerUserNumber" />
          <el-table-column label="小程序分级" align="center" prop="appletRating">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.ws_website_rating" :value="scope.row.appletRating"/>
            </template>
          </el-table-column>
          <el-table-column label="安全负责人姓名" align="center" prop="safeManagerName" />
          <el-table-column label="安全负责人手机号" align="center" prop="safeManagerPhone" />
          <el-table-column label="应急联络人姓名" align="center" prop="emergencyContactName" />
          <el-table-column label="状态" align="center" prop="status">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.ws_status" :value="scope.row.status"/>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200" fixed="right">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="handleDetail(scope.row)"
                v-hasPermi="['website:applet:query']"
              >详情</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleEdit(scope.row)"
                v-hasPermi="['website:applet:edit']"
              >修改</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['website:applet:remove']"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>

     
      </div>
    </div>
     <!-- 导入对话框 -->
      <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
        <el-upload
          ref="upload"
          :limit="1"
          accept=".zip"
          :headers="upload.headers"
          :action="upload.url + '?previewUrl=' + upload.previewUrl"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip text-center" slot="tip">
            <span>仅允许导入zip格式文件。</span>
            <el-link type="danger" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
          </div>
        </el-upload>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </el-dialog>
    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".zip"
        :headers="upload.headers"
        :action="upload.url + '?previewUrl=' + upload.previewUrl"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入zip格式文件。</span>
          <el-link type="danger" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 数据更新提醒组件 -->
    <data-update-reminder
      :visible.sync="reminderVisible"
      :biz-type="'APPLET'"
    />
  </div>
</template>

<script>
import { listApplet, delApplet } from "@/api/website/applet";
import { getToken } from "@/utils/auth";
import BasicDataSidebar from '@/components/BasicDataSidebar'
import CommonSearch from '@/components/CommonSearch'
import DataUpdateReminder from '@/components/DataUpdateReminder'

export default {
  name: "AppletIndex",
  dicts: ['ws_website_rating', 'certificate_type', 'sys_yes_no', 'ws_server_type', 'ws_status'],
  components: { BasicDataSidebar, CommonSearch, DataUpdateReminder },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 小程序信息审核表格数据
      appletList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 提醒组件显示状态
      reminderVisible: false,

      // 搜索配置
      searchConfig: {
        label: '注册登记号',
        key: 'registerNum',
        placeholder: '请输入注册登记号'
      },

      // 高级搜索字段
      advancedFields: [
        {
          label: '小程序名称',
          key: 'appletName',
          type: 'input',
          placeholder: '请输入小程序名称',
          span: 6
        },
        {
          label: '统一社会信用代码',
          key: 'creditCode',
          type: 'input',
          placeholder: '请输入统一社会信用代码',
          span: 6
        },
        {
          label: '工信部备案号',
          key: 'filingNumber',
          type: 'input',
          placeholder: '请输入工信部备案号',
          span: 6
        },
        {
          label: '托管服务器信息',
          key: 'serverType',
          type: 'select',
          placeholder: '请选择托管服务器信息',
          span: 6,
          options: []
        },
        {
          label: '小程序分级',
          key: 'appletRating',
          type: 'select',
          placeholder: '请选择小程序分级',
          span: 6,
          options: []
        },
        {
          label: '安全负责人姓名',
          key: 'safeManagerName',
          type: 'input',
          placeholder: '请输入安全负责人姓名',
          span: 6
        },
        {
          label: '安全负责人手机号',
          key: 'safeManagerPhone',
          type: 'input',
          placeholder: '请输入安全负责人手机号',
          span: 6
        },
        {
          label: '应急联络人姓名',
          key: 'emergencyContactName',
          type: 'input',
          placeholder: '请输入应急联络人姓名',
          span: 6
        },
        {
          label: '状态',
          key: 'status',
          type: 'select',
          placeholder: '请选择状态',
          span: 6,
          options: []
        }
      ],

      // 搜索表单
      searchForm: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        registerNum: null,
        appletName: null,
        creditCode: null,
        openDate: null,
        filingNumber: null,
        serverType: null,
        appletRating: null,
        preAuditContentFlag: null,
        safeManagerName: null,
        safeManagerPhone: null,
        emergencyContactName: null,
        status: null,
      },
       // 导入参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        previewUrl: process.env.VUE_APP_BASE_API + "/file/download/",
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/website/applet/importData"
      },
    };
  },
  created() {
    this.getList();
    this.initAdvancedFields();
  },
  watch: {
    // 监听字典数据变化
    'dict.type': {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.initAdvancedFields();
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 设置提醒
    handleReminder() {
      this.reminderVisible = true;
    },
    // 初始化高级搜索字段
    initAdvancedFields() {
      console.log('Applet - 初始化高级搜索字段',this.dict.type.ws_server_type);
      console.log('Applet - dict:', this.dict);
      console.log('Applet - 字典数据:', {
        ws_server_type: this.dict && this.dict.type && this.dict.type.ws_server_type,
        ws_website_rating: this.dict && this.dict.type && this.dict.type.ws_website_rating,
        ws_status: this.dict && this.dict.type && this.dict.type.ws_status
      });

      // 更新托管服务器信息选项
      const serverTypeField = this.advancedFields.find(field => field.key === 'serverType');
      if (serverTypeField) {
        serverTypeField.options = (this.dict && this.dict.type && this.dict.type.ws_server_type) ?
          this.dict.type.ws_server_type.map(item => ({
            label: item.label,
            value: item.value
          })) : [];
      }

      // 更新小程序分级选项
      const ratingField = this.advancedFields.find(field => field.key === 'appletRating');
      if (ratingField) {
        ratingField.options = (this.dict && this.dict.type && this.dict.type.ws_website_rating) ?
          this.dict.type.ws_website_rating.map(item => ({
            label: item.label,
            value: item.value
          })) : [];
      }

      // 更新状态选项
      const statusField = this.advancedFields.find(field => field.key === 'status');
      if (statusField) {
        statusField.options = (this.dict && this.dict.type && this.dict.type.ws_status) ?
          this.dict.type.ws_status.map(item => ({
            label: item.label,
            value: item.value
          })) : [];
      }

      console.log('Applet - 更新后的选项:', {
        serverType: serverTypeField?.options || [],
        appletRating: ratingField?.options || [],
        status: statusField?.options || []
      });
    },

    /** 查询小程序信息审核列表 */
    getList() {
      this.loading = true;
      listApplet(this.queryParams).then(response => {
        this.appletList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    // 处理搜索
    handleSearch(searchForm) {
      this.queryParams = {
        ...this.queryParams,
        ...searchForm,
        pageNum: 1
      };
      this.getList();
    },

    // 处理重置
    handleReset(searchForm) {
      this.searchForm = searchForm;
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        ...searchForm
      };
      this.getList();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.registerNum)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push("/data-management/website/applet/add");
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.$router.push(`/data-management/website/applet/detail/${row.registerNum}`);
    },
    /** 修改按钮操作 */
    handleEdit(row) {
      this.$router.push(`/data-management/website/applet/edit/${row.registerNum}`);
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const registerNum = row.registerNum || this.ids
      this.$router.push(`/data-management/website/applet/edit/${registerNum}`);
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const registerNums = row.registerNum || this.ids;
      this.$modal.confirm('是否确认删除小程序信息审核编号为"' + registerNums + '"的数据项？').then(function() {
        return delApplet(registerNums);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
     this.download( 'website/applet/export', {
        ...this.queryParams
      }, `小程序信息审核_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "数据导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
   /** 下载模板操作 */
    async importTemplate() {
      const url = process.env.VUE_APP_BASE_API+'/file/download/'+vueConfig.template_path_applet; // 替换成你要下载的文件的URL
      const fileName = '小程序导入模板.xlsx'; // 自定义文件名

      const response = await fetch(url);
      const blob = await response.blob();
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .screen-management-layout {
    display: flex;
    gap: 20px;
    height: 100%;

    .content-area {
      flex: 1;
      min-width: 0;
    }
  }

  .action-bar {
    background: white;
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
  }

  .table-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
  }

  .toolbar-right {
    float: right;
    display: flex;
    align-items: center;
    gap: 12px;
  }
}

// 全局样式：确保表单标签不换行
::v-deep .el-form-item__label {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

// 小程序名称容器样式

// 名称角标样式
.site-name-container {
  position: relative;
  display: inline-block;
  padding: 12px 12px 0 0;
  .update-badge {
    position: absolute;
    top: -2px;
    right: -10px;
    font-size: 10px;
    padding: 1px 4px;
    border-radius: 2px;
    // color: white;
    white-space: nowrap;
    z-index: 1;

    &.pending {
      color: #FE9400;
    }

    &.overdue {
      color: #FF2A2A;
    }
  }
}
</style>
