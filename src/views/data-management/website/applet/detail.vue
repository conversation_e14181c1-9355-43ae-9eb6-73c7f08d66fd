<template>
  <div style="display:flex;background: #f5f7fa;">
  <div class="app-container">
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">小程序详情</h2>
        <div class="header-actions">
          <!-- <el-button type="primary" size="small" @click="handleEdit">编辑</el-button> -->
          <el-button icon="el-icon-arrow-left" size="small" @click="goBack">返回</el-button>
        </div>
      </div>
    </div>

    <div class="detail-content" v-loading="loading">
      <!-- 主体信息模块 -->
      <div class="detail-section">
        <div class="section-header" @click="toggleSection('basic')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">主体信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.basic ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.basic ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>

        <div v-show="!sectionCollapsed.basic" class="section-content">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="小程序名称">
              {{ form.appletName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="统一社会信用代码">
              {{ form.creditCode || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="注册登记号">
              {{ form.registerNum || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="工信部备案号">
              {{ form.filingNumber || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="IP地址">
              {{ form.ipAddress || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="托管服务器信息">
              <dict-tag :options="dict.type.ws_server_type" :value="form.serverType"/>
            </el-descriptions-item>
            <el-descriptions-item label="市值">
              {{ form.marketValue || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="月活用户数量">
              {{ form.monthUserNumber || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="注册用户总量">
              {{ form.registerUserNumber || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="小程序分级">
              <dict-tag :options="dict.type.ws_website_rating" :value="form.appletRating"/>
            </el-descriptions-item>
            <el-descriptions-item label="是否涉及前置审核内容">
              <dict-tag :options="dict.type.sys_yes_no" :value="form.preAuditContentFlag"/>
            </el-descriptions-item>
            <el-descriptions-item label="标签">
              {{ form.label || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="运行截图">
              <div v-if="!form.screenshot">-</div>
              <div v-else>
                <image-preview
                  style="margin-right: 10px;"
                  v-for="item in form.screenshot.split(',')"
                  :key="item"
                  :src="item"
                  :width="100"
                  :height="100"
                />
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ form.openDate || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <dict-tag :options="dict.type.ws_status" :value="form.status"/>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 安全负责人模块 -->
      <div class="detail-section">
        <div class="section-header" @click="toggleSection('safety')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">安全负责人</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.safety ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.safety ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>

        <div v-show="!sectionCollapsed.safety" class="section-content">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="安全负责人姓名">
              {{ form.safeManagerName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="安全负责人证件类型">
              <dict-tag :options="dict.type.certificate_type" :value="form.safeManagerIdType"/>
            </el-descriptions-item>
            <el-descriptions-item label="安全负责人证件号码">
              {{ form.safeManagerIdNumber || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="安全负责人证件有效期">
              {{ form.safeManagerIdDate || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="安全负责人手机号">
              {{ form.safeManagerPhone || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="安全负责人电子邮箱">
              {{ form.safeManagerEmail || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 应急联络人模块 -->
      <div class="detail-section">
        <div class="section-header" @click="toggleSection('emergency')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">应急联络人</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.emergency ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.emergency ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>

        <div v-show="!sectionCollapsed.emergency" class="section-content">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="应急联络人姓名">
              {{ form.emergencyContactName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="应急联络人证件类型">
              <dict-tag :options="dict.type.certificate_type" :value="form.emergencyContactIdType"/>
            </el-descriptions-item>
            <el-descriptions-item label="应急联络人证件号码">
              {{ form.emergencyContactIdNumber || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="应急联络人证件有效期">
              {{ form.emergencyContactIdDate || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="应急联络人手机号">
              {{ form.emergencyContactPhone || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="应急联络人电子邮箱">
              {{ form.emergencyContactEmail || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </div>

    
  </div>
  <!-- 右侧数据维护记录区域 -->
    <collapsible-detail-right
      @collapse-change="handleRightCollapseChange"
      @width-change="handleRightWidthChange"
    >
      <!-- 锚点导航组件 -->
      <template #anchor>
        <anchor-navigation
          :anchor-items="anchorItems"
          @anchor-change="handleAnchorChange"
        />
      </template>

      <!-- 数据维护时间线 -->
      <template #timeline>
        <data-maintenance-timeline
          :biz-type="'APPLET'"
          :biz-id="form.registerNum"
          v-if="form.registerNum"
        />
      </template>
    </collapsible-detail-right>
  </div>
</template>

<script>
import { getApplet } from "@/api/website/applet";
import BasicDataSidebar from '@/components/BasicDataSidebar'
import DataMaintenanceTimeline from '@/components/DataMaintenanceTimeline'
import AnchorNavigation from '@/components/AnchorNavigation'
import CollapsibleDetailRight from '@/components/CollapsibleDetailRight'

export default {
  name: 'AppletDetail',
  dicts: ['ws_website_rating', 'certificate_type', 'sys_yes_no', 'ws_server_type', 'ws_status'],
  components: {
    BasicDataSidebar,
    DataMaintenanceTimeline,
    AnchorNavigation,
    CollapsibleDetailRight
  },
  data() {
    return {
      loading: false,
      registerNum: undefined,
      // 表单参数
      form: {},

      // 模块折叠状态
      sectionCollapsed: {
        basic: false,
        safety: false,
        emergency: false
      },

      // 锚点导航配置
      anchorItems: [
        { id: 'basic-info', label: '主体信息' },
        { id: 'safety-info', label: '安全负责人' },
        { id: 'emergency-info', label: '应急联络人' }
      ],

      // 右侧面板收起状态
      isRightCollapsed: false,
      // 右侧面板宽度
      rightPanelWidth: 360
    };
  },
  created() {
    this.registerNum = this.$route.params && this.$route.params.id;
    this.getDetail();
  },
  methods: {
    // 表单重置
    reset() {
      this.form = {
        registerNum: null,
        appletName: null,
        creditCode: null,
        openDate: null,
        ipAddress: null,
        filingNumber: null,
        serverType: null,
        marketValue: null,
        monthUserNumber: null,
        registerUserNumber: null,
        appletRating: null,
        preAuditContentFlag: null,
        screenshot: null,
        label: null,
        safeManagerName: null,
        safeManagerIdType: null,
        safeManagerIdNumber: null,
        safeManagerIdDate: null,
        safeManagerPhone: null,
        safeManagerEmail: null,
        emergencyContactName: null,
        emergencyContactIdType: null,
        emergencyContactIdNumber: null,
        emergencyContactIdDate: null,
        emergencyContactPhone: null,
        emergencyContactEmail: null,
        status: null
      };
    },
    /** 获取详情 */
    getDetail() {
      this.reset();
      if (!this.registerNum) {
        return;
      }
      getApplet(this.registerNum).then(response => {
        this.form = response.data;
      });
    },
    /** 返回按钮 */
    handleClose() {
      this.$router.push("/data-management/website/applet");
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 切换模块折叠状态
    toggleSection(section) {
      this.sectionCollapsed[section] = !this.sectionCollapsed[section];
    },

    // 处理锚点变化
    handleAnchorChange(anchorId) {
      console.log('当前激活的锚点:', anchorId);
    },

    // 处理右侧面板收起展开
    handleRightCollapseChange(isCollapsed) {
      this.isRightCollapsed = isCollapsed;
    },

    // 处理右侧面板宽度变化
    handleRightWidthChange(width) {
      this.rightPanelWidth = width;
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
  width: calc(100% - 380px);
  transition: width 0.3s ease;

  &.expanded {
    width: calc(100% - 60px);
  }

  .page-header {
    width: 99%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      justify-content: space-between;

      .page-title {
        margin: 0;
        font-size: 18px;
        color: #303133;
        font-weight: 600;
      }

      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
  }

  .detail-content {
    width: 99%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
  }

  .detail-section {
    margin-bottom: 24px;

    .section-header {
      background-color: #f6f7fb;
      padding: 12px 16px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 4px;
      margin-bottom: 16px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-line {
          width: 3px;
          height: 16px;
          background-color: #4584FF;
        }

        .title-text {
          font-size: 16px;
          font-weight: 600;
          color: #4584FF;
        }
      }

      .section-toggle {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #4584FF;
        font-size: 14px;

        .toggle-text {
          font-size: 14px;
        }

        i {
          font-size: 12px;
          transition: transform 0.3s;
        }
      }

      &:hover {
        background-color: #f0f2f5;
      }
    }

    .section-content {
      padding: 0 16px;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
