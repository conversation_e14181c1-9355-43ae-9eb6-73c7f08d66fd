<template>
<div style="display:flex;background: #f5f7fa;">
  <section class="app-container" :class="{ 'expanded': isRightCollapsed }" :style="{ width: `calc(100% - ${rightPanelWidth}px)` }">
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">网站详情</h2>
        <div class="header-actions">
          <el-button icon="el-icon-arrow-left" @click="goBack" size="small">返回</el-button>
        </div>
      </div>
    </div>

    <div class="detail-container" v-loading="loading">
          <!-- 主体信息模块 -->
          <div id="basic-info" class="detail-section">
            <div class="section-header" @click="toggleSection('basic')">
              <div class="section-title">
                <div class="title-line"></div>
                <span class="title-text">主体信息</span>
              </div>
              <div class="section-toggle">
                <span class="toggle-text">{{ sectionCollapsed.basic ? '展开' : '收起' }}</span>
                <i :class="sectionCollapsed.basic ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
              </div>
            </div>

            <div v-show="!sectionCollapsed.basic" class="section-content">
              <el-descriptions :column="3" border>
                <el-descriptions-item label="工信部备案号">
                  {{ form.filingNumber || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="网站名称">
                  {{ form.websiteName || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="网站开通日期">
                  {{ form.openDate || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="主域名">
                  {{ form.masterDomain || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="从域名">
                  {{ form.slaveDomain || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="IP">
                  {{ form.ipAddress || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="备案主体">
                  <dict-tag :options="dict.type.ws_filing_subject" :value="form.filingSubject"/>
                </el-descriptions-item>
                <el-descriptions-item label="社会信用代码">
                  {{ form.creditCode || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="域名服务商">
                  {{ form.domainServiceProvider || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="接入服务商">
                  {{ form.accessServiceProvider || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="网站接入方式">
                  {{ form.accessMethod || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="网站语种">
                  <dict-tag :options="dict.type.ws_language" :value="form.language"/>
                </el-descriptions-item>
                <el-descriptions-item label="市值">
                  {{ form.marketValue || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="月活用户数量">
                  {{ form.monthUserNumber || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="是否提供管制物品服务">
                  <dict-tag :options="dict.type.sys_yes_no" :value="form.provideControlledServiceFlag"/>
                </el-descriptions-item>
                <el-descriptions-item label="是否涉及前置审核内容">
                  <dict-tag :options="dict.type.sys_yes_no" :value="form.preAuditContentFlag"/>
                </el-descriptions-item>
                <el-descriptions-item label="网站分级">
                  <dict-tag :options="dict.type.ws_website_rating" :value="form.websiteRating"/>
                </el-descriptions-item>
                <el-descriptions-item label="域名证书" :span="3">
                  <div v-if="!form.domainCertificate">-</div>
                  <div v-else>
                    <image-preview
                      style="margin-right: 10px;"
                      v-for="item in form.domainCertificate.split(',')"
                      :key="item"
                      :src="item"
                      :width="100"
                      :height="100"
                    />
                  </div>
                </el-descriptions-item>
                <el-descriptions-item label="域名证书有效期">
                  {{ form.certificateDate || '-' }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>

          <!-- 互联网交互服务模块 -->
          <div id="internet-info" class="detail-section">
            <div class="section-header" @click="toggleSection('internet')">
              <div class="section-title">
                <div class="title-line"></div>
                <span class="title-text">互联网交互服务</span>
              </div>
              <div class="section-toggle">
                <span class="toggle-text">{{ sectionCollapsed.internet ? '展开' : '收起' }}</span>
                <i :class="sectionCollapsed.internet ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
              </div>
            </div>

            <div v-show="!sectionCollapsed.internet" class="section-content">
              <el-descriptions :column="3" border>
            <el-descriptions-item label="提供互联网交互服务">
              <dict-tag :options="dict.type.sys_yes_no" :value="form.provideInternetServiceFlag"/>
            </el-descriptions-item>
            <template v-if="form.provideInternetServiceFlag == 'Y'">
              <el-descriptions-item label="网络基础类A">
                <dict-tag :options="dict.type.ws_internet_service_a" :value="form.internetServiceA"/>
              </el-descriptions-item>
              <el-descriptions-item label="网络销售类B">
                <dict-tag :options="dict.type.ws_internet_service_b" :value="form.internetServiceB"/>
              </el-descriptions-item>
              <el-descriptions-item label="生活服务类C">
                <dict-tag :options="dict.type.ws_internet_service_c" :value="form.internetServiceC"/>
              </el-descriptions-item>
              <el-descriptions-item label="社交文娱类D">
                <dict-tag :options="dict.type.ws_internet_service_d" :value="form.internetServiceD"/>
              </el-descriptions-item>
              <el-descriptions-item label="信息咨询类E">
                <dict-tag :options="dict.type.ws_internet_service_e" :value="form.internetServiceE"/>
              </el-descriptions-item>
              <el-descriptions-item label="金融服务类F">
                <dict-tag :options="dict.type.ws_internet_service_f" :value="form.internetServiceF"/>
              </el-descriptions-item>
              <el-descriptions-item label="计算机应用类G">
                <dict-tag :options="dict.type.ws_internet_service_g" :value="form.internetServiceG"/>
              </el-descriptions-item>
              <el-descriptions-item label="上网场所类H">
                <dict-tag :options="dict.type.ws_internet_service_h" :value="form.internetServiceH"/>
              </el-descriptions-item>
              <el-descriptions-item label="联网单位类I">
                <dict-tag :options="dict.type.ws_internet_service_i" :value="form.internetServiceI"/>
              </el-descriptions-item>
              <el-descriptions-item label="其他Z">
                <dict-tag :options="dict.type.ws_internet_service_z" :value="form.internetServiceZ"/>
              </el-descriptions-item>
            </template>
          </el-descriptions>
        </div>
      </div>

      <!-- 安全负责人模块 -->
      <div id="safety-info" class="detail-section">
        <div class="section-header" @click="toggleSection('safety')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">安全负责人</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.safety ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.safety ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>

        <div v-show="!sectionCollapsed.safety" class="section-content">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="安全负责人姓名">
              {{ form.safeManagerName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="安全负责人证件类型">
              <dict-tag :options="dict.type.certificate_type" :value="form.safeManagerIdType"/>
            </el-descriptions-item>
            <el-descriptions-item label="安全负责人证件号码">
              {{ form.safeManagerIdNumber || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="安全负责人证件有效期">
              {{ form.safeManagerIdDate || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="安全负责人手机号">
              {{ form.safeManagerPhone || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="安全负责人电子邮箱">
              {{ form.safeManagerEmail || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 应急联络人模块 -->
      <div id="emergency-info" class="detail-section">
        <div class="section-header" @click="toggleSection('emergency')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">应急联络人</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.emergency ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.emergency ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>

        <div v-show="!sectionCollapsed.emergency" class="section-content">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="应急联络人姓名">
              {{ form.emergencyContactName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="应急联络人证件类型">
              <dict-tag :options="dict.type.certificate_type" :value="form.emergencyContactIdType"/>
            </el-descriptions-item>
            <el-descriptions-item label="应急联络人证件号码">
              {{ form.emergencyContactIdNumber || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="应急联络人证件有效期">
              {{ form.emergencyContactIdDate || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="应急联络人手机号">
              {{ form.emergencyContactPhone || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="应急联络人电子邮箱">
              {{ form.emergencyContactEmail || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </div>
  </section>

  <!-- 右侧数据维护记录区域 -->
  <collapsible-detail-right
    @collapse-change="handleRightCollapseChange"
    @width-change="handleRightWidthChange"
  >
    <!-- 锚点导航组件 -->
    <template #anchor>
      <anchor-navigation
        :anchor-items="anchorItems"
        @anchor-change="handleAnchorChange"
      />
    </template>

    <!-- 数据维护时间线 -->
    <template #timeline>
      <data-maintenance-timeline
        :biz-type="'WEB_SITE'"
        :biz-id="form.websiteId"
        v-if="form.websiteId"
      />
    </template>
  </collapsible-detail-right>
</div>
</template>

<script>
import { getWebsite } from "@/api/website/website";
import DataMaintenanceTimeline from '@/components/DataMaintenanceTimeline'
import AnchorNavigation from '@/components/AnchorNavigation'
import CollapsibleDetailRight from '@/components/CollapsibleDetailRight'

export default {
  name: 'WebsiteDetail',
  dicts: ['ws_website_rating', 'ws_internet_service_a', 'ws_internet_service_c', 'ws_internet_service_b', 'sys_yes_no', 'sys_normal_disable', 'ws_internet_service_z', 'ws_language', 'ws_filing_subject', 'certificate_type', 'ws_internet_service_e', 'ws_internet_service_d', 'ws_internet_service_g', 'ws_internet_service_f', 'ws_internet_service_i', 'ws_internet_service_h'],
  components: {
    DataMaintenanceTimeline,
    AnchorNavigation,
    CollapsibleDetailRight
  },
  data() {
    return {
      loading: false,
      websiteId: undefined,
      // 表单参数
      form: {},

      // 模块折叠状态
      sectionCollapsed: {
        basic: false,
        internet: false,
        safety: false,
        emergency: false
      },

      // 锚点导航配置
      anchorItems: [
        { id: 'basic-info', label: '主体信息' },
        { id: 'internet-info', label: '互联网交互服务' },
        { id: 'safety-info', label: '安全负责人' },
        { id: 'emergency-info', label: '应急联络人' }
      ],

      // 右侧面板收起状态
      isRightCollapsed: false,
      // 右侧面板宽度
      rightPanelWidth: 360
    };
  },
  created() {
    this.websiteId = this.$route.params && this.$route.params.id;
    this.getDetail();
  },
  methods: {
    // 表单重置
    reset() {
      this.form = {
        websiteId: null,
        filingNumber: null,
        websiteName: null,
        openDate: null,
        masterDomain: null,
        slaveDomain: null,
        ipAddress: null,
        filingSubject: null,
        creditCode: null,
        domainCertificate: null,
        certificateDate: null,
        accessServiceProvider: null,
        accessMethod: null,
        domainServiceProvider: null,
        language: null,
        marketValue: null,
        monthUserNumber: null,
        provideControlledServiceFlag: null,
        preAuditContentFlag: null,
        websiteRating: null,
        provideInternetServiceFlag: null,
        internetServiceA: null,
        internetServiceB: null,
        internetServiceC: null,
        internetServiceD: null,
        internetServiceE: null,
        internetServiceF: null,
        internetServiceG: null,
        internetServiceH: null,
        internetServiceI: null,
        internetServiceZ: null,
        safeManagerName: null,
        safeManagerIdType: null,
        safeManagerIdNumber: null,
        safeManagerIdDate: null,
        safeManagerPhone: null,
        safeManagerEmail: null,
        emergencyContactName: null,
        emergencyContactIdType: null,
        emergencyContactIdNumber: null,
        emergencyContactIdDate: null,
        emergencyContactPhone: null,
        emergencyContactEmail: null,
        status: null
      };
    },
    /** 获取详情 */
    getDetail() {
      this.reset();
      if (!this.websiteId) {
        return;
      }
      getWebsite(this.websiteId).then(response => {
        this.form = response.data;
      });
    },
    /** 返回按钮 */
    handleClose() {
      this.$router.push("/data-management/website");
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 切换模块折叠状态
    toggleSection(section) {
      this.sectionCollapsed[section] = !this.sectionCollapsed[section];
    },

    // 处理锚点变化
    handleAnchorChange(anchorId) {
      console.log('当前激活的锚点:', anchorId);
    },

    // 处理右侧面板收起展开
    handleRightCollapseChange(isCollapsed) {
      this.isRightCollapsed = isCollapsed;
    },

    // 处理右侧面板宽度变化
    handleRightWidthChange(width) {
      this.rightPanelWidth = width;
    }
  }
};

</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
  transition: width 0.3s ease;

  &.expanded {
    width: calc(100% - 60px) !important;
  }

   .page-header {
     width: 99%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      justify-content: space-between;
      .page-title {
        margin: 0;
        font-size: 18px;
        color: #303133;
      }
    }
  }

  .detail-container {
    width: 99%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;

    .detail-section {
      margin-bottom: 24px;

      .section-header {
        background-color: #f6f7fb;
        padding: 12px 16px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 4px;
        margin-bottom: 16px;

        .section-title {
          display: flex;
          align-items: center;
          gap: 8px;

          .title-line {
            width: 3px;
            height: 16px;
            background-color: #4584FF;
          }

          .title-text {
            font-size: 16px;
            font-weight: 600;
            color: #4584FF;
          }
        }

        .section-toggle {
          display: flex;
          align-items: center;
          gap: 4px;
          color: #4584FF;
          font-size: 14px;

          .toggle-text {
            font-size: 14px;
          }

          i {
            font-size: 12px;
          }
        }

        &:hover {
          background-color: #f0f2f5;
        }
      }

      .section-content {
        padding: 0 16px;
      }
    }
  }
}

::v-deep .el-descriptions-item__label {
  white-space: nowrap !important;
}
</style>
