<template>
  <div class="app-container">
    <div class="screen-management-layout">
      <!-- 左侧导航菜单 -->
      <basic-data-sidebar />

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <!-- 搜索组件 -->
        <common-search
          :search-config="searchConfig"
          :advanced-fields="advancedFields"
          :initial-form="searchForm"
          @search="handleSearch"
          @reset="handleReset"
        />

    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <el-row :gutter="10" class="mb8" style="width:100%;">
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['screen:enterprise:export']"
          >导出</el-button>
        </el-col>
        <el-col :span="1.5">
           <el-button
            type="success"
            icon="el-icon-plus"
            size="mini"
            @click="handleReminder"
            v-hasPermi="['screen:enterprise:add']"
          >设置到期提醒</el-button>
        </el-col>

        <div class="toolbar-right">
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </div>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table 
        v-loading="loading" 
        :data="enterpriseList" 
        :header-cell-style="{ backgroundColor: '#EBF2FF' }"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" fixed="left" />
        <el-table-column label="企业名称/部门" align="center" prop="enterpriseName" min-width="150">
          <template slot-scope="scope">
            <div class="site-name-container">
              <div class="text-nowrap" :title="scope.row.enterpriseName">{{ scope.row.enterpriseName }}</div>
              <span
                v-if="scope.row.dataUpdateStatus === 1"
                class="update-badge pending"
              >待更新</span>
              <span
                v-else-if="scope.row.dataUpdateStatus > 1"
                class="update-badge overdue"
              >已超期</span>
            </div>
            <br/>
            <span>{{ scope.row.principalDept }}</span>
          </template>
        </el-table-column>
        <el-table-column label="统一社会信用代码" align="center" prop="creditCode" width="180">
          <template slot-scope="scope">
            <div class="text-nowrap" :title="scope.row.creditCode">{{ scope.row.creditCode }}</div>
          </template>
        </el-table-column>
        <el-table-column label="联系人/电话" align="center" width="120">
          <template slot-scope="scope">
            <div class="text-nowrap" :title="scope.row.principal">{{ scope.row.principal }}</div>
            <div class="text-nowrap" :title="scope.row.principalPhone">{{ scope.row.principalPhone }}</div>
          </template>
        </el-table-column>
        <el-table-column label="单位性质" align="center" prop="unitProperties" width="100">
          <template slot-scope="scope">
            <dict-tag v-for="item in (scope.row.unitProperties || '').split(',')" :key="item" :options="dict.type.unit_properties" :value="item"/>
          </template>
        </el-table-column>
        <el-table-column label="单位地址" align="center" prop="address" min-width="150">
          <template slot-scope="scope">
            <div class="text-nowrap" :title="scope.row.address">{{ scope.row.address }}</div>
          </template>
        </el-table-column>
        <el-table-column label="联系人职务" align="center" prop="principalDuties" width="120">
          <template slot-scope="scope">
            <div class="text-nowrap" :title="scope.row.principalDuties">{{ scope.row.principalDuties }}</div>
          </template>
        </el-table-column>
        <el-table-column label="承诺书" align="center" prop="fileUrl" width="90">
          <template slot-scope="scope">
            <image-preview :src="scope.row.fileUrl" :width="50" :height="50"/>
          </template>
        </el-table-column>
        <el-table-column label="电子屏数" align="center" prop="screenNum" width="90">
          <template slot-scope="scope">
            <a href="javascript:void(0);" style="color: #409EFF;" @click="$router.push('/data-management/screen?enterpriseId='+scope.row.enterpriseId+'&enterpriseName='+ scope.row.enterpriseName)">
              {{ scope.row.screenNum }}
            </a>
          </template>
        </el-table-column>
        <el-table-column label="检查次数" align="center" prop="inspectionNum" width="90"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="220" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['screen:enterprise:query']"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['screen:enterprise:edit']"
            >修改</el-button>
            <el-button
              v-if="scope.row.screenNum==0"
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['screen:enterprise:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 添加或修改责任单位对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1050px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="138px">
        <el-row :gutter="10" class="mb8">
          <el-col :span="14">
            <el-form-item label="运营单位名称" prop="enterpriseName">
              <el-input v-model="form.enterpriseName" placeholder="请输入运营单位名称" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="统一社会信用代码" prop="creditCode">
              <el-input v-model="form.creditCode" placeholder="请输入统一社会信用代码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="14">
            <el-form-item label="联系人姓名" prop="principal">
              <el-input v-model="form.principal" placeholder="请输入联系人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="联系人电话" prop="principalPhone">
              <el-input v-model="form.principalPhone" placeholder="请输入联系人电话" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="14">
            <el-form-item label="联系人所属部门" prop="principalDept">
              <el-input v-model="form.principalDept" placeholder="请输入联系人所属部门" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="联系人职务" prop="principalDuties">
              <el-input v-model="form.principalDuties" placeholder="请输入联系人职务" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="14">
            <el-form-item label="单位办公地址" prop="address">
              <el-input v-model="form.address" placeholder="请输入单位办公地址" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="单位性质" prop="unitProperties">
              <el-select v-model="form.unitPropertieList" multiple style="width: 90%;" placeholder="请选择单位性质，支持多选！">
                <el-option
                  v-for="dict in dict.type.unit_properties"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
              <el-tooltip content="支持多选" placement="top">
                <el-button icon="el-icon-info" size="mini" circle></el-button>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="14">
            <el-form-item label="承诺书" prop="fileUrl">
              <image-upload v-model="form.fileUrl" minioId="screen"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
      </div>
    </div>

    <!-- 数据更新提醒组件 -->
    <data-update-reminder
      :visible.sync="reminderVisible"
      :biz-type="'ENTERPRISE'"
    />
  </div>
</template>

<script>
import { listEnterprise, getEnterprise, delEnterprise, addEnterprise, updateEnterprise } from "@/api/screen/enterprise";
import { listUserArea } from '@/api/system/area';
import CommonSearch from '@/components/CommonSearch'
import BasicDataSidebar from '@/components/BasicDataSidebar'
import ImageUpload from '@/components/ImageUpload'
import ImagePreview from '@/components/ImagePreview'
import DataUpdateReminder from '@/components/DataUpdateReminder'

export default {
  name: "Enterprise",
  dicts: ['unit_properties'],
  components: { CommonSearch, BasicDataSidebar, ImageUpload, ImagePreview, DataUpdateReminder },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 责任单位表格数据
      enterpriseList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 提醒组件显示状态
      reminderVisible: false,

      // 搜索配置
      searchConfig: {
        label: '企业名称',
        key: 'enterpriseName',
        placeholder: '请输入企业名称'
      },
      
      // 高级搜索字段
      advancedFields: [],
      
      // 搜索表单
      searchForm: {},
      
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        enterpriseName: null,
        unitProperties: null,
        creditCode: null,
        address: null,
        principal: null,
        principalPhone: null,
        principalDept: null,
        principalDuties: null,
      },
      
      // 表单参数
      form: {},
      
      // 表单校验
      rules: {
        enterpriseName: [
          { required: true, message: "运营使用单位名称不能为空", trigger: "blur" }
        ],
        unitProperties: [
          { required: true, message: "单位性质不能为空", trigger: "change" }
        ],
        creditCode: [
          { required: true, message: "统一社会信用代码不能为空", trigger: "blur" }
        ],
        address: [
          { required: true, message: "地址不能为空", trigger: "blur" }
        ],
        principal: [
          { required: true, message: "联系人姓名不能为空", trigger: "blur" }
        ],
        principalPhone: [
          { required: true, message: "联系人电话不能为空", trigger: "blur" }
        ],
        fileUrl: [
          { required: true, message: "承诺书不能为空", trigger: "blur" }
        ],
      },
      
      userAreaList: [],
      policeList: []
    };
  },
  created() {
    this.getList();
    this.initAdvancedFields();
  },
  methods: {
    // 初始化高级搜索字段
    initAdvancedFields() {
      this.advancedFields = [
        {
          label: '统一社会信用代码',
          key: 'creditCode',
          type: 'input',
          placeholder: '请输入统一社会信用代码',
          span: 6
        },
        {
          label: '单位地址',
          key: 'address',
          type: 'input',
          placeholder: '请输入单位地址',
          span: 6
        }
      ];
    },

    /** 查询责任单位列表 */
    getList() {
      this.loading = true;
      listEnterprise(this.queryParams).then(response => {
        this.enterpriseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    // 处理搜索
    handleSearch(searchForm) {
      this.queryParams = {
        ...this.queryParams,
        ...searchForm,
        pageNum: 1
      };
      this.getList();
    },

    // 处理重置
    handleReset(searchForm) {
      this.searchForm = searchForm;
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        enterpriseName: null,
        unitProperties: null,
        creditCode: null,
        address: null,
        principal: null,
        principalPhone: null,
        principalDept: null,
        principalDuties: null,
        ...searchForm
      };
      this.getList();
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    // 表单重置
    reset() {
      this.form = {
        enterpriseId: null,
        enterpriseName: null,
        unitProperties: null,
        creditCode: null,
        address: null,
        principal: null,
        principalPhone: null,
        principalDept: null,
        principalDuties: null,
        fileUrl: null,
        screenNum: null,
        inspectionNum: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        unitPropertieList: []
      };
      this.resetForm("form");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.enterpriseId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加责任单位";
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const enterpriseId = row.enterpriseId || this.ids
      getEnterprise(enterpriseId).then(response => {
        this.form = response.data;
        // 处理单位性质多选
        if (this.form.unitProperties) {
          this.form.unitPropertieList = this.form.unitProperties.split(',');
        }
        this.open = true;
        this.title = "修改责任单位";
      });
    },

    /** 查看按钮操作 */
    handleView(row) {
      this.$router.push("/data-management/screen/enterprise/detail/" + row.enterpriseId);
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 处理单位性质多选
          if (this.form.unitPropertieList && this.form.unitPropertieList.length > 0) {
            this.form.unitProperties = this.form.unitPropertieList.join(',');
          }

          if (this.form.enterpriseId != null) {
            updateEnterprise(this.form).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addEnterprise(this.form).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const enterpriseIds = row.enterpriseId || this.ids;
      this.$modal.confirm('是否确认删除责任单位编号为"' + enterpriseIds + '"的数据项？').then(function() {
        return delEnterprise(enterpriseIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download( 'screen/enterprise/export', {
        ...this.queryParams
      }, `责任单位_${new Date().getTime()}.xlsx`)
    },

    // 获取用户区域列表
    getUserAreaList() {
      listUserArea().then(response => {
        this.userAreaList = response.data;
        this.initAdvancedFields();
      });
    },

    // 设置提醒
    handleReminder() {
      this.reminderVisible = true;
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .screen-management-layout {
    display: flex;
    gap: 20px;
    height: 100%;

    .content-area {
      flex: 1;
      min-width: 0;
    }
  }

  .action-bar {
    background: white;
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
  }
  .toolbar-right {float:right;}
  .table-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
  }
}

// 表格样式
::v-deep .el-table {
  .text-nowrap {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// 名称角标样式
.site-name-container {
  position: relative;
  display: inline-block;
  padding: 12px 12px 0 0;
  .update-badge {
    position: absolute;
    top: -2px;
    right: -10px;
    font-size: 10px;
    padding: 1px 4px;
    border-radius: 2px;
    // color: white;
    white-space: nowrap;
    z-index: 1;

    &.pending {
      color: #FE9400;
    }

    &.overdue {
      color: #FF2A2A;
    }
  }
}
</style>
