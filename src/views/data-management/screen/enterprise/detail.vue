<template>
  <div class="app-container">
    <div class="page-header">
      <div class="header-content">
        
        <el-button icon="el-icon-arrow-left" @click="goBack" size="small">返回</el-button>
        
      </div>
    </div>

    <div class="detail-container" v-loading="loading">
      <!-- 基本信息模块 -->
      <div class="detail-section">
        <div class="section-header" @click="toggleSection('basic')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">基本信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.basic ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.basic ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        
        <div v-show="!sectionCollapsed.basic" class="section-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="运营使用单位名称">
              {{ form.enterpriseName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="单位统一社会信用代码">
              {{ form.creditCode || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="单位性质">
              <dict-tag v-for="item in (form.unitProperties || '').split(',')" :key="item" :options="dict.type.unit_properties" :value="item"/>
            </el-descriptions-item>
            <el-descriptions-item label="单位办公地址">
              {{ form.address || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="联系人姓名">
              {{ form.principal || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="联系人电话">
              {{ form.principalPhone || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="联系人所属部门">
              {{ form.principalDept || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="联系人职务">
              {{ form.principalDuties || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="承诺书" :span="2">
              <image-preview v-if="form.fileUrl" :src="form.fileUrl" :width="100" :height="100"/>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ parseTime(form.createTime) || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="修改时间">
              {{ parseTime(form.updateTime) || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 电子屏列表模块 -->
      <div class="detail-section">
        <div class="section-header" @click="toggleSection('screens')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">电子屏列表</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.screens ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.screens ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        
        <div v-show="!sectionCollapsed.screens" class="section-content">
          <el-table :data="screenList" :header-cell-style="{ backgroundColor: '#EBF2FF' }" border>
            <el-table-column label="电子屏名称" align="center" prop="screenName" />
            <el-table-column label="照片" align="center" prop="images" width="80">
              <template slot-scope="scope">
                <image-viewer :src="scope.row.images" :width="50" :height="50" title="电子屏照片"/>
              </template>
            </el-table-column>
            <el-table-column label="所属派出所" align="center" prop="policeName"/>
            <el-table-column label="归属场所" align="center" prop="site.siteName"/>
            <el-table-column label="重点区域" align="center" width="150">
              <template slot-scope="scope">
                <div v-if="scope.row.keyareaList && scope.row.keyareaList.length>0">
                  <el-tag 
                    v-for="(item, index) in scope.row.keyareaList" 
                    :key="index"
                    class="keyarea-tag"
                    style="margin: 2px;"
                  >{{ item.keyareaName }}</el-tag>
                </div>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="专项" align="center" width="150">
              <template slot-scope="scope">
                <div v-if="scope.row.specialList && scope.row.specialList.length>0">
                  <el-tag 
                    v-for="(item, index) in scope.row.specialList" 
                    :key="index"
                    class="special-tag"
                    style="margin: 2px;"
                  >{{ item.specialName }}</el-tag>
                </div>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="100">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="viewScreen(scope.row)"
                >查看</el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <pagination
            v-show="screenTotal>0"
            :total="screenTotal"
            :page.sync="screenParams.pageNum"
            :limit.sync="screenParams.pageSize"
            @pagination="queryListScreen"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getEnterprise } from "@/api/screen/enterprise";
import { listScreen } from "@/api/screen/screen";
import ImageViewer from '@/components/ImageViewer'

export default {
  name: "EnterpriseDetail",
  dicts: ['unit_properties'],
  components: { ImageViewer },
  data() {
    return {
      loading: false,
      // 表单参数
      form: {},
      
      // 电子屏列表参数
      screenParams: {
        pageNum: 1,
        pageSize: 10,
        enterpriseId: null,
      },
      screenTotal: 0,
      screenList: [],
      
      // 模块折叠状态
      sectionCollapsed: {
        basic: false,
        screens: false
      }
    };
  },
  created() {
    const enterpriseId = this.$route.params.id || this.$route.query.enterpriseId;
    if (enterpriseId) {
      this.form.enterpriseId = enterpriseId;
      this.getEnterprise(enterpriseId);
      this.screenParams.enterpriseId = enterpriseId;
      this.queryListScreen();
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    
    /** 查询责任单位详细 */
    getEnterprise(enterpriseId) {
      this.loading = true;
      getEnterprise(enterpriseId).then(response => {
        this.form = response.data;
        this.loading = false;
      });
    },
    
    // 查询电子屏列表
    queryListScreen(){
      listScreen(this.screenParams).then(response => {
        this.screenList = response.rows;
        this.screenTotal = response.total;
      })
    },
    
    // 查看电子屏详情
    viewScreen(row) {
      this.$router.push("/data-management/screen/detail/" + row.screenId);
    },
    
    // 切换模块折叠状态
    toggleSection(section) {
      this.sectionCollapsed[section] = !this.sectionCollapsed[section];
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

   .page-header {
     width: 94%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      justify-content: space-between;
      .page-title {
        margin: 0;
        font-size: 18px;
        color: #303133;
      }
    }
  }

  .detail-container {
    width: 94%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
    
    .detail-section {
      margin-bottom: 24px;

      .section-header {
        background-color: #f6f7fb;
        padding: 12px 16px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 4px;
        margin-bottom: 16px;

        .section-title {
          display: flex;
          align-items: center;
          gap: 8px;

          .title-line {
            width: 3px;
            height: 16px;
            background-color: #4584FF;
          }

          .title-text {
            font-size: 16px;
            font-weight: 600;
            color: #4584FF;
          }
        }

        .section-toggle {
          display: flex;
          align-items: center;
          gap: 4px;
          color: #4584FF;
          font-size: 14px;

          .toggle-text {
            font-size: 14px;
          }

          i {
            font-size: 12px;
          }
        }

        &:hover {
          background-color: #f0f2f5;
        }
      }

      .section-content {
        padding: 0 16px;
      }
    }
    
    .keyarea-tag {
      border-color: #4584FF;
      color: #4584FF;
      background-color: #E9F1FF;
    }
    
    .special-tag {
      border-color: #19BB77;
      color: #19BB77;
      background-color: #EAFEF6;
    }
  }
}
</style>
