<template>
  <div class="app-container">
    <div class="page-header">
      <div class="header-content">
        
        <el-button icon="el-icon-arrow-left" @click="goBack" size="small">返回</el-button>
        
      </div>
    </div>

    <div class="detail-container" v-loading="loading">
      <!-- 基本信息模块 -->
      <div class="detail-section">
        <div class="section-header" @click="toggleSection('basic')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">基本信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.basic ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.basic ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        
        <div v-show="!sectionCollapsed.basic" class="section-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="联网系统名称">
              {{ form.netsystemName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="等级保护级别">
              <dict-tag :options="dict.type.grade_protection_level" :value="form.protectionLevel"/>
            </el-descriptions-item>
            <el-descriptions-item label="等级保护备案机关">
              {{ form.protectionLevelOrgan || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="域名/地址">
              {{ form.accessUrl || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="连接控制系统方式">
              <dict-tag :options="dict.type.screen_control_sys_way" :value="form.connectType"/>
            </el-descriptions-item>
            <el-descriptions-item label="控制系统运行状态">
              <dict-tag :options="dict.type.screen_control_sys_status" :value="form.runStatus"/>
            </el-descriptions-item>
            <el-descriptions-item label="系统开发/制造商">
              {{ form.manufacturer || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="开发厂商统一社会信用代码">
              {{ form.manufacturerCreditCode || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="开发厂商负责人姓名">
              {{ form.principal || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="开发厂商负责人电话">
              {{ form.principalPhone || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="运维厂商名称">
              {{ form.operation || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="运维厂商统一社会信用代码">
              {{ form.operationCreditCode || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="运维厂商负责人">
              {{ form.operationPrincipal || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="运维厂商负责人电话">
              {{ form.operationPrincipalPhone || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="是否建立内容审核机制">
              <dict-tag :options="dict.type.sys_yes_no" :value="form.examineFlag"/>
            </el-descriptions-item>
            <el-descriptions-item label="是否建立一键关停等应急措施">
              <dict-tag :options="dict.type.sys_yes_no" :value="form.shutdownFlag"/>
            </el-descriptions-item>
            <el-descriptions-item label="是否完成两高一弱专项整治">
              <dict-tag :options="dict.type.sys_yes_no" :value="form.rectificationFlag"/>
            </el-descriptions-item>
            <el-descriptions-item label="物理位置">
              {{ form.address || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ parseTime(form.createTime) || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="更新时间">
              {{ parseTime(form.updateTime) || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 电子屏列表模块 -->
      <div class="detail-section">
        <div class="section-header" @click="toggleSection('screens')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">电子屏列表</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.screens ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.screens ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        
        <div v-show="!sectionCollapsed.screens" class="section-content">
          <el-table :data="screenList" :header-cell-style="{ backgroundColor: '#EBF2FF' }" border>
            <el-table-column label="电子屏名称" align="center" prop="screenName" />
            <el-table-column label="照片" align="center" prop="images" width="80">
              <template slot-scope="scope">
                <image-viewer :src="scope.row.images" :width="50" :height="50" title="电子屏照片"/>
              </template>
            </el-table-column>
            <el-table-column label="所属派出所" align="center" prop="policeName"/>
            <el-table-column label="责任单位" align="center" prop="enterprise.enterpriseName"/>
            <el-table-column label="归属场所" align="center" prop="site.siteName"/>
            <el-table-column label="重点区域" align="center" width="150">
              <template slot-scope="scope">
                <div v-if="scope.row.keyareaList && scope.row.keyareaList.length>0">
                  <el-tag 
                    v-for="(item, index) in scope.row.keyareaList" 
                    :key="index"
                    class="keyarea-tag"
                    style="margin: 2px;"
                  >{{ item.keyareaName }}</el-tag>
                </div>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="专项" align="center" width="150">
              <template slot-scope="scope">
                <div v-if="scope.row.specialList && scope.row.specialList.length>0">
                  <el-tag 
                    v-for="(item, index) in scope.row.specialList" 
                    :key="index"
                    class="special-tag"
                    style="margin: 2px;"
                  >{{ item.specialName }}</el-tag>
                </div>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="100">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="viewScreen(scope.row)"
                >查看</el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <pagination
            v-show="screenTotal>0"
            :total="screenTotal"
            :page.sync="screenParams.pageNum"
            :limit.sync="screenParams.pageSize"
            @pagination="queryListScreen"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getNetsystem } from "@/api/screen/netsystem";
import { listScreen } from "@/api/screen/screen";
import ImageViewer from '@/components/ImageViewer'

export default {
  name: "NetsystemDetail",
  dicts: ['screen_control_sys_way', 'grade_protection_level', 'screen_control_sys_status', 'sys_yes_no'],
  components: { ImageViewer },
  data() {
    return {
      loading: false,
      // 表单参数
      form: {},
      
      // 电子屏列表参数
      screenParams: {
        pageNum: 1,
        pageSize: 10,
        netsystemId: null,
      },
      screenTotal: 0,
      screenList: [],
      
      // 模块折叠状态
      sectionCollapsed: {
        basic: false,
        screens: false
      }
    };
  },
  created() {
    const netsystemId = this.$route.params && this.$route.params.id;
    this.getNetsystem(netsystemId);
    this.screenParams.netsystemId = netsystemId;
    this.queryListScreen();
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    
    /** 查询联网系统详细 */
    getNetsystem(netsystemId) {
      this.loading = true;
      getNetsystem(netsystemId).then(response => {
        this.form = response.data;
        this.loading = false;
      });
    },
    
    // 查询电子屏列表
    queryListScreen(){
      listScreen(this.screenParams).then(response => {
        this.screenList = response.rows;
        this.screenTotal = response.total;
      })
    },
    
    // 查看电子屏详情
    viewScreen(row) {
      this.$router.push("/data-management/screen/detail/" + row.screenId);
    },
    
    // 切换模块折叠状态
    toggleSection(section) {
      this.sectionCollapsed[section] = !this.sectionCollapsed[section];
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .page-header {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;

      .page-header {
     width: 94%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      justify-content: space-between;
      .page-title {
        margin: 0;
        font-size: 18px;
        color: #303133;
      }
    }
  }
  }

  .detail-container {
    width: 98%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
    
    .detail-section {
      margin-bottom: 24px;

      .section-header {
        background-color: #f6f7fb;
        padding: 12px 16px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 4px;
        margin-bottom: 16px;

        .section-title {
          display: flex;
          align-items: center;
          gap: 8px;

          .title-line {
            width: 3px;
            height: 16px;
            background-color: #4584FF;
          }

          .title-text {
            font-size: 16px;
            font-weight: 600;
            color: #4584FF;
          }
        }

        .section-toggle {
          display: flex;
          align-items: center;
          gap: 4px;
          color: #4584FF;
          font-size: 14px;

          .toggle-text {
            font-size: 14px;
          }

          i {
            font-size: 12px;
          }
        }

        &:hover {
          background-color: #f0f2f5;
        }
      }

      .section-content {
        padding: 0 16px;
      }
    }
    
    .keyarea-tag {
      border-color: #4584FF;
      color: #4584FF;
      background-color: #E9F1FF;
    }
    
    .special-tag {
      border-color: #19BB77;
      color: #19BB77;
      background-color: #EAFEF6;
    }
  }
}
</style>
