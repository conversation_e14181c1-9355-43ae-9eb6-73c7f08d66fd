<template>
  <div class="app-container">
    <div class="screen-management-layout">
      <!-- 左侧导航菜单 -->
      <basic-data-sidebar />

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <!-- 搜索组件 -->
        <common-search
          :search-config="searchConfig"
          :advanced-fields="advancedFields"
          :initial-form="searchForm"
          @search="handleSearch"
          @reset="handleReset"
        />

    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <el-row :gutter="10" class="mb8" style="width:100%;">
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['screen:netsystem:export']"
          >导出</el-button>
        </el-col>
        <el-col :span="1.5">
           <el-button
            type="success"
            icon="el-icon-plus"
            size="mini"
            @click="handleReminder"
            v-hasPermi="['screen:netsystem:add']"
          >设置到期提醒</el-button>
        </el-col>

        <div class="toolbar-right">
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </div>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table 
        v-loading="loading" 
        :data="netsystemList" 
        :header-cell-style="{ backgroundColor: '#EBF2FF' }"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" fixed="left" />
        <el-table-column label="联网系统名称" align="center" show-overflow-tooltip prop="netsystemName" min-width="120">
          <template slot-scope="scope">
            <div class="site-name-container">
              <span :title="scope.row.netsystemName">{{ scope.row.netsystemName }}</span>
              <span
                v-if="scope.row.dataUpdateStatus === 1"
                class="update-badge pending"
              >待更新</span>
              <span
                v-else-if="scope.row.dataUpdateStatus > 1"
                class="update-badge overdue"
              >已超期</span>
            </div>
            <br/>
            <span :title="scope.row.principal">{{ scope.row.principal }}</span>
            <br/>
            <span :title="scope.row.principalPhone">{{ scope.row.principalPhone }}</span>
          </template>
        </el-table-column>

        <el-table-column label="等级保护级别" align="center" show-overflow-tooltip prop="protectionLevel" min-width="120">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.grade_protection_level" :value="scope.row.protectionLevel"/>
          </template>
        </el-table-column>
        <el-table-column label="等级保护备案机关" align="center" show-overflow-tooltip prop="protectionLevelOrgan" min-width="130" />
        <el-table-column label="系统开发/制造商" align="center" show-overflow-tooltip prop="manufacturer" min-width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.manufacturer }}</span>
            <br/>
            <span>{{ scope.row.manufacturerCreditCode }}</span>
          </template>
        </el-table-column>
        <el-table-column label="运维厂商" align="center" show-overflow-tooltip prop="operation">
          <template slot-scope="scope">
            <span>{{ scope.row.operation }}</span>
            <br/>
            <span>{{ scope.row.operationCreditCode }}</span>
            <br/>
            <span>{{ scope.row.operationPrincipal }}</span>
          </template>
        </el-table-column>

        <el-table-column label="连接控制系统方式" align="center" show-overflow-tooltip prop="connectType" min-width="130">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.screen_control_sys_way" :value="scope.row.connectType"/>
          </template>
        </el-table-column>

        <el-table-column label="联网系统物理位置" align="center" show-overflow-tooltip prop="address" min-width="130" />
        <el-table-column label="联网系统域名/地址" align="center" show-overflow-tooltip prop="accessUrl" min-width="130" />
        <el-table-column label="控制系统运行状态" align="center" show-overflow-tooltip prop="runStatus" min-width="130">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.screen_control_sys_status" :value="scope.row.runStatus"/>
          </template>
        </el-table-column>
        <el-table-column label="电子屏数" align="center" prop="screenNum" width="90">
          <template slot-scope="scope">
            <a href="javascript:void(0);" style="color: #409EFF;" @click="$router.push('/data-management/screen?netsystemId='+scope.row.netsystemId+'&netsystemName='+ scope.row.netsystemName)">
              {{ scope.row.screenNum }}
            </a>
          </template>
        </el-table-column>
        <el-table-column label="检查次数" align="center" prop="inspectionNum" width="90"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['screen:netsystem:query']"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['screen:netsystem:edit']"
            >修改</el-button>
            <el-button
              v-if="scope.row.screenNum==0"
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['screen:netsystem:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 添加或修改联网系统对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="950px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-row :gutter="10" class="mb8">
          <el-col :span="8">
            <el-form-item label="联网系统名称" prop="netsystemName">
              <el-input v-model="form.netsystemName" placeholder="请输入联网系统名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="等级保护级别" prop="protectionLevel">
              <el-select v-model="form.protectionLevel" style="width: 100%;" placeholder="请选择等级保护级别">
                <el-option
                  v-for="dict in dict.type.grade_protection_level"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="等级保护备案机关" prop="protectionLevelOrgan">
              <el-input v-model="form.protectionLevelOrgan" placeholder="等级保护备案机关" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="8">
            <el-form-item label="域名/地址" prop="accessUrl">
              <el-input v-model="form.accessUrl" placeholder="请输入联网系统域名/地址" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="连接控制系统方式" prop="connectType">
              <el-select v-model="form.connectType" style="width: 100%;" placeholder="请选择连接控制系统方式">
                <el-option
                  v-for="dict in dict.type.screen_control_sys_way"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="控制系统运行状态" prop="runStatus">
              <el-select v-model="form.runStatus" style="width: 100%;" placeholder="请选择控制系统运行状态">
                <el-option
                  v-for="dict in dict.type.screen_control_sys_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="24">
            <el-form-item label="物理位置" prop="address">
              <el-input v-model="form.address" placeholder="请输入后台控制系统安装部位所在物理位置" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="12">
            <el-form-item label="系统开发/制造商" prop="manufacturer">
              <el-input v-model="form.manufacturer" placeholder="请输入后台控制系统研发厂商" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开发厂商统一社会信用代码" prop="manufacturerCreditCode">
              <el-input v-model="form.manufacturerCreditCode" placeholder="请输入开发厂商单位统一社会信用代码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="12">
            <el-form-item label="开发厂商负责人姓名" prop="principal">
              <el-input v-model="form.principal" placeholder="请输入开发厂商负责人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开发厂商负责人电话" prop="principalPhone">
              <el-input v-model="form.principalPhone" placeholder="请输入开发厂商负责人电话" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="12">
            <el-form-item label="运维厂商名称" prop="operation">
              <el-input v-model="form.operation" placeholder="请输入运维厂商名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="运维厂商统一社会信用代码" prop="operationCreditCode">
              <el-input v-model="form.operationCreditCode" placeholder="请输入运维厂商单位统一社会信用代码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="12">
            <el-form-item label="运维厂商负责人" prop="operationPrincipal">
              <el-input v-model="form.operationPrincipal" placeholder="请输入运维厂商负责人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="运维厂商负责人电话" prop="operationPrincipalPhone">
              <el-input v-model="form.operationPrincipalPhone" placeholder="请输入运维厂商负责人电话" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="8">
            <el-form-item label="是否建立内容审核机制" prop="examineFlag">
              <el-radio-group v-model="form.examineFlag">
                <el-radio
                  v-for="dict in dict.type.sys_yes_no"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否建立一键关停等应急措施" prop="shutdownFlag">
              <el-radio-group v-model="form.shutdownFlag">
                <el-radio
                  v-for="dict in dict.type.sys_yes_no"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否完成两高一弱专项整治" prop="rectificationFlag">
              <el-radio-group v-model="form.rectificationFlag">
                <el-radio
                  v-for="dict in dict.type.sys_yes_no"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
      </div>
    </div>

    <!-- 数据更新提醒组件 -->
    <data-update-reminder
      :visible.sync="reminderVisible"
      :biz-type="'SCREEN'"
    />
  </div>
</template>

<script>
import { listNetsystem, getNetsystem, delNetsystem, addNetsystem, updateNetsystem } from "@/api/screen/netsystem";
import CommonSearch from '@/components/CommonSearch'
import BasicDataSidebar from '@/components/BasicDataSidebar'
import DataUpdateReminder from '@/components/DataUpdateReminder'

export default {
  name: "Netsystem",
  dicts: ['system_type', 'screen_control_sys_way', 'grade_protection_level', 'screen_control_sys_status', 'sys_yes_no'],
  components: { CommonSearch, BasicDataSidebar, DataUpdateReminder },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 联网系统表格数据
      netsystemList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 提醒组件显示状态
      reminderVisible: false,

      // 搜索配置
      searchConfig: {
        label: '联网系统名称',
        key: 'netsystemName',
        placeholder: '请输入联网系统名称'
      },
      
      // 高级搜索字段
      advancedFields: [],
      
      // 搜索表单
      searchForm: {},
      
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        netsystemName: null,
        protectionLevel: null,
        protectionLevelOrgan: null,
        connectType: null,
        address: null,
        principal: null,
        principalPhone: null,
        accessUrl: null,
        runStatus: null,
        manufacturer: null,
        manufacturerCreditCode: null,
        operation: null,
        operationCreditCode: null,
        operationPrincipal: null,
        operationPrincipalPhone: null,
      },
      
      // 表单参数
      form: {},
      
      // 表单校验
      rules: {
        netsystemName: [
          { required: true, message: "联网系统名称不能为空", trigger: "blur" }
        ],
        address: [
          { required: true, message: "后台控制系统安装部位所在物理位置不能为空", trigger: "blur" }
        ],
        accessUrl: [
          { required: true, message: "联网系统域名/地址不能为空", trigger: "blur" }
        ],
        connectType: [
          { required: true, message: "连接控制系统方式不能为空", trigger: "change" }
        ],
        runStatus: [
          { required: true, message: "控制系统运行状态不能为空", trigger: "change" }
        ],
        manufacturer: [
          { required: true, message: "后台控制系统研发厂商不能为空", trigger: "blur" }
        ],
        manufacturerCreditCode: [
          { required: true, message: "开发厂商单位统一社会信用代码不能为空", trigger: "blur" }
        ],
        principal: [
          { required: true, message: "开发厂商负责人姓名不能为空", trigger: "blur" }
        ],
        principalPhone: [
          { required: true, message: "开发厂商负责人电话不能为空", trigger: "blur" }
        ],
        operation: [
          { required: true, message: "运维厂商名称不能为空", trigger: "blur" }
        ],
        operationCreditCode: [
          { required: true, message: "运维厂商单位统一社会信用代码不能为空", trigger: "blur" }
        ],
        operationPrincipal: [
          { required: true, message: "运维厂商负责人不能为空", trigger: "blur" }
        ],
        operationPrincipalPhone: [
          { required: true, message: "运维厂商负责人电话不能为空", trigger: "blur" }
        ],
        examineFlag: [
          { required: true, message: "请选择是否建立内容审核机制", trigger: "change" }
        ],
        shutdownFlag: [
          { required: true, message: "请选择是否建立一键关停等应急措施", trigger: "change" }
        ],
        rectificationFlag: [
          { required: true, message: "请选择是否完成两高一弱专项整治", trigger: "change" }
        ],
        protectionLevel: [
          { required: true, message: "等级保护级别不能为空", trigger: "change" }
        ],
        protectionLevelOrgan: [
          { required: true, message: "等级保护备案机关不能为空", trigger: "change" }
        ],
      },
      
    };
  },
  created() {
    this.getList();
  },
  methods: {

    /** 查询联网系统列表 */
    getList() {
      this.loading = true;
      listNetsystem(this.queryParams).then(response => {
        this.netsystemList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    // 处理搜索
    handleSearch(searchForm) {
      this.queryParams = {
        ...this.queryParams,
        ...searchForm,
        pageNum: 1
      };
      this.getList();
    },

    // 处理重置
    handleReset(searchForm) {
      this.searchForm = searchForm;
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        ...searchForm
      };
      this.getList();
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    // 表单重置
    reset() {
      this.form = {
        netsystemId: null,
        netsystemName: null,
        protectionLevel: null,
        protectionLevelOrgan: null,
        connectType: null,
        address: null,
        principal: null,
        principalPhone: null,
        accessUrl: null,
        runStatus: null,
        manufacturer: null,
        manufacturerCreditCode: null,
        operation: null,
        operationCreditCode: null,
        operationPrincipal: null,
        operationPrincipalPhone: null,
        examineFlag: null,
        shutdownFlag: null,
        rectificationFlag: null,
        screenNum: null,
        inspectionNum: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.netsystemId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加联网系统";
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const netsystemId = row.netsystemId || this.ids
      getNetsystem(netsystemId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改联网系统";
      });
    },

    /** 查看按钮操作 */
    handleView(row) {
      this.$router.push("/data-management/screen/netsystem/detail/" + row.netsystemId);
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.netsystemId != null) {
            updateNetsystem(this.form).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addNetsystem(this.form).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const netsystemIds = row.netsystemId || this.ids;
      this.$modal.confirm('是否确认删除联网系统编号为"' + netsystemIds + '"的数据项？').then(function() {
        return delNetsystem(netsystemIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('screen/netsystem/export', {
        ...this.queryParams
      }, `联网系统_${new Date().getTime()}.xlsx`)
    },

    // 设置提醒
    handleReminder() {
      this.reminderVisible = true;
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .screen-management-layout {
    display: flex;
    gap: 20px;
    height: 100%;

    .content-area {
      flex: 1;
      min-width: 0;
    }
  }

  .action-bar {
    background: white;
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
  }
  .toolbar-right {float:right;}
  .table-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
  }
}

// 表格样式
::v-deep .el-table {
  .text-nowrap {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// 名称角标样式
.site-name-container {
  position: relative;
  display: inline-block;
  padding: 12px 12px 0 0;
  .update-badge {
    position: absolute;
    top: -2px;
    right: -10px;
    font-size: 10px;
    padding: 1px 4px;
    border-radius: 2px;
    // color: white;
    white-space: nowrap;
    z-index: 1;

    &.pending {
      color: #FE9400;
    }

    &.overdue {
      color: #FF2A2A;
    }
  }
}
</style>
