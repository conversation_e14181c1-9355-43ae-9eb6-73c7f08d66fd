<template>
  <div class="app-container">
    <div class="screen-management-layout">
      <!-- 左侧导航菜单 -->
      <basic-data-sidebar />

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <!-- 搜索组件 -->
        <common-search
          :search-config="searchConfig"
          :advanced-fields="advancedFields"
          :initial-form="searchForm"
          @search="handleSearch"
          @reset="handleReset"
        />

    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <el-row :gutter="10" class="mb8" style="width:100%;">
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['screen:site:export']"
          >导出</el-button>
        </el-col>
        <el-col :span="1.5">
           <el-button
            type="success"
            icon="el-icon-plus"
            size="mini"
            @click="handleReminder"
            v-hasPermi="['screen:site:add']"
          >设置到期提醒</el-button>
        </el-col>

        <div class="toolbar-right">
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </div>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table 
        v-loading="loading" 
        :data="siteList" 
        :header-cell-style="{ backgroundColor: '#EBF2FF' }"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" fixed="left" />
        <el-table-column label="归属场所" align="center" prop="siteName" min-width="120">
          <template slot-scope="scope">
            <div class="site-name-container">
              <span>{{ scope.row.siteName }}</span>
              <span
                v-if="scope.row.dataUpdateStatus === 1"
                class="update-badge pending"
              >待更新</span>
              <span
                v-else-if="scope.row.dataUpdateStatus > 1"
                class="update-badge overdue"
              >已超期</span>
            </div>
            <br/>
            <span>{{ scope.row.siteAddress }}</span>
          </template>
        </el-table-column>
        <el-table-column label="场所类型" align="center" prop="siteType" width="140">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.site_type" :value="scope.row.siteType"/>
          </template>
        </el-table-column>
        <el-table-column label="负责人/电话" align="center" width="120">
          <template slot-scope="scope">
            <div class="text-nowrap" :title="scope.row.principal">{{ scope.row.principal }}</div>
            <div class="text-nowrap" :title="scope.row.principalPhone">{{ scope.row.principalPhone }}</div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="所属派出所" align="center" prop="policeName" width="120">
          <template slot-scope="scope">
            <div class="text-nowrap" :title="scope.row.policeName">{{ scope.row.policeName }}</div>
          </template>
        </el-table-column> -->
      <el-table-column label="场所状态" align="center" show-overflow-tooltip prop="siteStatus" min-width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.site_business_status"    :value="scope.row.siteStatus"/>
        </template>
      </el-table-column>
        <el-table-column label="电子屏数" align="center" prop="screenNum" width="90">
          <template slot-scope="scope">
            <a href="javascript:void(0);" style="color: #409EFF;" @click="$router.push('/data-management/screen?siteCode='+scope.row.siteCode+'&siteName='+ scope.row.siteName)">
              {{ scope.row.screenNum }}
            </a>
          </template>
        </el-table-column>
        <el-table-column label="检查次数" align="center" prop="inspectionNum" width="90"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['screen:site:query']"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['screen:site:edit']"
            >修改</el-button>
            <el-button
              v-if="scope.row.screenNum==0"
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['screen:site:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 添加或修改归属场所对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <!-- <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="场所名称" prop="siteName">
              <el-input v-model="form.siteName" placeholder="请输入场所名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="场所类型" prop="siteType">
              <el-select v-model="form.siteType" placeholder="请选择场所类型" style="width: 100%">
                <el-option
                  v-for="dict in dict.type.site_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="场所地址" prop="siteAddress">
              <el-input v-model="form.siteAddress" placeholder="请输入场所地址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="负责人" prop="principal">
              <el-input v-model="form.principal" placeholder="请输入负责人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人电话" prop="principalPhone">
              <el-input v-model="form.principalPhone" placeholder="请输入负责人电话" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="负责人证件类型" prop="principalIdType">
              <el-select v-model="form.principalIdType" placeholder="请选择负责人证件类型" style="width: 100%">
                <el-option
                  v-for="dict in dict.type.certificate_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人证件号码" prop="principalIdNumber">
              <el-input v-model="form.principalIdNumber" placeholder="请输入负责人证件号码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="区域" prop="areaCode">
              <el-select v-model="form.areaCode" placeholder="请选择区域" style="width: 100%" @change="handleAreaChange">
                <el-option
                  v-for="area in userAreaList"
                  :key="area.areaCode"
                  :label="area.areaName"
                  :value="area.areaCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="派出所" prop="policeCode">
              <el-select v-model="form.policeCode" placeholder="请选择派出所" style="width: 100%">
                <el-option
                  v-for="police in policeList"
                  :key="police.areaCode"
                  :label="police.areaName"
                  :value="police.areaCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row> -->
                <el-row :gutter="10" class="mb8">
          <el-col :span="8">
            <el-form-item label="场所名称" prop="siteName">
              <el-input v-model="form.siteName" placeholder="请输入场所名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="场所状态" prop="siteStatus">
              <el-select v-model="form.siteStatus" style="width: 100%;" placeholder="请选择场所状态">
                <el-option
                  v-for="dict in dict.type.site_business_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="场所类型" prop="siteType">
              <el-select v-model="form.siteType" style="width: 100%;" placeholder="请选择场所类型">
                <el-option
                  v-for="dict in dict.type.site_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="8">
            <el-form-item label="负责人姓名" prop="principal">
              <el-input v-model="form.principal" placeholder="请输入负责人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="负责人电话" prop="principalPhone">
              <el-input v-model="form.principalPhone" placeholder="请输入负责人电话" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="负责人证件类型" prop="principalCredentialType">
              <el-select v-model="form.principalCredentialType" style="width: 100%;" placeholder="请选择场所负责人证件类型">
                <el-option
                  v-for="dict in dict.type.certificate_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="8">
            <el-form-item label="负责人证件号码" prop="principalCertificateCode">
              <el-input v-model="form.principalCertificateCode" placeholder="请输入场所负责人证件号码" />
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="场所地址" prop="siteAddress">
              <el-input v-model="form.siteAddress" placeholder="请输入场所地址" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
      </div>
    </div>

    <!-- 数据更新提醒组件 -->
    <data-update-reminder
      :visible.sync="reminderVisible"
      :biz-type="'SCREEN'"
    />
  </div>
</template>

<script>
import { listSite, getSite, delSite, addSite, updateSite } from "@/api/screen/site";
import { listUserArea, listArea } from '@/api/system/area';
import CommonSearch from '@/components/CommonSearch'
import BasicDataSidebar from '@/components/BasicDataSidebar'
import DataUpdateReminder from '@/components/DataUpdateReminder'

export default {
  name: "Site",
  dicts: ['site_type','site_business_status', 'certificate_type'],
  components: { CommonSearch, BasicDataSidebar, DataUpdateReminder },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 归属场所表格数据
      siteList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 提醒组件显示状态
      reminderVisible: false,

      // 搜索配置
      searchConfig: {
        label: '场所名称',
        key: 'siteName',
        placeholder: '请输入场所名称'
      },
      
      // 高级搜索字段
      advancedFields: [],
      
      // 搜索表单
      searchForm: {},
      
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        siteName: null,
        siteType: null,
        siteAddress: null,
        principal: null,
        principalPhone: null,
        areaCode: null,
        policeCode: null
      },
      
       // 表单参数
      form: {},
      // 表单校验
      rules: {
        siteName: [
          { required: true, message: "场所名称不能为空", trigger: "blur" }
        ],
        siteType: [
          { required: true, message: "场所类型不能为空", trigger: "change" }
        ],
        siteStatus: [
          { required: true, message: "场所状态不能为空", trigger: "change" }
        ],
        siteAddress: [
          { required: true, message: "场所地址不能为空", trigger: "blur" }
        ],
        principal: [
          { required: true, message: "负责人姓名不能为空", trigger: "blur" }
        ],
        principalPhone: [
          { required: true, message: "负责人电话不能为空", trigger: "blur" }
        ],
      },
      userAreaList: [],
      policeList: []
    };
  },
  created() {
    this.getList();
    this.getUserAreaList();
    this.initAdvancedFields();
  },
  methods: {
    // 初始化高级搜索字段
    initAdvancedFields() {
      this.advancedFields = [
        {
          label: '场所类型',
          key: 'siteType',
          type: 'select',
          placeholder: '请选择场所类型',
          span: 6,
          options: (this.dict && this.dict.type && this.dict.type.site_type) ? this.dict.type.site_type.map(item => ({
            label: item.label,
            value: item.value
          })) : []
        },
        {
          label: '场所地址',
          key: 'siteAddress',
          type: 'input',
          placeholder: '请输入场所地址',
          span: 6
        },
        {
          label: '负责人',
          key: 'principal',
          type: 'input',
          placeholder: '请输入负责人姓名',
          span: 6
        },
      ];
    },

    /** 查询归属场所列表 */
    getList() {
      this.loading = true;
      listSite(this.queryParams).then(response => {
        this.siteList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    // 处理搜索
    handleSearch(searchForm) {
      this.queryParams = {
        ...this.queryParams,
        ...searchForm,
        pageNum: 1
      };
      this.getList();
    },
     // 处理重置
    handleReset(searchForm) {
      this.searchForm = searchForm;
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        ...searchForm
      };
      this.getList();
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    // 表单重置
    reset() {
       this.form = {
        siteCode: null,
        siteName: null,
        siteCreditCode: null,
        siteAreaCode: null,
        siteAreaName: null,
        siteLongitude: null,
        siteLatitude: null,
        siteAddress: null,
        legalPerson: null,
        legalPhone: null,
        legalCredentialType: null,
        legalCertificateCode: null,
        principal: null,
        principalPhone: null,
        principalCredentialType: null,
        principalCertificateCode: null,
        siteType: null,
        siteStatus: null,
        screenNum: null,
        inspectionNum: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.siteCode)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加归属场所";
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const siteCode = row.siteCode || this.ids
      getSite(siteCode).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改归属场所";

        // 加载派出所列表
        if (this.form.areaCode) {
          this.handleAreaChange(this.form.areaCode);
        }
      });
    },

    /** 查看按钮操作 */
    handleView(row) {
      this.$router.push("/data-management/screen/site/detail/" + row.siteCode);
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.siteCode != null) {
            updateSite(this.form).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSite(this.form).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const siteCodes = row.siteCode || this.ids;
      this.$modal.confirm('是否确认删除归属场所编号为"' + siteCodes + '"的数据项？').then(function() {
        return delSite(siteCodes);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('screen/site/export', {
        ...this.queryParams
      }, `归属场所_${new Date().getTime()}.xlsx`)
    },

    // 获取用户区域列表
    getUserAreaList() {
      listUserArea().then(response => {
        this.userAreaList = response.data;
        this.initAdvancedFields();
      });
    },

    // 区域变化处理
    handleAreaChange(areaCode) {
      this.form.policeCode = null;
      this.policeList = [];

      if (areaCode) {
        let params = {
          parentCode: areaCode
        }
        listArea(params).then(response => {
          this.policeList = response.data;
        });
      }
    },

    // 设置提醒
    handleReminder() {
      this.reminderVisible = true;
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .screen-management-layout {
    display: flex;
    gap: 20px;
    height: 100%;

    .content-area {
      flex: 1;
      min-width: 0;
    }
  }

  .action-bar {
    background: white;
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
  }
  .toolbar-right {float:right;}
  .table-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
  }
}

// 表格样式
::v-deep .el-table {
  .text-nowrap {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// 名称角标样式
.site-name-container {
  position: relative;
  display: inline-block;
  padding: 12px 12px 0 0;
  .update-badge {
    position: absolute;
    top: -2px;
    right: -10px;
    font-size: 10px;
    padding: 1px 4px;
    border-radius: 2px;
    // color: white;
    white-space: nowrap;
    z-index: 1;

    &.pending {
      color: #FE9400;
    }

    &.overdue {
      color: #FF2A2A;
    }
  }
}
</style>
