<template>
  <div class="app-container">
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">归属场所详情</h2>
        <el-button icon="el-icon-arrow-left" @click="goBack">返回</el-button>
        
      </div>
    </div>

    <div class="detail-container" v-loading="loading">
      <!-- 基本信息模块 -->
      <div class="detail-section">
        <div class="section-header" @click="toggleSection('basic')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">基本信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.basic ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.basic ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        
        <div v-show="!sectionCollapsed.basic" class="section-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="场所名称">
              {{ form.siteName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="场所类型">
              <dict-tag :options="dict.type.site_type" :value="form.siteType"/>
            </el-descriptions-item>
            <el-descriptions-item label="场所地址" :span="2">
              {{ form.siteAddress || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="负责人">
              {{ form.principal || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="负责人电话">
              {{ form.principalPhone || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="负责人证件类型">
              <dict-tag :options="dict.type.certificate_type" :value="form.principalIdType"/>
            </el-descriptions-item>
            <el-descriptions-item label="负责人证件号码">
              {{ form.principalIdNumber || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="所属区域">
              {{ form.areaName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="所属派出所">
              {{ form.policeName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ parseTime(form.createTime) || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="更新时间">
              {{ parseTime(form.updateTime) || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 电子屏列表模块 -->
      <div class="detail-section">
        <div class="section-header" @click="toggleSection('screens')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">电子屏列表</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.screens ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.screens ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        
        <div v-show="!sectionCollapsed.screens" class="section-content">
          <el-table :data="screenList" :header-cell-style="{ backgroundColor: '#EBF2FF' }" border>
            <el-table-column label="电子屏名称" align="center" prop="screenName" />
            <el-table-column label="照片" align="center" prop="images" width="80">
              <template slot-scope="scope">
                <image-viewer :src="scope.row.images" :width="50" :height="50" title="电子屏照片"/>
              </template>
            </el-table-column>
            <el-table-column label="所属派出所" align="center" prop="policeName"/>
            <el-table-column label="责任单位" align="center" prop="enterprise.enterpriseName"/>
            <el-table-column label="责任单位联系人" align="center" prop="enterprise.principal"/>
            <el-table-column label="责任单位联系电话" align="center" prop="enterprise.principalPhone"/>
            <el-table-column label="操作" align="center" width="100">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="viewScreen(scope.row)"
                >查看</el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <pagination
            v-show="screenTotal>0"
            :total="screenTotal"
            :page.sync="screenParams.pageNum"
            :limit.sync="screenParams.pageSize"
            @pagination="queryListScreen"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getSite } from "@/api/screen/site";
import { listScreen } from "@/api/screen/screen";
import ImageViewer from '@/components/ImageViewer'

export default {
  name: "SiteDetail",
  dicts: ['site_type', 'certificate_type'],
  components: { ImageViewer },
  data() {
    return {
      loading: false,
      // 表单参数
      form: {},
      
      // 电子屏列表参数
      screenParams: {
        pageNum: 1,
        pageSize: 10,
        siteCode: null,
      },
      screenTotal: 0,
      screenList: [],
      
      // 模块折叠状态
      sectionCollapsed: {
        basic: false,
        screens: false
      }
    };
  },
  created() {
    const siteCode = this.$route.params && this.$route.params.id;
    this.getSite(siteCode);
    this.screenParams.siteCode = siteCode;
    this.queryListScreen();
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    
    /** 查询归属场所详细 */
    getSite(siteCode) {
      this.loading = true;
      getSite(siteCode).then(response => {
        this.form = response.data;
        this.loading = false;
      });
    },
    
    // 查询电子屏列表
    queryListScreen(){
      listScreen(this.screenParams).then(response => {
        this.screenList = response.rows;
        this.screenTotal = response.total;
      })
    },
    
    // 查看电子屏详情
    viewScreen(row) {
      this.$router.push("/data-management/screen/detail/" + row.screenId);
    },
    
    // 切换模块折叠状态
    toggleSection(section) {
      this.sectionCollapsed[section] = !this.sectionCollapsed[section];
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .page-header {
     width: 94%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      justify-content: space-between;
      .page-title {
        margin: 0;
        font-size: 18px;
        color: #303133;
      }
    }
  }

  .detail-container {
    width: 94%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
    
    .detail-section {
      margin-bottom: 24px;

      .section-header {
        background-color: #f6f7fb;
        padding: 12px 16px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 4px;
        margin-bottom: 16px;

        .section-title {
          display: flex;
          align-items: center;
          gap: 8px;

          .title-line {
            width: 3px;
            height: 16px;
            background-color: #4584FF;
          }

          .title-text {
            font-size: 16px;
            font-weight: 600;
            color: #4584FF;
          }
        }

        .section-toggle {
          display: flex;
          align-items: center;
          gap: 4px;
          color: #4584FF;
          font-size: 14px;

          .toggle-text {
            font-size: 14px;
          }

          i {
            font-size: 12px;
          }
        }

        &:hover {
          background-color: #f0f2f5;
        }
      }

      .section-content {
        padding: 0 16px;
      }
    }
  }
}
</style>
