<template>
  <div class="app-container">
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">{{ title }}</h2>
        <el-button icon="el-icon-arrow-left" size="small" @click="handleClose">返回</el-button>
      </div>
    </div>

    <div class="form-container">
      <el-form ref="form" :model="form" :rules="rules" label-width="150px" v-loading="loading">
        <!-- 基本信息模块 -->
        <div class="form-section">
          <div class="section-header" @click="toggleSection('basic')">
            <div class="section-title">
              <div class="title-line"></div>
              <span class="title-text">基本信息</span>
            </div>
            <div class="section-toggle">
              <span class="toggle-text">{{ sectionCollapsed.basic ? '展开' : '收起' }}</span>
              <i :class="sectionCollapsed.basic ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            </div>
          </div>

          <div v-show="!sectionCollapsed.basic" class="section-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="电子屏名称" prop="screenName">
                  <el-input v-model="form.screenName" placeholder="请输入电子屏名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="电子屏性质" prop="screenNature">
                  <el-select v-model="form.screenNature" placeholder="请选择电子屏性质" style="width: 100%">
                    <el-option
                      v-for="dict in dict.type.screen_nature"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="屏幕类型" prop="screenType">
                  <el-select v-model="form.screenType" placeholder="请选择屏幕类型" style="width: 100%">
                    <el-option
                      v-for="dict in dict.type.screen_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="位置类型" prop="addressType">
                  <el-select v-model="form.addressType" placeholder="请选择位置类型" style="width: 100%">
                    <el-option
                      v-for="dict in dict.type.screen_location_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="显示类型" prop="showType">
                  <el-select v-model="form.showType" placeholder="请选择显示类型" style="width: 100%">
                    <el-option
                      v-for="dict in dict.type.screen_display_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <!-- <el-form-item label="所属区域" prop="areaCode">
                  <el-select v-model="form.areaCode" placeholder="请选择区域" style="width: 100%" @change="handleAreaChange">
                    <el-option
                      v-for="area in areaList"
                      :key="area.areaCode"
                      :label="area.areaName"
                      :value="area.areaCode"
                    ></el-option>
                  </el-select>
                </el-form-item> -->
                <el-form-item label="所属区域" prop="areaCode">
                  <el-select v-model="form.areaCode" style="width: 100%;" placeholder="请选择所属区域" @change="getPoliceList" clearable>
                    <el-option
                      v-for="item in areaList"
                      :key="item.areaCode"
                      :label="item.areaName"
                      :value="item.areaCode"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="所属派出所" prop="policeCode">
                  <el-select v-model="form.policeCode" style="width: 100%;" filterable placeholder="请选择所属派出所" clearable>
                    <el-option
                      v-for="item in policeList"
                      :key="item.areaCode"
                      :label="item.areaName"
                      :value="item.areaCode"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="详细地址" prop="address">
                  <el-input v-model="form.address" placeholder="请输入详细地址" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="经度" prop="longitude">
                  <el-input v-model="form.longitude" placeholder="请输入经度" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="纬度" prop="latitude">
                  <el-input v-model="form.latitude" placeholder="请输入纬度" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-button type="text" @click="btnMapLocation" icon="el-icon-location">位置</el-button>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="屏幕尺寸(m²)" prop="size">
                  <el-input v-model="form.size" placeholder="请输入屏幕尺寸" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="楼层" prop="floorLevel">
                  <el-select v-model="form.floorLevel" placeholder="请选择楼层" style="width: 100%">
                    <el-option
                      v-for="dict in dict.type.location_floor_level"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="是否下发风险提示单" prop="riskTipFlag">
                  <el-select v-model="form.riskTipFlag" style="width: 100%;" placeholder="请选择状态">
                    <el-option
                      v-for="dict in dict.type.sys_yes_no"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="责任民警" prop="policeMan">
                  <el-input v-model="form.policeMan" placeholder="请输入责任民警" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="责任民警联系电话" prop="policePhone">
                  <el-input v-model="form.policePhone" placeholder="请输入责任民警联系电话" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="电子屏照片" prop="images">
              <image-upload v-model="form.images"/>
            </el-form-item>
          </div>
        </div>

        <!-- 地图选择组件 -->
        <!-- 地图经纬度选择窗口 yxh -->
    <MapPoint ref="PointMap1"  :visible.sync="openMap" :modalObj='modalObj' @value-changed="getMapLocation" ></MapPoint>

         <!-- 责任单位信息模块 -->
        <div class="form-section">
          <div class="section-header" @click="toggleSection('enterprise')">
            <div class="section-title">
              <div class="title-line"></div>
              <span class="title-text">责任单位信息</span>
            </div>
            <div class="section-toggle">
              <span class="toggle-text">{{ sectionCollapsed.enterprise ? '展开' : '收起' }}</span>
              <i :class="sectionCollapsed.enterprise ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            </div>
          </div>

          <div v-show="!sectionCollapsed.enterprise" class="section-content">
            <el-alert title="提示说明：请在单位名称搜索栏中查询已入库的单位信息。若未能找到您所需的单位，请在输入栏中详细填写该单位的信息，并提交。随后，系统将自动为您新增该单位至系统中。" type="info" show-icon style="margin-bottom: 16px;"></el-alert>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="运营使用单位名称" prop="enterprise.enterpriseName">
                  <el-autocomplete
                    style="width: 100%;"
                    v-model="form.enterprise.enterpriseName"
                    :fetch-suggestions="querySearchEnterprise"
                    placeholder="请输入运营使用单位名称"
                    @select="handleSelectEnterprise"
                  >
                    <template slot-scope="{ item }">
                      <span>{{ item.enterpriseName }}</span>
                    </template>
                  </el-autocomplete>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="单位性质" prop="enterprise.unitPropertieList">
                  <el-select v-model="form.enterprise.unitPropertieList" multiple style="width: 90%;" placeholder="请选择单位性质">
                    <el-option
                      v-for="dict in dict.type.unit_properties"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="单位统一社会信用代码" prop="enterprise.creditCode">
                  <el-input v-model="form.enterprise.creditCode" placeholder="请输入单位统一社会信用代码" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="16">
                <el-form-item label="单位办公地址" prop="enterprise.address">
                  <el-input v-model="form.enterprise.address" placeholder="请输入单位办公地址" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="联系人姓名" prop="enterprise.principal">
                  <el-input v-model="form.enterprise.principal" placeholder="请输入联系人姓名" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="联系人电话" prop="enterprise.principalPhone">
                  <el-input v-model="form.enterprise.principalPhone" placeholder="请输入联系人电话" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="联系人所属部门" prop="enterprise.principalDept">
                  <el-input v-model="form.enterprise.principalDept" placeholder="请输入联系人所属部门" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="联系人职务" prop="enterprise.principalDuties">
                  <el-input v-model="form.enterprise.principalDuties" placeholder="请输入联系人职务" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="承诺书" prop="enterprise.fileUrl">
                  <image-upload v-model="form.enterprise.fileUrl"/>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 重点区域信息模块 -->
        <div class="form-section">
          <div class="section-header" @click="toggleSection('keyarea')">
            <div class="section-title">
              <div class="title-line"></div>
              <span class="title-text">重点区域信息</span>
            </div>
            <div class="section-toggle">
              <span class="toggle-text">{{ sectionCollapsed.keyarea ? '展开' : '收起' }}</span>
              <i :class="sectionCollapsed.keyarea ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            </div>
          </div>

          <div v-show="!sectionCollapsed.keyarea" class="section-content">
            <el-form-item label="启用重点区域">
              <el-switch v-model="loadKeyarea"></el-switch>
            </el-form-item>
            <div v-if="loadKeyarea">
              <el-form-item label="重点区域选择" prop="keyareaIds">
                <el-select v-model="form.keyareaIds" style="width: 100%;" multiple filterable placeholder="请选择重点区域">
                  <el-option
                    v-for="dict in keyareaList"
                    :key="dict.keyareaId"
                    :label="dict.keyareaName"
                    :value="dict.keyareaId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
        </div>

        <!-- 专项信息模块 -->
        <div class="form-section">
          <div class="section-header" @click="toggleSection('special')">
            <div class="section-title">
              <div class="title-line"></div>
              <span class="title-text">专项信息</span>
            </div>
            <div class="section-toggle">
              <span class="toggle-text">{{ sectionCollapsed.special ? '展开' : '收起' }}</span>
              <i :class="sectionCollapsed.special ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            </div>
          </div>

          <div v-show="!sectionCollapsed.special" class="section-content">
            <el-form-item label="启用专项信息">
              <el-switch v-model="loadSpecial"></el-switch>
            </el-form-item>
            <div v-if="loadSpecial">
              <el-form-item label="专项信息选择" prop="specialIds">
                <el-select v-model="form.specialIds" style="width: 100%;" multiple filterable placeholder="请选择专项">
                  <el-option
                    v-for="dict in specialList"
                    :key="dict.specialId"
                    :label="dict.specialName"
                    :value="dict.specialId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
        </div>

        <!-- 归属场所信息模块 -->
        <div class="form-section">
          <div class="section-header" @click="toggleSection('site')">
            <div class="section-title">
              <div class="title-line"></div>
              <span class="title-text">归属场所信息</span>
            </div>
            <div class="section-toggle">
              <span class="toggle-text">{{ sectionCollapsed.site ? '展开' : '收起' }}</span>
              <i :class="sectionCollapsed.site ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            </div>
          </div>

          <div v-show="!sectionCollapsed.site" class="section-content">
            <el-form-item label="启用归属场所">
              <el-switch v-model="loadSite"></el-switch>
            </el-form-item>
            <div v-if="loadSite">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="场所名称" prop="site.siteName">
                    <el-autocomplete
                      style="width: 100%;"
                      v-model="form.site.siteName"
                      :fetch-suggestions="querySearchSite"
                      placeholder="请输入场所名称"
                      @select="handleSelectSite"
                    >
                      <template slot-scope="{ item }">
                        <span>{{ item.siteName }}</span>
                      </template>
                    </el-autocomplete>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="场所状态" prop="site.siteStatus">
                    <el-select v-model="form.site.siteStatus" style="width: 100%;" placeholder="请选择场所状态">
                      <el-option
                        v-for="dict in dict.type.site_business_status"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="场所类型" prop="site.siteType">
                    <el-select v-model="form.site.siteType" style="width: 100%;" placeholder="请选择场所类型">
                      <el-option
                        v-for="dict in dict.type.site_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="负责人姓名" prop="site.principal">
                    <el-input v-model="form.site.principal" placeholder="请输入负责人姓名" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="负责人电话" prop="site.principalPhone">
                    <el-input v-model="form.site.principalPhone" placeholder="请输入负责人电话" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="证件类型" prop="site.principalCredentialType">
                    <el-select v-model="form.site.principalCredentialType" style="width: 100%;" placeholder="请选择证件类型">
                      <el-option
                        v-for="dict in dict.type.certificate_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="证件号码" prop="site.principalCertificateCode">
                    <el-input v-model="form.site.principalCertificateCode" placeholder="请输入证件号码" />
                  </el-form-item>
                </el-col>
                <el-col :span="16">
                  <el-form-item label="场所地址" prop="site.siteAddress">
                    <el-input v-model="form.site.siteAddress" placeholder="请输入场所地址" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>

        <!-- 屏幕联网信息模块 -->
        <div class="form-section">
          <div class="section-header" @click="toggleSection('netsystem')">
            <div class="section-title">
              <div class="title-line"></div>
              <span class="title-text">屏幕联网信息</span>
            </div>
            <div class="section-toggle">
              <span class="toggle-text">{{ sectionCollapsed.netsystem ? '展开' : '收起' }}</span>
              <i :class="sectionCollapsed.netsystem ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            </div>
          </div>

          <div v-show="!sectionCollapsed.netsystem" class="section-content">
            <el-form-item label="启用屏幕联网" >
              <el-switch v-model="loadNetsystem"></el-switch>
            </el-form-item>
            <div v-if="loadNetsystem">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="联网系统名称" prop="netsystem.netsystemName">
                    <el-autocomplete
                      style="width: 100%;"
                      v-model="form.netsystem.netsystemName"
                      :fetch-suggestions="querySearchNetsystem"
                      placeholder="请输入联网系统名称"
                      @select="handleSelectNetsystem"
                    >
                      <template slot-scope="{ item }">
                        <span>{{ item.netsystemName }}</span>
                      </template>
                    </el-autocomplete>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="等级保护级别" prop="netsystem.protectionLevel">
                    <el-select v-model="form.netsystem.protectionLevel" style="width: 100%;" placeholder="请选择等级保护级别">
                      <el-option
                        v-for="dict in dict.type.grade_protection_level"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="等级保护备案机关" prop="netsystem.protectionLevelOrgan">
                    <el-input v-model="form.netsystem.protectionLevelOrgan" placeholder="等级保护备案机关" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="域名/地址" prop="netsystem.accessUrl">
                    <el-input v-model="form.netsystem.accessUrl" placeholder="请输入联网系统域名/地址" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="连接控制系统方式" prop="netsystem.connectType">
                    <el-select v-model="form.netsystem.connectType" style="width: 100%;" placeholder="请选择连接控制系统方式">
                      <el-option
                        v-for="dict in dict.type.screen_control_sys_way"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="控制系统运行状态" prop="netsystem.runStatus">
                    <el-select v-model="form.netsystem.runStatus" style="width: 100%;" placeholder="请选择控制系统运行状态">
                      <el-option
                        v-for="dict in dict.type.screen_control_sys_status"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="物理位置" prop="netsystem.address">
                    <el-input v-model="form.netsystem.address" placeholder="请输入后台控制系统安装部位所在物理位置（云上的填具体云平台名称）" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="系统开发/制造商" prop="netsystem.manufacturer" label-width="180px">
                    <el-input v-model="form.netsystem.manufacturer" placeholder="请输入后台控制系统研发厂商" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="开发厂商统一社会信用代码" prop="netsystem.manufacturerCreditCode" label-width="180px">
                    <el-input v-model="form.netsystem.manufacturerCreditCode" placeholder="请输入开发厂商单位统一社会信用代码" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="开发厂商负责人姓名" prop="netsystem.principal" label-width="180px">
                    <el-input v-model="form.netsystem.principal" placeholder="请输入开发厂商负责人姓名" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="开发厂商负责人电话" prop="netsystem.principalPhone" label-width="180px">
                    <el-input v-model="form.netsystem.principalPhone" placeholder="请输入开发厂商负责人电话" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="运维厂商名称" prop="netsystem.operation" label-width="180px">
                    <el-input v-model="form.netsystem.operation" placeholder="请输入运维厂商名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="运维厂商统一社会信用代码" prop="netsystem.operationCreditCode" label-width="180px">
                    <el-input v-model="form.netsystem.operationCreditCode" placeholder="请输入运维厂商单位统一社会信用代码" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="运维厂商负责人" prop="netsystem.operationPrincipal" label-width="180px">
                    <el-input v-model="form.netsystem.operationPrincipal" placeholder="请输入运维厂商负责人" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="运维厂商负责人电话" prop="netsystem.operationPrincipalPhone" label-width="180px">
                    <el-input v-model="form.netsystem.operationPrincipalPhone" placeholder="请输入运维厂商负责人电话" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="是否建立内容审核机制" prop="netsystem.examineFlag">
                    <el-radio-group v-model="form.netsystem.examineFlag">
                      <el-radio
                        v-for="dict in dict.type.sys_yes_no"
                        :key="dict.value"
                        :label="dict.value"
                      >{{dict.label}}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="是否建立一键关停" prop="netsystem.shutdownFlag">
                    <el-radio-group v-model="form.netsystem.shutdownFlag">
                      <el-radio
                        v-for="dict in dict.type.sys_yes_no"
                        :key="dict.value"
                        :label="dict.value"
                      >{{dict.label}}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="是否完成专项整治" prop="netsystem.rectificationFlag">
                    <el-radio-group v-model="form.netsystem.rectificationFlag">
                      <el-radio
                        v-for="dict in dict.type.sys_yes_no"
                        :key="dict.value"
                        :label="dict.value"
                      >{{dict.label}}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>

       



        <div class="form-actions">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" :loading="submitting" @click="submitForm">保存</el-button>
        </div>
      </el-form>
    </div>


  </div>
</template>

<script>
import { addScreen, updateScreen, getScreen } from "@/api/screen/screen";
import { listEnterprise } from "@/api/screen/enterprise";
import { listNetsystem } from "@/api/screen/netsystem";
import { listKeyarea } from "@/api/screen/keyarea";
import { listSpecial } from "@/api/screen/special";
import { listSite } from "@/api/screen/site";
import { listArea, listUserArea } from '@/api/system/area';
// import MapPoint from '@/components/MapPoint';

export default {
  name: "ScreenAdd",
  dicts: ['screen_display_type', 'location_floor_level', 'sys_common_status', 'screen_type', 'screen_nature', 'screen_location_type', 'site_type', 'site_business_status', 'certificate_type', 'screen_control_sys_way', 'grade_protection_level', 'screen_control_sys_status', 'sys_yes_no', 'unit_properties'],
  components: { MapPoint:(d)=>import('@/components/MapPoint/index.vue') },
  data() {
    return {
      // 页面状态
      title: "新增电子屏",
      screenId: null,
      loading: false,
      submitting: false,

      // 模块开关
      loadKeyarea: false,
      loadSpecial: false,
      loadSite: false,
      loadNetsystem: false,

      // 表单参数
      form: {
        screenId: null,
        screenName: null,
        screenNature: null,
        screenType: null,
        addressType: null,
        showType: null,
        areaCode: null,
        areaName: null,
        policeCode: null,
        policeName: null,
        longitude: 116.4177920000,
        latitude: 39.9960840000,
        address: null,
        size: null,
        floorLevel: null,
        images: null,
        enterpriseId: null,
        siteCode: null,
        keyareaId: null,
        netsystemId: null,
        policeMan: null,
        policePhone: null,
        riskTipFlag: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        enterprise: {},
        site: {},
        netsystem: {},
        keyareaIds: [],
        specialIds: []
      },

      // 表单校验
      rules: {
        areaCode: [
          { required: true, message: "区域编码不能为空", trigger: "blur" }
        ],
        policeCode: [
          { required: true, message: "派出所编码不能为空", trigger: "blur" }
        ],
        screenName: [
          { required: true, message: "电子屏名称不能为空", trigger: "blur" }
        ],
        address: [
          { required: true, message: "地址不能为空", trigger: "blur" }
        ],
        longitude: [
          { required: true, message: "经度不能为空", trigger: "blur" }
        ],
        latitude: [
          { required: true, message: "纬度不能为空", trigger: "blur" }
        ],
        floorLevel: [
          { required: true, message: "所在楼层不能为空", trigger: "change" }
        ],
        size: [
          { required: true, message: "屏幕尺寸不能为空", trigger: "blur" }
        ],
        screenNature: [
          { required: true, message: "电子屏性质不能为空", trigger: "change" }
        ],
        screenType: [
          { required: true, message: "前端电子屏材质类型不能为空", trigger: "change" }
        ],
        showType: [
          { required: true, message: "前端电子屏显示类型不能为空", trigger: "change" }
        ],
        addressType: [
          { required: true, message: "前端电子屏位置类型不能为空", trigger: "change" }
        ],
        riskTipFlag: [
          { required: true, message: "请选择下发风险提示", trigger: "change" }
        ],
        policeMan: [
          { required: true, message: "责任民警不能为空", trigger: "blur" }
        ],
        policePhone: [
          { required: true, message: "责任民警电话不能为空", trigger: "blur" }
        ],
        // 责任单位
        'enterprise.enterpriseName': [
          { required: true, message: "运营使用单位名称不能为空", trigger: "blur" }
        ],
        'enterprise.unitPropertieList': [
          { required: true, message: "单位性质不能为空", trigger: "change" }
        ],
        'enterprise.creditCode': [
          { required: true, message: "统一社会信用代码不能为空", trigger: "blur" }
        ],
        'enterprise.address': [
          { required: true, message: "地址不能为空", trigger: "blur" }
        ],
        'enterprise.principal': [
          { required: true, message: "联系人姓名不能为空", trigger: "blur" }
        ],
        'enterprise.principalPhone': [
          { required: true, message: "联系人电话不能为空", trigger: "blur" }
        ],
        'enterprise.fileUrl': [
          { required: true, message: "承诺书不能为空", trigger: "blur" }
        ]
      },

      // 下拉选项数据
      areaList: [],
      policeList: [],
      keyareaList: [],
      specialList: [],

      // 模块折叠状态
      sectionCollapsed: {
        basic: false,
        keyarea: false,
        special: false,
        site: false,
        netsystem: false,
        enterprise: false
      },

      // 地图相关
      openMap: false,
      modalObj: {
        title: '电子屏经纬度获取',
        modal: true,
        width: '1050px',
        address: '',
        longitude: 116.4177920000,
        latitude: 39.9960840000,
        optType: '0'
      }
    };
  },
  created() {
    // 获取路由参数
    const screenId = this.$route.params.screenId;

    // 初始化数据
    this.getAreaList();
    this.getKeyareaList();
    this.getSpecialList();

    // 设置地图默认参数
    var sTitle = '电子屏经纬度获取';
    this.modalObj = {
      title: sTitle,
      modal: true,
      width: '1050px',
      address: this.form.address,
      longitude: this.form.longitude,
      latitude: this.form.latitude,
      optType: '0'
    };

    // 判断是新增还是编辑
    if (screenId) {
      this.screenId = screenId;
      this.getDetail();
    }
  },
  methods: {
     /**经纬度 位置选择 */
    btnMapLocation() {

      // 检测是否已经输入了地址 没有输入地址 提示先输入地址
      if(this.form.address==undefined || this.form.address==null) {

      }
      else {
        // console.log('-----btnMapLocation--this.form.address=',this.form.address);
        this.openMap =true; // 显示弹框
        var sTitle ='电子屏经纬度获取'

        this.modalObj = { title: sTitle, modal: true, width: '1050px',
          address: this.form.address,
          longitude: this.form.longitude,
          latitude: this.form.latitude,
          optType: '0'
        };  // optType(0=新增、1=编辑、2=未分配编辑)。
        // console.log('btnMapLocation---this.modalObj=',this.modalObj);

        this.$nextTick(() => {
          console.log('this.$refs.PointMap1= ',this.$refs.PointMap1)
          this.$refs.PointMap1.open();
        })

      }
    },
    /**选择点位 后更新经纬度信息  */
    getMapLocation(longitude, latitude, address) {
      console.log('--longitude=', longitude,' latitude=',latitude,' address=',address)
      // 更新地址和 经纬度信息字段
      this.form.longitude = longitude;
      this.form.latitude = latitude;
      this.form.address= address;
      //
    },

    // 获取区域列表
    getAreaList() {
      let params = {
        parentId: '110000'
      }
      listArea(params).then(response => {
        this.areaList = response.data;
      });
    },

    // 获取派出所列表
    getPoliceList() {
      if (!this.form.areaCode) {
        this.policeList = [];
        return;
      }
      let params = {
        parentId: this.form.areaCode
      }
      listArea(params).then(response => {
        this.policeList = response.data;
      });
    },

    // 获取重点区域列表
    getKeyareaList() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      }
      listKeyarea(params).then(response => {
        this.keyareaList = response.rows;
      });
    },

    // 获取专项列表
    getSpecialList() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      }
      listSpecial(params).then(response => {
        this.specialList = response.rows;
      });
    },

    // 重置表单
    reset() {
      this.form = {
        screenId: null,
        screenName: null,
        screenNature: null,
        screenType: null,
        addressType: null,
        showType: null,
        areaCode: null,
        areaName: null,
        policeCode: null,
        policeName: null,
        longitude: 116.4177920000,
        latitude: 39.9960840000,
        address: null,
        size: null,
        floorLevel: null,
        images: null,
        enterpriseId: null,
        siteCode: null,
        keyareaId: null,
        netsystemId: null,
        policeMan: null,
        policePhone: null,
        riskTipFlag: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        enterprise: {},
        site: {},
        netsystem: {},
        keyareaIds: [],
        specialIds: []
      };
      this.resetForm("form");
    },

    // 获取详情数据
    getDetail() {
      this.reset();
      if (this.screenId === 0) {
        this.title = "新增电子屏";
        return;
      }
      getScreen(this.screenId).then(response => {
        this.form = response.data;
        if (this.form.keyareaIds && this.form.keyareaIds != '0') {
          this.form.keyareaIds = this.form.keyareaIds.split(",");
        }
        if (this.form.specialIds && this.form.specialIds != '0') {
          this.form.specialIds = this.form.specialIds.split(",");
        }
        this.title = "修改电子屏";

        // 如果有区域代码，加载对应的派出所列表
        if (this.form.areaCode) {
          this.getPoliceList();
        }

        this.loadKeyarea = this.form.keyareaIds != '0';
        this.loadSpecial = this.form.specialIds != '0';
        this.loadSite = this.form.siteCode != '0';
        this.loadNetsystem = this.form.netsystemId != '0';
      });
    },

    // 区域变化处理
    handleAreaChange(areaCode) {
      this.form.policeCode = null;
      this.policeList = [];

      if (areaCode) {
        let params = {
          parentId: areaCode
        }
        listArea(params).then(response => {
          this.policeList = response.data;
        });
      }
    },

    // 责任单位远程搜索
    remoteMethod(query) {
      if (query !== '') {
        this.loading = true;
        let params = {
          pageNum: 1,
          pageSize: 20,
          enterpriseName: query
        }
        listEnterprise(params).then(response => {
          this.loading = false;
          this.enterpriseOptions = response.rows;
        });
      } else {
        this.enterpriseOptions = [];
      }
    },

    // 责任单位变化处理
    handleEnterpriseChange(enterpriseId) {
      const enterprise = this.enterpriseOptions.find(item => item.enterpriseId === enterpriseId);
      if (enterprise) {
        this.form.enterpriseName = enterprise.enterpriseName;
      }
    },

    // 重点区域变化处理
    handleKeyareaChange(keyareaId) {
      if (keyareaId === "0") {
        this.form.keyareaName = null;
      } else {
        const keyarea = this.keyareaList.find(item => item.keyareaId === keyareaId);
        if (keyarea) {
          this.form.keyareaName = keyarea.keyareaName;
        }
      }
    },

    // 专项变化处理
    handleSpecialChange(specialId) {
      if (specialId === "0") {
        this.form.specialName = null;
      } else {
        const special = this.specialList.find(item => item.specialId === specialId);
        if (special) {
          this.form.specialName = special.specialName;
        }
      }
    },

    // 归属场所远程搜索
    remoteSiteMethod(query) {
      if (query !== '') {
        this.siteLoading = true;
        let params = {
          pageNum: 1,
          pageSize: 20,
          siteName: query
        }
        listSite(params).then(response => {
          this.siteLoading = false;
          this.siteOptions = response.rows;
        });
      } else {
        this.siteOptions = [];
      }
    },

    // 归属场所变化处理
    handleSiteChange(siteCode) {
      if (siteCode === "0") {
        this.form.siteName = null;
      } else {
        const site = this.siteOptions.find(item => item.siteCode === siteCode);
        if (site) {
          this.form.siteName = site.siteName;
        }
      }
    },

    // 联网系统远程搜索
    remoteNetsystemMethod(query) {
      if (query !== '') {
        this.netsystemLoading = true;
        let params = {
          pageNum: 1,
          pageSize: 20,
          netsystemName: query
        }
        listNetsystem(params).then(response => {
          this.netsystemLoading = false;
          this.netsystemOptions = response.rows;
        });
      } else {
        this.netsystemOptions = [];
      }
    },

    // 联网系统变化处理
    handleNetsystemChange(netsystemId) {
      if (netsystemId === "0") {
        this.form.netsystemName = null;
      } else {
        const netsystem = this.netsystemOptions.find(item => item.netsystemId === netsystemId);
        if (netsystem) {
          this.form.netsystemName = netsystem.netsystemName;
        }
      }
    },

    // 场所搜索
    querySearchSite(queryString, cb) {
      if (!queryString) {
        cb([]);
        return;
      }
      let params = {
        pageNum: 1,
        pageSize: 20,
        siteName: queryString
      }
      listSite(params).then(response => {
        cb(response.rows || []);
      });
    },

    // 选择场所
    handleSelectSite(item) {
      this.form.site = { ...item };
    },

    // 联网系统搜索
    querySearchNetsystem(queryString, cb) {
      if (!queryString) {
        cb([]);
        return;
      }
      let params = {
        pageNum: 1,
        pageSize: 20,
        netsystemName: queryString
      }
      listNetsystem(params).then(response => {
        cb(response.rows || []);
      });
    },

    // 选择联网系统
    handleSelectNetsystem(item) {
      this.form.netsystem = { ...item };
    },

    // 企业搜索
    querySearchEnterprise(queryString, cb) {
      if (!queryString) {
        cb([]);
        return;
      }
      let params = {
        pageNum: 1,
        pageSize: 20,
        enterpriseName: queryString
      }
      listEnterprise(params).then(response => {
        cb(response.rows || []);
      });
    },

    // 选择企业
    handleSelectEnterprise(item) {
      this.form.enterprise = { ...item };
    },



    // 获取区域列表
    getAreaList(){
      let params = {
        parentId: '110000'
      }
      listArea(params).then(response => {
        this.areaList = response.data;
      });
    },

    // 获取派出所列表
    getPoliceList() {
      let params = {
        parentId: this.form.areaCode
      }
      listArea(params).then(response => {
        this.policeList = response.data;
      });
    },
    

    // 切换模块折叠状态
    toggleSection(section) {
      this.sectionCollapsed[section] = !this.sectionCollapsed[section];
    },

    // 提交表单
    submitForm() {
      let that = this;
      if (!that.loadNetsystem) {
        that.form.netsystem = {};
      }
      if (!that.loadSite) {
        that.form.site = {};
      }
      console.log('submitForm---this.form1=', that.form.keyareaIds);
      if (that.form.keyareaIds) {
        that.form.keyareaIds = Array.prototype.join.call(that.form.keyareaIds, ',');
        console.log('submitForm---this.form2=', that.form.keyareaIds);
      }
      if (that.form.specialIds) {
        that.form.specialIds = Array.prototype.join.call(that.form.specialIds, ',');
      }
      that.$refs["form"].validate(valid => {
        if (valid) {
          if (that.form.screenId != null) {
            updateScreen(that.form).then(() => {
              that.$modal.msgSuccess("修改成功");
              that.handleClose();
            });
          } else {
            addScreen(that.form).then(() => {
              that.$modal.msgSuccess("新增成功");
              that.handleClose();
            });
          }
        }
      });
    },

    // 企业搜索
    querySearchEnterprise(queryString, cb) {
      if (!queryString) {
        cb([]);
      }
      this.form.enterprise.enterpriseId = null;
      let results;
      if (queryString) {
        listEnterprise({enterpriseName: queryString}).then(response => {
          results = response.rows;
          cb(results);
        });
      }
    },

    // 选择企业
    handleSelectEnterprise(item) {
      this.form.enterprise = item;
    },

    // 场所搜索
    querySearchSite(queryString, cb) {
      if (!queryString) {
        cb([]);
      }
      this.form.site.siteCode = null;
      let results;
      if (queryString) {
        listSite({siteName: queryString}).then(response => {
          results = response.rows;
          cb(results);
        });
      }
    },

    // 选择场所
    handleSelectSite(item) {
      this.form.site = item;
    },

    // 联网系统搜索
    querySearchNetsystem(queryString, cb) {
      if (!queryString) {
        cb([]);
      }
      this.form.netsystem.netsystemId = null;
      let results;
      if (queryString) {
        listNetsystem({netsystemName: queryString}).then(response => {
          results = response.rows;
          cb(results);
        });
      }
    },

    // 选择联网系统
    handleSelectNetsystem(item) {
      this.form.netsystem = item;
    },

    /** 关闭弹出窗口 */
    // 返回按钮
    handleClose() {
      window.history.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .page-header {
     width: 94%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      justify-content: space-between;
      .page-title {
        margin: 0;
        font-size: 18px;
        color: #303133;
      }
    }
  }

  .form-container {
    width: 94%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
  }

  .form-section {
    margin-bottom: 24px;

    .section-header {
      background-color: #f6f7fb;
      padding: 12px 16px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 4px;
      margin-bottom: 16px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-line {
          width: 3px;
          height: 16px;
          background-color: #4584FF;
        }

        .title-text {
          font-size: 16px;
          font-weight: 600;
          color: #4584FF;
        }
      }

      .section-toggle {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #4584FF;
        font-size: 14px;

        .toggle-text {
          font-size: 14px;
        }

        i {
          font-size: 12px;
        }
      }

      &:hover {
        background-color: #f0f2f5;
      }
    }

    .section-content {
      padding: 0 16px;
    }
  }

  .form-actions {
    background: white;
    padding: 20px;
    border-radius: 8px;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    border-top: 1px solid #f0f0f0;
  }
}

// 全局样式：确保表单标签不换行
::v-deep .el-form-item__label {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}
</style>
