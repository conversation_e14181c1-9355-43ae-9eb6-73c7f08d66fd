<template>
  <div class="app-container">
    <div class="screen-management-layout">
      <!-- 左侧导航菜单 -->
      <basic-data-sidebar />

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <!-- 搜索组件 -->
        <common-search
          :search-config="searchConfig"
          :advanced-fields="advancedFields"
          :initial-form="searchForm"
          @search="handleSearch"
          @reset="handleReset"
        />

        <!-- 操作按钮区域 -->
        <div class="action-bar">
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['screen:keyarea:add']"
              >新增</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="warning"
                plain
                icon="el-icon-download"
                size="mini"
                @click="handleExport"
                v-hasPermi="['screen:keyarea:export']"
              >导出</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
          </el-row>
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
          <el-table 
            v-loading="loading" 
            :data="keyareaList" 
            :header-cell-style="{ backgroundColor: '#EBF2FF' }"
            border
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" align="center" fixed="left" />
            <el-table-column label="重点区域名称" align="center" prop="keyareaName" min-width="150">
              <template slot-scope="scope">
                <div class="text-nowrap" :title="scope.row.keyareaName">{{ scope.row.keyareaName }}</div>
              </template>
            </el-table-column>
            <el-table-column label="类型" align="center" prop="keyareaType" width="120">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.key_area_type" :value="scope.row.keyareaType"/>
              </template>
            </el-table-column>
            <el-table-column label="电子屏数" align="center" prop="screenNum" width="90">
              <template slot-scope="scope">
                <a href="javascript:void(0);" style="color: #409EFF;" @click="$router.push('/data-management/screen?keyareaId='+scope.row.keyareaId+'&keyareaName='+ scope.row.keyareaName)">
                  {{ scope.row.screenNum }}
                </a>
              </template>
            </el-table-column>
            <el-table-column label="检查次数" align="center" prop="inspectionNum" width="90"/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="380" fixed="right">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-view"
                  @click="handleView(scope.row)"
                  v-hasPermi="['screen:keyarea:query']"
                >详情</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['screen:keyarea:edit']"
                >修改</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-monitor"
                  @click="allotScreen(scope.row)"
                  v-hasPermi="['screen:keyarea:edit']"
                >分配电子屏</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-upload2"
                  @click="handleImport(scope.row)"
                  v-hasPermi="['screen:keyarea:edit']"
                >导入电子屏</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  v-if="scope.row.screenNum==0"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['screen:keyarea:remove']"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页组件 -->
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>

        <!-- 添加或修改重点区域对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
          <el-form ref="form" :model="form" :rules="rules" label-width="120px">
            <el-form-item label="重点区域名称" prop="keyareaName">
              <el-input v-model="form.keyareaName" placeholder="请输入重点区域名称" />
            </el-form-item>
            <el-form-item label="类型" prop="keyareaType">
              <el-select v-model="form.keyareaType" style="width: 100%;" placeholder="请选择类型">
                <el-option
                  v-for="dict in dict.type.key_area_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>

        <!-- 分配电子屏对话框 -->
        <el-dialog :title="screenTitle" :visible.sync="screenOpen" width="1100px" @close="cancelScreen" append-to-body>
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
            <el-form-item label="选择电子屏" prop="screenName">
              <el-autocomplete
                style="width: 600px;"
                v-model="screenName"
                :fetch-suggestions="querySearchScreen"
                placeholder="请输入电子屏名称进行选择"
                @select="handleSelectScreen"
              >
                <template slot-scope="{ item }">
                  <span>{{ item.screenName }}</span>
                </template>
              </el-autocomplete>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-plus" size="mini" @click="submitScreen">分配</el-button>
            </el-form-item>
          </el-form>
          <el-divider content-position="left">已添加电子屏列表</el-divider>
          <el-table :data="screenList" :header-cell-style="{ backgroundColor: '#EBF2FF' }" border>
            <el-table-column label="电子屏名称" align="center" prop="screenName" />
            <el-table-column label="所属派出所" align="center" prop="policeName" width="150"/>
            <el-table-column label="责任单位" align="center" prop="enterprise.enterpriseName"/>
            <el-table-column label="责任单位联系人" align="center" prop="enterprise.principal" width="150"/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="100">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="deleteScreen(scope.row)"
                >移除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="screenTotal>0"
            :total="screenTotal"
            :page.sync="screenParams.pageNum"
            :limit.sync="screenParams.pageSize"
            @pagination="queryListScreen"
          />
          <div slot="footer" class="dialog-footer">
            <el-button @click="cancelScreen">关 闭</el-button>
          </div>
        </el-dialog>

        <!-- 电子屏导入对话框 -->
        <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
          <el-upload
            ref="upload"
            :limit="1"
            accept=".zip"
            :headers="upload.headers"
            :action="upload.url + '?previewUrl=' + upload.previewUrl+ '&speciesId=&keyareaId='+upload.keyareaId"
            :disabled="upload.isUploading"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
            :auto-upload="false"
            drag
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip text-center" slot="tip">
              <span>仅允许导入zip格式文件。</span>
              <el-link type="danger" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
            </div>
          </el-upload>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitFileForm">确 定</el-button>
            <el-button @click="upload.open = false">取 消</el-button>
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { listKeyarea, getKeyarea, delKeyarea, addKeyarea, updateKeyarea, getUnScreen, addScreenRef, delScreenRef } from "@/api/screen/keyarea";
import { listScreen } from "@/api/screen/screen";
import CommonSearch from '@/components/CommonSearch'
import BasicDataSidebar from '@/components/BasicDataSidebar'

export default {
  name: "Keyarea",
  dicts: ['key_area_type'],
  components: { CommonSearch, BasicDataSidebar },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 重点区域表格数据
      keyareaList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      
      // 搜索配置
      searchConfig: {
        label: '重点区域名称',
        key: 'keyareaName',
        placeholder: '请输入重点区域名称'
      },
      
      // 高级搜索字段
      advancedFields: [],
      
      // 搜索表单
      searchForm: {},
      
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyareaName: null,
        keyareaType: null,
        shpFile: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        keyareaName: [
          { required: true, message: "重点区域名称不能为空", trigger: "blur" }
        ],
        keyareaType: [
          { required: true, message: "类型不能为空", trigger: "change" }
        ],
      },
      screenParams: {
        pageNum: 1,
        pageSize: 10,
      },
      screenTotal: 0,
      // 弹出层标题
      screenTitle: "",
      // 是否显示弹出层
      screenOpen: false,
      screenList: [],
      screenName: null,
      screenId: null,
      // 电子屏导入参数
      upload: {
        keyareaId: null,
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        previewUrl: process.env.VUE_APP_BASE_API + "/file/download/",
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/screen/screen/importData"
      },
      importLog: null
    };
  },
  created() {
    this.getList();
  },

  mounted() {
    // 确保字典数据加载完成后再初始化高级搜索字段
    this.$nextTick(() => {
      this.initAdvancedFields();
    });
  },
  watch: {
    // 监听字典数据变化，重新初始化高级搜索字段
    'dict.type.key_area_type': {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.initAdvancedFields();
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 初始化高级搜索字段
    initAdvancedFields() {
      // 确保字典数据已加载
      if (this.dict && this.dict.type && this.dict.type.key_area_type) {
        this.advancedFields = [
          {
            label: '类型',
            key: 'keyareaType',
            type: 'select',
            placeholder: '请选择类型',
            span: 6,
            options: this.dict.type.key_area_type.map(item => ({
              label: item.label,
              value: item.value
            }))
          }
        ];
      } else {
        // 如果字典数据还没加载，延迟初始化
        setTimeout(() => {
          this.initAdvancedFields();
        }, 100);
      }
    },

    /** 查询重点区域列表 */
    getList() {
      this.loading = true;
      listKeyarea(this.queryParams).then(response => {
        this.keyareaList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    // 处理搜索
    handleSearch(searchForm) {
      this.queryParams = {
        ...this.queryParams,
        ...searchForm,
        pageNum: 1
      };
      this.getList();
    },

    // 处理重置
    handleReset(searchForm) {
      this.searchForm = searchForm;
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        ...searchForm
      };
      this.getList();
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    // 表单重置
    reset() {
      this.form = {
        keyareaId: null,
        keyareaName: null,
        keyareaType: null,
        shpFile: null,
        screenNum: null,
        inspectionNum: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.keyareaId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加重点区域";
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const keyareaId = row.keyareaId || this.ids
      getKeyarea(keyareaId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改重点区域";
      });
    },

    /** 查看按钮操作 */
    handleView(row) {
      this.$router.push("/data-management/screen/keyarea/detail/" + row.keyareaId);
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.keyareaId != null) {
            updateKeyarea(this.form).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addKeyarea(this.form).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const keyareaIds = row.keyareaId || this.ids;
      this.$modal.confirm('是否确认删除重点区域编号为"' + keyareaIds + '"的数据项？').then(function() {
        return delKeyarea(keyareaIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download( 'screen/keyarea/export', {
        ...this.queryParams
      }, `重点区域_${new Date().getTime()}.xlsx`)
    },

    /** 分配电子屏 */
    allotScreen(row) {
      this.screenList = [];
      this.screenOpen = true;
      this.screenTitle = "分配电子屏";
      // 根据keyareaId查询电子屏列表
      this.screenParams.keyareaId = row.keyareaId;
      this.queryListScreen();
    },

    queryListScreen(){
      listScreen(this.screenParams).then(response => {
        this.screenList = response.rows;
        this.screenTotal = response.total;
      })
    },

    /** 提交按钮 */
    submitScreen() {
      addScreenRef({keyareaId: this.screenParams.keyareaId, screenId: this.screenId}).then(() => {
        this.queryListScreen();
        this.screenId = null;
        this.screenName = null;
        this.$modal.msgSuccess("分配电子屏成功");
      })
    },

    deleteScreen(row){
      delScreenRef({keyareaId: this.screenParams.keyareaId, screenId: row.screenId}).then(() => {
        this.queryListScreen();
        this.$modal.msgSuccess("移除电子屏成功");
      })
    },

    // 取消按钮
    cancelScreen() {
      this.screenOpen = false;
      this.screenParams.keyareaId = null;
      this.screenId = null;
      this.screenName = null;
      this.getList();
    },

    querySearchScreen(queryString, cb) {
      if(!queryString){
        cb([]);
      }
      let results;
      if(queryString){
        getUnScreen(this.screenParams.keyareaId, queryString).then(response => {
          results = response.data;
          cb(results);
        });
      }
    },

    handleSelectScreen(item) {
      this.screenId = item.screenId;
      this.screenName = item.screenName;
    },

    /** 导入按钮操作 */
    handleImport(row) {
      this.upload.title = "电子屏导入";
      this.upload.open = true;
      this.upload.keyareaId = row.keyareaId;
    },

    /** 下载模板操作 */
    async importTemplate() {
      const url = process.env.VUE_APP_BASE_API+'/file/download/screen/docs/Led电子屏台账批量上传模板.xlsx';
      const fileName = '电子屏导入模板.xlsx';

      const response = await fetch(url);
      const blob = await response.blob();
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.download = fileName;
      link.click();
    },

    /** 文件上传中处理 */
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },

    /** 文件上传成功处理 */
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },

    /** 提交上传文件 */
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .screen-management-layout {
    display: flex;
    gap: 20px;
    height: 100%;

    .content-area {
      flex: 1;
      min-width: 0;
    }
  }

  .action-bar {
    background: white;
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
  }

  .table-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
  }
}

// 表格样式
::v-deep .el-table {
  .text-nowrap {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
