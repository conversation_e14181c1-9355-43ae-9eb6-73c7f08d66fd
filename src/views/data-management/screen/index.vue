<template>
  <div class="app-container">
    <div class="screen-management-layout">
      <!-- 左侧导航菜单 -->
      <basic-data-sidebar />

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <!-- 搜索组件 -->
        <common-search
          :search-config="searchConfig"
          :advanced-fields="advancedFields"
          :initial-form="searchForm"
          @search="handleSearch"
          @reset="handleReset"
        />

    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['screen:screen:add']"
          >电子屏新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            icon="el-icon-setting"
            size="mini"
            @click="handleReminder"
          >设置到期提醒</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleBatchTemplate"
          >批量模版下载</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-upload2"
            size="mini"
            @click="handleImport"
            v-hasPermi="['screen:screen:add']"
          >电子屏导入</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleDownload"
            v-hasPermi="['screen:screen:add']"
          >下载承诺书</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['screen:screen:export']"
          >电子屏导出</el-button>
        </el-col>
        <div class="toolbar-right">
           <el-button
            type="text"
            icon="el-icon-setting"
            size="mini"
            class="display-fields-btn"
            @click="handleDisplayFieldsSettings"
          >设置显示字段</el-button>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </div>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="screenList"
        :header-cell-style="{ backgroundColor: '#EBF2FF' }"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" fixed="left" />
        <el-table-column v-if="displayFields.screenName" label="电子屏名称/位置" align="center" min-width="220" prop="screenName">
          <template slot-scope="scope">
            <div class="site-name-container">
              <div class="text-nowrap">{{scope.row.screenName}}</div>
              <span
                v-if="scope.row.dataUpdateStatus === 1"
                class="update-badge pending"
              >待更新</span>
              <span
                v-else-if="scope.row.dataUpdateStatus > 1"
                class="update-badge overdue"
              >已超期</span>
            </div>
            <div class="text-nowrap">{{scope.row.address}}</div>
          </template>
        </el-table-column>
        <el-table-column v-if="displayFields.policeStation" label="所属派出所" align="center" prop="areaName" width="110">
          <template slot-scope="scope">
            <div class="text-nowrap">{{ scope.row.areaName }}</div>
            <div class="text-nowrap">{{ scope.row.policeName }}</div>
          </template>
        </el-table-column>
        <el-table-column v-if="displayFields.images" label="照片" align="center" prop="images" width="80">
          <template slot-scope="scope">
            <image-viewer :src="scope.row.images" :width="50" :height="50" title="电子屏照片"/>
          </template>
        </el-table-column>
        <el-table-column v-if="displayFields.screenSize" label="屏幕尺寸/类型" align="center" prop="size" width="120">
          <template slot-scope="scope">
            <div class="text-nowrap">{{ scope.row.size }}m²</div>
            <dict-tag :options="dict.type.screen_type" :value="scope.row.screenType"/>
          </template>
        </el-table-column>
        <el-table-column v-if="displayFields.enterprise" label="责任单位" align="center" prop="enterprise.enterpriseName" min-width="100">
          <template slot-scope="scope">
            <div class="text-nowrap" :title="scope.row.enterprise.enterpriseName">{{ scope.row.enterprise.enterpriseName }}</div>
            <div class="text-nowrap" :title="scope.row.enterprise.creditCode">{{ scope.row.enterprise.creditCode}}</div>
          </template>
        </el-table-column>
        <el-table-column v-if="displayFields.contact" label="责任单位联系人" align="center" prop="enterprise.principal" min-width="120">
          <template slot-scope="scope">
            <div class="text-nowrap" :title="scope.row.enterprise.principal">{{ scope.row.enterprise.principal }}</div>
            <div class="text-nowrap" :title="scope.row.enterprise.principalPhone">{{ scope.row.enterprise.principalPhone}}</div>
          </template>
        </el-table-column>
        <el-table-column v-if="displayFields.keyarea" label="重点区域" align="center" prop="status" width="180">
          <template slot-scope="scope">
            <div v-if="scope.row.keyareaList && scope.row.keyareaList.length>0">
              <el-tag
                v-for="(item, index) in scope.row.keyareaList"
                :key="index"
                @click="showKeyarea(item.keyareaId)"
                class="keyarea-tag"
                style="margin: 2px;"
              >{{ item.keyareaName }}</el-tag>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column v-if="displayFields.special" label="专项" align="center" prop="specialList" width="200">
          <template slot-scope="scope">
            <div v-if="scope.row.specialList && scope.row.specialList.length>0">
              <el-tag
                v-for="(item, index) in scope.row.specialList"
                :key="index"
                @click="showSpecial(item.specialId)"
                class="special-tag"
                style="margin: 2px;"
              >{{ item.specialName }}</el-tag>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column v-if="displayFields.site" label="归属场所" align="center" prop="site.siteName" min-width="90">
          <template slot-scope="scope">
            <div v-if="scope.row.site && scope.row.site.siteCode != '0'">
              <div class="text-nowrap" :title="scope.row.site.siteName">{{ scope.row.site.siteName }}</div>
              <div class="text-nowrap" :title="scope.row.site.siteAddress">{{ scope.row.site.siteAddress}}</div>
            </div>
            <span v-else> - </span>
          </template>
        </el-table-column>
        <el-table-column v-if="displayFields.netsystem" label="联网系统" align="center" prop="status" min-width="90">
          <template slot-scope="scope">
            <div v-if="scope.row.netsystemId != '0'" class="text-nowrap" :title="scope.row.netsystem && scope.row.netsystem.netsystemName">
              {{ scope.row.netsystem && scope.row.netsystem.netsystemName }}
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column v-if="displayFields.inspectionNum" label="近三月检查数" align="center" prop="inspectionNum" width="100"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="220" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['screen:screen:query']"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleEdit(scope.row)"
              v-hasPermi="['screen:screen:edit']"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['screen:screen:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 电子屏导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".zip"
        :headers="upload.headers"
        :action="upload.url + '?previewUrl=' + upload.previewUrl+ '&keyareaId=&speciesId='"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入zip格式文件。</span>
          <el-link type="danger" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
      </div>
    </div>

    <!-- 数据更新提醒组件 -->
    <data-update-reminder
      :visible.sync="reminderVisible"
      :biz-type="'SCREEN'"
    />

    <!-- 字段显示设置组件 -->
    <FieldDisplaySettings
      :visible.sync="displayFieldsDialogVisible"
      :available-fields="availableDisplayFields"
      :current-fields="currentSelectedFields"
      :api-url="'/screen/screen/list'"
      @save-success="handleFieldSettingsSaveSuccess"
      @close="handleFieldSettingsClose"
    />
  </div>
</template>

<script>
import { listScreen, delScreen } from "@/api/screen";
import { listKeyarea } from "@/api/screen/keyarea";
import { listSpecial } from "@/api/screen/special";
import { listUserArea } from '@/api/system/area';
import { getToken } from "@/utils/auth";
import CommonSearch from '@/components/CommonSearch'
import BasicDataSidebar from '@/components/BasicDataSidebar'
import ImageViewer from '@/components/ImageViewer'
import DataUpdateReminder from '@/components/DataUpdateReminder'
import FieldDisplaySettings from '@/components/FieldDisplaySettings'

export default {
  name: "Screen",
  dicts: ['screen_display_type', 'location_floor_level', 'sys_common_status', 'screen_type', 'screen_nature', 'screen_location_type', 'yes_or_no', 'result_flag'],
  components: { CommonSearch, BasicDataSidebar, ImageViewer, DataUpdateReminder, FieldDisplaySettings },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      // 电子屏信息表格数据
      screenList: [],

      // 搜索配置
      searchConfig: {
        label: '电子屏名称/位置',
        key: 'screenName',
        placeholder: '请输入电子屏名称或位置'
      },

      // 高级搜索字段
      advancedFields: [],

      // 搜索表单
      searchForm: {},

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        screenName: null,
        screenNature: null,
        screenType: null,
        addressType: null,
        showType: null,
        areaCode: null,
        policeCode: null,
        address: null,
        size: null,
        floorLevel: null,
        images: null,
        enterpriseId: null,
        enterpriseName: null,
        keyareaId: null,
        keyareaName: null,
        specialId: null,
        specialName: null,
        siteCode: null,
        siteName: null,
        netsystemId: null,
        netsystemName: null,
        accessUrl: null,
        status: null,
        params: {
          keyareaFlag: null
        }
      },

      userAreaList: [],
      keyareaList: [],
      specialList: [],

      // 电子屏导入参数
      // 电子屏导入参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        previewUrl: process.env.VUE_APP_BASE_API + "/file/download/",
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/screen/screen/importData"
      },
      importLog: null,

      // 提醒组件显示状态
      reminderVisible: false,

      // 字段显示设置
      displayFieldsDialogVisible: false,
      displayFields: {
        screenName: true,
        policeStation: true,
        images: true,
        screenSize: true,
        enterprise: true,
        contact: true,
        keyarea: true,
        special: true,
        site: true,
        netsystem: true,
        inspectionNum: true
      },
      // 可用的显示字段配置
      availableDisplayFields: [
        { key: 'screenName', label: '电子屏名称/位置', description: '电子屏的名称和位置信息' },
        { key: 'policeStation', label: '所属派出所', description: '电子屏所属的派出所' },
        { key: 'images', label: '照片', description: '电子屏的照片' },
        { key: 'screenSize', label: '屏幕尺寸/类型', description: '电子屏的尺寸和类型' },
        { key: 'enterprise', label: '责任单位', description: '负责该电子屏的企业单位' },
        { key: 'contact', label: '责任单位联系人', description: '责任单位的联系人信息' },
        { key: 'keyarea', label: '重点区域', description: '电子屏所在的重点区域' },
        { key: 'special', label: '专项', description: '电子屏相关的专项信息' },
        { key: 'site', label: '归属场所', description: '电子屏归属的场所' },
        { key: 'netsystem', label: '联网系统', description: '电子屏接入的联网系统' },
        { key: 'inspectionNum', label: '近三月检查数', description: '近三个月的检查次数' }
      ]
    };
  },
  computed: {
    // 当前选中的字段
    currentSelectedFields() {
      return Object.keys(this.displayFields).filter(key => this.displayFields[key]);
    }
  },
  created() {
    console.log(this.$route, '=======')
    let overTimeQuery = this.$route.query && this.$route.query.overTimeQuery
    // 将字符串转换为数字
    if (overTimeQuery) {
      overTimeQuery = parseInt(overTimeQuery);
    }
    this.getList(overTimeQuery);
    this.getUserAreaList();
    this.getKeyareaList();
    this.getSpecialList();
  },
  methods: {
    // 初始化高级搜索字段
    initAdvancedFields() {
      console.log('Screen - 初始化高级搜索字段');
      console.log('Screen - dict:', this.dict);
      console.log('Screen - yes_or_no:', this.dict && this.dict.type && this.dict.type.yes_or_no);

      this.advancedFields = [
        {
          label: '区域',
          key: 'areaCode',
          type: 'select',
          placeholder: '请选择区域',
          span: 6,
          options: this.userAreaList.map(item => ({
            label: item.areaName,
            value: item.areaCode
          }))
        },
        {
          label: '责任单位',
          key: 'enterpriseName',
          type: 'input',
          placeholder: '请输入责任单位名称',
          span: 6
        },
        {
          label: '重点区域',
          key: 'keyareaId',
          type: 'select',
          placeholder: '请选择重点区域',
          span: 6,
          options: this.keyareaList.map(item => ({
            label: item.keyareaName,
            value: item.keyareaId
          }))
        },
        {
          label: '专项',
          key: 'specialId',
          type: 'select',
          placeholder: '请选择专项',
          span: 6,
          options: this.specialList.map(item => ({
            label: item.specialName,
            value: item.specialId
          }))
        },
        {
          label: '归属场所',
          key: 'siteName',
          type: 'input',
          placeholder: '请输入归属场所名称',
          span: 6
        },
        {
          label: '联网系统',
          key: 'netsystemName',
          type: 'input',
          placeholder: '请输入联网系统名称',
          span: 6
        },
        {
          label: '是否重点区域',
          key: 'keyareaFlag',
          type: 'select',
          placeholder: '请选择',
          span: 6,
          options: (this.dict && this.dict.type && this.dict.type.yes_or_no) ? this.dict.type.yes_or_no.map(item => ({
            label: item.label,
            value: item.value
          })) : []
        },
        {
          label: '电子屏性质',
          key: 'screenNature',
          type: 'select',
          placeholder: '请选择电子屏性质',
          span: 6,
          options: (this.dict && this.dict.type && this.dict.type.screen_nature) ? this.dict.type.screen_nature.map(item => ({
            label: item.label,
            value: item.value
          })) : []
        },
        {
          label: '位置类型',
          key: 'addressType',
          type: 'select',
          placeholder: '请选择位置类型',
          span: 6,
          options: (this.dict && this.dict.type && this.dict.type.screen_location_type) ? this.dict.type.screen_location_type.map(item => ({
            label: item.label,
            value: item.value
          })) : []
        },
        {
          label: '显示类型',
          key: 'showType',
          type: 'select',
          placeholder: '请选择显示类型',
          span: 6,
          options: (this.dict && this.dict.type && this.dict.type.screen_display_type) ? this.dict.type.screen_display_type.map(item => ({
            label: item.label,
            value: item.value
          })) : []
        }
      ];
    },

    /** 查询电子屏信息列表 */
    getList(params) {
      this.loading = true;

      // 如果params是数字且为overTimeQuery的有效值，则设置overTimeQuery
      if (typeof params === 'number' && (params === 1 || params === 2)) {
        this.queryParams.overTimeQuery = params;
      } else if (typeof params === 'object' && params !== null) {
        // 如果是对象，可能是翻页等其他参数，不处理overTimeQuery
        console.log('---翻页或其他参数----', params);
      } else if (params === undefined || params === null) {
        // 如果没有参数，清除overTimeQuery（如果需要的话）
        // 注意：这里不自动清除，保持当前状态
      }
      listScreen(this.queryParams).then(response => {
        this.screenList = response.rows;
        this.total = response.total;

        // 处理API返回的fields字段
        if (response.fields) {
          this.handleApiFields(response.fields);
        }

        this.loading = false;
      });
    },

    // 处理搜索
    handleSearch(searchForm) {
      // 分离需要放在params中的字段
      const { keyareaFlag, ...otherFields } = searchForm;

      this.queryParams = {
        ...this.queryParams,
        ...otherFields,
        pageNum: 1
      };

      // 处理params中的字段
      if (keyareaFlag !== undefined) {
        this.queryParams.params = {
          ...this.queryParams.params,
          keyareaFlag: keyareaFlag
        };
      }

      this.getList();
    },

    // 处理重置
    handleReset(searchForm) {
      this.searchForm = searchForm;
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        screenName: null,
        screenNature: null,
        screenType: null,
        addressType: null,
        showType: null,
        areaCode: null,
        policeCode: null,
        address: null,
        size: null,
        floorLevel: null,
        images: null,
        enterpriseId: null,
        enterpriseName: null,
        keyareaId: null,
        keyareaName: null,
        specialId: null,
        specialName: null,
        siteCode: null,
        siteName: null,
        netsystemId: null,
        netsystemName: null,
        accessUrl: null,
        status: null,
        params: {
          keyareaFlag: null
        },
        ...searchForm
      };
      this.getList();
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.screenId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push("/data-management/screen/add");
    },

    /** 修改按钮操作 */
    handleEdit(row) {
      const screenId = row.screenId || this.ids
      this.$router.push("/data-management/screen/edit/"+screenId);
    },

    /** 查看按钮操作 */
    handleView(row) {
      this.$router.push("/data-management/screen/detail/"+row.screenId);
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const screenIds = row.screenId || this.ids;
      this.$modal.confirm('是否确认删除电子屏信息编号为"' + row.screenName + '"的数据项？').then(function() {
        return delScreen(screenIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "电子屏导入";
      this.upload.open = true;
    },

    /** 下载模板操作 */
    async importTemplate() {
      const url = process.env.VUE_APP_BASE_API+'/file/download/'+vueConfig.template_path_screen; // 替换成你要下载的文件的URL
      const fileName = '电子屏导入模板.xlsx'; // 自定义文件名

      const response = await fetch(url);
      const blob = await response.blob();
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    // 文件上传成功处理
    handleFileSuccess(response) {
      this.loading = false;
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.importLog = response.msg;

      this.$confirm(
        "<div style=' width: 600px; overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"
        + response.msg
        + "</div>",
        '导入结果',
        {
        confirmButtonText: '下载导入日志',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true,
        showCancelButton: true,
        showClose: false,
        closeOnClickModal: false,
      })
        .then(() => {
          if(this.importLog){
            this.downloadImportLog('log.txt', this.importLog)
          }else{
            this.$modal.msgError("暂无日志");
          }
        })
        .catch(() => {});

      this.getList();
    },

    // 提交上传文件
    submitFileForm() {
      this.upload.open = false;
      this.$refs.upload.submit();
    },

    // 文件上传中处理
    handleFileUploadProgress() {
      this.loading = true;
      this.upload.isUploading = true;
    },

    async handleDownload() {
      const url = process.env.VUE_APP_BASE_API+'/file/download/screen/docs/%E6%89%BF%E8%AF%BA%E4%B9%A6.pdf';
      const fileName = '承诺书.pdf';

      const response = await fetch(url);
      const blob = await response.blob();
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('screen/screen/export', {
        ...this.queryParams
      }, `电子屏台账_${new Date().getTime()}.xlsx`)
    },

    getUserAreaList(){
      listUserArea().then(response => {
        this.userAreaList = response.data;
        this.initAdvancedFields();
      });
    },

    getKeyareaList(){
      let params = {
        pageNum: 1,
        pageSize: 9999,
      }
      listKeyarea(params).then(response => {
        this.keyareaList = response.rows;
        this.initAdvancedFields();
      });
    },

    getSpecialList(){
      let params = {
        pageNum: 1,
        pageSize: 9999,
      }
      listSpecial(params).then(response => {
        this.specialList = response.rows;
        this.initAdvancedFields();
      });
    },

    // 获取区域列表
    getAreaList() {
      let params = {
        parentId: '110000'
      }
      listArea(params).then(response => {
        this.areaList = response.data;
      });
    },

    // 获取派出所列表
    getPoliceList() {
      let params = {
        parentId: this.queryParams.areaCode
      }
      listArea(params).then(response => {
        this.policeList = response.data;
      });
    },

    showKeyarea(keyareaId) {
      console.log('显示重点区域:', keyareaId);
    },

    showSpecial(specialId) {
      console.log('显示专项:', specialId);
    },

    downloadImportLog(fileName, data){
      let aLink = document.createElement('a')
      let blob = new Blob([this.htmlToString(data)]);
      aLink.download = fileName
      aLink.href = URL.createObjectURL(blob)
      aLink.click()
    },

    htmlToString(html) {
      html = html.replaceAll('<br/>','\n');
      return html;
    },

    // 设置到期提醒
    handleExpireReminder() {
      this.$message.info('设置到期提醒功能开发中');
    },

    // 批量模版下载
    handleBatchTemplate() {
      this.$message.info('批量模版下载功能开发中');
    },

    // 设置提醒
    handleReminder() {
      this.reminderVisible = true;
    },

    // 设置显示字段
    handleDisplayFieldsSettings() {
      this.displayFieldsDialogVisible = true;
    },

    // 处理字段设置保存成功
    handleFieldSettingsSaveSuccess(selectedFields) {
      // 重置所有字段为false
      Object.keys(this.displayFields).forEach(key => {
        this.displayFields[key] = false;
      });

      // 设置选中的字段为true
      selectedFields.forEach(field => {
        if (this.displayFields.hasOwnProperty(field)) {
          this.displayFields[field] = true;
        }
      });

      // 保存到本地存储
      localStorage.setItem('screenDisplayFields', JSON.stringify(this.displayFields));
      this.$message.success('显示字段设置已保存');
    },

    // 处理字段设置弹窗关闭
    handleFieldSettingsClose() {
      this.displayFieldsDialogVisible = false;
    },

    // 处理API返回的fields字段
    handleApiFields(fields) {
      console.log('API返回的fields字段:', fields);

      if (fields && typeof fields === 'string') {
        // 解析fields字符串
        const fieldsArray = fields.split('|');
        console.log('解析后的字段数组:', fieldsArray);

        // 重置所有字段为false
        Object.keys(this.displayFields).forEach(key => {
          this.displayFields[key] = false;
        });

        // 根据API返回的字段设置为true
        fieldsArray.forEach(field => {
          if (this.displayFields.hasOwnProperty(field)) {
            this.displayFields[field] = true;
          }
        });

        console.log('更新后的displayFields:', this.displayFields);
      } else if (!fields) {
        // 如果fields为null、空或undefined，显示所有字段
        console.log('fields为空，显示所有字段');
        Object.keys(this.displayFields).forEach(key => {
          this.displayFields[key] = true;
        });
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .screen-management-layout {
    display: flex;
    gap: 20px;
    height: 100%;

    .content-area {
      flex: 1;
      min-width: 0;
    }
  }

  .action-bar {
    background: white;
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
  }

  .table-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
  }

  .toolbar-right {
    // margin-left: auto;
    float:right;
    display: flex;
    align-items: center;
    gap: 12px;

    .display-fields-btn {
      color: #ACB0B3;
      padding: 0;
      border: none;
      background: none;

      &:hover {
        color: #4584FF;
      }
    }
  }
}

// 表格样式
::v-deep .el-table {
  .text-nowrap {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// 名称角标样式
.site-name-container {
  position: relative;
  display: inline-block;
  padding: 12px 12px 0 0;
  .update-badge {
    position: absolute;
    top: -2px;
    right: -10px;
    font-size: 10px;
    padding: 1px 4px;
    border-radius: 2px;
    // color: white;
    white-space: nowrap;
    z-index: 1;

    &.pending {
      color: #FE9400;
    }

    &.overdue {
      color: #FF2A2A;
    }
  }
}

::v-deep .el-table {

  .keyarea-tag {
    border-color: #4584FF;
    color: #4584FF;
    background-color: #E9F1FF;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }

  .special-tag {
    border-color: #19BB77;
    color: #19BB77;
    background-color: #EAFEF6;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
}

::v-deep .el-message-box {
  width: 620px !important;
}
</style>
