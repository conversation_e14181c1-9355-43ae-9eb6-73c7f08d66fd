<template>
<div style="display:flex;background: #f5f7fa;">


  <section class="app-container">
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">电子屏详情</h2>
        <div class="header-actions">
          <el-button type="primary" size="small" @click="handleEdit">编辑</el-button>
          <el-button icon="el-icon-arrow-left" size="small" @click="goBack">返回</el-button>
        </div>
      </div>
    </div>

    <div class="detail-container" v-loading="loading">
      <!-- 左右布局容器 -->
          <!-- 基本信息模块 -->
      <div id="basic-info" class="detail-section">
        <div class="section-header" @click="toggleSection('basic')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">基本信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.basic ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.basic ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>

        <div v-show="!sectionCollapsed.basic" class="section-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="电子屏名称">
              {{ form.screenName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="电子屏性质">
              <dict-tag :options="dict.type.screen_nature" :value="form.screenNature"/>
            </el-descriptions-item>
            <el-descriptions-item label="屏幕类型">
              <dict-tag :options="dict.type.screen_type" :value="form.screenType"/>
            </el-descriptions-item>
            <el-descriptions-item label="位置类型">
              <dict-tag :options="dict.type.screen_location_type" :value="form.addressType"/>
            </el-descriptions-item>
            <el-descriptions-item label="显示类型">
              <dict-tag :options="dict.type.screen_display_type" :value="form.showType"/>
            </el-descriptions-item>
            <el-descriptions-item label="所属区域">
              <template v-for="(item, index) in areaList">
                <el-tag
                  v-if="item.areaCode === form.areaCode"
                  :disable-transitions="true"
                  :key="item.areaCode"
                  :index="index"
                  type="primary"
                >
                  {{ item.areaName }}
                </el-tag>
              </template>
              <span v-if="!getAreaName(form.areaCode)">-</span>
            </el-descriptions-item>
            <el-descriptions-item label="所属派出所">
              <template v-for="(item, index) in policeList">
                <el-tag
                  v-if="item.areaCode === form.policeCode"
                  :disable-transitions="true"
                  :key="item.areaCode"
                  :index="index"
                  type="primary"
                >
                  {{ item.areaName }}
                </el-tag>
              </template>
              <span v-if="!getPoliceName(form.policeCode)">-</span>
            </el-descriptions-item>
            <el-descriptions-item label="详细地址">
              {{ form.address || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="经度">
              {{ form.longitude || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="纬度">
              {{ form.latitude || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="屏幕尺寸">
              {{ form.size ? form.size + 'm²' : '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="楼层">
              <dict-tag :options="dict.type.location_floor_level" :value="form.floorLevel"/>
            </el-descriptions-item>
            <el-descriptions-item label="是否下发风险提示单">
              <dict-tag :options="dict.type.sys_yes_no" :value="form.riskTipFlag"/>
            </el-descriptions-item>
            <el-descriptions-item label="责任民警">
              {{ form.policeMan || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="责任民警联系电话">
              {{ form.policePhone || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <dict-tag :options="dict.type.sys_common_status" :value="form.status"/>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ parseTime(form.createTime) || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="电子屏照片" :span="2">
              <image-viewer :src="form.images" :width="100" :height="100" title="电子屏照片" v-if="form.images"/>
              <span v-else>-</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 责任单位信息模块 -->
      <div id="enterprise-info" class="detail-section">
        <div class="section-header" @click="toggleSection('enterprise')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">责任单位信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.enterprise ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.enterprise ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>

        <div v-show="!sectionCollapsed.enterprise" class="section-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="责任单位名称">
              {{ form.enterprise && form.enterprise.enterpriseName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="统一社会信用代码">
              {{ form.enterprise && form.enterprise.creditCode || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="负责人">
              {{ form.enterprise && form.enterprise.principal || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="联系电话">
              {{ form.enterprise && form.enterprise.principalPhone || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="企业地址" :span="2">
              {{ form.enterprise && form.enterprise.enterpriseAddress || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>



      <!-- 重点区域信息模块 -->
      <div class="detail-section" v-if="form.keyareaList && form.keyareaList.length > 0">
        <div class="section-header" @click="toggleSection('keyarea')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">重点区域信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.keyarea ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.keyarea ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>

        <div v-show="!sectionCollapsed.keyarea" class="section-content">
          <el-descriptions :column="2" border v-for="(item, index) in form.keyareaList" :key="index" style="margin-bottom: 16px;">
            <el-descriptions-item label="重点区域">
              【{{ index + 1 }}】{{ item.keyareaName }}
            </el-descriptions-item>
            <el-descriptions-item label="类型">
              <dict-tag :options="dict.type.key_area_type" :value="item.keyareaType"/>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 专项信息模块 -->
      <div class="detail-section" v-if="form.specialList && form.specialList.length > 0">
        <div class="section-header" @click="toggleSection('special')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">专项信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.special ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.special ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>

        <div v-show="!sectionCollapsed.special" class="section-content">
          <el-descriptions :column="2" border v-for="(item, index) in form.specialList" :key="index" style="margin-bottom: 16px;">
            <el-descriptions-item label="专项名称">
              【{{ index + 1 }}】{{ item.specialName }}
            </el-descriptions-item>
            <el-descriptions-item label="任务周期">
              <dict-tag :options="dict.type.special_cycle" :value="item.specialCycle"/>
            </el-descriptions-item>
            <el-descriptions-item label="开始时间">
              {{ parseTime(item.beginTime, '{y}-{m}-{d}') || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="结束时间">
              {{ parseTime(item.endTime, '{y}-{m}-{d}') || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="任务说明" :span="2">
              {{ item.content || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 归属场所信息模块 -->
      <div class="detail-section" v-if="form.site && form.site.siteName">
        <div class="section-header" @click="toggleSection('site')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">归属场所信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.site ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.site ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>

        <div v-show="!sectionCollapsed.site" class="section-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="场所名称">
              {{ form.site.siteName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="场所状态">
              <dict-tag :options="dict.type.site_business_status" :value="form.site.siteStatus"/>
            </el-descriptions-item>
            <el-descriptions-item label="场所类型">
              <dict-tag :options="dict.type.site_type" :value="form.site.siteType"/>
            </el-descriptions-item>
            <el-descriptions-item label="负责人姓名">
              {{ form.site.principal || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="负责人电话">
              {{ form.site.principalPhone || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="负责人证件类型">
              <dict-tag :options="dict.type.certificate_type" :value="form.site.principalCredentialType"/>
            </el-descriptions-item>
            <el-descriptions-item label="负责人证件号码">
              {{ form.site.principalCertificateCode || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="场所地址">
              {{ form.site.siteAddress || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 屏幕联网信息模块 -->
      <div class="detail-section" v-if="form.netsystem && form.netsystem.netsystemName">
        <div class="section-header" @click="toggleSection('netsystem')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">屏幕联网信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.netsystem ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.netsystem ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>

        <div v-show="!sectionCollapsed.netsystem" class="section-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="联网系统名称">
              {{ form.netsystem.netsystemName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="等级保护级别">
              <dict-tag :options="dict.type.grade_protection_level" :value="form.netsystem.protectionLevel"/>
            </el-descriptions-item>
            <el-descriptions-item label="等级保护备案机关">
              {{ form.netsystem.protectionLevelOrgan || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="域名/地址">
              {{ form.netsystem.accessUrl || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="连接控制系统方式">
              <dict-tag :options="dict.type.screen_control_sys_way" :value="form.netsystem.connectType"/>
            </el-descriptions-item>
            <el-descriptions-item label="控制系统运行状态">
              <dict-tag :options="dict.type.screen_control_sys_status" :value="form.netsystem.runStatus"/>
            </el-descriptions-item>
            <el-descriptions-item label="物理位置" :span="2">
              {{ form.netsystem.address || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="系统开发/制造商">
              {{ form.netsystem.manufacturer || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="开发厂商统一社会信用代码">
              {{ form.netsystem.manufacturerCreditCode || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="开发厂商负责人姓名">
              {{ form.netsystem.principal || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="开发厂商负责人电话">
              {{ form.netsystem.principalPhone || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="运维厂商名称">
              {{ form.netsystem.operation || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="运维厂商统一社会信用代码">
              {{ form.netsystem.operationCreditCode || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="运维厂商负责人">
              {{ form.netsystem.operationPrincipal || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="运维厂商负责人电话">
              {{ form.netsystem.operationPrincipalPhone || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="是否建立内容审核机制">
              <dict-tag :options="dict.type.sys_yes_no" :value="form.netsystem.examineFlag"/>
            </el-descriptions-item>
            <el-descriptions-item label="是否建立一键关停">
              <dict-tag :options="dict.type.sys_yes_no" :value="form.netsystem.shutdownFlag"/>
            </el-descriptions-item>
            <el-descriptions-item label="是否完成专项整治">
              <dict-tag :options="dict.type.sys_yes_no" :value="form.netsystem.rectificationFlag"/>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 检查记录模块 -->
      <div class="detail-section" v-if="form.inspectionList && form.inspectionList.length > 0">
        <div class="section-header" @click="toggleSection('inspection')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">近期检查记录</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.inspection ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.inspection ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>

        <div v-show="!sectionCollapsed.inspection" class="section-content">
          <el-table :data="form.inspectionList" :header-cell-style="{ backgroundColor: '#EBF2FF' }">
            <el-table-column prop="inspectionDate" label="检查日期" width="120">
              <template slot-scope="scope">
                {{ parseTime(scope.row.inspectionDate, '{y}-{m}-{d}') }}
              </template>
            </el-table-column>
            <el-table-column prop="inspectionType" label="检查类型" width="100"/>
            <el-table-column prop="inspectionResult" label="检查结果" width="100">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.result_flag" :value="scope.row.inspectionResult"/>
              </template>
            </el-table-column>
            <el-table-column prop="inspectionContent" label="检查内容" show-overflow-tooltip/>
            <el-table-column prop="inspectionPerson" label="检查人员" width="100"/>
            <el-table-column prop="remark" label="备注" show-overflow-tooltip/>
          </el-table>
        </div>
      </div>

      <!-- 检查任务列表模块 -->
      <div id="task-list" class="detail-section">
        <div class="section-header" @click="toggleSection('taskResult')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">检查任务列表({{taskTotal}})</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.taskResult ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.taskResult ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>

        <div v-show="!sectionCollapsed.taskResult" class="section-content">
          <!-- 搜索区域 -->
          <!-- <div class="search-container">
            <el-form :model="taskQueryParams" ref="taskQueryForm" size="small" :inline="true" label-width="80px">
              <el-form-item label="任务名称" prop="taskName">
                <el-input
                  v-model="taskQueryParams.taskName"
                  placeholder="请输入任务名称"
                  clearable
                  style="width: 200px;"
                  @keyup.enter.native="handleTaskQuery"
                />
              </el-form-item>
              <el-form-item label="检查结果" prop="resultFlag">
                <el-select v-model="taskQueryParams.resultFlag" placeholder="请选择检查结果" clearable style="width: 150px;">
                  <el-option
                    v-for="dict in dict.type.result_flag"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="检查时间">
                <el-date-picker
                  v-model="taskDateRange"
                  style="width: 240px"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleTaskQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetTaskQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </div> -->

          <!-- 表格区域 -->
          <el-table v-loading="taskLoading" :data="taskResultList" :header-cell-style="{ backgroundColor: '#EBF2FF' }"
        border>
            <el-table-column label="任务名称"  prop="taskName" min-width="150">
              <template slot-scope="scope">
                <span style="color: #4584FF; cursor: pointer;" @click="handleTaskDetail(scope.row)" :title="scope.row.taskName">
                  {{ scope.row.taskName }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="电子屏名称/位置"  prop="screenName" min-width="200">
              <template slot-scope="scope">
                <div>{{ scope.row.electronicScreen.screenName || '-' }}</div>
                <div style="color: #909399; font-size: 12px;">{{ scope.row.electronicScreen.address || '-' }}</div>
              </template>
            </el-table-column>
            <el-table-column label="所属派出所"  prop="areaName" width="120">
              <template slot-scope="scope">
                <div>{{ scope.row.electronicScreen.areaName || '-' }}</div>
                <div style="color: #909399; font-size: 12px;">{{ scope.row.electronicScreen.policeName || '-' }}</div>
              </template>
            </el-table-column>
            <el-table-column label="电子屏照片"  prop="images" width="100">
              <template slot-scope="scope">
                <image-viewer :src="scope.row.electronicScreen.images" :width="50" :height="50" title="电子屏照片"/>
              </template>
            </el-table-column>
            <el-table-column label="责任单位" prop="enterprise" min-width="180">
              <template slot-scope="scope">
                <div v-if="scope.row.enterprise" >{{ scope.row.enterprise.enterpriseName || '-' }}</div>
                <div v-if="scope.row.enterprise" style="color: #909399; font-size: 12px;">{{ scope.row.enterprise.creditCode || '-' }}</div>
              </template>
            </el-table-column>
            <el-table-column label="联系人"  prop="contact" width="120">
              <template slot-scope="scope">
                <div v-if="scope.row.enterprise">{{ scope.row.enterprise.principal || '-' }}</div>
                <div v-if="scope.row.enterprise" style="color: #909399; font-size: 12px;">{{ scope.row.enterprise.principalPhone || '-' }}</div>
              </template>
            </el-table-column>
            <el-table-column label="检查照片" prop="checkImages" width="100">
              <template slot-scope="scope">
                <image-viewer :src="scope.row.images" :width="50" :height="50" title="检查照片"/>
              </template>
            </el-table-column>
            <el-table-column label="检查文书"  prop="fileUrl" width="80">
              <template slot-scope="scope">
                <el-link v-if="scope.row.fileUrl" type="primary" :underline="false" :href="scope.row.fileUrl" target="_blank">
                  <i class="el-icon-tickets"></i>
                </el-link>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="检查结果"  prop="resultFlag" width="100">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.result_flag" :value="scope.row.resultFlag"/>
              </template>
            </el-table-column>
            <el-table-column label="异常信息"  prop="exceptionType" min-width="150">
              <template slot-scope="scope">
                <div v-if="scope.row.exceptionType">
                  <dict-tag :options="dict.type.exception_type" :value="scope.row.exceptionType"/>
                  <div v-if="scope.row.exceptionDesc" style="color: #909399; font-size: 12px; margin-top: 4px;">
                    {{ scope.row.exceptionDesc }}
                  </div>
                </div>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="检查日期" align="center" prop="createTime" width="120">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页组件 -->
          <pagination
            v-show="taskTotal>0"
            :total="taskTotal"
            :page.sync="taskQueryParams.pageNum"
            :limit.sync="taskQueryParams.pageSize"
            @pagination="getTaskList"
          />
        </div>
      </div>

        
    </div>
  </section>
  <!-- 右侧数据维护记录区域 -->
  <collapsible-detail-right @collapse-change="handleRightCollapseChange">
    <!-- 锚点导航组件 -->
      <template #anchor> 
        <anchor-navigation
        :anchor-items="anchorItems"
        @anchor-change="handleAnchorChange"
      />
     </template>
    
     <template #timeline>
       <data-maintenance-timeline
        :biz-type="'SCREEN'"
        :biz-id="form.screenId"
        v-if="form.screenId"
      />
    </template>
 </collapsible-detail-right>
</div>
</template>

<script>
import { getScreen } from "@/api/screen/screen";
import ImageViewer from '@/components/ImageViewer'
import { listTaskResult } from "@/api/screen/taskResult";
import { listArea } from '@/api/system/area';
import DataMaintenanceTimeline from '@/components/DataMaintenanceTimeline'
import AnchorNavigation from '@/components/AnchorNavigation'
import CollapsibleDetailRight from '@/components/CollapsibleDetailRight'

export default {
  name: "ScreenDetail",
  dicts: ['screen_display_type', 'location_floor_level', 'sys_common_status', 'screen_type', 'screen_nature', 'screen_location_type', 'result_flag', 'key_area_type', 'special_cycle', 'site_business_status', 'site_type', 'certificate_type', 'screen_control_sys_way', 'grade_protection_level', 'screen_control_sys_status', 'sys_yes_no', 'exception_type'],
  components: {
    CollapsibleDetailRight,
    ImageViewer,
    DataMaintenanceTimeline,
    AnchorNavigation
  },
  data() {
    return {
      loading: false,
      // 表单参数
      form: {},

      // 模块折叠状态
      sectionCollapsed: {
        basic: false,
        enterprise: false,
        relation: false,
        keyarea: false,
        special: false,
        site: false,
        netsystem: false,
        inspection: false,
        taskResult: false
      },

      // 检查任务列表相关数据
      taskLoading: false,
      taskTotal: 0,
      taskResultList: [],
      taskDateRange: [],
      taskQueryParams: {
        pageNum: 1,
        pageSize: 10,
        taskName: null,
        resultFlag: null,
        screenId: null
      },

      // 区域和派出所列表
      areaList: [],
      policeList: [],

      // 锚点导航配置
      anchorItems: [
        { id: 'basic-info', label: '基本信息' },
        { id: 'enterprise-info', label: '责任单位信息' },
        { id: 'task-list', label: '检查任务列表', count: 0 }
      ]
    };
  },
  created() {
    const screenId = this.$route.params && this.$route.params.id;
    this.getScreen(screenId);
    this.getAreaList();
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    
    // 处理编辑
    handleEdit() {
      this.$router.push(`/data-management/screen/edit/${this.form.screenId}`);
    },
    
    /** 查询电子屏信息详细 */
    getScreen(screenId) {
      this.loading = true;
      getScreen(screenId).then(response => {
        this.form = response.data;
        this.loading = false;

        console.log('response.data--getScreen--detail--', response.data)
        // 设置检查任务查询参数中的screenId
        this.taskQueryParams.screenId = screenId;
        // 加载检查任务列表
        this.getTaskList();

        // 如果有区域代码，获取对应的派出所列表
        if (this.form.areaCode) {
          this.getPoliceList();
        }
      });
    },

    // 切换模块折叠状态
    toggleSection(section) {
      this.sectionCollapsed[section] = !this.sectionCollapsed[section];
    },

    /** 查询检查任务结果列表 */
    getTaskList() {
      this.taskLoading = true;
      // 模拟API调用，实际项目中需要替换为真实的API
      listTaskResult(this.addDateRange(this.taskQueryParams, this.taskDateRange)).then(response => {
        this.taskResultList = response.rows;
        this.taskTotal = response.total;
        this.taskLoading = false;
        // 更新锚点中的任务数量
        this.updateAnchorTaskCount();
      });


    },

    /** 搜索按钮操作 */
    handleTaskQuery() {
      this.taskQueryParams.pageNum = 1;
      this.getTaskList();
    },

    /** 重置按钮操作 */
    resetTaskQuery() {
      this.taskQueryParams = {
        pageNum: 1,
        pageSize: 10,
        taskName: null,
        resultFlag: null,
        screenId: this.taskQueryParams.screenId
      };
      this.taskDateRange = [];
      this.getTaskList();
    },

    /** 任务详情点击处理 */
    handleTaskDetail() {
      this.$message.info('任务详情功能开发中...');
    },

    // 获取区域列表
    getAreaList() {
      let params = {
        parentId: '110000'
      }
      listArea(params).then(response => {
        this.areaList = response.data;
        // 如果有区域代码，获取对应的派出所列表
        if (this.form.areaCode) {
          this.getPoliceList();
        }
      });
    },

    // 获取派出所列表
    getPoliceList() {
      if (!this.form.areaCode) {
        this.policeList = [];
        return;
      }
      let params = {
        parentId: this.form.areaCode
      }
      listArea(params).then(response => {
        this.policeList = response.data;
      });
    },

    // 获取区域名称
    getAreaName(areaCode) {
      const area = this.areaList.find(item => item.areaCode === areaCode);
      return area ? area.areaName : '';
    },

    // 获取派出所名称
    getPoliceName(policeCode) {
      const police = this.policeList.find(item => item.areaCode === policeCode);
      return police ? police.areaName : '';
    },

    /** 日期范围处理工具方法 */
    addDateRange(params, dateRange, propName) {
      let search = params;
      search.params = typeof(search.params) === 'object' && search.params !== null && !Array.isArray(search.params) ? search.params : {};
      dateRange = Array.isArray(dateRange) ? dateRange : [];
      if (typeof(propName) === 'undefined') {
        search.params['beginTime'] = dateRange[0];
        search.params['endTime'] = dateRange[1];
      } else {
        search.params['begin' + propName] = dateRange[0];
        search.params['end' + propName] = dateRange[1];
      }
      return search;
    },

    // 处理锚点变化
    handleAnchorChange(anchorId) {
      console.log('当前激活的锚点:', anchorId);
    },

    // 更新锚点项目中的任务数量
    updateAnchorTaskCount() {
      const taskAnchor = this.anchorItems.find(item => item.id === 'task-list');
      if (taskAnchor) {
        taskAnchor.count = this.taskTotal;
      }
    },
    // 处理右侧面板收起展开
    handleRightCollapseChange(isCollapsed) {
      this.isRightCollapsed = isCollapsed;
  }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
width: calc(100% - 380px);
  .page-header {
    width: 99%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      justify-content: space-between;
      .page-title {
        margin: 0;
        font-size: 18px;
        color: #303133;
      }
    }
  }
  .detail-container {
    width: 99%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;

   

    .detail-section {
      margin-bottom: 24px;

      .section-header {
        background-color: #f6f7fb;
        padding: 12px 16px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 4px;
        margin-bottom: 16px;

        .section-title {
          display: flex;
          align-items: center;
          gap: 8px;

          .title-line {
            width: 3px;
            height: 16px;
            background-color: #4584FF;
          }

          .title-text {
            font-size: 16px;
            font-weight: 600;
            color: #4584FF;
          }
        }

        .section-toggle {
          display: flex;
          align-items: center;
          gap: 4px;
          color: #4584FF;
          font-size: 14px;

          .toggle-text {
            font-size: 14px;
          }

          i {
            font-size: 12px;
          }
        }

        &:hover {
          background-color: #f0f2f5;
        }
      }

      .section-content {
        padding: 0 16px;
      }
    }

    .keyarea-tag {
      border-color: #4584FF;
      color: #4584FF;
      background-color: #E9F1FF;
    }

    .special-tag {
      border-color: #19BB77;
      color: #19BB77;
      background-color: #EAFEF6;
    }

    .search-container {
      background-color: #f8f9fa;
      padding: 16px;
      border-radius: 4px;
      margin-bottom: 16px;
      border: 1px solid #e9ecef;
    }
  }
}

.detail-right {
  width: 360px;
  flex-shrink: 0;
  background-color: #f5f7fa;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}
</style>
