
<!-- -->
<template>
    <div class="screenmap-container" >
        <div class="map-controls">
            <div class="tabs-container">
                <div class="map-tabs">
                    <!-- 隐藏全部按钮 -->
                    <!-- <div
                        class="tab-button"
                        :class="{ active: isAllSelected }"
                        @click="handleTabClick('all')"
                    >
                        全部 <span>({{getAllBadgeCount()}})</span>
                    </div> -->
                    <div
                        class="tab-button"
                        :class="{ active: selectedTabs.includes('screen') }"
                        @click="handleTabClick('screen')"
                    >
                        <img src="@/assets/map/screen.svg" class="tab-icon">
                        电子屏<span v-if="loadingStates.screenNum"><i class="el-icon-loading"></i></span><span v-else>({{screenStatistic.screenNum || 0}})</span>
                    </div>
                    <div
                        class="tab-button"
                        :class="{ active: selectedTabs.includes('netbar') }"
                        @click="handleTabClick('netbar')"
                    >
                        <img src="@/assets/map/netbar.svg" class="tab-icon">
                        网吧<span v-if="loadingStates.netbarNum"><i class="el-icon-loading"></i></span><span v-else>({{screenStatistic.netbarNum || 0}})</span>
                    </div>
                    <div
                        class="tab-button"
                        :class="{ active: selectedTabs.includes('wifi') }"
                        @click="handleTabClick('wifi')"
                    >
                        <img src="@/assets/map/wifi.svg" class="tab-icon">
                        非经营<span v-if="loadingStates.wifiNum"><i class="el-icon-loading"></i></span><span v-else>({{screenStatistic.wifiNum || 0}})</span>
                    </div>

                    <!-- 新增的五个业务类型按钮 -->
                    <div
                        class="tab-button"
                        :class="{ active: selectedTabs.includes('levelprotect') }"
                        @click="handleTabClick('levelprotect')"
                    >
                        <img src="@/assets/map/levelProject.svg" class="tab-icon">
                        等保备案<span v-if="loadingStates.levelprotectNum"><i class="el-icon-loading"></i></span><span v-else>({{businessStatistic.levelprotectNum || 0}})</span>
                    </div>
                    <div
                        class="tab-button"
                        :class="{ active: selectedTabs.includes('website') }"
                        @click="handleTabClick('website')"
                    >
                        <img src="@/assets/map/website.svg" class="tab-icon">
                        网站备案<span v-if="loadingStates.websiteNum"><i class="el-icon-loading"></i></span><span v-else>({{businessStatistic.websiteNum || 0}})</span>
                    </div>
                    <div
                        class="tab-button"
                        :class="{ active: selectedTabs.includes('app') }"
                        @click="handleTabClick('app')"
                    >
                        <img src="@/assets/map/app.svg" class="tab-icon">
                        APP<span v-if="loadingStates.appNum"><i class="el-icon-loading"></i></span><span v-else>({{businessStatistic.appNum || 0}})</span>
                    </div>
                    <div
                        class="tab-button"
                        :class="{ active: selectedTabs.includes('applet') }"
                        @click="handleTabClick('applet')"
                    >
                        <img src="@/assets/map/applet.svg" class="tab-icon">
                        小程序<span v-if="loadingStates.appletNum"><i class="el-icon-loading"></i></span><span v-else>({{businessStatistic.appletNum || 0}})</span>
                    </div>
                    <div
                        class="tab-button"
                        :class="{ active: selectedTabs.includes('operator') }"
                        @click="handleTabClick('operator')"
                    >
                        <img src="@/assets/map/operator.svg" class="tab-icon">
                        运营商<span v-if="loadingStates.operatorNum"><i class="el-icon-loading"></i></span><span v-else>({{businessStatistic.operatorNum || 0}})</span>
                    </div>
                </div>
            </div>

            <!-- <div class="search-toggle">
                <div class="map-service-info">
                    <span style="color: #666; font-size: 12px;">使用百度地图WebGL服务</span>
                </div>
                <el-button type="primary" size="small" @click="showSearchClick" class="toggle-btn">
                    <i :class="isMapSearchShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                    {{ isMapSearchShow ? '隐藏' : '更多' }}
                </el-button>
            </div> -->
        </div>


        <!-- <div class="search-form" v-show="isMapSearchShow">
            <el-form :model="queryMapParams" ref="queryMapForm" size="small" :inline="true" class="map-search-form">
                <el-form-item label="名称/地址" prop="keyword">
                    <el-input
                        v-model="queryMapParams.params.keyword"
                        :placeholder="getSearchPlaceholder()"
                        clearable
                        @keyup.enter.native="handleQuery"
                        style="width: 200px;"
                    />
                </el-form-item>
                <el-form-item label="区域" prop="areaCode">
                    <el-select v-model="queryMapParams.areaCode" filterable placeholder="请选择区域" clearable style="width: 150px;">
                        <el-option
                            v-for="item in userAreaList"
                            :key="item.areaCode"
                            :label="item.areaName"
                            :value="item.areaCode"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="责任单位" prop="enterpriseId" v-if="activeName !== 'tabNetbar' && activeName !== 'tabWifi'">
                    <el-autocomplete
                        style="width: 190px;"
                        v-model="queryMapParams.params.enterpriseName"
                        :fetch-suggestions="querySearchEnterprise"
                        placeholder="请输入责任单位名称"
                        @select="handleSelectEnterprise"
                    >
                        <template slot-scope="{ item }">
                            <span>{{ item.enterpriseName }}</span>
                        </template>
                    </el-autocomplete>
                </el-form-item>
                <el-form-item label="重点区域" prop="keyareaId" v-if="activeName === 'tabImportant' || activeName === 'tabAllData'">
                    <el-select v-model="queryMapParams.keyareaId" filterable placeholder="请选择重点区域" clearable style="width: 150px;">
                        <el-option
                            v-for="item in keyareaList"
                            :key="item.keyareaId"
                            :label="item.keyareaName"
                            :value="item.keyareaId"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="专项" prop="specialId" v-if="activeName === 'tabSpecial' || activeName === 'tabAllData'">
                    <el-select v-model="queryMapParams.specialId" filterable placeholder="请选择专项" clearable style="width: 150px;">
                        <el-option
                            v-for="item in specialList"
                            :key="item.specialId"
                            :label="item.specialName"
                            :value="item.specialId"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>
        </div> -->

        <div class="map-container">
            <div id="divMap" class="map-content"></div>
        </div>
    </div>
</template>

<!-- <script type="text/javascript" src="../../../public/static/map_load.js"></script> -->
 
<script>
import { listEnterprise } from "@/api/screen/enterprise";
import { getDataStatistics } from "@/api/data-management";
import request from '@/utils/request';

// import {SearchMap} from './searchform.vue' // 暂时移除
import {searchScreen} from "@/api/screen/screenMap";
// SVG图标通过代码生成，不需要导入文件
import { listArea, listUserArea } from '@/api/system/area';
import { listKeyarea } from "@/api/screen/keyarea";
import { listSpecial } from "@/api/screen/special";
import { getSiteBaseList } from "@/api/netbar/siteBase";
import { poiList } from "@/api/wifi/site";

export default ({
  name: "screenMap",
  // components:{SearchMap}, // 暂时移除
  data() {
    return {
        zoom: 12, // 缩放图层
        center: [116.4, 39.9],
        activeName:'tabAllData',

        // 多选逻辑
        selectedTabs: ['screen'], // 当前选中的tab数组，默认选中电子屏
        isAllSelected: false, // 全部按钮是否选中

        isMapSearchShow: false, // 是否显示地图搜索
        mapInstance: null, // 地图实例
        loading:false, //
        isFristLoad: false, // 是否第一次页面加载
        // map: null,

        // 查询参数
        queryMapParams: {
            params: {
                keyword: null,          // 电子屏名称/地址关键词
                enterpriseName: null,   // 责任单位名称
                enterpriseId:null,      // 责任单位Id
                keyareaFlag: null,      // 重点区域
                keyareaId: null,        // 重点区域ID
                specialFlag: null,      // 专项
                specialId: null,        // 专项ID

            },
            areaCode: null,        // 区域
            enterpriseId: null,    // 责任单位
            enterpriseName: null,
            siteCode: '',        // 场所编码
            keyareaId: '',       // 重点区域
            specialId: '',       // 专项
            netsystemId: '',     // 联网系统ID
            accessUrl: null,       //
            status: null,          // 电子屏状态
        },
        // 派出所列表
        userAreaList: [],          //
        policeList: [],            // 派出所列表
        keyareaList: [],           // 重点区域列表
        specialList: [],           // 专项列表
        //
        screenList:[], // 电子屏信息列表
        screenStatistic: {
            allNum:0,
            screenNum: 0,     // 电子屏数
            netbarNum: 0,     // 网吧数
            wifiNum: 0,       // 非经营数
        },

        // 业务类型统计数据
        businessStatistic: {
            levelprotectNum: 0,  // 等保备案数
            websiteNum: 0,       // 网站备案数
            appNum: 0,           // APP数
            appletNum: 0,        // 小程序数
            operatorNum: 0,      // 运营商数
        },

        // 加载状态
        loadingStates: {
            screenNum: false,      // 电子屏加载状态
            netbarNum: false,      // 网吧加载状态
            wifiNum: false,        // 非经营加载状态
            levelprotectNum: false, // 等保备案加载状态
            websiteNum: false,     // 网站备案加载状态
            appNum: false,         // APP加载状态
            appletNum: false,      // 小程序加载状态
            operatorNum: false,    // 运营商加载状态
        },

        scrWidth: 1024, // 屏幕宽度
        scrHeight: 768, // 屏幕高度
        mapHeight: 768, // 地图高度

        // 地图相关
        mapInstance: null, // 地图实例
        markerClusterer: null, // 点聚合实例

        // 图标缓存
        iconCache: new Map(), // 缓存生成的图标URL

    };
  },
  mounted() {
    window.addEventListener('resize', this.resizeWidthHeight); // 监听窗口大小变化

    const enterpriseId = this.$route.query.enterpriseId;
    const enterpriseName = this.$route.query.enterpriseName;
    if(enterpriseId && enterpriseName){
      this.queryMapParams.enterpriseId = enterpriseId
      this.queryMapParams.enterpriseName = enterpriseName
      this.queryMapParams.params.enterpriseId = enterpriseId;// 默认选择责任单位
    }

    // 初始化地图
    this.initMap();

    this.isFristLoad=true;

    // 初始化时先调用getDataStatistics接口获取统计数据
    console.log('开始初始化，调用getDataStatistics接口');
    this.getDataStatisticsData();

    // 初始化时加载电子屏数据（默认激活）
    this.loadScreenData();

    this.isFristLoad =false;

    // 将路由跳转方法挂载到window对象上，供信息窗口使用
    window.navigateToDetail = this.navigateToDetail;

    // this.getAreaList();
    // this.getKeyareaList();
    // this.getSpecialList();
    // this.getUserAreaList();

  },
  methods: {
    // 创建SVG图标
    createSvgIcon(type, color) {
      // 检查缓存
      const cacheKey = `${type}-${color}`;
      if (this.iconCache.has(cacheKey)) {
        return this.iconCache.get(cacheKey);
      }

      let svgContent = '';

      if (type === 'screen') {
        // 电子屏SVG内容 - 显示器样式
        svgContent = `
          <svg width="27" height="48" viewBox="0 0 27 48" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                <feDropShadow dx="1" dy="2" stdDeviation="1" flood-color="rgba(0,0,0,0.3)"/>
              </filter>
            </defs>
            <!-- 显示器屏幕 -->
            <rect x="4" y="6" width="19" height="14" rx="2" fill="${color}" filter="url(#shadow)"/>
            <rect x="5.5" y="7.5" width="16" height="11" fill="white" stroke="${color}" stroke-width="0.5"/>
            <!-- 支架 -->
            <rect x="12" y="20" width="3" height="8" fill="${color}"/>
            <!-- 底座 -->
            <ellipse cx="13.5" cy="30" rx="8" ry="3" fill="${color}"/>
            <!-- 定位圆点 -->
            <circle cx="13.5" cy="40" r="6" fill="${color}" filter="url(#shadow)"/>
            <text x="13.5" y="44" text-anchor="middle" fill="white" font-size="9" font-weight="bold">屏</text>
          </svg>
        `;
      } else if (type === 'netbar') {
        // 网吧SVG内容 - 电脑样式
        svgContent = `
          <svg width="27" height="48" viewBox="0 0 27 48" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                <feDropShadow dx="1" dy="2" stdDeviation="1" flood-color="rgba(0,0,0,0.3)"/>
              </filter>
            </defs>
            <!-- 显示器 -->
            <rect x="3" y="4" width="21" height="16" rx="2" fill="${color}" filter="url(#shadow)"/>
            <rect x="4.5" y="5.5" width="18" height="13" fill="white" stroke="${color}" stroke-width="0.5"/>
            <!-- 键盘 -->
            <rect x="5" y="22" width="17" height="6" rx="1" fill="${color}" opacity="0.8"/>
            <!-- 定位圆点 -->
            <circle cx="13.5" cy="38" r="8" fill="${color}" filter="url(#shadow)"/>
            <text x="13.5" y="42" text-anchor="middle" fill="white" font-size="9" font-weight="bold">网</text>
          </svg>
        `;
      }

      // 将SVG转换为Data URL
      const svgBlob = new Blob([svgContent], { type: 'image/svg+xml' });
      const iconUrl = URL.createObjectURL(svgBlob);

      // 缓存图标URL
      this.iconCache.set(cacheKey, iconUrl);

      return iconUrl;
    },

    // 获取搜索框占位符
    getSearchPlaceholder() {
      switch (this.activeName) {
        case 'tabNetbar':
          return '请输入网吧名称/地址';
        case 'tabWifi':
          return '请输入非经营场所名称/地址';
        default:
          return '请输入电子屏名称/地址';
      }
    },

   

    // 检查百度地图API是否已加载
    loadBaiduMapAPI() {
      return new Promise((resolve, reject) => {
        // 检查是否已经加载
        if (window.BMap) {
          console.log('百度地图API已加载');
          resolve();
          return;
        }

        // 等待API加载完成
        let checkCount = 0;
        const maxChecks = 100; // 最多检查10秒
        const checkLoaded = () => {
          checkCount++;
          if (window.BMap) {
            console.log('百度地图API加载完成');
            resolve();
          } else if (checkCount >= maxChecks) {
            console.error('百度地图API加载超时，当前window.BMap:', window.BMap);
            console.error('当前window对象包含的地图相关属性:', Object.keys(window).filter(key => key.includes('Map') || key.includes('map')));
            reject(new Error('地图API加载超时'));
          } else {
            setTimeout(checkLoaded, 100);
          }
        };

        console.log('等待百度地图API加载...');
        checkLoaded();
      });
    },

    // 初始化地图
    async initMap() {
      try {
        // 加载地图API
        await this.loadBaiduMapAPI();

        // 初始化地图尺寸
        this.resizeWidthHeight();

        console.log('地图API加载成功，开始初始化地图');
      } catch (error) {
        console.error('地图初始化失败:', error);
        this.$message.error('地图加载失败，请检查网络连接或切换地图服务');
      }
    },

    /**责任单位搜索 */
    querySearchEnterprise(queryString, cb) {
      if(!queryString){
        cb([]);
      }
      this.queryMapParams.enterpriseId = null;
      let results;
      if(queryString){
        listEnterprise({enterpriseName: queryString}).then(response => {
          results = response.rows;
          cb(results);
        });
      }
    },
    /** 责任单位选择 */
    handleSelectEnterprise(item) {
      this.queryMapParams.enterpriseId = item.enterpriseId;
      this.queryMapParams.enterpriseName = item.enterpriseName;
    },
    /** 窗口大小改变 */
    resizeWidthHeight() {
        //console.log(' resizeWidthHeight=', window.innerHeight);
        this.scrWidth = window.innerWidth;//
        this.scrHeight = window.innerHeight //
        if(this.scrHeight>500 ) {
            //console.log('....')
            if(this.isMapSearchShow) {
                this.mapHeight = this.scrHeight -212;
            }
            else {
                this.mapHeight = this.scrHeight -168;
            }
            // console.log(' mapHeight=', this.mapHeight);
        }
        // map.resize()  :style="{height: mapHeight+'px' } "
        let docDivMap = document.getElementById("divMap");
        if(docDivMap != null) {
            document.getElementById("divMap").style.height= this.mapHeight +'px';
            // // 动态改变 地图div大小
            this.initBaiduMap();//
        }

    },
    /** 地图初始化 */
    initBaiduMap() {
        // 检查地图API是否已加载
        if (!window.BMap) {
          console.warn('百度地图API未加载，跳过地图初始化');
          return;
        }

        try {
          console.log('开始初始化百度地图');

          // 检查地图容器是否存在
          const mapContainer = document.getElementById("divMap");
          if (!mapContainer) {
            console.error('地图容器不存在');
            return;
          }

          // 获取默认坐标点
          const defaultPoint = window.vueConfig?.defaultPoint || {lng: 116.404, lat: 39.915};

          // 如果地图实例已存在，先销毁
          if (this.mapInstance) {
            try {
              this.mapInstance = null;
            } catch (e) {
              console.warn('销毁旧地图实例失败:', e);
            }
          }

          let map = new window.BMap.Map("divMap");
          let point = new window.BMap.Point(defaultPoint.lng, defaultPoint.lat);

          map.centerAndZoom(point, 14);

          // 保存地图实例
          this.mapInstance = map;

          // 添加地图控件
          //添加地图类型控件 地图/卫星
          map.addControl(
              new window.BMap.MapTypeControl({
              mapTypes: [window.BMAP_NORMAL_MAP, window.BMAP_SATELLITE_MAP, window.BMAP_HYBRID_MAP],
              })
          );
          map.addControl(new window.BMap.NavigationControl()); //地图平移缩放控件
          map.addControl(new window.BMap.ScaleControl()); //显示比例尺在右下角

          map.enableScrollWheelZoom(true);                  // 启用滚轮放大缩小。
          map.enableContinuousZoom();  //启用连续缩放效果

          // 添加地图事件监听
          this.addMapEventListeners(map);

          // 绘制数据点位
          this.drawDataPoints(map);

        } catch (error) {
          console.error('地图初始化失败:', error);
        }
    },

    // 添加地图事件监听
    addMapEventListeners(map) {
        setTimeout(function() {
            // console.log('鼠标滚轮缩放')
        }, 1000);

        map.addEventListener("click", function(_e) {
            // console.log('click= ', _e.point.lng + "," + _e.point.lat);
            //map.clearOverlays(); //清空原来的标注
        });
    },

    // 绘制数据点位
    drawDataPoints(map) {
        console.log('=== drawDataPoints 开始 ===');
        console.log('地图实例:', map);
        console.log('screenList数据:', this.screenList);
        console.log('screenList长度:', this.screenList ? this.screenList.length : 0);
        console.log('当前activeName:', this.activeName);
        // // 实例化鼠标绘制工具
        // var drawingManager = new BMapGLLib.DrawingManager(map, {
        //     // isOpen: true, //是否开启绘制模式
        //     enableDrawingTool: true, // 是否显示工具栏
        //     enableCalculate: true, // 绘制是否进行测距(画线时候)、测面(画圆、多边形、矩形) 
        //     enableSorption: true,
        //     drawingToolOptions: {
        //         enableTips: true,
        //         customContainer: 'selectbox_Drawing',
        //         hasCustomStyle: true,
        //         anchor: BMAP_ANCHOR_TOP_LEFT,
        //         offset: new BMapGL.Size(15, 15), // 偏离值
        //         scale: 1, // 工具栏缩放比例
        //         drawingModes: [
        //             BMAP_DRAWING_MARKER,
        //             BMAP_DRAWING_POLYLINE,
        //             BMAP_DRAWING_RECTANGLE,
        //             BMAP_DRAWING_POLYGON,
        //             BMAP_DRAWING_CIRCLE,
        //         ]
        //     },
        //     enableSorption: true, // 是否开启边界吸附功能
        //     sorptionDistance: 20, // 边界吸附距离
        //     enableGpc: true, // 是否开启延边裁剪功能
        //     enableLimit: true,  // 是否开启超限提示
        //     limitOptions: {
        //         area: 50000000, // 面积超限值
        //         distance: 30000
        //     },
        //     circleOptions: styleOptions, // 圆的样式
        //     polylineOptions: styleOptions, // 线的样式
        //     polygonOptions: styleOptions, // 多边形的样式
        //     rectangleOptions: styleOptions, // 矩形的样式
        //     labelOptions: labelOptions // label的样式
        // });




        // 清除现有标记点和聚合
        map.clearOverlays();
        // if (this.markerClusterer) {
        //     this.markerClusterer.clearMarkers();
        // }

        // 根据当前tab类型渲染不同的数据点位
        if(this.screenList != null && this.screenList.length > 0) {
            let markers = [];

            this.screenList.forEach(item => {
                let pt, type;

                if (this.activeName === 'tabNetbar') {
                    // 网吧数据
                    pt = new window.BMap.Point(
                        parseFloat(item.longitude),
                        parseFloat(item.latitude)
                    );
                    type = 'netbar';
                } else if (this.activeName === 'tabWifi') {
                    // 非经营数据
                    pt = new window.BMap.Point(
                        parseFloat(item.xpoint),
                        parseFloat(item.ypoint)
                    );
                    type = 'wifi';
                } else if (this.activeName === 'tabAllData' || this.activeName === 'tabMultiple') {
                    // 全部数据 - 根据数据字段判断类型
                    console.log('全部数据项:', item);
                    console.log('字段检查 - netsystemId:', item.netsystemId, 'siteCode:', item.siteCode, 'serviceCode:', item.serviceCode);

                    // 使用netsystemId字段精确识别电子屏数据
                    if (item.hasOwnProperty('netsystemId')) {
                        // 电子屏数据（有netsystemId字段）
                        pt = new window.BMap.Point(
                            parseFloat(item.longitude),
                            parseFloat(item.latitude)
                        );
                        type = 'screen';
                        console.log('识别为电子屏数据');
                    } else if (item.hasOwnProperty('siteCode') && item.siteCode) {
                        // 网吧数据（有siteCode字段且没有netsystemId）
                        pt = new window.BMap.Point(
                            parseFloat(item.longitude),
                            parseFloat(item.latitude)
                        );
                        type = 'netbar';
                        console.log('识别为网吧数据');
                    } else if (item.hasOwnProperty('serviceCode') && item.serviceCode) {
                        // 非经营数据（有serviceCode字段且没有netsystemId）
                        pt = new window.BMap.Point(
                            parseFloat(item.xpoint),
                            parseFloat(item.ypoint)
                        );
                        type = 'wifi';
                        console.log('识别为非经营数据');
                    } else {
                        // 其他数据，默认按电子屏处理
                        pt = new window.BMap.Point(
                            parseFloat(item.longitude),
                            parseFloat(item.latitude)
                        );
                        type = 'screen';
                        console.log('识别为其他数据，按电子屏处理');
                    }
                } else {
                    // 电子屏数据（电子屏tab）
                    pt = new window.BMap.Point(
                        parseFloat(item.longitude),
                        parseFloat(item.latitude)
                    );
                    type = 'screen';
                }

                if (pt && !isNaN(pt.lng) && !isNaN(pt.lat)) {
                    let marker = this.createMarker(pt, item, type);
                    markers.push(marker);
                }
            });

            // 暂时禁用聚合功能，直接添加标记点
            if (markers.length > 0) {
                console.log(`准备添加 ${markers.length} 个标记点到地图`);
                markers.forEach(marker => {
                    if (marker) {
                        map.addOverlay(marker);
                    }
                });
                console.log(`已成功添加 ${markers.length} 个标记点到地图`);
            }

            // 自动调整地图视野以包含所有标记点
            this.adjustMapView(map);
        }

    },

    // 创建标记点
    createMarker(pointV, itemInfo, type = 'screen') {
        var myIcon = null;
        let iconUrl = '';

        if (type === 'netbar') {
            // 网吧标记点 - 使用PNG图标
            iconUrl = require('@/assets/map/netbar.png');
            myIcon = new window.BMap.Icon(iconUrl, new window.BMap.Size(28, 39), {
                anchor: new window.BMap.Size(14, 39),
                imageSize: new window.BMap.Size(28, 39)
            });
        } else if (type === 'wifi') {
            // 非经营标记点 - 使用PNG图标，原图尺寸28x39
            iconUrl = require('@/assets/map/wifi.png');
            myIcon = new window.BMap.Icon(iconUrl, new window.BMap.Size(28, 39), {
                anchor: new window.BMap.Size(14, 39),
                imageSize: new window.BMap.Size(28, 39)
            });
        } else {
            // 电子屏标记点 - 使用PNG图标，原图尺寸28x39
            iconUrl = require('@/assets/map/screen.png');
            myIcon = new window.BMap.Icon(iconUrl, new window.BMap.Size(28, 39), {
                anchor: new window.BMap.Size(14, 39),
                imageSize: new window.BMap.Size(28, 39)
            });
        }

        var marker = new window.BMap.Marker(pointV, {icon: myIcon});

        // 创建信息窗口内容
        let infoHtml = '';
        let opts = {
            width: 390,
            height: 150
        };

        if (type === 'netbar') {
            // 网吧信息窗口 - 新的详细设计
            opts = {
                width: 540,
                height: 480,
                title: ''
            };

            // 构建详细的网吧信息
            infoHtml = this.createNetbarInfoWindow(itemInfo);
        } else if (type === 'wifi') {
            // 非经营信息窗口 - 新的详细设计
            opts = {
                width: 480,
                height: 380,
                title: ''
            };

            // 构建详细的非经营信息
            infoHtml = this.createWifiInfoWindow(itemInfo);
        } else {
            // 电子屏信息窗口 - 新的详细设计
            opts = {
                width: 400,
                height: 400,
                title: ''
            };

            // 构建详细的电子屏信息
            infoHtml = this.createScreenInfoWindow(itemInfo);
        }

        var infoWindow = new window.BMap.InfoWindow(infoHtml, opts);

        // 点标记添加点击事件
        marker.addEventListener('click', () => {
            this.mapInstance.openInfoWindow(infoWindow, pointV);
        });

        return marker;
    },

    // 创建电子屏详细信息窗口
    createScreenInfoWindow(itemInfo) {
        // 处理联网状态
        const isConnected = itemInfo.netsystemId !== '0';
        const connectionStatus = isConnected ? '已联网' : '未联网';

        // 处理检查状态颜色
        const checkStatusColor = itemInfo.checkStatus === '正常' ? '#09c81a' : '#666';

        // 处理是否下发风险提示单
        const riskNoticeColor = itemInfo.isRiskNotice === '是' ? '#f24016' : '#000000a5';

        // 处理专项标签
        const specialTag = itemInfo.specialIds !== '0' && itemInfo.specialName ?
            `<span style="display: inline-block; padding: 2px 8px; border: 1px solid #09c81a; color: #09c81a; background: #d6ffda; border-radius: 4px; font-size: 12px;">${itemInfo.specialName}</span>` : '';

        // 处理重点区域标签
        const keyareaTag = itemInfo.keyareaIds !== '0' && itemInfo.keyareaName ?
            `<span style="display: inline-block; padding: 2px 8px; border: 1px solid #4584ff; color: #4584ff; background: #4584ff1f; border-radius: 4px; font-size: 12px;">${itemInfo.keyareaName}</span>` : '';

        // 处理电子屏照片（可能有多张）
        let photoHtml = '<span style="font-size:12px;color: #999;">暂无照片</span>';
        if (itemInfo.images && itemInfo.images.length > 0) {
            const imageUrls = itemInfo.images.split(',').filter(img => img.trim());
            if (imageUrls.length > 0) {
                photoHtml = imageUrls.slice(0, 3).map(img => {
                    const imageUrl = img.includes('http') ? img : `${process.env.VUE_APP_BASE_API}/file/download/${img.trim()}`;
                    return `<img src="${imageUrl}" style="width: 50px; height: 35px; object-fit: cover; border-radius: 4px; cursor: pointer; margin-right: 4px;" onclick="window.open('${imageUrl}', '_blank')">`;
                }).join('');
                if (imageUrls.length > 3) {
                    photoHtml += `<span style="color: #666; font-size: 12px;">等${imageUrls.length}张</span>`;
                }
            }
        }

        return `
            <div style="
                background: white;
                border-radius: 8px;
                padding: 16px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                max-height: 440px;
                overflow-y: auto;
                line-height: 1.5;
            ">
                <!-- 第一行：标题和状态 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px; border-bottom: 1px solid #f0f0f0; padding-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style="font-size: 16px; font-weight: bold; color: #333;">${itemInfo.screenName || '未命名电子屏'}</span>
                        <span style="display: inline-block; margin-left: 8px; padding: 2px 8px; border: 1px solid #09c81a; color: #09c81a; background: #d6ffda; border-radius: 4px; font-size: 12px;">${connectionStatus}</span>
                    </div>
                    <a href="javascript:void(0)" onclick="window.parent.navigateToDetail('screen', '${itemInfo.screenId}')" style="color: #4584ff; text-decoration: none; font-size: 14px;">查看详情 ></a>
                </div>

                <!-- 第二行：位置和面积 -->
                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style="color: #666; font-size: 14px;">所在位置：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.address || '未知'}</span>
                    </div>
                    <div style="min-width: 120px; text-align: right;">
                        <span style="color: #666; font-size: 14px;">屏幕面积(㎡)：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.screenArea || itemInfo.size || '未知'} ㎡</span>
                    </div>
                </div>

                <!-- 第三行：区域和内容类型 -->
                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style="color: #666; font-size: 14px;">电子屏所在区域：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.screenLocation || itemInfo.screenNature || '未知'}</span>
                    </div>
                    <div style="min-width: 120px; text-align: right;">
                        <span style="color: #666; font-size: 14px;">显示内容类型：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.contentType || '文字'}</span>
                    </div>
                </div>

                <!-- 第四行：楼层和检查状态 -->
                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style="color: #666; font-size: 14px;">所在楼层：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.floor || '未知'}</span>
                    </div>
                    <div style="min-width: 120px; text-align: right;">
                        <span style="color: #666; font-size: 14px;">最近检查状态：</span>
                        <span style="color: ${checkStatusColor}; font-size: 14px;">${itemInfo.checkStatus || '未知'}</span>
                    </div>
                </div>

                <!-- 第五行：材质和专项 -->
                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style="color: #666; font-size: 14px;">电子屏材质：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.screenMaterial || itemInfo.screenType || '未知'}</span>
                    </div>
                    <div style="min-width: 120px; text-align: right;">
                        <span style="color: #666; font-size: 14px;">所属专项：</span>
                        ${specialTag || '<span style="color: #999; font-size: 14px;">无</span>'}
                    </div>
                </div>

                <!-- 第六行：重点区域和风险提示单 -->
                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style="color: #666; font-size: 14px;">所属重点区域：</span>
                        ${keyareaTag || '<span style="color: #999; font-size: 14px;">无</span>'}
                    </div>
                    <div style="min-width: 120px; text-align: right;">
                        <span style="color: #666; font-size: 14px;">是否下发风险提示单：</span>
                        <span style="color: ${riskNoticeColor}; font-size: 14px;">${itemInfo.isRiskNotice || '否'}</span>
                    </div>
                </div>

                <!-- 第七行：使用性质和归属场所 -->
                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style="color: #666; font-size: 14px;">使用性质：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.useNature || '商用'}</span>
                    </div>
                    <div style="min-width: 120px; text-align: right;">
                        <span style="color: #666; font-size: 14px;">是否归属场所：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.belongToSite || '是'}</span>
                    </div>
                </div>

                <!-- 第八行：照片和经纬度 -->
                <div style="display: flex; justify-content: space-between; margin-bottom: 0;">
                    <div style="flex: 1;">
                        <span style="color: #666; font-size: 14px;">电子屏照片：</span>
                        ${photoHtml}
                    </div>
                    <div style="min-width: 120px; text-align: right;">
                        <span style="color: #666; font-size: 14px;">经纬度：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.longitude || '0'},${itemInfo.latitude || '0'}</span>
                    </div>
                </div>
            </div>
        `;
    },

    // 创建网吧详细信息窗口
    createNetbarInfoWindow(itemInfo) {
        // 处理营业状态标签
        const businessStatusTag = itemInfo.businessStatus === '营业' || itemInfo.businessStatusLabel === '营业' ?
            `<span style="display: inline-block; padding: 2px 8px; border: 1px solid #09c81a; color: #09c81a; background: #d6ffda; border-radius: 4px; font-size: 12px;">营业</span>` :
            `<span style="display: inline-block; padding: 2px 8px; border: 1px solid #666; color: #666; background: #f5f5f5; border-radius: 4px; font-size: 12px;">${itemInfo.businessStatusLabel || '未知'}</span>`;

        // 处理接入方式标签
        const accessMethodTag = itemInfo.accessMethod ?
            `<span style="display: inline-block; padding: 2px 8px; border: 1px solid #1890ff; color: #1890ff; background: #e8f4ff; border-radius: 4px; font-size: 12px;">${itemInfo.accessMethod}</span>` : '';

        return `
            <div style="
                background: white;
                border-radius: 8px;
                padding: 16px;
                color: #333;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                max-height: 440px;
                overflow-y: auto;
                line-height: 1.5;
            ">
                <!-- 第一行：场所编码和查看详情 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px; border-bottom: 1px solid #f0f0f0; padding-bottom: 8px;">
                    <div style="flex: 1;font-size: 16px; font-weight: bold; color: #333;">
                        <span >场所编码：</span>
                        <span>${itemInfo.siteCode || '未知'}</span>
                    </div>
                    <a href="javascript:void(0)" onclick="window.parent.navigateToDetail('netbar', '${itemInfo.siteCode}')" style="color: #4584ff; text-decoration: none; font-size: 14px;">查看详情 ></a>
                </div>

                <!-- 第二行：场所名称和统一社会信用代码 -->
                <div style="display: flex;color: #333; justify-content: space-between; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style=" font-size: 14px;">场所名称：</span>
                        <span style="font-size: 14px;">${itemInfo.siteName || itemInfo.name || '未知'}</span>
                    </div>
                    <div style="min-width: 200px; text-align: right;">
                        <span style=" font-size: 14px;"><span style="color: red;">*</span>统一社会信用代码：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.creditCode || itemInfo.unifiedSocialCreditCode || '未知'}</span>
                    </div>
                </div>

                <!-- 第三行：所属区域和所属派出所 -->
                <div style="display: flex; color: #333; justify-content: space-between; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style="font-size: 14px;"><span style="color: red;">*</span>所属区域：</span>
                        <span style=" font-size: 14px;">${itemInfo.areaCodeLabel || itemInfo.areaName || '未知'}</span>
                    </div>
                    <div style="min-width: 150px; text-align: right;">
                        <span style="font-size: 14px;"><span style="color: red;">*</span>所属派出所：</span>
                        <span style="font-size: 14px;">${itemInfo.policeName || itemInfo.policeStation || '未知'}</span>
                    </div>
                </div>

                <!-- 第四行：营业状态和临停截止日期 -->
                <div style="display: flex; color: #333;justify-content: space-between; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style="; font-size: 14px;">营业状态：</span>
                        ${businessStatusTag}
                    </div>
                    <div style="min-width: 150px;color: #333; text-align: right;">
                        <span style="font-size: 14px;">临停截止日期：</span>
                        <span style=" font-size: 14px;">${itemInfo.tempStopEndDate || '无'}</span>
                    </div>
                </div>

                <!-- 第五行：场所地址 -->
                <div style="margin-bottom: 8px;color: #333;">
                    <span style=" font-size: 14px;"><span style="color: red;">*</span>场所地址：</span>
                    <span style=" font-size: 14px;">${itemInfo.address || itemInfo.siteAddress || '未知'}</span>
                </div>

                <!-- 第六行：审计编号和许可证编号 -->
                <div style="display: flex;color: #333; justify-content: space-between; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style=" font-size: 14px;">审计编号：</span>
                        <span style=" font-size: 14px;">${itemInfo.auditNumber || '未知'}</span>
                    </div>
                    <div style="min-width: 150px; color: #333;text-align: right;">
                        <span style=" font-size: 14px;">许可证编号：</span>
                        <span style=" font-size: 14px;">${itemInfo.licenseNumber || '未知'}</span>
                    </div>
                </div>

                <!-- 第七行：备案终端数和实际终端数 -->
                <div style="display: flex; color: #333;justify-content: space-between; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style=" font-size: 14px;"><span style="color: red;">*</span>备案终端数：</span>
                        <span style=" font-size: 14px;">${itemInfo.registeredTerminals || itemInfo.terminalCount || '0'}</span>
                    </div>
                    <div style="min-width: 150px;color: #333; text-align: right;">
                        <span style=" font-size: 14px;">实际终端数：</span>
                        <span style=" font-size: 14px;">${itemInfo.actualTerminals || itemInfo.realTerminalCount || '0'}</span>
                    </div>
                </div>

                <!-- 第八行：计费系统和计费系统版本 -->
                <div style="color: #333;display: flex; justify-content: space-between; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style="font-size: 14px;">计费系统：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.billingSystem || '未知'}</span>
                    </div>
                    <div style="min-width: 150px; color: #333;text-align: right;">
                        <span style=" font-size: 14px;">计费系统版本：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.billingSystemVersion || '未知'}</span>
                    </div>
                </div>

                <!-- 第九行：是否连锁和接入运营商 -->
                <div style="display: flex;color: #333; justify-content: space-between; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style=" font-size: 14px;"><span style="color: red;">*</span>是否连锁：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.isChain || '否'}</span>
                    </div>
                    <div style="min-width: 150px; text-align: right;">
                        <span style="font-size: 14px;"><span style="color: red;">*</span>接入运营商：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.operator || itemInfo.accessOperator || '未知'}</span>
                    </div>
                </div>

                <!-- 第十行：备案IP起始和结束IP -->
                <div style="display: flex;color: #333; justify-content: space-between; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style=" font-size: 14px;">备案IP起始：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.startIp || '未知'}</span>
                    </div>
                    <div style="min-width: 150px; text-align: right;">
                        <span style=" font-size: 14px;">结束IP：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.endIp || '未知'}</span>
                    </div>
                </div>

                <!-- 第十一行：出口IP起始和截止地址 -->
                <div style="display: flex;color: #333; justify-content: space-between; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style=" font-size: 14px;">出口IP起始地址：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.exitStartIp || '未知'}</span>
                    </div>
                    <div style="min-width: 150px; color: #333;text-align: right;">
                        <span style="font-size: 14px;">出口IP截止地址：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.exitEndIp || '未知'}</span>
                    </div>
                </div>

                <!-- 第十二行：分级分类和安全员数 -->
                <div style="display: flex; color: #333;justify-content: space-between; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style=" font-size: 14px;"><span style="color: red;">*</span>分级分类：</span>
                        <span style=" font-size: 14px;">${itemInfo.classification || itemInfo.gradeClassification || '未知'}</span>
                    </div>
                    <div style="color: #333;min-width: 150px; text-align: right;">
                        <span style=" font-size: 14px;"><span style="color: red;">*</span>安全员数：</span>
                        <span style=" font-size: 14px;">${itemInfo.securityStaffCount || '0'}</span>
                    </div>
                </div>

                <!-- 第十三行：临停备注和营业面积 -->
                <div style="display: flex;color: #333; justify-content: space-between; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style=" font-size: 14px;">临停备注：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.tempStopRemark || '无'}</span>
                    </div>
                    <div style="min-width: 150px; color: #333;text-align: right;">
                        <span style="font-size: 14px;"><span style="color: red;">*</span>营业面积：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.businessArea || '0'} ㎡</span>
                    </div>
                </div>

                <!-- 第十四行：经纬度和接入方式 -->
                <div style="display: flex;color: #333; justify-content: space-between; margin-bottom: 0;">
                    <div style="flex: 1;">
                        <span style=" font-size: 14px;">📍 经度：</span>
                        <span style=" font-size: 14px;">${itemInfo.longitude || '0'}</span>
                        <span style=" font-size: 14px; margin-left: 8px;">纬度：</span>
                        <span style=" font-size: 14px;">${itemInfo.latitude || '0'}</span>
                    </div>
                    <div style="min-width: 150px; color: #333;text-align: right;">
                        <span style=" font-size: 14px;">接入方式：</span>
                        ${accessMethodTag || '<span style="color: #999; font-size: 14px;">未知</span>'}
                    </div>
                </div>
            </div>
        `;
    },

    // 创建非经营详细信息窗口
    createWifiInfoWindow(itemInfo) {
        // 处理关联设备列表
        let equipmentHtml = '<span style="font-size: 12px;color: #999;">无关联设备</span>';
        if (itemInfo.equipmentList && itemInfo.equipmentList.length > 0) {
            equipmentHtml = itemInfo.equipmentList.map(equipment => {
                return `<a href="javascript:void(0)" onclick="window.parent.navigateToDetail('wifi-equipment', '${equipment.equipmentNum}')" style="color: #4584FF; font-size: 12px;text-decoration: none; margin-right: 8px;">${equipment.name || equipment.equipmentName || '设备'}</a>`;
            }).join('、');
        } else if (itemInfo.equipmentNames) {
            // 如果是字符串形式的设备名称
            const equipmentNames = itemInfo.equipmentNames.split('、').filter(name => name.trim());
            equipmentHtml = equipmentNames.map((name, index) => {
                return `<a href="javascript:void(0)" onclick="window.parent.navigateToDetail('wifi-equipment', '1${index + 1}')" style="color: #4584FF;font-size: 12px; text-decoration: none; margin-right: 8px;">${name.trim()}</a>`;
            }).join('、');
        }

        return `
            <div style="
                background: white;
                border-radius: 8px;
                padding: 16px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                max-height: 440px;
                color: #333;
                overflow-y: auto;
                line-height: 1.5;
            ">
                <!-- 第一行：场所名称和查看详情 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px; border-bottom: 1px solid #f0f0f0; padding-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style="font-size: 16px; font-weight: bold; color: #333;">${itemInfo.serviceName || itemInfo.siteName || '--'}</span>
                    </div>
                    <a href="javascript:void(0)" onclick="window.parent.navigateToDetail('wifi', '${itemInfo.serviceCode || itemInfo.siteCode}')" style="color: #4584ff; text-decoration: none; font-size: 14px;">查看详情 ></a>
                </div>

                <!-- 第二行：社采编码和场所性质 -->
                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style=" font-size: 14px;">社采编码：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.socialCollectionCode || itemInfo.serviceCode || '--'}</span>
                    </div>
                    <div style="min-width: 150px;color: #333; text-align: right;">
                        <span style=" font-size: 14px;">场所性质：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.businessNatureName || itemInfo.siteNature || '--'}</span>
                    </div>
                </div>

                <!-- 第三行：场所类型和所属省 -->
                <div style="display: flex; color: #333;justify-content: space-between; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style=" font-size: 14px;">场所类型：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.siteType || itemInfo.businessType || '--'}</span>
                    </div>
                    <div style="min-width: 150px; color: #333;text-align: right;">
                        <span style=" font-size: 14px;">所属省：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.provinceName || '--'}</span>
                    </div>
                </div>

                <!-- 第四行：所属城市和所属区域 -->
                <div style="display: flex;color: #333;justify-content: space-between; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style=" font-size: 14px;">所属城市：</span>
                        <span style="font-size: 14px;">${itemInfo.cityName || '--'}</span>
                    </div>
                    <div style="min-width: 150px; text-align: right;">
                        <span style=" font-size: 14px;">所属区域：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.areaName || itemInfo.districtName || '--'}</span>
                    </div>
                </div>

                <!-- 第五行：所属派出所和地址 -->
                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style=" font-size: 14px;">所属派出所：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.policeName || itemInfo.policeStation || '--'}</span>
                    </div>
                    <div style="min-width: 200px; text-align: right;">
                        <span style="font-size: 14px;">地址：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.address || itemInfo.siteAddress || '--'}</span>
                    </div>
                </div>

                <!-- 第六行：采集类型和场所服务状态代码 -->
                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style=" font-size: 14px;">采集类型：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.collectionType || '--'}</span>
                    </div>
                    <div style="min-width: 200px; text-align: right;">
                        <span style=" font-size: 14px;">场所服务状态代码：</span>
                        <span style="color: #333; font-size: 14px;">${itemInfo.statusName || itemInfo.serviceStatus || '--'}</span>
                    </div>
                </div>

                <!-- 第七行：关联设备 -->
                <div style="margin-bottom: 0;">
                    <span style=" font-size: 14px;">关联设备：</span>
                    <div style="margin-top: 4px;">
                        ${equipmentHtml}
                    </div>
                </div>
            </div>
        `;
    },

    // 初始化点聚合
    // initMarkerClusterer(map, markers) {
    //     // 检查聚合库是否加载
    //     if (window.BMapLib && window.BMapLib.MarkerClusterer) {
    //         // 清除旧的聚合
    //         if (this.markerClusterer) {
    //             this.markerClusterer.clearMarkers();
    //         }

    //         // 创建新的聚合
    //         this.markerClusterer = new window.BMapLib.MarkerClusterer(map, {
    //             markers: markers,
    //             girdSize: 60,
    //             maxZoom: 18,
    //             minClusterSize: 2,
    //             styles: [{
    //                 url: '/static/images/map/cluster.png',
    //                 size: new window.BMap.Size(53, 52),
    //                 opt_anchor: [25, 25],
    //                 textColor: '#fff',
    //                 textSize: 12
    //             }]
    //         });
    //     } else {
    //         // 如果聚合库未加载，直接添加标记点
    //         console.warn('聚合库未加载，直接添加标记点');
    //         markers.forEach(marker => {
    //             map.addOverlay(marker);
    //         });
    //     }
    // },

    // 调整地图视野以包含所有标记点
    adjustMapView(map) {
        if (!this.screenList || this.screenList.length === 0) return;

        let points = [];
        this.screenList.forEach(item => {
            let lng, lat;

            if (this.activeName === 'tabNetbar') {
                lng = parseFloat(item.longitude);
                lat = parseFloat(item.latitude);
            } else if (this.activeName === 'tabWifi') {
                lng = parseFloat(item.xpoint);
                lat = parseFloat(item.ypoint);
            } else {
                lng = parseFloat(item.longitude);
                lat = parseFloat(item.latitude);
            }

            if (!isNaN(lng) && !isNaN(lat)) {
                points.push(new window.BMap.Point(lng, lat));
            }
        });

        if (points.length > 0) {
            if (points.length === 1) {
                // 只有一个点时，直接定位到该点
                map.centerAndZoom(points[0], 15);
            } else {
                // 多个点时，调整视野包含所有点
                map.setViewport(points);
            }
        }
    },

    /** 搜索 */
    handleQuery() {
        // this.queryMapParams.pageNum = 1;
        let scrStatistic={
                allNum: 0,
                screenNum: 0,     // 电子屏数
                netbarNum: 0,     // 网吧数
                wifiNum: 0,       // 非经营数
            };

        // 根据不同的tab调用不同的API
        if (this.activeName === 'tabAllData') {
            // 全部tab需要合并三种数据
            this.handleAllDataQuery(scrStatistic);
            return scrStatistic;
        } else if (this.activeName === 'tabNetbar') {
            this.handleNetbarQuery(scrStatistic);
            return scrStatistic;
        } else if (this.activeName === 'tabWifi') {
            this.handleWifiQuery(scrStatistic);
            return scrStatistic;
        }

        // 电子屏tab的查询逻辑
        if (this.activeName === 'tabScreen') {
            searchScreen(this.queryMapParams).then(response => {
                this.loading = true;

                if(response.code === 200 || response.code === '200') {
                    this.screenList = response.data;
                    scrStatistic.screenNum = this.screenList.length;
                    this.screenStatistic.screenNum = this.screenList.length;
                } else {
                    this.screenList = [];
                }

                // 刷新地图点位布局
                this.resizeWidthHeight();

                // 绘制地图标记点
                if (this.mapInstance && this.screenList && this.screenList.length > 0) {
                    this.drawDataPoints(this.mapInstance);
                } else if (!this.mapInstance) {
                    setTimeout(() => {
                        if (this.mapInstance && this.screenList && this.screenList.length > 0) {
                            this.drawDataPoints(this.mapInstance);
                        }
                    }, 1000);
                }

                this.loading = false;
            }).catch(() => {
                this.loading = false;
                this.resizeWidthHeight();
            });
        }

        return scrStatistic;
    },

    /** 全部数据查询 - 合并三种数据 */
    handleAllDataQuery(scrStatistic) {
        console.log('开始获取全部数据...');

        // 使用Promise.all并行获取所有数据
        const screenPromise = searchScreen(this.queryMapParams);
        const netbarPromise = getSiteBaseList({ pageNum: 1, pageSize: 9999 });
        const wifiPromise = poiList({});

        Promise.all([screenPromise, netbarPromise, wifiPromise]).then(responses => {
            const [screenResponse, netbarResponse, wifiResponse] = responses;
            let allData = [];

            // 1. 处理电子屏数据
            if (screenResponse.code === 200 || screenResponse.code === '200') {
                const screenData = screenResponse.data || [];
                allData = allData.concat(screenData);
                scrStatistic.screenNum = screenData.length;
                this.screenStatistic.screenNum = screenData.length;
            } else {
                console.error('获取电子屏数据失败:', screenResponse);
                scrStatistic.screenNum = 0;
                this.screenStatistic.screenNum = 0;
            }

            // 2. 处理网吧数据
            if (netbarResponse.code === 200) {
                const netbarData = (netbarResponse.data || []).slice(0, 124);
                allData = allData.concat(netbarData);
                scrStatistic.netbarNum = netbarData.length;
                this.screenStatistic.netbarNum = netbarData.length;
            } else {
                console.error('获取网吧数据失败:', netbarResponse);
                scrStatistic.netbarNum = 0;
                this.screenStatistic.netbarNum = 0;
            }

            // 3. 处理非经营数据
            if (wifiResponse.code === 200) {
                const wifiData = wifiResponse.data || [];
                allData = allData.concat(wifiData);
                scrStatistic.wifiNum = wifiData.length;
                this.screenStatistic.wifiNum = wifiData.length;
            } else {
                console.error('获取非经营数据失败:', wifiResponse);
                scrStatistic.wifiNum = 0;
                this.screenStatistic.wifiNum = 0;
            }

            // 4. 设置合并后的数据
            this.screenList = allData;
            scrStatistic.allNum = allData.length;
            this.screenStatistic.allNum = allData.length;


            // 检查数据类型分布
            let screenCount = 0, netbarCount = 0, wifiCount = 0;
            allData.forEach(item => {
                if (item.hasOwnProperty('netsystemId')) {
                    screenCount++;
                } else if (item.hasOwnProperty('siteCode') && item.siteCode) {
                    netbarCount++;
                } else if (item.hasOwnProperty('serviceCode') && item.serviceCode) {
                    wifiCount++;
                }
            });
            // console.log('数据类型分布 - 电子屏:', screenCount, '网吧:', netbarCount, '非经营:', wifiCount);

            // 强制更新统计数据，确保界面显示正确
            this.$forceUpdate();

            // 刷新地图点位布局
            this.resizeWidthHeight();

            // 绘制地图标记点
            if (this.mapInstance && this.screenList && this.screenList.length > 0) {
                this.drawDataPoints(this.mapInstance);
            } else if (!this.mapInstance) {
                setTimeout(() => {
                    if (this.mapInstance && this.screenList && this.screenList.length > 0) {
                        this.drawDataPoints(this.mapInstance);
                    }
                }, 1000);
            }

            this.loading = false;
        }).catch(error => {
            console.error('加载全部数据失败:', error);
            this.loading = false;
            this.resizeWidthHeight();
        });
    },

    /** 网吧查询 */
    handleNetbarQuery(scrStatistic) {
        const params = {
            pageNum: 1,
            pageSize: 9999,
            // siteName: this.queryMapParams.params.keyword,
            // areaCode: this.queryMapParams.areaCode
        };

        getSiteBaseList(params).then(response => {
            this.loading = true;
            if (response.code === 200) {
                // 截取前124条数据，避免重复数据导致的问题
                const allData = response.data || [];
                this.screenList = allData.slice(0, 124);

                // 只更新网吧的统计数据，保持其他数据不变
                this.screenStatistic.netbarNum = this.screenList.length;
            } else {
                this.screenList = [];
            }

            this.resizeWidthHeight();

            // 绘制地图标记点
            if (this.mapInstance && this.screenList && this.screenList.length > 0) {
                this.drawDataPoints(this.mapInstance);
            }

            this.loading = false;
        }).catch(() => {
            this.loading = false;
            this.resizeWidthHeight();
        });
    },

    /** 非经营查询 */
    handleWifiQuery(scrStatistic) {
        const params = {
            pageNum: 1,
            pageSize: 9999,
            serviceName: this.queryMapParams.params.keyword,
            areaCode: this.queryMapParams.areaCode
        };

        poiList(params).then(response => {
            this.loading = true;

            if (response.code === 200) {
                this.screenList = response.data || [];
                // 只更新非经营的统计数据，保持其他数据不变
                this.screenStatistic.wifiNum = this.screenList.length;
            } else {
                this.screenList = [];
            }

            this.resizeWidthHeight();

            // 绘制地图标记点
            if (this.mapInstance && this.screenList && this.screenList.length > 0) {
                this.drawDataPoints(this.mapInstance);
            }

            this.loading = false;
        }).catch(() => {
            this.loading = false;
            this.resizeWidthHeight();
        });
    },

    // 获取网吧统计数据
    getNetbarCount() {
        this.loadingStates.netbarNum = true;
        const params = {
            pageNum: 1,
            pageSize: 5
        };

        getSiteBaseList(params).then(response => {
            if (response.code === 200) {
                this.screenStatistic.netbarNum = response.data.length || 0;
            }
        }).catch(() => {
            this.screenStatistic.netbarNum = 0;
        }).finally(() => {
            this.loadingStates.netbarNum = false;
        });
    },

    // 获取非经营统计数据
    getWifiCount() {
        this.loadingStates.wifiNum = true;

        poiList({}).then(response => {
            if (response.code === 200) {
                this.screenStatistic.wifiNum = response.data.length || 0;
            }
        }).catch(() => {
            this.screenStatistic.wifiNum = 0;
        }).finally(() => {
            this.loadingStates.wifiNum = false;
        });
    },
    /** */
    getAreaList(){
      let params = {
        parentId: '110000'
      }
      listArea(params).then(response => {
        this.areaList = response.data;
      });
    },
    /**  */
    getUserAreaList(){
      listUserArea().then(response => {
        this.userAreaList = response.data;
      });
    },
    /** 所属派出所列表 */
    getPoliceList(){
      let params = {
        parentId: this.form.areaCode
      }
      listArea(params).then(response => {
        this.policeList = response.data;
      });
    },
    /** 重点区域列表 */
    getKeyareaList(){
      let params = {
        pageNum: 1,
        pageSize: 9999,
      }
      listKeyarea(params).then(response => {
        this.keyareaList = response.rows;
      });
    },
    /** 专项列表 */
    getSpecialList(){
      let params = {
        pageNum: 1,
        pageSize: 9999,
      }
      listSpecial(params).then(response => {
        this.specialList = response.rows;
      });
    },
    /** 重置 */
    resetQuery() {
        //
        // this.resetForm("queryMapForm");
        this.queryMapParams.params.keyword=null;
        this.queryMapParams.params.enterpriseName=null;
        this.queryMapParams.params.enterpriseId=null;

        this.queryMapParams.enterpriseName=null;
        this.queryMapParams.enterpriseId=null;
        this.queryMapParams.keyword=null;
        this.queryMapParams.keyareaId=null; // 重点区域
        this.queryMapParams.specialId=null; // 专项
        this.queryMapParams.areaCode =null; // 区域

        //
        this.handleQuery();

    },
    /** 搜索点击显示/隐藏 */
    showSearchClick() {
        if(this.isMapSearchShow) {
            this.isMapSearchShow=false;
        } else {
            this.isMapSearchShow =true;
        }
        this.resizeWidthHeight();
    },
    /** 多选tab点击处理 */
    handleTabClick(tabType) {

        if (tabType === 'all') {
            // 点击全部按钮
            this.isAllSelected = true;
            this.selectedTabs = [];
            this.activeName = 'tabAllData';
            // 加载所有数据
            this.loadAllTabsData();
            // 显示所有业务类型的圆
            setTimeout(() => {
                this.drawBusinessCircles(['levelprotect', 'website', 'app', 'applet', 'operator']);
            }, 500);
        } else {
            // 点击其他按钮
            this.isAllSelected = false;
            this.activeName = 'tabMultiple'; // 设置为多选模式

            // 切换选中状态
            const index = this.selectedTabs.indexOf(tabType);
            if (index > -1) {
                // 如果已选中，则取消选中
                this.selectedTabs.splice(index, 1);
            } else {
                // 如果未选中，则添加选中
                this.selectedTabs.push(tabType);

                // 如果是业务类型按钮，重新获取数据
                if (['levelprotect', 'website', 'app', 'applet', 'operator'].includes(tabType)) {
                    this.getSingleBusinessData(tabType);
                }
            }

            // 根据选中的类型处理数据和显示
            this.handleSelectedTabsChange();
        }
    },



    /** 获取业务类型圆的数据 */
    getBusinessCircleData(businessType) {
        const businessConfig = {
            levelprotect: {
                color: '#11DD63',
                count: this.businessStatistic.levelprotectNum,
                name: '等保备案'
            },
            website: {
                color: '#E6C411',
                count: this.businessStatistic.websiteNum,
                name: '网站备案'
            },
            app: {
                color: '#58CCFF',
                count: this.businessStatistic.appNum,
                name: 'APP'
            },
            applet: {
                color: '#F5BC6C',
                count: this.businessStatistic.appletNum,
                name: '小程序'
            },
            operator: {
                color: '#35B7C8',
                count: this.businessStatistic.operatorNum,
                name: '运营商'
            }
        };

        return businessConfig[businessType] || { color: '#666', count: 0, name: '未知' };
    },

    /** 绘制业务类型彩色圆 */
    drawBusinessCircles(businessTypes = null) {
        if (!this.mapInstance) {
            console.warn('地图实例未初始化');
            return;
        }

        // 清除之前的业务圆形覆盖物
        this.clearBusinessCircles();

        // 如果没有传入类型，使用当前选中的业务类型
        const typesToDraw = businessTypes || this.selectedTabs.filter(type =>
            ['levelprotect', 'website', 'app', 'applet', 'operator'].includes(type)
        );

        if (typesToDraw.length === 0) {
            return;
        }

        // 使用固定位置而不是每次重新计算
        const circlePositions = this.getFixedCirclePositions(typesToDraw.length);

        // 为每个选中的业务类型绘制圆
        typesToDraw.forEach((businessType, index) => {
            const position = circlePositions[index];
            const circleData = this.getBusinessCircleData(businessType);
            this.createBusinessCircle(position, circleData);
        });
    },

    /** 获取固定的圆形位置 */
    getFixedCirclePositions(count) {
        // 获取地图中心点和范围
        const center = this.mapInstance.getCenter();
        const bounds = this.mapInstance.getBounds();
        const sw = bounds.getSouthWest();
        const ne = bounds.getNorthEast();

        const lngRange = (ne.lng - sw.lng) * 0.6; // 使用60%的范围避免太靠边
        const latRange = (ne.lat - sw.lat) * 0.6;

        const positions = [];

        // 预定义的相对位置（相对于中心点的偏移比例）
        const fixedOffsets = [
            { lngOffset: 0.3, latOffset: 0.3 },   // 右上
            { lngOffset: -0.3, latOffset: 0.3 },  // 左上
            { lngOffset: 0.3, latOffset: -0.3 },  // 右下
            { lngOffset: -0.3, latOffset: -0.3 }, // 左下
            { lngOffset: 0, latOffset: 0 }        // 中心
        ];

        for (let i = 0; i < count && i < fixedOffsets.length; i++) {
            const offset = fixedOffsets[i];
            const lng = center.lng + lngRange * offset.lngOffset;
            const lat = center.lat + latRange * offset.latOffset;
            positions.push(new window.BMap.Point(lng, lat));
        }

        return positions;
    },

    /** 创建业务圆形覆盖物 */
    createBusinessCircle(position, circleData) {
        // 创建圆形HTML元素
        const circleHtml = `
            <div style="
                width: 120px;
                height: 120px;
                border-radius: 50%;
                background-color: ${circleData.color};
                border: 4px solid white;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 30px;
                font-weight: bold;
                box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                cursor: pointer;
            " title="${circleData.name}: ${circleData.count}">
                ${circleData.count}
            </div>
        `;

        // 创建自定义覆盖物
        const overlay = new window.BMap.Label(circleHtml, {
            position: position,
            offset: new window.BMap.Size(-60, -60) // 居中偏移（120px的一半）
        });

        overlay.setStyle({
            border: 'none',
            background: 'transparent',
            padding: '0'
        });

        // 添加到地图
        this.mapInstance.addOverlay(overlay);

        // 保存引用以便后续清除
        if (!this.businessCircleOverlays) {
            this.businessCircleOverlays = [];
        }
        this.businessCircleOverlays.push(overlay);
    },

    /** 清除业务圆形覆盖物 */
    clearBusinessCircles() {
        if (this.businessCircleOverlays && this.businessCircleOverlays.length > 0) {
            this.businessCircleOverlays.forEach(overlay => {
                this.mapInstance.removeOverlay(overlay);
            });
            this.businessCircleOverlays = [];
        }
    },

    /** 获取业务类型统计数据 */
    getBusinessStatistics() {
        // 获取等保备案数据
        this.loadingStates.levelprotectNum = true;
        request({
            url: '/levelprotect/levelprotect/list',
            method: 'get',
            params: { pageNum: 1, pageSize: 999 }
        }).then(response => {
            if (response.code === 200) {
                this.businessStatistic.levelprotectNum = response.rows?.length || 0;
            }
        }).catch(error => {
            console.error('获取等保备案数据失败:', error);
        }).finally(() => {
            this.loadingStates.levelprotectNum = false;
        });

        // 获取网站备案数据
        this.loadingStates.websiteNum = true;
        request({
            url: '/website/website/list',
            method: 'get',
            params: { pageNum: 1, pageSize: 999 }
        }).then(response => {
            if (response.code === 200) {
                this.businessStatistic.websiteNum = response.rows?.length || 0;
            }
        }).catch(error => {
            console.error('获取网站备案数据失败:', error);
        }).finally(() => {
            this.loadingStates.websiteNum = false;
        });

        // 获取APP数据
        this.loadingStates.appNum = true;
        request({
            url: '/website/app/list',
            method: 'get',
            params: { pageNum: 1, pageSize: 999 }
        }).then(response => {
            if (response.code === 200) {
                this.businessStatistic.appNum = response.rows?.length || 0;
            }
        }).catch(error => {
            console.error('获取APP数据失败:', error);
        }).finally(() => {
            this.loadingStates.appNum = false;
        });

        // 获取小程序数据
        this.loadingStates.appletNum = true;
        request({
            url: '/website/applet/list',
            method: 'get',
            params: { pageNum: 1, pageSize: 999 }
        }).then(response => {
            if (response.code === 200) {
                this.businessStatistic.appletNum = response.rows?.length || 0;
            }
        }).catch(error => {
            console.error('获取小程序数据失败:', error);
        }).finally(() => {
            this.loadingStates.appletNum = false;
        });

        // 获取运营商数据
        this.loadingStates.operatorNum = true;
        request({
            url: '/operator/operatorInfo/list',
            method: 'get',
            params: { pageNum: 1, pageSize: 999 }
        }).then(response => {
            if (response.code === 200) {
                this.businessStatistic.operatorNum = response.data.rows?.length || 0;
            }
        }).catch(error => {
            console.error('获取运营商数据失败:', error);
        }).finally(() => {
            this.loadingStates.operatorNum = false;
        });
    },

    /** 获取单个业务类型数据 */
    getSingleBusinessData(businessType) {

        const apiConfig = {
            levelprotect: '/levelprotect/levelprotect/list',
            website: '/website/website/list',
            app: '/website/app/list',
            applet: '/website/applet/list',
            operator: '/operator/operatorInfo/list'
        };

        const loadingKey = businessType + 'Num';
        this.loadingStates[loadingKey] = true;

        request({
            url: apiConfig[businessType],
            method: 'get',
            params: { pageNum: 1, pageSize: 999 }
        }).then(response => {
            if (response.code === 200) {
                this.businessStatistic[loadingKey] = response.data?.length || 0;
            }
        }).catch(error => {
            console.error(`获取${businessType}数据失败:`, error);
        }).finally(() => {
            this.loadingStates[loadingKey] = false;
            console.log(`${businessType}loading状态已关闭`);
        });
    },

    /** 获取数据统计 */
    async getDataStatisticsData() {
        console.log('调用getDataStatistics接口');
        try {
            const response = await getDataStatistics();
            console.log('getDataStatistics响应:', response);

            if (response.code === 200 && response.data) {
                // 根据接口返回的data数组更新各业务类型的数量
                response.data.forEach((item, index) => {
                    const num = item.num || 0;

                    // 根据index映射到对应的业务类型
                    switch(item.code) {
                        case 'SCREEN': // 电子屏
                            this.screenStatistic.screenNum = num;
                            break;
                        case 'SITE_BASE': // 网吧
                            this.screenStatistic.netbarNum = num;
                            break;
                        case 'WIFI': // 非经营
                            this.screenStatistic.wifiNum = num;
                            break;
                        case 'LEVEL_PROTECT': // 等保备案
                            this.businessStatistic.levelprotectNum = num;
                            break;
                        case 'WEB_SITE': // 网站备案
                            this.businessStatistic.websiteNum = num;
                            break;
                        case 'APP': // APP
                            this.businessStatistic.appNum = num;
                            break;
                        case 'APPLET': // 小程序
                            this.businessStatistic.appletNum = num;
                            break;
                        case 'OPERATOR_INFO': // 运营商
                            this.businessStatistic.operatorNum = num;
                            break;
                    }
                });

                console.log('统计数据更新完成:', {
                    screen: this.screenStatistic.screenNum,
                    netbar: this.screenStatistic.netbarNum,
                    wifi: this.screenStatistic.wifiNum,
                    business: this.businessStatistic
                });
            }
        } catch (error) {
            console.error('获取数据统计失败:', error);
        }
    },

    /** 加载电子屏数据 */
    async loadScreenData() {
        console.log('加载电子屏数据');
        this.loadingStates.screenNum = true;

        try {
            // 调用电子屏接口
            const response = await request({
                url: '/screen/screen/poiList',
                method: 'get',
                params: {}
            });

            console.log('电子屏数据响应:', response);

            if (response.code === 200 && response.data) {
                this.screenList = response.data;
                console.log('电子屏数据加载完成，数量:', response.data.length);

                // 绘制地图标记点
                if (this.mapInstance && this.screenList && this.screenList.length > 0) {
                    this.drawDataPoints(this.mapInstance);
                }
            }
        } catch (error) {
            console.error('加载电子屏数据失败:', error);
        } finally {
            this.loadingStates.screenNum = false;
        }
    },

    /** 路由跳转处理方法 */
    navigateToDetail(type, id) {
        console.log('navigateToDetail called:', type, id);

        switch(type) {
            case 'screen':
                this.$router.push(`/data-management/screen/detail/${id}`);
                break;
            case 'netbar':
                this.$router.push(`/data-management/netbar/detail/${id}`);
                break;
            case 'wifi':
                this.$router.push(`/data-management/wifi/site/detail/${id}`);
                break;
            case 'wifi-equipment':
                this.$router.push(`/data-management/wifi/equipment/detail/${id}`);
                break;
            default:
                console.warn('未知的详情页类型:', type);
        }
    },

    /** 计算全部按钮的badge数值 */
    getAllBadgeCount() {
        return (this.screenStatistic.screenNum || 0) +
               (this.screenStatistic.netbarNum || 0) +
               (this.screenStatistic.wifiNum || 0);
    },

    /** 加载所有tab的数据 */
    loadAllTabsData() {
        // 合并所有数据
        let allData = [];

        // 加载电子屏数据（全部）
        this.loadingStates.screenNum = true;
        this.queryMapParams.netsystemId = null;
        this.queryMapParams.params.keyareaFlag = null;
        this.queryMapParams.params.specialFlag = null;

        const screenPromise = searchScreen(this.queryMapParams);
        const netbarPromise = getSiteBaseList({ pageNum: 1, pageSize: 9999 });
        const wifiPromise = poiList({});

        Promise.all([screenPromise, netbarPromise, wifiPromise]).then(responses => {
            const [screenResponse, netbarResponse, wifiResponse] = responses;

            // 处理电子屏数据
            if (screenResponse.code === 200 || screenResponse.code === '200') {
                const screenData = screenResponse.data || [];
                allData = allData.concat(screenData);
                this.screenStatistic.screenNum = screenData.length;
            }

            // 处理网吧数据
            if (netbarResponse.code === 200) {
                const netbarData = (netbarResponse.data || []).slice(0, 124);
                allData = allData.concat(netbarData);
                this.screenStatistic.netbarNum = netbarData.length;
            }

            // 处理非经营数据
            if (wifiResponse.code === 200) {
                const wifiData = wifiResponse.data || [];
                allData = allData.concat(wifiData);
                this.screenStatistic.wifiNum = wifiData.length;
            }

            // 设置合并后的数据
            this.screenList = allData;
            this.screenStatistic.allNum = allData.length;


            // 绘制地图标记点
            if (this.mapInstance && this.screenList && this.screenList.length > 0) {
                this.drawDataPoints(this.mapInstance);
            }
        }).catch(error => {
            console.error('加载全部数据失败:', error);
        }).finally(() => {
            this.loadingStates.screenNum = false;
        });
    },

    /** 处理选中tabs变化 */
    handleSelectedTabsChange() {
        // 分离标记点类型和圆形类型
        const markerTypes = this.selectedTabs.filter(type => ['screen', 'netbar', 'wifi'].includes(type));
        const circleTypes = this.selectedTabs.filter(type => ['levelprotect', 'website', 'app', 'applet', 'operator'].includes(type));

        // 加载标记点数据
        if (markerTypes.length > 0) {
            this.loadSelectedTabsData(markerTypes);
        } else {
            this.screenList = [];
            if (this.mapInstance) {
                this.drawDataPoints(this.mapInstance);
            }
        }

        // 绘制圆形
        this.drawBusinessCircles(circleTypes);
    },

    /** 加载选中tabs的数据 */
    loadSelectedTabsData(filterTypes = null) {
        // 使用传入的类型或当前选中的标记点类型
        const typesToLoad = filterTypes || this.selectedTabs.filter(type => ['screen', 'netbar', 'wifi'].includes(type));

        if (typesToLoad.length === 0) {
            this.screenList = [];
            if (this.mapInstance) {
                this.drawDataPoints(this.mapInstance);
            }
            return;
        }

        let allData = [];
        const promises = [];

        typesToLoad.forEach(tabType => {
            switch(tabType) {
                case 'screen':
                    // 电子屏
                    const screenParams = { ...this.queryMapParams };
                    screenParams.netsystemId = null;
                    screenParams.params.keyareaFlag = null;
                    screenParams.params.specialFlag = null;
                    promises.push(searchScreen(screenParams));
                    break;
                case 'netbar':
                    // 网吧
                    promises.push(getSiteBaseList({ pageNum: 1, pageSize: 9999 }));
                    break;
                case 'wifi':
                    // 非经营
                    promises.push(poiList({}));
                    break;
            }
        });

        Promise.all(promises).then(results => {
            results.forEach((response, index) => {
                if (response.code === 200 || response.code === '200') {
                    let data = response.data || [];
                    // 如果是网吧数据，需要截取
                    if (typesToLoad[index] === 'netbar') {
                        data = data.slice(0, 124);
                    }
                    allData = allData.concat(data);
                }
            });

            this.screenList = allData;
            console.log('选中数据加载完成，总数量:', allData.length);

            // 绘制地图标记点
            if (this.mapInstance && this.screenList && this.screenList.length > 0) {
                this.drawDataPoints(this.mapInstance);
            }
        }).catch(error => {
            console.error('加载选中数据失败:', error);
        });
    },








  }
});

</script>

<!-- <link rel="stylesheet" href="<%= BASE_URL %>/dr/DrawingManager.css"></link> -->
<style>

    .up-view-area {
        padding: 20px 20px 0px 20px;
        display: flex;
        align-items: center;
    }
    .down-view-area {
        padding: 0px 20px 20px 20px;
        display: flex;
        align-items: center;
    }
    .view-span {
        font-size: small;
    }
    .view-tag{
        margin: 0 15px 0 3px;
    }
    .el-divider--horizontal{
        margin: 20px 0;
    }

    #divMap {
        width: 100%;
        height: 1024px;
        border: 1px solid rgb(204, 204, 204);
    }
    .card-common {
        padding: 2px;
        color: white;

    }

    .card-count {

        /* background-color: #628FF4; */
    }

    .card-network {

        background-color: #EE6668;
    }
    .card-unit {

        background-color: #13AE14;
    }
    .card-check {

        background-color: #F9C862;
    }

    .div-title {
        font-size: 20px;
    }
    .div-num{
        margin-top: 10px;
        float: right;
        font-size: 48px;
        font-weight:900;
    }

</style>

<style lang="scss" scoped>
.screenmap-container {
  padding: 20px 40px;
  background-color: #fff;
  min-height: 100vh;

  .map-controls {
    // background: white;
    // border-radius: 8px;
    // padding: 20px;
    margin-bottom: 20px;
    // box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .tabs-container {
      flex: 1;

      .map-tabs {
        display: flex;
        gap: 5px;
        flex-wrap: wrap;

        .tab-button {
          height: 40px;
          line-height: 40px;
          padding: 0 20px;
          border: 1px solid #dcdfe6;
          border-radius: 4px;
          background: #F9FCFF;
          color: #666;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.3s;
          display: flex;
          align-items: center;
          white-space: nowrap;

          &.active {
            background: #4584FF;
            color: white;
            border-color: #4584FF;

            .tab-icon {
              filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
            }
          }

          &:hover {
            background: #ecf5ff;
            color: #4584FF;

            .tab-icon {
              filter: brightness(0) saturate(100%) invert(39%) sepia(57%) saturate(1739%) hue-rotate(204deg) brightness(94%) contrast(101%);
            }
          }

          &.active:hover {
            background: #4584FF;
            color: white;

            .tab-icon {
              filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
            }
          }

          .tab-icon {
            width: 16px;
            height: 16px;
            margin-right: 5px;
            vertical-align: middle;
            filter: brightness(0) saturate(100%) invert(40%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
            transition: filter 0.3s;
          }

          i {
            margin-right: 5px;
          }

          .el-badge {
            margin-left: 8px;

            ::v-deep .el-badge__content {
              background-color: #ff4757;
              border: none;
              font-size: 10px;
              height: 16px;
              line-height: 16px;
              padding: 0 5px;
              min-width: 16px;
            }
          }

          .el-icon-loading {
            color: #409EFF;
            font-size: 14px;
            margin-left: 4px;
          }
        }
      }
    }

    .search-toggle {
      display: flex;
      align-items: center;

      .map-service-switch {
        display: flex;
        align-items: center;

        ::v-deep .el-switch {
          .el-switch__core {
            background-color: #dcdfe6;

            &:after {
              background-color: #fff;
            }
          }

          &.is-checked {
            .el-switch__core {
              background-color: #4584FF;
            }
          }
        }
      }

      .toggle-btn {
        background: #4584FF;
        border-color: #4584FF;
        color: white;

        &:hover {
          background: #3a73e6;
          border-color: #3a73e6;
        }
      }
    }
  }

  .search-form {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;

    .map-search-form {
      ::v-deep .el-form-item {
        margin-bottom: 15px;
        margin-right: 20px;

        .el-form-item__label {
          color: #606266;
          font-weight: 500;
        }

        .el-input__inner,
        .el-select .el-input__inner {
          border: 1px solid #dcdfe6;
          border-radius: 4px;

          &:focus {
            border-color: #4584FF;
          }
        }

        .el-button {
          &.el-button--primary {
            background: #4584FF;
            border-color: #4584FF;

            &:hover {
              background: #3a73e6;
              border-color: #3a73e6;
            }
          }
        }
      }
    }
  }

  .map-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    .map-content {
      width: 100%;
      height: 600px;
      border-radius: 8px;
    }


  }
}
</style>
