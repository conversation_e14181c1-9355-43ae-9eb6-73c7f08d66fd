
<!-- -->
<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryMapForm" size="small" :inline="true" v-show="showSearch">
            <el-form-item label="电子屏名称/位置" prop="screenName">
                <el-input
                v-model="queryParams.screenName"
                placeholder="请输入名称"
                clearable
                @keyup.enter.native="handleQuery"
                />
            </el-form-item>
            <el-form-item label="责任单位" prop="enterpriseId">
                <el-input
                v-model="queryParams.enterpriseId"
                placeholder="请输入责任单位"
                clearable
                @keyup.enter.native="handleQuery"
                />
            </el-form-item>

        </el-form>
    </div>
</template>

<!-- <script type="text/javascript" src="../../../public/static/map_load.js"></script> -->
<script>
// import { defineComponent } from '@vue/composition-api'
// import echarts from "echarts";


export default ({
  name: "screenMap",
  data() {
    return {


    };
  },

  mounted() {
    console.log('---', 'searchForm...');
  },
  methods: {



  }
});

</script>

<style>

</style>
