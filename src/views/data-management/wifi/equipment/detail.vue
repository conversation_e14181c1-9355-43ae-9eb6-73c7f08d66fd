<template>
<div style="display:flex;background: #f5f7fa;">
  <div class="app-container">
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">非经营设备详情</h2>
        <div class="header-actions">
          <el-button type="primary" size="small" @click="handleEdit">编辑</el-button>
          <el-button icon="el-icon-arrow-left" size="small" @click="goBack">返回</el-button>
        </div>
      </div>
    </div>

    <div class="detail-content" v-loading="loading">
      <!-- 基础信息 -->
      <div class="detail-section" id="basic-info">
        <div class="section-header" @click="toggleSection('basic')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">基础信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.basic ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.basic ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.basic" class="section-content">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="设备编码">
              {{ form.equipmentCode || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="设备名称">
              {{ form.equipmentName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="设备类型">
              <dict-tag :options="dict.type.wifi_equipment_type" :value="form.equipmentType"/>
            </el-descriptions-item>
            <el-descriptions-item label="设备品牌">
              {{ form.brand || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="设备型号">
              {{ form.model || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="MAC地址">
              {{ form.macAddress || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="IP地址">
              {{ form.ipAddress || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="设备状态">
              <dict-tag :options="dict.type.wifi_equipment_status" :value="form.status"/>
            </el-descriptions-item>
            <el-descriptions-item label="安装位置">
              {{ form.installLocation || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 技术参数 -->
      <div class="detail-section" id="technical-info">
        <div class="section-header" @click="toggleSection('technical')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">技术参数</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.technical ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.technical ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.technical" class="section-content">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="工作频段">
              {{ form.frequency || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="发射功率">
              {{ form.transmitPower || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="天线增益">
              {{ form.antennaGain || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="覆盖范围">
              {{ form.coverageRange || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="支持协议">
              {{ form.supportProtocol || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="最大连接数">
              {{ form.maxConnections || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 管理信息 -->
      <div class="detail-section" id="management-info">
        <div class="section-header" @click="toggleSection('management')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">管理信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.management ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.management ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.management" class="section-content">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="管理员">
              {{ form.adminName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="管理员电话">
              {{ form.adminPhone || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="安装日期">
              {{ form.installDate || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="维护周期">
              {{ form.maintenanceCycle || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="最后维护时间">
              {{ form.lastMaintenanceDate || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="备注">
              {{ form.remark || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </div>

    <!-- 右侧数据维护记录区域 -->
    <collapsible-detail-right
      @collapse-change="handleRightCollapseChange"
      @width-change="handleRightWidthChange"
    >
      <!-- 锚点导航组件 -->
      <template #anchor>
        <anchor-navigation
          :anchor-items="anchorItems"
          @anchor-change="handleAnchorChange"
        />
      </template>

      <!-- 数据维护时间线 -->
      <template #timeline>
        <data-maintenance-timeline
          :biz-type="'WIFI_EQUIPMENT'"
          :biz-id="form.equipmentNum"
          v-if="form.equipmentNum"
        />
      </template>
    </collapsible-detail-right>
  </div>
</div>
</template>

<script>
import { getEquipment } from "@/api/wifi/equipment";
import DataMaintenanceTimeline from '@/components/DataMaintenanceTimeline'
import AnchorNavigation from '@/components/AnchorNavigation'
import CollapsibleDetailRight from '@/components/CollapsibleDetailRight'

export default {
  name: "WifiEquipmentDetail",
  dicts: ['wifi_equipment_type', 'wifi_equipment_status'],
  components: {
    DataMaintenanceTimeline,
    AnchorNavigation,
    CollapsibleDetailRight
  },
  data() {
    return {
      loading: false,
      equipmentCode: undefined,
      // 表单参数
      form: {},
      // 模块折叠状态
      sectionCollapsed: {
        basic: false,
        technical: false,
        management: false
      },

      // 锚点导航配置
      anchorItems: [
        { id: 'basic-info', label: '基础信息' },
        { id: 'technical-info', label: '技术参数' },
        { id: 'management-info', label: '管理信息' }
      ],

      // 右侧面板收起状态
      isRightCollapsed: false,
      // 右侧面板宽度
      rightPanelWidth: 360
    };
  },
  created() {
     // 支持两种方式获取：路由参数和查询参数
    this.equipmentNum = this.$route.params && this.$route.params.equipmentNum;
    if (!this.equipmentNum) {
      this.equipmentNum = this.$route.query && this.$route.query.id;
    }
    this.getDetail();
  },
  methods: {
    /** 获取详情 */
    getDetail() {
      // this.reset();
      // if (!this.getDetail) {
      //   return;
      // }
      this.loading = true;
      // getEquipment(this.equipmentNum).then(response => {
      //   this.form = response.data;
      // });
      getEquipment(this.equipmentNum).then(response => {
         console.log('------',response)
        this.form = response.data;
       
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },

    /** 重置表单 */
    reset() {
      this.form = {
        equipmentCode: null,
        equipmentName: null,
        equipmentType: null,
        brand: null,
        model: null,
        macAddress: null,
        ipAddress: null,
        status: null,
        installLocation: null,
        frequency: null,
        transmitPower: null,
        antennaGain: null,
        coverageRange: null,
        supportProtocol: null,
        maxConnections: null,
        adminName: null,
        adminPhone: null,
        installDate: null,
        maintenanceCycle: null,
        lastMaintenanceDate: null,
        remark: null
      };
    },

    /** 编辑按钮 */
    handleEdit() {
      this.$router.push("/data-management/wifi/equipment/edit/" + this.equipmentNum);
    },

    /** 返回按钮 */
    goBack() {
      this.$router.go(-1);
    },

    /** 切换模块折叠状态 */
    toggleSection(section) {
      this.sectionCollapsed[section] = !this.sectionCollapsed[section];
    },

    // 处理锚点变化
    handleAnchorChange(anchorId) {
      console.log('当前激活的锚点:', anchorId);
    },

    // 处理右侧面板收起展开
    handleRightCollapseChange(isCollapsed) {
      this.isRightCollapsed = isCollapsed;
    },

    // 处理右侧面板宽度变化
    handleRightWidthChange(width) {
      this.rightPanelWidth = width;
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
  width: calc(100% - 380px);
  transition: width 0.3s ease;

  &.expanded {
    width: calc(100% - 60px);
  }

  .page-header {
    width: 99%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      justify-content: space-between;

      .page-title {
        margin: 0;
        font-size: 18px;
        color: #303133;
        font-weight: 600;
      }

      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
  }

  .detail-content {
    width: 99%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
  }

  .detail-section {
    margin-bottom: 24px;

    .section-header {
      background-color: #f6f7fb;
      padding: 12px 16px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 4px;
      margin-bottom: 16px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-line {
          width: 3px;
          height: 16px;
          background-color: #4584FF;
        }

        .title-text {
          font-size: 16px;
          font-weight: 600;
          color: #4584FF;
        }
      }

      .section-toggle {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #4584FF;
        font-size: 14px;

        .toggle-text {
          font-size: 14px;
        }

        i {
          font-size: 12px;
          transition: transform 0.3s;
        }
      }

      &:hover {
        background-color: #f0f2f5;
      }
    }

    .section-content {
      padding: 0 16px;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

::v-deep .el-descriptions__label {
  font-weight: 500;
  color: #666;
}

::v-deep .el-descriptions__content {
  color: #333;
}
</style>