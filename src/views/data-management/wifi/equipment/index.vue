<template>
  <div class="app-container">
    <div class="screen-management-layout">
      <!-- 左侧导航 -->
      <basic-data-sidebar />

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <!-- 搜索区域 -->
        <common-search
          :search-config="searchConfig"
          :advanced-fields="advancedFields"
          :initial-form="searchForm"
          @search="handleSearch"
          @reset="handleReset"
        />

        <!-- 操作按钮区域 -->
        <div class="action-bar">
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['wifi:equipment:add']"
              >新增</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="warning"
                plain
                icon="el-icon-download"
                size="mini"
                @click="handleExport"
                v-hasPermi="['wifi:equipment:export']"
              >导出</el-button>
            </el-col>
            <el-col :span="1.5">
               <el-button
                type="success"
                icon="el-icon-plus"
                size="mini"
                @click="handleReminder"
                v-hasPermi="['wifi:equipment:add']"
              >设置到期提醒</el-button>
            </el-col>

            <div class="toolbar-right">
              <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
            </div>
          </el-row>
        </div>

        <!-- 表格区域 -->
         
        <div class="table-container">
          <el-table
            v-loading="loading"
            :data="equipmentList"
            @selection-change="handleSelectionChange"
            style="width: 100%"
             :header-cell-style="{ backgroundColor: '#EBF2FF' }"
             border
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="设备编号" align="center" prop="equipmentNum" />
            <el-table-column label="设备名称" align="center" prop="equipmentName">
              <template slot-scope="scope">
                <div class="site-name-container">
                  <span>{{ scope.row.equipmentName }}</span>
                  <span
                    v-if="scope.row.dataUpdateStatus === 1"
                    class="update-badge pending"
                  >待更新</span>
                  <span
                    v-else-if="scope.row.dataUpdateStatus > 1"
                    class="update-badge overdue"
                  >已超期</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="设备mac地址" align="center" prop="mac" />
            <el-table-column label="设备ip" align="center" prop="ip" />
            <el-table-column label="安装日期" align="center" prop="installDate" width="180">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.installDate, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="设备类型" align="center" prop="equipmentType">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.wifi_equipment_type" :value="scope.row.equipmentType"/>
              </template>
            </el-table-column>
            <el-table-column label="最近联系时间" align="center" prop="lastConnectTime" width="180">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.lastConnectTime, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200" fixed="right">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-view"
                  @click="handleView(scope.row)"
                  v-hasPermi="['wifi:equipment:query']"
                >详情</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['wifi:equipment:edit']"
                >修改</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['wifi:equipment:remove']"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
    </div>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xlsx格式文件。</span>
          <el-link type="danger" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 数据更新提醒组件 -->
    <data-update-reminder
      :visible.sync="reminderVisible"
      :biz-type="'WIFI'"
    />
  </div>
</template>

<script>
import { listEquipment, delEquipment } from "@/api/wifi/equipment";
import { getToken } from "@/utils/auth";
import BasicDataSidebar from '@/components/BasicDataSidebar'
import CommonSearch from '@/components/CommonSearch'
import DataUpdateReminder from '@/components/DataUpdateReminder'

export default {
  name: "WifiEquipmentIndex",
  dicts: ['wifi_equipment_type'],
  components: { BasicDataSidebar, CommonSearch, DataUpdateReminder },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备表格数据
      equipmentList: [],
      // 提醒组件显示状态
      reminderVisible: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        equipmentName: null,
      },
      // 搜索配置
      searchConfig: {
        label: '设备名称',
        key: 'equipmentName',
        placeholder: '请输入设备名称'
      },
      // 高级搜索字段
      advancedFields: [],
      // 搜索表单
      searchForm: {},
      // 导入参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/wifi/equipment/importData"
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 设置提醒
    handleReminder() {
      this.reminderVisible = true;
    },
    // 处理搜索
    handleSearch(searchForm) {
      this.queryParams = {
        ...this.queryParams,
        ...searchForm,
        pageNum: 1
      };
      this.getList();
    },

    // 处理重置
    handleReset(searchForm) {
      this.searchForm = searchForm;
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        ...searchForm
      };
      this.getList();
    },
    /** 查询设备列表 */
    getList() {
      this.loading = true;
      listEquipment(this.queryParams).then(response => {
        this.equipmentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.equipmentNum)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push("/data-management/wifi/equipment/add");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const equipmentNum = row.equipmentNum || this.ids[0];
      this.$router.push("/data-management/wifi/equipment/edit/" + equipmentNum);
    },
    /** 详情按钮操作 */
    handleView(row) {
      this.$router.push("/data-management/wifi/equipment/detail/" + row.equipmentNum);
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const equipmentNums = row.equipmentNum || this.ids;
      this.$modal.confirm('是否确认删除设备编号为"' + equipmentNums + '"的数据项？').then(function() {
        return delEquipment(equipmentNums);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('wifi/equipment/export', {
        ...this.queryParams
      }, `equipment_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "数据导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    async importTemplate() {
      const url = process.env.VUE_APP_BASE_API+'/file/download/equipment_template.xlsx';
      const fileName = '设备导入模板.xlsx';

      const response = await fetch(url);
      const blob = await response.blob();
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      let that = this;
      that.loading = false;
      that.upload.open = false;
      that.upload.isUploading = false;
      that.$refs.upload.clearFiles();
      that.importLog = response.msg;

      that.$confirm(
        "<div style=' width: 700px; overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"
        + response.msg
        + "</div>",
        '导入结果',
        {
          confirmButtonText: '下载导入日志',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          showCancelButton: true,
          showClose: false,
          closeOnClickModal: false,
        })
        .then(() => {
          if(that.importLog){
            that.downloadImportLog('log.txt', that.importLog)
          }else{
            that.$modal.msgError("暂无日志");
          }
        })
        .catch(() => {});

      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.upload.open = false;
      this.$refs.upload.submit();
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.loading = true;
      this.upload.isUploading = true;
    },
    downloadImportLog(fileName, data){
      let aLink = document.createElement('a')
      let blob = new Blob([this.htmlToString(data)]);
      aLink.download = fileName
      aLink.href = URL.createObjectURL(blob)
      aLink.click()
    },
    htmlToString(html) {
      html = html.replaceAll('<br/>','\n');
      return html;
    },
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/screen-management.scss';

.action-bar {
  background: white;
  padding: 16px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.toolbar-right {
  float: right;
  display: flex;
  align-items: center;
  gap: 12px;
}

// 修复表格头部背景色
::v-deep .el-table {
  .el-table__header-wrapper {
    .el-table__header {
      th {
        background-color: #EBF2FF !important;
        color: #333;
        font-weight: 500;
      }
    }
  }
}
// 名称角标样式
.site-name-container {
  position: relative;
  display: inline-block;
  padding: 12px 12px 0 0;
  .update-badge {
    position: absolute;
    top: -2px;
    right: -10px;
    font-size: 10px;
    padding: 1px 4px;
    border-radius: 2px;
    // color: white;
    white-space: nowrap;
    z-index: 1;

    &.pending {
      color: #FE9400;
    }

    &.overdue {
      color: #FF2A2A;
    }
  }
}
</style>
