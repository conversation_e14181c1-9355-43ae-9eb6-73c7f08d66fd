<template>
  <div class="app-container">
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">{{ form.equipmentNum ? '编辑设备' : '新增设备' }}</h2>
        <el-button icon="el-icon-arrow-left" size="small" @click="handleClose">返回</el-button>
      </div>
    </div>
    <div class="form-container">

      <el-form ref="form" :model="form" :rules="rules" label-width="200px">
        <!-- 基础信息 -->
        <div class="form-section">
          <div class="section-header" @click="toggleSection('basic')">
            <div class="section-title">
              <div class="title-line"></div>
              <span class="title-text">基础信息</span>
            </div>
            <div class="section-toggle">
              <span class="toggle-text">{{ sectionCollapsed.basic ? '展开' : '收起' }}</span>
              <i :class="sectionCollapsed.basic ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            </div>
          </div>
          <div v-show="!sectionCollapsed.basic" class="section-content">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="设备名称" prop="equipmentName">
                  <el-input v-model="form.equipmentName" placeholder="请输入设备名称" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="厂商名称" prop="vendorName">
                  <el-input v-model="form.vendorName" placeholder="请输入厂商名称" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="厂商编码" prop="vendorNum">
                  <el-input v-model="form.vendorNum" placeholder="请输入厂商编码" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="安全厂商组织机构代码" prop="securityFactoryOrgcode">
                  <el-input v-model="form.securityFactoryOrgcode" placeholder="请输入安全厂商组织机构代码" maxlength="9" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="设备mac地址" prop="mac">
                  <el-input v-model="form.mac" placeholder="请输入设备mac地址" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="设备编号" prop="equipmentNum">
                  <el-input v-model="form.equipmentNum" placeholder="请输入设备编号" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="所属场所" prop="serviceName">
                  <el-autocomplete
                    style="width: 100%;"
                    v-model="form.serviceName"
                    :fetch-suggestions="querySearchSite"
                    placeholder="请输入非经营场所名称"
                    @select="handleSelectSite"
                  >
                    <template slot-scope="{ item }">
                      <span>{{ item.serviceName }}</span>
                    </template>
                  </el-autocomplete>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="安装日期" prop="installDate">
                  <el-date-picker clearable
                                  style="width: 100%;"
                                  v-model="form.installDate"
                                  type="date"
                                  value-format="yyyy-MM-dd"
                                  placeholder="请选择安装日期">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="设备ip" prop="ip">
                  <el-input v-model="form.ip" placeholder="请输入设备ip" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="设备能力" prop="equipmentFunction"> 
                  <el-select v-model="form.equipmentFunction" style="width: 100%;" placeholder="请选择设备能力">
                    <el-option
                      v-for="dict in dict.type.wifi_equipment_function"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select> 
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="设备类型" prop="equipmentType">
                  <el-select v-model="form.equipmentType" style="width: 100%;" placeholder="请选择设备类型">
                    <el-option
                      v-for="dict in dict.type.wifi_equipment_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="设备场景" prop="equipmentScence"> 
                  <el-select v-model="form.equipmentScence" style="width: 100%;" placeholder="请选择设备场景">
                    <el-option
                      v-for="dict in dict.type.wifi_equipment_scence"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select> 
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 位置信息 -->
        <div class="form-section">
          <div class="section-header" @click="toggleSection('location')">
            <div class="section-title">
              <div class="title-line"></div>
              <span class="title-text">位置信息</span>
            </div>
            <div class="section-toggle">
              <span class="toggle-text">{{ sectionCollapsed.location ? '展开' : '收起' }}</span>
              <i :class="sectionCollapsed.location ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            </div>
          </div>
          <div v-show="!sectionCollapsed.location" class="section-content">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="ap/特征采集设备经度" prop="longitude">
                      <el-input v-model="form.longitude" placeholder="请输入ap/特征采集设备经度" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="ap/特征采集设备维度" prop="latiture">
                      <el-input v-model="form.latiture" placeholder="请输入ap/特征采集设备维度" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="安装位置楼层" prop="installPoint">
                      <el-input v-model="form.installPoint" placeholder="请输入安装位置楼层" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
        </div>

        <!-- 技术参数 -->
        <div class="form-section">
          <div class="section-header" @click="toggleSection('technical')">
            <div class="section-title">
              <div class="title-line"></div>
              <span class="title-text">技术参数</span>
            </div>
            <div class="section-toggle">
              <span class="toggle-text">{{ sectionCollapsed.technical ? '展开' : '收起' }}</span>
              <i :class="sectionCollapsed.technical ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            </div>
          </div>
          <div v-show="!sectionCollapsed.technical" class="section-content">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="采集半径" prop="collectionRadius">
                      <el-input v-model="form.collectionRadius" placeholder="请输入采集半径" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="最近联系时间" prop="lastConnectTime">
                      <el-date-picker clearable
                                      style="width: 100%;"
                                      v-model="form.lastConnectTime"
                                      type="date"
                                      value-format="yyyy-MM-dd"
                                      placeholder="请选择最近联系时间">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="上传数据时间间隔" prop="uploadTimeInterval">
                      <el-input v-model="form.uploadTimeInterval" placeholder="请输入上传数据时间间隔" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="软件版本号" prop="wadVersion">
                      <el-input v-model="form.wadVersion" placeholder="请输入软件版本号" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="固件版本号" prop="firmwareVersion">
                      <el-input v-model="form.firmwareVersion" placeholder="请输入固件版本号" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
          </div>

        <!-- 备注信息 -->
        <div class="form-section">
          <div class="section-header" @click="toggleSection('remark')">
            <div class="section-title">
              <div class="title-line"></div>
              <span class="title-text">备注信息</span>
            </div>
            <div class="section-toggle">
              <span class="toggle-text">{{ sectionCollapsed.remark ? '展开' : '收起' }}</span>
              <i :class="sectionCollapsed.remark ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            </div>
          </div>
          <div v-show="!sectionCollapsed.remark" class="section-content">
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                  <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="form-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="handleClose">取 消</el-button>
        </div>

      </el-form>
    </div>
  </div>
</template>

<script>
import { getEquipment, addEquipment, updateEquipment } from "@/api/wifi/equipment";
import { listWifiSite, getWifiSite } from "@/api/wifi/site";
import { handlePageClose } from "@/utils/pageUtils";
export default {
  name: 'WifiEquipmentForm',
  dicts: ['wifi_equipment_type','wifi_equipment_scence','wifi_equipment_function'],
  data () {
    return {
      equipmentNum: undefined,
      // 表单参数
      form: {
         serviceName:'',
      },
      siteInf:{},
      // 折叠状态
      sectionCollapsed: {
        basic: false,
        location: false,
        technical: false,
        remark: false,
      },
      // 表单校验
      rules: {
        equipmentNum: [
          { required: true, message: "设备编号不能为空", trigger: "blur" }
        ],
        serviceName:[{ required: true, message: "所属场所不能为空", trigger: "blur" }],
        equipmentName: [
          { required: true, message: "设备名称不能为空", trigger: "blur" }
        ],
        mac: [
          { required: true, message: "设备mac地址不能为空", trigger: "blur" }
        ],
        securityFactoryOrgcode: [
          { required: true, message: "安全厂商组织机构代码不能为空", trigger: "blur" }
        ],
        vendorName: [
          { required: true, message: "厂商名称不能为空", trigger: "blur" }
        ],
        serviceCode: [
          { required: true, message: "所属场所不能为空", trigger: "blur" }
        ],
        installDate: [
          { required: true, message: "安装日期不能为空", trigger: "blur" }
        ],
        equipmentType: [
          { required: true, message: "设备类型不能为空", trigger: "change" }
        ],
        equipmentFunction: [
          { required: true, message: "设备能力不能为空", trigger: "blur" }
        ],
      }
    }
  },
  created () {
     // 支持两种方式获取：路由参数和查询参数
    this.equipmentNum = this.$route.params && this.$route.params.equipmentNum;
    if (!this.equipmentNum) {
      this.equipmentNum = this.$route.query && this.$route.query.id;
    }
    this.getDetail();
  },
  methods: {
    handleClose() {
      // 使用公共方法关闭页面
      handlePageClose(this);
    },
    // 切换折叠状态
    toggleSection(section) {
      this.sectionCollapsed[section] = !this.sectionCollapsed[section];
    },
    reset() {
      this.form = {
        equipmentNum: null,
        equipmentName: null,
        mac: null,
        ip: null,
        securityFactoryOrgcode: null,
        vendorName: null,
        vendorNum: null,
        serviceCode: null,
        installDate: null,
        installPoint: null,
        equipmentType: null,
        longitude: null,
        latiture: null,
        uploadTimeInterval: null,
        collectionRadius: null,
        lastConnectTime: null,
        remark: null,
        wadVersion: null,
        firmwareVersion: null,
        equipmentScence: null,
        equipmentFunction: null,
      };
      this.resetForm("form");
    },
        // 查询非经营场所名称
    querySearchSite(queryString, cb) {
      if(!queryString){
        cb([]);
      } 
      let results;
      if(queryString){
        listWifiSite({serviceName: queryString}).then(response => {
          results = response.rows;
          cb(results);
        });
      }
    },
    //  
    handleSelectSite(item) {
      this.form.serviceCode = item.serviceCode;// 场所编码
      this.form.serviceName = item.serviceName;// 场所名称
      console.log('this.form.serviceCode=',item);
    },
    getDetail () {
      this.reset();
      if(!this.equipmentNum){
        return
      }
      getEquipment(this.equipmentNum).then(response => {
        this.form = response.data;
        let strName =this.getSiteInfo(this.form.serviceCode);  // 获取所属场所名称 
      });
    },
     // 获取指定场所编码 对应的厂商名称  async await
    getSiteInfo(queryString) {
      // console.log('getSiteInfo=',queryString); 
      getWifiSite(queryString).then(response => {
        console.log('listWifiSite= ',response);
         if(response.code==200) {
          this.form.serviceName =response.data.serviceName;
          console.log('this.form.serviceName=  ' ,this.form.serviceName); 
         }
      });  
    },
    submitForm() {
      let that = this
      that.$refs["form"].validate(valid => {
        if (valid) {
          if (that.form.equipmentNum != null) {
            updateEquipment(that.form).then(response => {
              that.$modal.msgSuccess("修改成功");
              that.handleClose();
            });
          } else {
            addEquipment(that.form).then(response => {
              that.$modal.msgSuccess("新增成功");
              that.handleClose();
            });
          }
        }
      });
    },
    
  },
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .page-header {
    width: 94%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      justify-content: space-between;

      .page-title {
        margin: 0;
        font-size: 18px;
        color: #303133;
        font-weight: 600;
      }
    }
  }

  .form-container {
    width: 94%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
  }

  .form-section {
    margin-bottom: 24px;

    .section-header {
      background-color: #f6f7fb;
      padding: 12px 16px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 4px;
      margin-bottom: 16px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-line {
          width: 3px;
          height: 16px;
          background-color: #4584FF;
        }

        .title-text {
          font-size: 16px;
          font-weight: 600;
          color: #4584FF;
        }
      }

      .section-toggle {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #4584FF;
        font-size: 14px;

        .toggle-text {
          font-size: 14px;
        }

        i {
          font-size: 12px;
          transition: transform 0.3s;
        }
      }

      &:hover {
        background-color: #f0f2f5;
      }
    }

    .section-content {
      padding: 0 16px;
    }
  }
}

// 全局样式：确保表单标签不换行
::v-deep .el-form-item__label {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.form-footer {
  text-align: right;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}
</style>
