<template>
  <div style="display:flex;background: #f5f7fa;">

 
  <div class="app-container">
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">非经营场所详情</h2>
        <div class="header-actions">
          <el-button type="primary" size="small" @click="handleEdit">编辑</el-button>
          <el-button icon="el-icon-arrow-left" size="small" @click="goBack">返回</el-button>
        </div>
      </div>
    </div>

    <div class="detail-content">
      <!-- 基础信息 -->
      <div class="detail-section">
        <div class="section-header" @click="toggleSection('basic')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">基础信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.basic ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.basic ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.basic" class="section-content">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="场所编码">
              {{ form.serviceCode }}
            </el-descriptions-item>
            <el-descriptions-item label="场所名称">
              {{ form.serviceName }}
            </el-descriptions-item>
            <el-descriptions-item label="社采编码">
              {{ form.shecaiCode }}
            </el-descriptions-item>
            <el-descriptions-item label="场所地址">
              {{ form.address }}
            </el-descriptions-item>
            <el-descriptions-item label="场所性质">
              <dict-tag :options="dict.type.wifi_business_nature" :value="form.businessNature"/>
            </el-descriptions-item>
            <el-descriptions-item label="场所服务状态">
              <dict-tag :options="dict.type.wifi_status" :value="form.status"/>
            </el-descriptions-item>
            <el-descriptions-item label="场所负责人">
              {{ form.personName }}
            </el-descriptions-item>
            <el-descriptions-item label="负责人电话">
              {{ form.personTel }}
            </el-descriptions-item>
            <el-descriptions-item label="负责人身份证号">
              {{ form.personIdcard }}
            </el-descriptions-item>
            <el-descriptions-item label="经度">
              {{ form.longitude }}
            </el-descriptions-item>
            <el-descriptions-item label="纬度">
              {{ form.latitude }}
            </el-descriptions-item>
            <el-descriptions-item label="邮政编码">
              {{ form.zipCode }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 网络信息 -->
      <div class="detail-section">
        <div class="section-header" @click="toggleSection('network')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">网络信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.network ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.network ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.network" class="section-content">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="接入运营商">
              <dict-tag :options="dict.type.wifi_access_operator" :value="form.accessOperator"/>
            </el-descriptions-item>
            <el-descriptions-item label="接入方式">
              <dict-tag :options="dict.type.wifi_access_method" :value="form.accessMethod"/>
            </el-descriptions-item>
            <el-descriptions-item label="网络带宽">
              {{ form.bandwidth }}
            </el-descriptions-item>
            <el-descriptions-item label="AP数量">
              {{ form.apCount }}
            </el-descriptions-item>
            <el-descriptions-item label="SSID">
              {{ form.ssid }}
            </el-descriptions-item>
            <el-descriptions-item label="认证方式">
              <dict-tag :options="dict.type.wifi_auth_method" :value="form.authMethod"/>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </div>

    <!-- 右侧数据维护记录区域 -->
    <collapsible-detail-right
      @collapse-change="handleRightCollapseChange"
      @width-change="handleRightWidthChange"
    >
      <!-- 锚点导航组件 -->
      <template #anchor>
        <anchor-navigation
          :anchor-items="anchorItems"
          @anchor-change="handleAnchorChange"
        />
      </template>

      <!-- 数据维护时间线 -->
      <template #timeline>
        <data-maintenance-timeline
          :biz-type="'WIFI_SITE'"
          :biz-id="form.serviceCode"
          v-if="form.serviceCode"
        />
      </template>
    </collapsible-detail-right>
  </div>
   </div>
</template>

<script>
import { getWifiSite } from "@/api/wifi/site";
import DataMaintenanceTimeline from '@/components/DataMaintenanceTimeline'
import AnchorNavigation from '@/components/AnchorNavigation'
import CollapsibleDetailRight from '@/components/CollapsibleDetailRight'

export default {
  name: "WifiSiteDetail",
  dicts: ['wifi_business_nature', 'wifi_status', 'wifi_access_operator', 'wifi_access_method', 'wifi_auth_method'],
  components: {
    DataMaintenanceTimeline,
    AnchorNavigation,
    CollapsibleDetailRight
  },
  data() {
    return {
      loading: false,
      serviceCode: undefined,
      // 表单参数
      form: {},
      // 模块折叠状态
      sectionCollapsed: {
        basic: false,
        network: false
      },

      // 锚点导航配置
      anchorItems: [
        { id: 'basic-info', label: '基础信息' },
        { id: 'network-info', label: '网络信息' }
      ],

      // 右侧面板收起状态
      isRightCollapsed: false,
      // 右侧面板宽度
      rightPanelWidth: 360
    };
  },
  created() {
    // 支持两种方式获取：路由参数和查询参数
    this.serviceCode = this.$route.params && this.$route.params.id;
    if (!this.serviceCode) {
      this.serviceCode = this.$route.query && this.$route.query.serviceCode;
    }
    this.getDetail();
  },
  methods: {
    /** 获取详情 */
    getDetail() {
      if (!this.serviceCode) {
        return;
      }
      this.loading = true;
      getWifiSite(this.serviceCode).then(response => {
        this.form = response.data;
        this.loading = false;
      });
    },
    /** 切换模块折叠状态 */
    toggleSection(section) {
      this.sectionCollapsed[section] = !this.sectionCollapsed[section];
    },
    /** 编辑按钮 */
    handleEdit() {
      this.$router.push("/data-management/wifi/site/edit/" + this.serviceCode);
    },
    /** 返回按钮 */
    goBack() {
      window.history.go(-1);
    },
    handleClose() {
      this.goBack();
    },

    // 处理锚点变化
    handleAnchorChange(anchorId) {
      console.log('当前激活的锚点:', anchorId);
    },

    // 处理右侧面板收起展开
    handleRightCollapseChange(isCollapsed) {
      this.isRightCollapsed = isCollapsed;
    },

    // 处理右侧面板宽度变化
    handleRightWidthChange(width) {
      this.rightPanelWidth = width;
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
  width: calc(100% - 380px);
  transition: width 0.3s ease;

  &.expanded {
    width: calc(100% - 60px);
  }

  .page-header {
    width: 99%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      justify-content: space-between;

      .page-title {
        margin: 0;
        font-size: 18px;
        color: #303133;
        font-weight: 600;
      }

      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
  }

  .detail-content {
    width: 99%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
  }

  .detail-section {
    margin-bottom: 24px;

    .section-header {
      background-color: #f6f7fb;
      padding: 12px 16px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 4px;
      margin-bottom: 16px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-line {
          width: 3px;
          height: 16px;
          background-color: #4584FF;
        }

        .title-text {
          font-size: 16px;
          font-weight: 600;
          color: #4584FF;
        }
      }

      .section-toggle {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #4584FF;
        font-size: 14px;

        .toggle-text {
          font-size: 14px;
        }

        i {
          font-size: 12px;
          transition: transform 0.3s;
        }
      }

      &:hover {
        background-color: #f0f2f5;
      }
    }

    .section-content {
      padding: 0 16px;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

::v-deep .el-descriptions__label {
  font-weight: 500;
  color: #666;
}

::v-deep .el-descriptions__content {
  color: #333;
}
</style>