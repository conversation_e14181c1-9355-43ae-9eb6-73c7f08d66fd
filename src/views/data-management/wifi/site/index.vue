<template>
  <div class="app-container">
    <div class="screen-management-layout">
      <!-- 左侧导航 -->
      <basic-data-sidebar />

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <!-- 搜索区域 -->
        <common-search
          :search-config="searchConfig"
          :search-params="queryParams"
          @search="handleQuery"
          @reset="resetQuery"
        />

        <!-- 操作按钮区域 -->
        <div class="action-bar">
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['wifi:site:add']"
              >新增</el-button>
            </el-col>
            <!-- <el-col :span="1.5">
              <el-button
                type="success"
                plain
                icon="el-icon-edit"
                size="mini"
                :disabled="single"
                @click="handleUpdate"
                v-hasPermi="['wifi:site:edit']"
              >修改</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="danger"
                plain
                icon="el-icon-delete"
                size="mini"
                :disabled="multiple"
                @click="handleDelete"
                v-hasPermi="['wifi:site:remove']"
              >删除</el-button>
            </el-col> -->
            <el-col :span="1.5">
              <el-button
                type="warning"
                plain
                icon="el-icon-download"
                size="mini"
                @click="handleExport"
                v-hasPermi="['wifi:site:export']"
              >导出</el-button>
            </el-col>
            <el-col :span="1.5">
               <el-button
                type="success"
                icon="el-icon-plus"
                size="mini"
                @click="handleReminder"
                v-hasPermi="['wifi:site:add']"
              >设置到期提醒</el-button>
            </el-col>

            <div class="toolbar-right">
              <el-button type="text" icon="el-icon-setting" @click="handleDisplayFields" class="display-fields-btn">
                设置显示字段
              </el-button>
              <right-toolbar @queryTable="getList"></right-toolbar>
            </div>
          </el-row>
        </div>

        <!-- 表格区域 -->
        <div class="table-container">
          <el-table
            v-loading="loading"
            :data="wifiSiteList"
            @selection-change="handleSelectionChange"
            border
            style="width: 100%"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="场所编码" align="center" prop="serviceCode" />
            <el-table-column label="场所名称" align="center" prop="serviceName">
              <template slot-scope="scope">
                <div class="site-name-container">
                  <span>{{ scope.row.serviceName }}</span>
                  <span
                    v-if="scope.row.dataUpdateStatus === 1"
                    class="update-badge pending"
                  >待更新</span>
                  <span
                    v-else-if="scope.row.dataUpdateStatus > 1"
                    class="update-badge overdue"
                  >已超期</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="社采编码" align="center" prop="shecaiCode" />
            <el-table-column label="法人姓名" align="center" prop="principal" />
            <el-table-column label="法人电话" align="center" prop="principalTel" />
            <el-table-column label="场所性质" align="center" width="140" prop="businessNature">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.wifi_business_nature" :value="scope.row.businessNature"/>
              </template>
            </el-table-column>
            <el-table-column label="所属省" align="center" prop="provinceCode" />
            <el-table-column label="所属城市" align="center" prop="cityCode" />
            <el-table-column label="所属分区" align="center" prop="areaName" />
            <el-table-column label="派出所" align="center" prop="policeName" />
            <el-table-column label="营业开始时间" align="center" width="110" prop="startTime" />
            <el-table-column label="营业结束时间" align="center" width="110" prop="endTime" />
            <el-table-column label="操作" align="center" width="220" class-name="small-padding fixed-width" fixed="right">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-view"
                  @click="handleView(scope.row)"
                  v-hasPermi="['wifi:site:query']"
                >详情</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['wifi:site:edit']"
                >修改</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['wifi:site:remove']"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
    </div>

    <!-- 数据更新提醒组件 -->
    <data-update-reminder
      :visible.sync="reminderVisible"
      :biz-type="'WIFI'"
    />
  </div>
</template>

<script>
import { listWifiSite, delWifiSite } from "@/api/wifi/site";
import BasicDataSidebar from '@/components/BasicDataSidebar'
import CommonSearch from '@/components/CommonSearch'
import DataUpdateReminder from '@/components/DataUpdateReminder'

export default {
  name: "WifiSiteIndex",
  dicts: ['wifi_business_nature', 'wifi_status'],
  components: { BasicDataSidebar, CommonSearch, DataUpdateReminder },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 非经营场所表格数据
      wifiSiteList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        serviceName: null,
      },
      // 搜索配置
      searchConfig: {
        label: '场所名称',
        key: 'serviceName',
        placeholder: '请输入场所名称'
      },

      // 搜索表单
      searchForm: {},
      // 数据更新提醒弹窗显示状态
      reminderVisible: false,
    };
  },
  created() {
    this.getList();
    this.initAdvancedFields();
  },
  methods: {
    // 设置提醒
    handleReminder() {
      this.reminderVisible = true;
    },
    // 初始化高级搜索字段
    initAdvancedFields() {
      this.advancedFields = [];
    },

    // 处理搜索
    handleSearch(searchForm) {
      this.queryParams = {
        ...this.queryParams,
        ...searchForm,
        pageNum: 1
      };
      this.getList();
    },
    /** 查询非经营场所列表 */
    getList() {
      this.loading = true;
      listWifiSite(this.queryParams).then(response => {
        this.wifiSiteList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery(searchForm) {
      this.searchForm = searchForm;
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        ...searchForm
      };
      this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.serviceCode)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push("/data-management/wifi/site/add");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const serviceCode = row.serviceCode || this.ids[0];
      this.$router.push("/data-management/wifi/site/edit/" + row.serviceCode);
    },
    /** 详情按钮操作 */
    handleView(row) {
      this.$router.push("/data-management/wifi/site/detail/" + row.serviceCode);
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const serviceCodes = row.serviceCode || this.ids;
      this.$modal.confirm('是否确认删除场所编码为"' + serviceCodes + '"的数据项？').then(function() {
        return delWifiSite(serviceCodes);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('wifi/site/export', {
        ...this.queryParams
      }, `wifi_site_${new Date().getTime()}.xlsx`)
    },
    /** 设置显示字段 */
    handleDisplayFields() {
      this.$modal.msgInfo("设置显示字段功能开发中");
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/screen-management.scss';

.action-bar {
  background: white;
  padding: 16px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.toolbar-right {
  float: right;
  display: flex;
  align-items: center;
  gap: 12px;

  .display-fields-btn {
    color: #ACB0B3;
    padding: 0;
    border: none;
    background: none;

    &:hover {
      color: #4584FF;
    }
  }
}

// 修复表格头部背景色
::v-deep .el-table {
  .el-table__header-wrapper {
    .el-table__header {
      th {
        background-color: #EBF2FF !important;
        color: #333;
        font-weight: 500;
      }
    }
  }
}

// 场所名称容器样式
.site-name-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

// 更新状态徽章样式
.update-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;

  &.pending {
    background-color: #FE9400;
    color: white;
  }

  &.overdue {
    background-color: #FF2A2A;
    color: white;
  }
}
</style>