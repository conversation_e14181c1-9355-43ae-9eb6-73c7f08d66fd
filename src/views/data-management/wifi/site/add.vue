<template>
  <div class="app-container">
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">{{ form.id ? '编辑' : '新增' }}非经营场所</h2>
        <el-button icon="el-icon-arrow-left" size="small" @click="goBack">返回</el-button>
      </div>
    </div>
    <section class="form-container">

    <el-form ref="form" :model="form" :rules="rules" label-width="200px">
      <!-- 场所基础信息 -->
      <div class="form-section">
        <div class="section-header" @click="toggleSection('basic')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">场所基础信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.basic ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.basic ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.basic" class="section-content">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="场所编码" prop="serviceCode">
                <el-input v-model="form.serviceCode" placeholder="请输入场所编码" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="场所名称" prop="serviceName">
                <el-input v-model="form.serviceName" placeholder="请输入场所名称" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="社采编码" prop="shecaiCode">
                <el-input v-model="form.shecaiCode" maxlength="16" placeholder="请输入社采编码" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="场所性质" prop="businessNature">
                <el-select v-model="form.businessNature" placeholder="请选择场所性质" clearable style="width: 100%">
                  <el-option
                    v-for="dict in dict.type.wifi_business_nature"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="场所类型" prop="serviceType">
                <el-select v-model="form.serviceType" placeholder="请选择场所类型" clearable style="width: 100%">
                  <el-option
                    v-for="dict in dict.type.wifi_service_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="邮编" prop="zip">
                <el-input v-model="form.zip" placeholder="请输入邮编" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="所属省" prop="provinceCode">
                 <el-select v-model="form.provinceCode" style="width: 100%;" placeholder="请选择所属省/市" @change="getPoliceList" clearable>
                  <el-option
                    v-for="item in provinceList"
                    :key="item.areaCode"
                    :label="item.areaName"
                    :value="item.areaCode"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所属城市" prop="cityCode">
                <el-select v-model="form.cityCode" style="width: 100%;" placeholder="请选择所属城市" @change="getPoliceList" clearable>
                  <el-option
                    v-for="item in provinceList"
                    :key="item.areaCode"
                    :label="item.areaName"
                    :value="item.areaCode"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所属区域" prop="areaCode">
                <el-select v-model="form.areaCode" placeholder="请选择所属区域" clearable style="width: 100%" @change="getPoliceList">
                  <el-option
                    v-for="item in areaList"
                    :key="item.areaCode"
                    :label="item.areaName"
                    :value="item.areaCode"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="所属派出所" prop="policeCode">
                <el-select v-model="form.policeCode" placeholder="请选择所属派出所" clearable style="width: 100%" filterable>
                  <el-option
                    v-for="item in policeList"
                    :key="item.areaCode"
                    :label="item.areaName"
                    :value="item.areaCode"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="地址" prop="address">
                <el-input v-model="form.address" placeholder="请输入地址" />
              </el-form-item>
            </el-col>
            
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="经度" prop="xpoint">
                <el-input v-model="form.xpoint" placeholder="请输入经度" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="纬度" prop="ypoint">
                <el-input v-model="form.ypoint" placeholder="请输入纬度" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-button type="text" @click="btnMapLocation" icon="el-icon-location">位置</el-button>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="采集类型" prop="capType">
                <el-select v-model="form.capType" placeholder="请选择采集类型" clearable style="width: 100%">
                  <el-option
                    v-for="dict in dict.type.wifi_cap_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="营业开始时间" prop="startTime">
                <el-time-select
                  v-model="form.startTime"
                  style="width: 100%"
                  :picker-options="{
                    start: '08:30',
                    step: '00:15',
                    end: '18:30'
                  }"
                  placeholder="请输入营业开始时间">
                </el-time-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="营业结束时间" prop="endTime">
                <el-time-select
                  v-model="form.endTime"
                  style="width: 100%"
                  :picker-options="{
                    start: '08:30',
                    step: '00:15',
                    end: '18:30'
                  }"
                  placeholder="请输入营业结束时间">
                </el-time-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="场所服务状态代码" prop="status">
                <el-select v-model="form.status" placeholder="请选择场所服务状态代码" clearable style="width: 100%">
                  <el-option
                    v-for="dict in dict.type.wifi_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="接入方式代码" prop="netType">
                <el-select v-model="form.netType" placeholder="请选择接入方式代码" clearable style="width: 100%">
                  <el-option
                    v-for="dict in dict.type.wifi_net_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="接入服务运营商代码" prop="producerCode">
                <el-select v-model="form.producerCode" placeholder="请选择接入服务运营商代码" clearable style="width: 100%">
                  <el-option
                    v-for="dict in dict.type.wifi_producer_code"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="出口ip地址" prop="exitIp">
                <el-input v-model="form.exitIp" placeholder="请输入出口ip地址" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="网络认证账号或固定ip地址" prop="authAccount">
                <el-input v-model="form.authAccount" placeholder="请输入网络认证账号或固定ip地址" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="带宽" prop="bandWidth">
                <el-input v-model="form.bandWidth" placeholder="请输入带宽" type="number">
                  <template slot="append">M</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 场所人员信息 -->
      <div class="form-section">
        <div class="section-header" @click="toggleSection('personnel')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">场所人员信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.personnel ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.personnel ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>
        <div v-show="!sectionCollapsed.personnel" class="section-content">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="场所负责人" prop="personName">
                <el-input v-model="form.personName" placeholder="请输入场所负责人" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="负责人电话" prop="personTel">
                <el-input v-model="form.personTel" placeholder="请输入负责人电话" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="负责人身份证号" prop="personIdcard">
                <el-input v-model="form.personIdcard" placeholder="请输入负责人身份证号" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="从业人数" prop="practitionerNumber">
                <el-input v-model="form.practitionerNumber" placeholder="请输入从业人数" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="法人姓名" prop="principal">
                <el-input v-model="form.principal" placeholder="请输入法人姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="法人电话" prop="principalTel">
                <el-input v-model="form.principalTel" placeholder="请输入法人电话" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="法人证件号码" prop="principalCertCode">
                <el-input v-model="form.principalCertCode" placeholder="请输入法人证件号码" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="信息安全员" prop="inforMan">
                <el-input v-model="form.inforMan" placeholder="请输入信息安全员" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="安全员联系电话" prop="inforManTel">
                <el-input v-model="form.inforManTel" placeholder="请输入安全员联系电话" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="安全员email地址" prop="inforManEmail">
                <el-input v-model="form.inforManEmail" type="email" placeholder="请输入安全员email地址" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="所属网监部门" prop="netMonitorDepartment">
                <el-input v-model="form.netMonitorDepartment" placeholder="请输入所属网监部门" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="网监负责人" prop="netMonitorMan">
                <el-input v-model="form.netMonitorMan" placeholder="请输入网监负责人" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="网监负责人联系电话" prop="netMonitorManTel">
                <el-input v-model="form.netMonitorManTel" placeholder="请输入网监负责人联系电话" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="终端安全厂商组织机构编码" prop="terminalFactoryOrgcode">
                <el-input v-model="form.terminalFactoryOrgcode" placeholder="请输入终端安全厂商组织机构编码" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="场所营业执照统一社会信用代码" prop="orgCode">
                <el-input v-model="form.orgCode" placeholder="请输入场所营业执照统一社会信用代码" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="认证厂商统一社会信用代码" prop="authOrgcode">
                <el-input v-model="form.authOrgcode" placeholder="请输入认证厂商统一社会信用代码" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="运营厂商统一社会信用代码" prop="serviceOrgcode">
                <el-input v-model="form.serviceOrgcode" placeholder="请输入运营厂商统一社会信用代码" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="安全设备商统一社会信用代码" prop="hardwOrgcode">
                <el-input v-model="form.hardwOrgcode" placeholder="请输入安全设备硬件提供商统一社会信用代码" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否安全措施" prop="isSafe">
                <el-select v-model="form.isSafe" placeholder="请输入是否安全措施" clearable style="width: 100%">
                  <el-option
                    v-for="dict in dict.type.sys_yes_no"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="form-footer">
        <el-button type="primary" @click="submitForm" :loading="submitting">确 定</el-button>
        <el-button @click="goBack">取 消</el-button>
      </div>

    </el-form>

    <!-- 地图选择组件 -->
    <MapPoint ref="PointMap1" :visible.sync="openMap" :modalObj='modalObj' @value-changed="getMapLocation" />
    </section>
  </div>
</template>

<script>
import { getWifiSite, addWifiSite, updateWifiSite } from "@/api/wifi/site";
import { listArea } from '@/api/system/area';

export default {
  name: "WifiSiteAdd",
  dicts: ['wifi_producer_code', 'wifi_status', 'wifi_cap_type', 'wifi_net_type', 'wifi_business_nature', 'wifi_service_type', 'sys_yes_no'],
  components: { MapPoint:(d)=>import('@/components/MapPoint/index.vue') },
  data() {
    return {
      loading: false,
      submitting: false,
      serviceCode: undefined,
      // 表单参数
      form: {},
      // 模块折叠状态
      sectionCollapsed: {
        basic: false,
        personnel: false
      },
      // 区域列表
      areaList: [],
      // 派出所列表
      policeList: [],
      // 表单校验
      rules: {
        serviceName: [
          { required: true, message: "场所名称不能为空", trigger: "blur" }
        ],
        shecaiCode: [
          {  max: 16,required: true, message: "社采编码不能为空", trigger: "blur" }
        ],
        address: [
          { required: true, message: "地址不能为空", trigger: "blur" }
        ],
        businessNature: [
          { required: true, message: "场所性质不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "场所服务状态代码不能为空", trigger: "change" }
        ],
        serviceType: [
          { required: true, message: "场所类型不能为空", trigger: "change" }
        ],
        provinceCode: [
          { required: true, message: "所属省不能为空", trigger: "blur" }
        ],
        cityCode: [
          { required: true, message: "所属城市不能为空", trigger: "blur" }
        ],
        areaCode: [
          { required: true, message: "所属分区不能为空", trigger: "blur" }
        ],
        policeCode: [
          { required: true, message: "派出所编码不能为空", trigger: "blur" }
        ],
        serviceCode: [
          { required: true, message: "场所编码不能为空", trigger: "blur" }
        ],
        // xpoint: [
        //   { required: true, message: "场所地图经度不能为空", trigger: "blur" }
        // ],
        // ypoint: [
        //   { required: true, message: "场所地图纬度不能为空", trigger: "blur" }
        // ],
        personName: [
          { required: true, message: "场所负责人不能为空", trigger: "blur" }
        ],
        personTel: [
          { required: true, message: "负责人电话不能为空", trigger: "blur" }
        ],
        capType: [
          { required: true, message: "采集类型不能为空", trigger: "change" }
        ],
        authOrgcode: [
          { required: true, message: "认证厂商统一社会信用代码不能为空", trigger: "blur" }
        ],
        serviceOrgcode: [
          { required: true, message: "运营厂商统一社会信用代码不能为空", trigger: "blur" }
        ],
        hardwOrgcode: [
          { required: true, message: "安全设备商统一社会信用代码不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
      },
      // 省市
      provinceList:[{areaCode:'110000',areaName:'北京市'}],
      // 地图相关
      openMap: false,
      modalObj: {
        title: 'WiFi场所经纬度获取',
        modal: true,
        width: '1050px',
        address: '',
        longitude: 116.4177920000,
        latitude: 39.9960840000,
        optType: '0'
      }
    };
  },
  created() {
    this.getAreaList();
 // 支持两种方式获取：路由参数和查询参数
    this.serviceCode = this.$route.params && this.$route.params.id;
    if (!this.serviceCode) {
      this.serviceCode = this.$route.query && this.$route.query.serviceCode;
    }
    // 设置地图默认参数
    var sTitle = 'WiFi场所经纬度获取';
    this.modalObj = {
      title: sTitle,
      modal: true,
      width: '1050px',
      address: this.form.address,
      longitude: this.form.xpoint || 116.4177920000,
      latitude: this.form.ypoint || 39.9960840000,
      optType: '0'
    };
    if (this.serviceCode) {
      this.getDetail();
    } else {
       this.reset();
    }
  },
  methods: {
    // 表单重置
    reset() {
      this.form = {
        serviceCode: null,
        serviceName: null,
        shecaiCode: null,
        address: null,
        zip: null,
        businessNature: null,
        principal: null,
        principalTel: null,
        inforMan: null,
        inforManTel: null,
        inforManEmail: null,
        producerCode: null,
        status: null,
        endingNumber: null,
        serverNumber: null,
        exitIp: null,
        authAccount: null,
        netType: null,
        practitionerNumber: null,
        netMonitorDepartment: null,
        netMonitorMan: null,
        netMonitorManTel: null,
        remark: null,
        serviceType: null,
        provinceCode: null,
        cityCode: null,
        areaCode: null,
        cityType: null,
        policeCode: null,
        mailAccount: null,
        mobileAccount: null,
        xpoint: null,
        ypoint: null,
        gisXpoint: null,
        gisYpoint: null,
        terminalFactoryOrgcode: null,
        orgCode: null,
        ipType: null,
        bandWidth: null,
        netLan: null,
        netLanTerminal: null,
        isSafe: null,
        wifiTerminal: null,
        principalCertType: null,
        principalCertCode: null,
        personName: null,
        personTel: null,
        personIdcard: null,
        personQq: null,
        inforManQq: null,
        startTime: null,
        endTime: null,
        capType: null,
        coordinateSystem: '百度坐标系',
        authOrgcode: null,
        serviceOrgcode: null,
        hardwOrgcode: null
      };
      this.resetForm("form");
    },
    /** 获取详情 */
    getDetail() {
      this.reset();
      if (!this.serviceCode) {
        return;
      }
      getWifiSite(this.serviceCode).then(response => {
        this.form = response.data;
        this.getPoliceList();
      });
    },
    /** 切换模块折叠状态 */
    toggleSection(section) {
      this.sectionCollapsed[section] = !this.sectionCollapsed[section];
    },
    /** 提交表单 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.submitting = true;
          if (this.form.serviceCode != null) {
            updateWifiSite(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.handleClose();
            }).finally(() => {
              this.submitting = false;
            });
          } else {
            addWifiSite(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.handleClose();
            }).finally(() => {
              this.submitting = false;
            });
          }
        } else {
          this.$modal.msgError("请完善必填信息");
          return false;
        }
      });
    },
    /** 返回按钮 */
    goBack() {
      this.$router.push('/data-management/wifi/site');
    },
    handleClose() {
      // 关闭当前页签
      this.$store.dispatch('tagsView/delView', this.$route).then(() => {
        // 页签关闭后跳转到列表页
       this.$router.go(-1);
      });
    },

    /** 获取区域列表 */
    getAreaList() {
      listArea({ parentId: '110000' }).then(response => {
        this.areaList = response.data || [];
      });
    },

    /** 获取派出所列表 */
    getPoliceList() {
      if (this.form.areaCode) {
        listArea({ parentId: this.form.areaCode }).then(response => {
          this.policeList = response.data || [];
        });
      } else {
        this.policeList = [];
        this.form.policeCode = null;
      }
    },

    /**经纬度 位置选择 */
    btnMapLocation() {
      // 检测是否已经输入了地址 没有输入地址 提示先输入地址
      if(this.form.address == undefined || this.form.address == null || this.form.address.trim() === '') {
        this.$message.warning('请先输入地址信息');
        return;
      }

      this.openMap = true; // 显示弹框
      var sTitle = 'WiFi场所经纬度获取';

      this.modalObj = {
        title: sTitle,
        modal: true,
        width: '1050px',
        address: this.form.address,
        longitude: this.form.xpoint || 116.4177920000,
        latitude: this.form.ypoint || 39.9960840000,
        optType: '0'
      };

      this.$nextTick(() => {
        this.$refs.PointMap1.open();
      })
    },

    /**选择点位 后更新经纬度信息  */
    getMapLocation(longitude, latitude, address) {
      console.log('--longitude=', longitude,' latitude=',latitude,' address=',address)
      // 更新地址和 经纬度信息字段
      this.form.xpoint = longitude;
      this.form.ypoint = latitude;
      this.form.address = address;
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .page-header {
    width: 94%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      justify-content: space-between;

      .page-title {
        margin: 0;
        font-size: 18px;
        color: #303133;
        font-weight: 600;
      }
    }
  }

  .form-container {
    width: 94%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
  }

  .form-section {
    margin-bottom: 24px;

    .section-header {
      background-color: #f6f7fb;
      padding: 12px 16px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 4px;
      margin-bottom: 16px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-line {
          width: 3px;
          height: 16px;
          background-color: #4584FF;
        }

        .title-text {
          font-size: 16px;
          font-weight: 600;
          color: #4584FF;
        }
      }

      .section-toggle {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #4584FF;
        font-size: 14px;

        .toggle-text {
          font-size: 14px;
        }

        i {
          font-size: 12px;
          transition: transform 0.3s;
        }
      }

      &:hover {
        background-color: #f0f2f5;
      }
    }

    .section-content {
      padding: 0 16px;
    }
  }
}

// 全局样式：确保表单标签不换行
::v-deep .el-form-item__label {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

// 必填字段样式
::v-deep .el-form-item.is-required .el-form-item__label:before {
  content: '*';
  color: #F56C6C;
  margin-right: 4px;
}

// 表单验证错误样式
::v-deep .el-form-item.is-error .el-input__inner,
::v-deep .el-form-item.is-error .el-select .el-input__inner,
::v-deep .el-form-item.is-error .el-textarea__inner {
  border-color: #F56C6C;
}

::v-deep .el-form-item__error {
  color: #F56C6C;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 100%;
  left: 0;
}

.form-footer {
  text-align: right;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}
</style>