<template>
  <div class="app-container">
    <div class="screen-management-layout">

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <div class="page-header">
          <div class="header-content">
            <h2 class="page-title">编辑等保备案11222</h2>
            <el-button icon="el-icon-arrow-left" size="small" @click="goBack">返回</el-button>
          </div>
        </div>

        <div class="form-container">
          <el-form ref="form" :model="form" :rules="rules" label-width="200px">
            <!-- 基本信息模块 -->
            <div class="form-section">
              <div class="section-header" @click="toggleSection('basic')">
                <div class="section-title">
                  <div class="title-line"></div>
                  <span class="title-text">基本信息</span>
                </div>
                <div class="section-toggle">
                  <span class="toggle-text">{{ sectionCollapsed.basic ? '展开' : '收起' }}</span>
                  <i :class="sectionCollapsed.basic ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
                </div>
              </div>

              <div v-show="!sectionCollapsed.basic" class="section-content">
                    <el-row :gutter="20">
                      <el-col :span="8">
                        <el-form-item label="网络名称" prop="networkName">
                          <el-input v-model="form.networkName" placeholder="请输入网络名称" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="单位名称" prop="unitName">
                          <el-input v-model="form.unitName" placeholder="请输入单位名称" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="统一社会信用代码" prop="creditCode">
                          <el-input v-model="form.creditCode" placeholder="请输入统一社会信用代码" />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="8">
                        <el-form-item label="联系人姓名" prop="contactName">
                          <el-input v-model="form.contactName" placeholder="联系人姓名" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="办公电话" prop="contactOfficePhone">
                          <el-input v-model="form.contactOfficePhone" placeholder="联系人办公电话" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="移动电话" prop="contactMobilePhone">
                          <el-input v-model="form.contactMobilePhone" placeholder="联系人移动电话" />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="8">
                        <el-form-item label="备案证明编号" prop="recordCertificateNumber">
                          <el-input v-model="form.recordCertificateNumber" placeholder="请输入备案证明编号" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="行业类别" prop="industryCategory">
                          <el-select v-model="form.industryCategory" placeholder="请选择行业类别" clearable style="width: 100%">
                            <el-option
                              v-for="dict in dict.type.lp_industry_category"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="保护等级" prop="protectionLevel">
                          <el-select v-model="form.protectionLevel" placeholder="请选择保护等级" clearable style="width: 100%">
                            <el-option
                              v-for="dict in dict.type.lp_protection_level"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="8">
                        <el-form-item label="备案日期" prop="filingDate">
                          <el-date-picker
                            v-model="form.filingDate"
                            type="date"
                            value-format="yyyy-MM-dd"
                            placeholder="备案日期"
                            style="width: 100%"
                            clearable
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="定级日期" prop="ratingDate">
                          <el-date-picker
                            v-model="form.ratingDate"
                            type="date"
                            value-format="yyyy-MM-dd"
                            placeholder="定级日期"
                            style="width: 100%"
                            clearable
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="投入运行使用日期" prop="commissioningUseDate">
                          <el-date-picker
                            v-model="form.commissioningUseDate"
                            type="date"
                            value-format="yyyy-MM-dd"
                            placeholder="请选择日期"
                            style="width: 100%"
                            clearable
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="8">
                        <el-form-item label="测评时间" prop="evaluationDate">
                          <el-date-picker
                            v-model="form.evaluationDate"
                            type="date"
                            value-format="yyyy-MM-dd"
                            placeholder="测评时间"
                            style="width: 100%"
                            clearable
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="业务类型" prop="businessType">
                          <el-input v-model="form.businessType" placeholder="请输入业务类型" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="服务范围" prop="serviceScope">
                          <el-input v-model="form.serviceScope" placeholder="请输入服务范围" />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="8">
                        <el-form-item label="服务对象" prop="serviceTarget">
                          <el-input v-model="form.serviceTarget" placeholder="请输入服务对象" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="覆盖范围" prop="coverageArea">
                          <el-select v-model="form.coverageArea" placeholder="请选择覆盖范围" clearable style="width: 100%">
                            <el-option
                              v-for="dict in dict.type.lp_coverage_area"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="系统编号" prop="systemNumber">
                          <el-input v-model="form.systemNumber" placeholder="请输入系统编号" />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="8">
                        <el-form-item label="网络性质" prop="networkProperties">
                          <el-select v-model="form.networkProperties" placeholder="请选择网络性质" clearable style="width: 100%">
                            <el-option
                              v-for="dict in dict.type.lp_network_properties"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="系统互联情况" prop="systemInterconnectionStatus">
                          <el-input v-model="form.systemInterconnectionStatus" placeholder="请输入系统互联情况" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="系统类型" prop="systemType">
                          <el-select v-model="form.systemType" placeholder="请选择系统类型" clearable style="width: 100%">
                            <el-option
                              v-for="dict in dict.type.lp_system_type"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="8">
                        <el-form-item label="数据来源" prop="dataSource">
                          <el-select v-model="form.dataSource" placeholder="请选择数据来源" clearable style="width: 100%">
                            <el-option
                              v-for="dict in dict.type.lp_data_source"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="专家评审情况" prop="expertReviewStatus">
                          <el-select v-model="form.expertReviewStatus" placeholder="专家评审情况" clearable style="width: 100%">
                            <el-option
                              v-for="dict in dict.type.lp_expert_review_status"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="状态" prop="status">
                          <el-select v-model="form.status" placeholder="请选择状态" clearable style="width: 100%">
                            <el-option
                              v-for="dict in dict.type.lp_status"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="24">
                        <el-form-item label="业务描述" prop="businessDesc">
                          <el-input type="textarea" :rows="3" v-model="form.businessDesc" placeholder="请输入业务描述" />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="8">
                        <el-form-item label="系统是否是分系统" prop="subsystemFlag">
                          <el-select v-model="form.subsystemFlag" placeholder="系统是否是分系统" clearable style="width: 100%">
                            <el-option
                              v-for="dict in dict.type.sys_yes_no"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="上级系统名称" prop="superiorSystemName">
                          <el-input v-model="form.superiorSystemName" placeholder="请输入上级系统名称" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="上级系统所属单位名称" prop="superiorSystemUnitName">
                          <el-input v-model="form.superiorSystemUnitName" placeholder="请输入上级系统所属单位名称" />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="8">
                        <el-form-item label="是否有主管部门" prop="competentDepartmentFlag">
                          <el-select v-model="form.competentDepartmentFlag" placeholder="是否有主管部门" clearable style="width: 100%">
                            <el-option
                              v-for="dict in dict.type.sys_yes_no"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="主管部门名称" prop="competentDepartmentName">
                          <el-input v-model="form.competentDepartmentName" placeholder="请输入主管部门名称" />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="8">
                        <el-form-item label="互联网IP地址" prop="internetIpAddress">
                          <el-input v-model="form.internetIpAddress" placeholder="请输入互联网IP地址" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="IPV6" prop="ipv6">
                          <el-input v-model="form.ipv6" placeholder="请输入IPV6" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="网址/域名" prop="websiteDomain">
                          <el-input v-model="form.websiteDomain" placeholder="请输入网址/域名" />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="8">
                        <el-form-item label="等级测评单位名称" prop="levelEvaluationUnit">
                          <el-input v-model="form.levelEvaluationUnit" placeholder="请输入等级测评单位名称" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="备案机关" prop="filingAuthority">
                          <el-input v-model="form.filingAuthority" placeholder="请输入备案机关" />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="8">
                        <el-form-item label="是否存在定级报告" prop="gradingReportFlag">
                          <el-select v-model="form.gradingReportFlag" placeholder="是否存在定级报告" clearable style="width: 100%">
                            <el-option
                              v-for="dict in dict.type.sys_yes_no"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="是否存在测评报告" prop="evaluationReportFlag">
                          <el-select v-model="form.evaluationReportFlag" placeholder="是否存在测评报告" clearable style="width: 100%">
                            <el-option
                              v-for="dict in dict.type.sys_yes_no"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="是否存在管理制度" prop="managementFlag">
                          <el-select v-model="form.managementFlag" placeholder="是否存在管理制度" clearable style="width: 100%">
                            <el-option
                              v-for="dict in dict.type.sys_yes_no"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="8">
                        <el-form-item label="是否存在实施方案" prop="implementationFlag">
                          <el-select v-model="form.implementationFlag" placeholder="是否存在实施方案" clearable style="width: 100%">
                            <el-option
                              v-for="dict in dict.type.sys_yes_no"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="是否存在检测报告" prop="detectionReportFlag">
                          <el-select v-model="form.detectionReportFlag" placeholder="是否存在检测报告" clearable style="width: 100%">
                            <el-option
                              v-for="dict in dict.type.sys_yes_no"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="是否存在专家评审意见" prop="reviewOpinionFlag">
                          <el-select v-model="form.reviewOpinionFlag" placeholder="是否存在专家评审意见" clearable style="width: 100%">
                            <el-option
                              v-for="dict in dict.type.sys_yes_no"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="8">
                        <el-form-item label="是否存在主管部门审批意见" prop="competentDepartmentApprovalFlag">
                          <el-select v-model="form.competentDepartmentApprovalFlag" placeholder="是否存在主管部门审批意见" clearable style="width: 100%">
                            <el-option
                              v-for="dict in dict.type.sys_yes_no"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="是否存在拓扑结构" prop="topologicalStructureFlag">
                          <el-select v-model="form.topologicalStructureFlag" placeholder="是否存在拓扑结构" clearable style="width: 100%">
                            <el-option
                              v-for="dict in dict.type.sys_yes_no"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="是否关基" prop="relatedFoundationFlag">
                          <el-select v-model="form.relatedFoundationFlag" placeholder="是否关基" clearable style="width: 100%">
                            <el-option
                              v-for="dict in dict.type.sys_yes_no"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </el-row>
              </div>
            </div>

            <div class="form-actions">
              <el-button @click="goBack">取消</el-button>
              <el-button type="primary" :loading="submitting" @click="submitForm">保存</el-button>
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getLevelprotect, addLevelprotect, updateLevelprotect } from "@/api/levelprotect/levelprotect";

export default {
  name: 'LevelprotectAdd',
  dicts: ['lp_industry_category', 'lp_data_source', 'lp_network_properties', 'lp_expert_review_status', 'sys_yes_no', 'lp_protection_level', 'lp_coverage_area', 'lp_system_type', 'lp_status'],
  components: {  },
  data() {
    return {
      submitting: false,
      levelProtectId: undefined,
      // 表单参数
      form: {},

      // 模块折叠状态
      sectionCollapsed: {
        basic: false
      },
      // 表单校验
      rules: {
        networkName: [
          { required: true, message: "网络名称不能为空", trigger: "blur" }
        ],
        unitName: [
          { required: true, message: "单位名称不能为空", trigger: "blur" }
        ],
        creditCode: [
          { required: true, message: "单位信用代码不能为空", trigger: "blur" }
        ],
        contactName: [
          { required: true, message: "联系人姓名不能为空", trigger: "blur" }
        ],
        contactOfficePhone: [
          { required: true, message: "联系人办公电话不能为空", trigger: "blur" }
        ],
        contactMobilePhone: [
          { required: true, message: "联系人移动电话不能为空", trigger: "blur" }
        ],
        recordCertificateNumber: [
          { required: true, message: "备案证明编号不能为空", trigger: "blur" }
        ],
        industryCategory: [
          { required: true, message: "行业类别不能为空", trigger: "change" }
        ],
        protectionLevel: [
          { required: true, message: "保护等级不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.levelProtectId = this.$route.params && this.$route.params.id;
    if (!this.levelProtectId) {
      this.levelProtectId = this.$route.query && this.$route.query.levelProtectId;
    }
    console.log(this.levelProtectId, 'pppppp')
    this.getDetail();
  },
  methods: {
    // 表单重置
    reset() {
      this.form = {
        levelProtectId: null,
        networkName: null,
        unitName: null,
        creditCode: null,
        contactName: null,
        contactOfficePhone: null,
        contactMobilePhone: null,
        recordCertificateNumber: null,
        industryCategory: null,
        protectionLevel: null,
        systemNumber: null,
        filingDate: null,
        ratingDate: null,
        commissioningUseDate: null,
        evaluationDate: null,
        businessType: null,
        serviceScope: null,
        serviceTarget: null,
        coverageArea: null,
        networkProperties: null,
        systemInterconnectionStatus: null,
        systemType: null,
        dataSource: null,
        expertReviewStatus: null,
        businessDesc: null,
        subsystemFlag: null,
        superiorSystemName: null,
        superiorSystemUnitName: null,
        competentDepartmentFlag: null,
        competentDepartmentName: null,
        internetIpAddress: null,
        ipv6: null,
        websiteDomain: null,
        levelEvaluationUnit: null,
        filingAuthority: null,
        gradingReportFlag: null,
        evaluationReportFlag: null,
        managementFlag: null,
        implementationFlag: null,
        detectionReportFlag: null,
        reviewOpinionFlag: null,
        competentDepartmentApprovalFlag: null,
        topologicalStructureFlag: null,
        relatedFoundationFlag: null,
        status: null
      };
      this.resetForm("form");
    },
    /** 获取详情 */
    getDetail() {
      this.reset();
      if (!this.levelProtectId) {
        return;
      }
      getLevelprotect(this.levelProtectId).then(response => {
        console.log('等级保护详情数据:', response);
        if (response.code === 200 && response.data) {
          this.form = { ...this.form, ...response.data };
          console.log('表单数据已更新:', this.form);
        } else {
          this.$modal.msgError('获取详情失败');
        }
      }).catch(error => {
        console.error('获取详情失败:', error);
        this.$modal.msgError('获取详情失败');
      });
    },
    /** 提交按钮 */
    submitForm() {
      console.log('levelprotect submitForm called');
      this.$refs["form"].validate((valid, invalidFields) => {
        console.log('Validation result:', valid);
        if (invalidFields) {
          console.log('Invalid fields:', invalidFields);
        }

        if (valid) {
          this.submitting = true;
          if (this.form.levelProtectId != null) {
            updateLevelprotect(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.$router.push("/data-management/levelprotect");
            }).catch((error) => {
              console.error('Update error:', error);
              this.$modal.msgError("修改失败：" + (error.message || error));
            }).finally(() => {
              this.submitting = false;
            });
          } else {
            addLevelprotect(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.$router.push("/data-management/levelprotect");
            }).catch((error) => {
              console.error('Add error:', error);
              this.$modal.msgError("新增失败：" + (error.message || error));
            }).finally(() => {
              this.submitting = false;
            });
          }
        } else {
          console.log('Form validation failed');
          this.$modal.msgError("表单验证失败，请检查必填项");
        }
      });
    },
    /** 返回按钮 */
    handleClose() {
      window.history.go(-1);
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 切换模块折叠状态
    toggleSection(section) {
      this.sectionCollapsed[section] = !this.sectionCollapsed[section];
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .page-header {
     width: 94%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      justify-content: space-between;
      .page-title {
        margin: 0;
        font-size: 18px;
        color: #303133;
      }
    }
  }

  .form-container {
    width: 94%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
  }

  .form-section {
    margin-bottom: 24px;

    .section-header {
      background-color: #f6f7fb;
      padding: 12px 16px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 4px;
      margin-bottom: 16px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-line {
          width: 3px;
          height: 16px;
          background-color: #4584FF;
        }

        .title-text {
          font-size: 16px;
          font-weight: 600;
          color: #4584FF;
        }
      }

      .section-toggle {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #4584FF;
        font-size: 14px;

        .toggle-text {
          font-size: 14px;
        }

        i {
          font-size: 12px;
        }
      }

      &:hover {
        background-color: #f0f2f5;
      }
    }

    .section-content {
      padding: 0 16px;
    }
  }

  .form-actions {
    background: white;
    padding: 20px;
    border-radius: 8px;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    border-top: 1px solid #f0f0f0;
  }
}

// 全局样式：确保表单标签不换行
::v-deep .el-form-item__label {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}
</style>
