<template>
  <div class="app-container">
    <div class="screen-management-layout">
      <!-- 左侧导航菜单 -->
      <basic-data-sidebar />

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <!-- 搜索区域 -->
        <common-search
          :search-config="searchConfig"
          :advanced-fields="advancedFields"
          :initial-form="searchForm"
          @search="handleSearch"
           @reset="handleReset"
        />

    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['levelprotect:levelprotect:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            icon="el-icon-upload2"
            size="mini"
            @click="handleImport"
            v-hasPermi="['levelprotect:levelprotect:add']"
          >导入</el-button>
        </el-col>
        <!-- <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['levelprotect:levelprotect:remove']"
          >删除</el-button>
        </el-col> -->
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['levelprotect:levelprotect:export']"
          >导出</el-button>
        </el-col>
        <el-col :span="1.5">
           <el-button
            type="success"
            icon="el-icon-plus"
            size="mini"
            @click="handleReminder"
            v-hasPermi="['levelprotect:levelprotect:add']"
          >设置到期提醒</el-button>
        </el-col>

        <div class="toolbar-right">
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </div>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="levelprotectList"
        @selection-change="handleSelectionChange"
        :header-cell-style="{ backgroundColor: '#EBF2FF' }"
        border
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="网络名称" align="center" prop="networkName" show-overflow-tooltip />
        <el-table-column label="单位名称" align="center" prop="unitName" show-overflow-tooltip>
          <template slot-scope="scope">
            <div class="site-name-container">
              <span>{{ scope.row.unitName }}</span>
              <span
                v-if="scope.row.dataUpdateStatus === 1"
                class="update-badge pending"
              >待更新</span>
              <span
                v-else-if="scope.row.dataUpdateStatus > 1"
                class="update-badge overdue"
              >已超期</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="联系人姓名" align="center" prop="contactName" min-width="110" show-overflow-tooltip />
        <el-table-column label="移动电话" align="center" prop="contactMobilePhone" min-width="120" show-overflow-tooltip />
        <el-table-column label="备案证明编号" align="center" prop="recordCertificateNumber" min-width="120" show-overflow-tooltip />
        <el-table-column label="保护等级" align="center" prop="protectionLevel" show-overflow-tooltip>
          <template slot-scope="scope">
            <dict-tag :options="dict.type.lp_protection_level" :value="scope.row.protectionLevel"/>
          </template>
        </el-table-column>
        <el-table-column label="系统编号" align="center" prop="systemNumber" show-overflow-tooltip />
        <el-table-column label="备案日期" align="center" prop="filingDate" min-width="100" />
        <el-table-column label="业务类型" align="center" prop="businessType" show-overflow-tooltip />
        <el-table-column label="数据来源" align="center" prop="dataSource" show-overflow-tooltip>
          <template slot-scope="scope">
            <dict-tag :options="dict.type.lp_data_source" :value="scope.row.dataSource"/>
          </template>
        </el-table-column>
        <el-table-column label="专家评审情况" align="center" prop="expertReviewStatus" min-width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <dict-tag :options="dict.type.lp_expert_review_status" :value="scope.row.expertReviewStatus"/>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status" min-width="80" show-overflow-tooltip>
          <template slot-scope="scope">
            <dict-tag :options="dict.type.lp_status" :value="scope.row.status"/>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="$router.push('/data-management/levelprotect/detail/'+scope.row.levelProtectId)"
              v-hasPermi="['levelprotect:levelprotect:query']"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="$router.push('/data-management/levelprotect/edit/'+scope.row.levelProtectId)"
              v-hasPermi="['levelprotect:levelprotect:edit']"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['levelprotect:levelprotect:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?previewUrl=' + upload.previewUrl+ '&keyareaId=&speciesId='"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="danger" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
      </div>
    </div>

          <!-- 导入对话框 -->
          <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
            <el-upload
              ref="upload"
              :limit="1"
              accept=".xlsx, .xls"
              :headers="upload.headers"
              :action="upload.url + '?previewUrl=' + upload.previewUrl+ '&keyareaId=&speciesId='"
              :disabled="upload.isUploading"
              :on-progress="handleFileUploadProgress"
              :on-success="handleFileSuccess"
              :auto-upload="false"
              drag
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <div class="el-upload__tip text-center" slot="tip">
                <span>仅允许导入xls、xlsx格式文件。</span>
                <el-link type="danger" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
              </div>
            </el-upload>
            <div slot="footer" class="dialog-footer">
              <el-button type="primary" @click="submitFileForm">确 定</el-button>
              <el-button @click="upload.open = false">取 消</el-button>
            </div>
          </el-dialog>

    <!-- 数据更新提醒组件 -->
    <data-update-reminder
      :visible.sync="reminderVisible"
      :biz-type="'LEVEL_PROTECT'"
    />
      </div>
    </div>
  </div>
</template>

<script>
import { listLevelprotect, delLevelprotect } from "@/api/levelprotect/levelprotect";
import { getToken } from "@/utils/auth";
import BasicDataSidebar from '@/components/BasicDataSidebar'
import CommonSearch from '@/components/CommonSearch'
import DataUpdateReminder from '@/components/DataUpdateReminder'

export default {
  name: "LevelprotectIndex",
  dicts: ['lp_industry_category', 'lp_data_source', 'lp_network_properties', 'lp_expert_review_status', 'sys_yes_no', 'lp_protection_level', 'lp_coverage_area', 'lp_system_type', 'lp_status'],
  components: { BasicDataSidebar, CommonSearch, DataUpdateReminder },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 等级保护台账表格数据
      levelprotectList: [],
      // 日期范围
      dateRange: [],
      // 提醒组件显示状态
      reminderVisible: false,

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        networkName: null,
        unitName: null,
        contactName: null,
        contactOfficePhone: null,
        contactMobilePhone: null,
        recordCertificateNumber: null,
        industryCategory: null,
        protectionLevel: null,
        systemNumber: null,
        filingDate: null,
        ratingDate: null,
        commissioningUseDate: null,
        businessType: null,
        serviceScope: null,
        serviceTarget: null,
        coverageArea: null,
        networkProperties: null,
        systemInterconnectionStatus: null,
        systemType: null,
        dataSource: null,
        expertReviewStatus: null,
        businessDesc: null,
        subsystemFlag: null,
        superiorSystemName: null,
        superiorSystemUnitName: null,
        competentDepartmentFlag: null,
        competentDepartmentName: null,
        internetIpAddress: null,
        ipv6: null,
        websiteDomain: null,
        levelEvaluationUnit: null,
        filingAuthority: null,
        gradingReportFlag: null,
        evaluationReportFlag: null,
        managementFlag: null,
        implementationFlag: null,
        detectionReportFlag: null,
        reviewOpinionFlag: null,
        competentDepartmentApprovalFlag: null,
        topologicalStructureFlag: null,
        relatedFoundationFlag: null,
        status: null,
      },
      // 搜索配置
      searchConfig: {
        label: '网络名称',
        key: 'networkName',
        placeholder: '请输入网络名称'
      },
      // 高级搜索字段
      advancedFields: [],
      // 搜索表单
      searchForm: {},
      // 导入参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        previewUrl: process.env.VUE_APP_BASE_API + "/file/download/",
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/levelprotect/levelprotect/importData"
      },
    };
  },
  created() {
    this.getList();
    this.initAdvancedFields();
  },
  watch: {
    // 监听字典数据变化
    'dict.type': {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.initAdvancedFields();
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 初始化高级搜索字段
    initAdvancedFields() {
      console.log('Levelprotect - 初始化高级搜索字段');
      console.log('Levelprotect - dict:', this.dict);
      console.log('Levelprotect - 字典数据:', {
        lp_protection_level: this.dict && this.dict.type && this.dict.type.lp_protection_level,
        lp_industry_category: this.dict && this.dict.type && this.dict.type.lp_industry_category,
        lp_coverage_area: this.dict && this.dict.type && this.dict.type.lp_coverage_area,
        lp_network_properties: this.dict && this.dict.type && this.dict.type.lp_network_properties,
        lp_system_type: this.dict && this.dict.type && this.dict.type.lp_system_type,
        lp_data_source: this.dict && this.dict.type && this.dict.type.lp_data_source,
        lp_expert_review_status: this.dict && this.dict.type && this.dict.type.lp_expert_review_status,
        lp_status: this.dict && this.dict.type && this.dict.type.lp_status
      });

      this.advancedFields = [
        {
          label: '单位名称',
          key: 'unitName',
          type: 'input',
          placeholder: '请输入单位名称',
          span: 6
        },
        {
          label: '联系人姓名',
          key: 'contactName',
          type: 'input',
          placeholder: '请输入联系人姓名',
          span: 6
        },
        {
          label: '办公电话',
          key: 'contactOfficePhone',
          type: 'input',
          placeholder: '请输入联系人办公电话',
          span: 6
        },
        {
          label: '移动电话',
          key: 'contactMobilePhone',
          type: 'input',
          placeholder: '请输入联系人移动电话',
          span: 6
        },
        {
          label: '备案证明编号',
          key: 'recordCertificateNumber',
          type: 'input',
          placeholder: '请输入备案证明编号',
          span: 6
        },
        {
          label: '行业类别',
          key: 'industryCategory',
          type: 'select',
          placeholder: '请选择行业类别',
          span: 6,
          options: (this.dict && this.dict.type && this.dict.type.lp_industry_category) ? this.dict.type.lp_industry_category.map(item => ({
            label: item.label,
            value: item.value
          })) : []
        },
        {
          label: '保护等级',
          key: 'protectionLevel',
          type: 'select',
          placeholder: '请选择保护等级',
          span: 6,
          options: (this.dict && this.dict.type && this.dict.type.lp_protection_level) ? this.dict.type.lp_protection_level.map(item => ({
            label: item.label,
            value: item.value
          })) : []
        },
        {
          label: '系统编号',
          key: 'systemNumber',
          type: 'input',
          placeholder: '请输入系统编号',
          span: 6
        },
        {
          label: '服务范围',
          key: 'serviceScope',
          type: 'input',
          placeholder: '请输入服务范围',
          span: 6
        },
        {
          label: '服务对象',
          key: 'serviceTarget',
          type: 'input',
          placeholder: '请输入服务对象',
          span: 6
        },
        {
          label: '覆盖范围',
          key: 'coverageArea',
          type: 'select',
          placeholder: '请选择覆盖范围',
          span: 6,
          options: (this.dict && this.dict.type && this.dict.type.lp_coverage_area) ? this.dict.type.lp_coverage_area.map(item => ({
            label: item.label,
            value: item.value
          })) : []
        },
        {
          label: '网络性质',
          key: 'networkProperties',
          type: 'select',
          placeholder: '请选择网络性质',
          span: 6,
          options: (this.dict && this.dict.type && this.dict.type.lp_network_properties) ? this.dict.type.lp_network_properties.map(item => ({
            label: item.label,
            value: item.value
          })) : []
        },
        {
          label: '系统类型',
          key: 'systemType',
          type: 'select',
          placeholder: '请选择系统类型',
          span: 6,
          options: (this.dict && this.dict.type && this.dict.type.lp_system_type) ? this.dict.type.lp_system_type.map(item => ({
            label: item.label,
            value: item.value
          })) : []
        },
        {
          label: '数据来源',
          key: 'dataSource',
          type: 'select',
          placeholder: '请选择数据来源',
          span: 6,
          options: (this.dict && this.dict.type && this.dict.type.lp_data_source) ? this.dict.type.lp_data_source.map(item => ({
            label: item.label,
            value: item.value
          })) : []
        },
        {
          label: '专家评审情况',
          key: 'expertReviewStatus',
          type: 'select',
          placeholder: '请选择专家评审情况',
          span: 6,
          options: (this.dict && this.dict.type && this.dict.type.lp_expert_review_status) ? this.dict.type.lp_expert_review_status.map(item => ({
            label: item.label,
            value: item.value
          })) : []
        },
        {
          label: '状态',
          key: 'status',
          type: 'select',
          placeholder: '请选择状态',
          span: 6,
          options: (this.dict && this.dict.type && this.dict.type.lp_status) ? this.dict.type.lp_status.map(item => ({
            label: item.label,
            value: item.value
          })) : []
        },
        {
          label: '备案日期',
          key: 'dateRange',
          type: 'daterange',
          placeholder: '请选择备案日期',
          span: 6
        }
      ];

      console.log('Levelprotect - 更新后的选项数量:', {
        lp_protection_level: this.advancedFields.find(f => f.key === 'protectionLevel')?.options?.length,
        lp_industry_category: this.advancedFields.find(f => f.key === 'industryCategory')?.options?.length,
        lp_coverage_area: this.advancedFields.find(f => f.key === 'coverageArea')?.options?.length,
        lp_network_properties: this.advancedFields.find(f => f.key === 'networkProperties')?.options?.length,
        lp_system_type: this.advancedFields.find(f => f.key === 'systemType')?.options?.length,
        lp_data_source: this.advancedFields.find(f => f.key === 'dataSource')?.options?.length,
        lp_expert_review_status: this.advancedFields.find(f => f.key === 'expertReviewStatus')?.options?.length,
        lp_status: this.advancedFields.find(f => f.key === 'status')?.options?.length
      });
    },

    // 处理搜索
    handleSearch(searchForm) {
      this.queryParams = {
        ...this.queryParams,
        ...searchForm,
        pageNum: 1
      };
      // 处理日期范围
      if (searchForm.dateRange && searchForm.dateRange.length === 2) {
        this.dateRange = searchForm.dateRange;
        delete this.queryParams.dateRange;
      } else {
        this.dateRange = [];
      }
      this.getList();
    },
    // 处理重置
    handleReset(searchForm) {
      this.searchForm = searchForm;
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
         ...searchForm,
      };
      this.dateRange = [];
      this.getList();
    },
    /** 查询等级保护台账列表 */
    getList() {
      this.loading = true;
      listLevelprotect(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.levelprotectList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        levelProtectId: null,
        networkName: null,
        unitName: null,
        contactName: null,
        contactOfficePhone: null,
        contactMobilePhone: null,
        recordCertificateNumber: null,
        industryCategory: null,
        protectionLevel: null,
        systemNumber: null,
        filingDate: null,
        ratingDate: null,
        commissioningUseDate: null,
        businessType: null,
        serviceScope: null,
        serviceTarget: null,
        coverageArea: null,
        networkProperties: null,
        systemInterconnectionStatus: null,
        systemType: null,
        dataSource: null,
        expertReviewStatus: null,
        businessDesc: null,
        subsystemFlag: null,
        superiorSystemName: null,
        superiorSystemUnitName: null,
        competentDepartmentFlag: null,
        competentDepartmentName: null,
        internetIpAddress: null,
        ipv6: null,
        websiteDomain: null,
        levelEvaluationUnit: null,
        filingAuthority: null,
        gradingReportFlag: null,
        evaluationReportFlag: null,
        managementFlag: null,
        implementationFlag: null,
        detectionReportFlag: null,
        reviewOpinionFlag: null,
        competentDepartmentApprovalFlag: null,
        topologicalStructureFlag: null,
        relatedFoundationFlag: null,
        status: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.levelProtectId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push("/data-management/levelprotect/add");
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const levelProtectIds = row.levelProtectId || this.ids;
      this.$modal.confirm('是否确认删除等级保护台账编号为"' + levelProtectIds + '"的数据项？').then(function() {
        return delLevelprotect(levelProtectIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('levelprotect/levelprotect/export', {
        ...this.queryParams
      }, `等级保护_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "数据导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    async importTemplate() {
      const url = process.env.VUE_APP_BASE_API+'/file/download/'+vueConfig.template_path_levelprotect; // 替换成你要下载的文件的URL
      const fileName = '等级保护导入模板.xlsx'; // 自定义文件名

      const response = await fetch(url);
      const blob = await response.blob();
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      let that = this;
      that.loading = false;
      that.upload.open = false;
      that.upload.isUploading = false;
      that.$refs.upload.clearFiles();
      that.importLog = response.msg;

      that.$confirm(
        "<div style=' width: 600px; overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"
        + response.msg
        + "</div>",
        '导入结果',
        {
          confirmButtonText: '下载导入日志',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          showCancelButton: true,
          showClose: false,
          closeOnClickModal: false,
        })
        .then(() => {
          if(that.importLog){
            that.downloadImportLog('log.txt', that.importLog)
          }else{
            that.$modal.msgError("暂无日志");
          }
        })
        .catch(() => {

        });

      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.upload.open = false;
      this.$refs.upload.submit();
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.loading = true;
      this.upload.isUploading = true;
    },
    downloadImportLog(fileName, data){
      //创建一个a链接，用于触发下载事件的载体
      let aLink = document.createElement('a')
      //将实参字符串转二进制对象，如果不是文本可以通过添加第二个参数指定编码
      let blob = new Blob([this.htmlToString(data)]);
      //指定要下载的文件名(浏览器下载时，会根据文件后缀名指定解码)
      aLink.download = fileName
      //给a链接配置href指向刚才的二进制对象
      aLink.href = URL.createObjectURL(blob)
      //触发事件
      aLink.click()
    },
    htmlToString(html) {
      html = html.replaceAll('<br/>','\n');
      return html;
    },

    // 设置提醒
    handleReminder() {
      this.reminderVisible = true;
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .screen-management-layout {
    display: flex;
    gap: 20px;
    height: 100%;

    .content-area {
      flex: 1;
      min-width: 0;
    }
  }

  .action-bar {
    background: white;
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
  }

  .table-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
  }

  .toolbar-right {
    float: right;
    display: flex;
    align-items: center;
    gap: 12px;
  }
}

// 全局样式：确保表单标签不换行
::v-deep .el-form-item__label {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

// 名称角标样式
.site-name-container {
  position: relative;
  display: inline-block;
  padding: 12px 12px 0 0;
  .update-badge {
    position: absolute;
    top: -2px;
    right: -10px;
    font-size: 10px;
    padding: 1px 4px;
    border-radius: 2px;
    // color: white;
    white-space: nowrap;
    z-index: 1;

    &.pending {
      color: #FE9400;
    }

    &.overdue {
      color: #FF2A2A;
    }
  }
}
</style>
