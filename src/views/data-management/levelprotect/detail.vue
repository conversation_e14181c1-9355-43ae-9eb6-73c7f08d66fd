<template>
<div style="display:flex;background: #f5f7fa;">
  <section class="app-container" :class="{ 'expanded': isRightCollapsed }" :style="{ width: `calc(100% - ${rightPanelWidth}px)` }">
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">等级保护详情</h2>
        <div class="header-actions">
          <el-button icon="el-icon-arrow-left" size="small" @click="goBack">返回</el-button>
        </div>
      </div>
    </div>

    <div class="detail-container" v-loading="loading">
      <!-- 基本信息模块 -->
      <div id="basic-info" class="detail-section">
        <div class="section-header" @click="toggleSection('basic')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">基本信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.basic ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.basic ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>

        <div v-show="!sectionCollapsed.basic" class="section-content">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="网络名称">
              {{ form.networkName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="单位名称">
              {{ form.unitName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="统一社会信用代码">
              {{ form.creditCode || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="联系人姓名">
              {{ form.contactName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="办公电话">
              {{ form.contactOfficePhone || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="移动电话">
              {{ form.contactMobilePhone || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="备案证明编号">
              {{ form.recordCertificateNumber || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="行业类别">
              <dict-tag :options="dict.type.lp_industry_category" :value="form.industryCategory" />
            </el-descriptions-item>
            <el-descriptions-item label="保护等级">
              <dict-tag :options="dict.type.lp_protection_level" :value="form.protectionLevel" />
            </el-descriptions-item>
            <el-descriptions-item label="备案日期">
              {{ form.filingDate || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="定级日期">
              {{ form.ratingDate || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="投入运行使用日期">
              {{ form.commissioningUseDate || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="测评时间">
              {{ form.evaluationDate || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="业务类型">
              {{ form.businessType || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="服务范围">
              {{ form.serviceScope || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="服务对象">
              {{ form.serviceTarget || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="覆盖范围">
              <dict-tag :options="dict.type.lp_coverage_area" :value="form.coverageArea" />
            </el-descriptions-item>
            <el-descriptions-item label="系统编号">
              {{ form.systemNumber || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="网络性质">
              <dict-tag :options="dict.type.lp_network_properties" :value="form.networkProperties" />
            </el-descriptions-item>
            <el-descriptions-item label="系统互联情况">
              {{ form.systemInterconnectionStatus || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="系统类型">
              <dict-tag :options="dict.type.lp_system_type" :value="form.systemType" />
            </el-descriptions-item>
            <el-descriptions-item label="数据来源">
              <dict-tag :options="dict.type.lp_data_source" :value="form.dataSource" />
            </el-descriptions-item>
            <el-descriptions-item label="专家评审情况">
              <dict-tag :options="dict.type.lp_expert_review_status" :value="form.expertReviewStatus" />
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <dict-tag :options="dict.type.lp_status" :value="form.status" />
            </el-descriptions-item>
            <el-descriptions-item label="业务描述" :span="3">
              {{ form.businessDesc || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 系统信息模块 -->
      <div id="system-info" class="detail-section">
        <div class="section-header" @click="toggleSection('system')">
          <div class="section-title">
            <div class="title-line"></div>
            <span class="title-text">系统信息</span>
          </div>
          <div class="section-toggle">
            <span class="toggle-text">{{ sectionCollapsed.system ? '展开' : '收起' }}</span>
            <i :class="sectionCollapsed.system ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>

        <div v-show="!sectionCollapsed.system" class="section-content">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="系统是否是分系统">
              <dict-tag :options="dict.type.sys_yes_no" :value="form.subsystemFlag" />
            </el-descriptions-item>
            <el-descriptions-item label="上级系统名称">
              {{ form.superiorSystemName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="上级系统所属单位名称">
              {{ form.superiorSystemUnitName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="是否有主管部门">
              <dict-tag :options="dict.type.sys_yes_no" :value="form.competentDepartmentFlag" />
            </el-descriptions-item>
            <el-descriptions-item label="主管部门名称">
              {{ form.competentDepartmentName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="互联网IP地址">
              {{ form.internetIpAddress || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="IPV6">
              {{ form.ipv6 || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="网址/域名">
              {{ form.websiteDomain || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="等级测评单位名称">
              {{ form.levelEvaluationUnit || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="备案机关">
              {{ form.filingAuthority || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="是否关基">
              <dict-tag :options="dict.type.sys_yes_no" :value="form.relatedFoundationFlag" />
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </div>
  </section>

  <!-- 右侧数据维护记录区域 -->
  <collapsible-detail-right
    @collapse-change="handleRightCollapseChange"
    @width-change="handleRightWidthChange"
  >
    <!-- 锚点导航组件 -->
    <template #anchor>
      <anchor-navigation
        :anchor-items="anchorItems"
        @anchor-change="handleAnchorChange"
      />
    </template>

    <!-- 数据维护时间线 -->
    <template #timeline>
      <data-maintenance-timeline
        :biz-type="'LEVEL_PROTECT'"
        :biz-id="form.levelProtectId"
        v-if="form.levelProtectId"
      />
    </template>
  </collapsible-detail-right>
</div>
</template>

<script>
import { getLevelprotect } from "@/api/levelprotect/levelprotect";
import DataMaintenanceTimeline from '@/components/DataMaintenanceTimeline'
import AnchorNavigation from '@/components/AnchorNavigation'
import CollapsibleDetailRight from '@/components/CollapsibleDetailRight'

export default {
  name: 'LevelprotectDetail',
  dicts: ['lp_industry_category', 'lp_data_source', 'lp_network_properties', 'lp_expert_review_status', 'sys_yes_no', 'lp_protection_level', 'lp_coverage_area', 'lp_system_type', 'lp_status'],
  components: {
    DataMaintenanceTimeline,
    AnchorNavigation,
    CollapsibleDetailRight
  },
  data() {
    return {
      loading: false,
      levelProtectId: undefined,
      // 表单参数
      form: {},

      // 模块折叠状态
      sectionCollapsed: {
        basic: false,
        system: false
      },

      // 锚点导航配置
      anchorItems: [
        { id: 'basic-info', label: '基本信息' },
        { id: 'system-info', label: '系统信息' }
      ],

      // 右侧面板收起状态
      isRightCollapsed: false,
      // 右侧面板宽度
      rightPanelWidth: 360
    };
  },
  created() {
    this.levelProtectId = this.$route.params && this.$route.params.id;
    this.getDetail();
  },
  methods: {
    // 表单重置
    reset() {
      this.form = {
        creditCode: '',
        levelProtectId: null,
        networkName: null,
        unitName: null,
        contactName: null,
        contactOfficePhone: null,
        contactMobilePhone: null,
        recordCertificateNumber: null,
        industryCategory: null,
        protectionLevel: null,
        systemNumber: null,
        filingDate: null,
        ratingDate: null,
        commissioningUseDate: null,
        evaluationDate: null,
        businessType: null,
        serviceScope: null,
        serviceTarget: null,
        coverageArea: null,
        networkProperties: null,
        systemInterconnectionStatus: null,
        systemType: null,
        dataSource: null,
        expertReviewStatus: null,
        businessDesc: null,
        subsystemFlag: null,
        superiorSystemName: null,
        superiorSystemUnitName: null,
        competentDepartmentFlag: null,
        competentDepartmentName: null,
        internetIpAddress: null,
        ipv6: null,
        websiteDomain: null,
        levelEvaluationUnit: null,
        filingAuthority: null,
        gradingReportFlag: null,
        evaluationReportFlag: null,
        managementFlag: null,
        implementationFlag: null,
        detectionReportFlag: null,
        reviewOpinionFlag: null,
        competentDepartmentApprovalFlag: null,
        topologicalStructureFlag: null,
        relatedFoundationFlag: null,
        status: null
      };
    },
    /** 获取详情 */
    getDetail() {
      this.reset();
      if (!this.levelProtectId) {
        return;
      }
      getLevelprotect(this.levelProtectId).then(response => {
        this.form = response.data;
      });
    },
    /** 返回按钮 */
    handleClose() {
      this.$router.push("/data-management/levelprotect");
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 切换模块折叠状态
    toggleSection(section) {
      this.sectionCollapsed[section] = !this.sectionCollapsed[section];
    },

    // 处理锚点变化
    handleAnchorChange(anchorId) {
      console.log('当前激活的锚点:', anchorId);
    },

    // 处理右侧面板收起展开
    handleRightCollapseChange(isCollapsed) {
      this.isRightCollapsed = isCollapsed;
    },

    // 处理右侧面板宽度变化
    handleRightWidthChange(width) {
      this.rightPanelWidth = width;
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
  transition: width 0.3s ease;

  &.expanded {
    width: calc(100% - 60px) !important;
  }

  .page-header {
    width: 99%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 0 auto 20px auto;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      justify-content: space-between;
      .page-title {
        margin: 0;
        font-size: 18px;
        color: #303133;
      }
    }
  }

  .detail-container {
    width: 99%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;

    .detail-section {
      margin-bottom: 24px;

      .section-header {
        background-color: #f6f7fb;
        padding: 12px 16px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 4px;
        margin-bottom: 16px;

        .section-title {
          display: flex;
          align-items: center;
          gap: 8px;

          .title-line {
            width: 3px;
            height: 16px;
            background-color: #4584FF;
          }

          .title-text {
            font-size: 16px;
            font-weight: 600;
            color: #4584FF;
          }
        }

        .section-toggle {
          display: flex;
          align-items: center;
          gap: 4px;
          color: #4584FF;
          font-size: 14px;

          .toggle-text {
            font-size: 14px;
          }

          i {
            font-size: 12px;
          }
        }

        &:hover {
          background-color: #f0f2f5;
        }
      }

      .section-content {
        padding: 0 16px;
      }
    }
  }
}


</style>