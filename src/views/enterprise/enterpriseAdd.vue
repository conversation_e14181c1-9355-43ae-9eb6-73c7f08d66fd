<template>
  <div class="app-container">
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">{{ isEdit ? '编辑企业' : '新增企业' }}</h2>
        <el-button icon="el-icon-arrow-left" size="small" @click="goBack">返回</el-button>
      </div>
    </div>

    <div class="form-container">
      <el-form ref="form" :model="form" :rules="rules" label-width="200px">
        <!-- 单位主体信息模块 -->
        <div class="form-section">
          <div class="section-header" @click="toggleSection('basic')">
            <div class="section-title">
              <div class="title-line"></div>
              <span class="title-text">单位主体信息</span>
            </div>
            <div class="section-toggle">
              <span class="toggle-text">{{ sectionCollapsed.basic ? '展开' : '收起' }}</span>
              <i :class="sectionCollapsed.basic ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            </div>
          </div>

          <div v-show="!sectionCollapsed.basic" class="section-content">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="统一社会信用代码" prop="creditCode">
                  <el-input v-model="form.creditCode" :disabled="isEdit? true: false" placeholder="请输入统一社会信用代码" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="所属区域" prop="areaCode">
                  <el-select v-model="form.areaCode" style="width: 100%;" placeholder="请选择所属区域" @change="getPoliceList" clearable>
                    <el-option
                      v-for="item in areaList"
                      :key="item.areaCode"
                      :label="item.areaName"
                      :value="item.areaCode"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="单位性质" prop="property">
                  <el-select v-model="form.property" style="width: 100%;" filterable placeholder="请选择单位性质" clearable>
                    <el-option
                      v-for="dict in dict.type.ws_filing_subject"
                      :key="dict.label"
                      :label="dict.label"
                      :value="dict.label"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="16">
                <el-form-item label="单位名称" prop="name">
                  <el-input v-model="form.name" placeholder="请输入单位名称" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="人员规模" prop="staffNumRange">
                  <el-select v-model="form.staffNumRange" style="width: 100%;" clearable placeholder="请选择人员规模">
                    <el-option key="10" value="10人以下" label="10人以下" />
                    <el-option key="30" value="10-30人" label="10-30人" />
                    <el-option key="50" value="30-50人" label="30-50人" />
                    <el-option key="100" value="50-100人" label="50-100人" />
                    <el-option key="200" value="100-200人" label="100-200人" />
                    <el-option key="500" value="200-500人" label="200-500人" />
                    <el-option key="1000" value="500-1000人" label="500-1000人" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="状态" prop="status">
                  <el-select style="width: 100%;" v-model="form.status" placeholder="请选择营业状态" clearable>
                    <el-option key="1" value="1" label="有效" />
                    <el-option key="0" value="0" label="无效" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="公安备案号" prop="publicCode">
                  <el-input v-model="form.publicCode" placeholder="公安备案号" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="实际办公地址" prop="realLocation">
                  <el-input v-model="form.realLocation" placeholder="请输入实际办公地址" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="单位注册地址" prop="regLocation">
                  <el-input v-model="form.regLocation" placeholder="请输入单位注册地址" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="营业执照" prop="licensePic">
                  <image-upload v-model="form.licensePic"/>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 法人信息模块 -->
        <div class="form-section">
          <div class="section-header" @click="toggleSection('legal')">
            <div class="section-title">
              <div class="title-line"></div>
              <span class="title-text">法人信息</span>
            </div>
            <div class="section-toggle">
              <span class="toggle-text">{{ sectionCollapsed.legal ? '展开' : '收起' }}</span>
              <i :class="sectionCollapsed.legal ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            </div>
          </div>

          <div v-show="!sectionCollapsed.legal" class="section-content">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="法人" prop="corporationName">
                  <el-input v-model="form.corporationName" placeholder="法人" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="法人电话" prop="corporationPhoneNumber">
                  <el-input v-model="form.corporationPhoneNumber" placeholder="法人电话" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="证件类型" prop="corporationCertificateType">
                  <el-select v-model="form.corporationCertificateType" style="width: 100%;" placeholder="请选择证件类型" clearable>
                    <el-option
                      v-for="dict in certificateTypeList"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="证件号码" prop="corporationCertificateCode">
                  <el-input v-model="form.corporationCertificateCode" placeholder="法人证件号码" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="证件有效期" prop="corporationCertificateValidityPeriod">
                  <el-input v-model="form.corporationCertificateValidityPeriod" placeholder="证件有效期" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="法人证件(人像)" prop="corporationCertificateBack">
                  <image-upload v-model="form.corporationCertificateBack" :limit="1"/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="法人证件照(国徽)" prop="corporationCertificateFront">
                  <image-upload v-model="form.corporationCertificateFront" :limit="1"/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="法人证件(手持)" prop="corporationCertificateHand">
                  <image-upload v-model="form.corporationCertificateHand" :limit="1"/>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 负责人信息模块 -->
        <div class="form-section">
          <div class="section-header" @click="toggleSection('principal')">
            <div class="section-title">
              <div class="title-line"></div>
              <span class="title-text">负责人信息</span>
            </div>
            <div class="section-toggle">
              <span class="toggle-text">{{ sectionCollapsed.principal ? '展开' : '收起' }}</span>
              <i :class="sectionCollapsed.principal ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            </div>
          </div>

          <div v-show="!sectionCollapsed.principal" class="section-content">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="负责人" prop="handlerName">
                  <el-input v-model="form.handlerName" placeholder="负责人" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="负责人电话" prop="handlerPhoneNumber">
                  <el-input v-model="form.handlerPhoneNumber" placeholder="负责人电话" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="办公室电话" prop="handlerOfficePhoneNumber">
                  <el-input v-model="form.handlerOfficePhoneNumber" placeholder="办公室电话" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="证件类型" prop="handlerCertificateType">
                  <el-select v-model="form.handlerCertificateType" style="width: 100%;" placeholder="请选择证件类型" clearable>
                    <el-option
                      v-for="dict in certificateTypeList"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="负责人证件号码" prop="handlerCertificateCode">
                  <el-input v-model="form.handlerCertificateCode" placeholder="负责人证件号码" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="证件有效期" prop="handlerCertificateValidityPeriod">
                  <el-input v-model="form.handlerCertificateValidityPeriod" placeholder="证件有效期" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="16">
                <el-form-item label="常驻地址" prop="handlerPermanentAddress">
                  <el-input v-model="form.handlerPermanentAddress" placeholder="常驻地址" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="电子邮件" prop="handlerEmail">
                  <el-input v-model="form.handlerEmail" placeholder="负责人电子邮件" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="负责人证件(人像)" prop="handlerCertificateBack">
                  <image-upload v-model="form.handlerCertificateBack" :limit="1"/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="负责人证件照(国徽)" prop="handlerCertificateFront">
                  <image-upload v-model="form.handlerCertificateFront" :limit="1"/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="负责人证件(手持)" prop="handlerCertificateHand">
                  <image-upload v-model="form.handlerCertificateHand" :limit="1"/>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="form-footer">
          <el-button type="primary" @click="submitForm" :loading="loading">确 定</el-button>
          <el-button @click="goBack">取 消</el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
import { listArea } from '@/api/system/area';
import { getFlowNo, addEnterprise, getEnterprise } from '@/api/erp';
import ImageUpload from '@/components/ImageUpload';

export default {
  name: "EnterpriseAdd",
  dicts: ['ws_filing_subject', 'certificate_type'],
  components: {
    ImageUpload
  },
  data() {
    return {
      loading: false,
      isEdit: false,
      id: '',
      flowNo: '',

      // 折叠状态
      sectionCollapsed: {
        basic: false,
        legal: false,
        principal: false
      },

      // 证件类型
      certificateTypeList: [],

      form: {
        property: null,
        status: null,
        publicCode: null,
        staffNumRange: null,
        regLocation: null,
        realLocation: null,
        licensePic: null,
        corporationName: null,
        corporationPhoneNumber: null,
        corporationCertificateType: null,
        corporationCertificateValidityPeriod: null,
        corporationCertificateCode: null,
        corporationCertificateBack: null,
        corporationCertificateFront: null,
        corporationCertificateHand: null,
        buzType: '01',
        handlerName: null,
        handlerPhoneNumber: null,
        handlerOfficePhoneNumber: null,
        handlerEmail: null,
        handlerPermanentAddress: null,
        handlerCertificateType: null,
        handlerCertificateValidityPeriod: null,
        handlerCertificateCode: null,
        handlerCertificateBack: null,
        handlerCertificateFront: null,
        handlerCertificateHand: null,
      },

      // 区域列表
      areaList: [],

      // 表单校验
      rules: {
        creditCode: [
          { required: true, message: "统一社会信用代码不能为空", trigger: "blur" }
        ],
        name: [
          { required: true, message: "单位名称不能为空", trigger: "blur" }
        ]
      }
    }
  },
  watch: {
    // 监听路由变化，处理新增和编辑模式切换
    '$route'(to, from) {
      console.log('路由变化:', from.path, '->', to.path);
      console.log('路由参数:', to.query);

      // 重新初始化页面
      this.initPage();
    }
  },
  created() {
    this.initPage();
  },
  methods: {
    // 初始化页面
    initPage() {
      console.log('初始化页面，路由参数:', this.$route.query);

      // 获取基础数据
      this.getAreaList();
      this.certificateTypeList = this.dict.type.certificate_type;

      // 获取路由参数
      this.id = this.$route.query.id;
      this.isEdit = this.$route.query.mode === 'edit';

      console.log('页面模式:', this.isEdit ? '编辑' : '新增', 'ID:', this.id);

      if (this.id && this.isEdit) {
        // 编辑模式：加载企业信息
        this.getEnterpriseInfo();
      } else {
        // 新增模式：重置表单
        this.resetForm();
      }

      // 获取流水号
      let params = { flowType: 1 };
      getFlowNo(params).then(response => {
        this.flowNo = response.data;
      });
    },

    // 重置表单
    resetForm() {
      this.form = {
        property: null,
        status: null,
        publicCode: null,
        staffNumRange: null,
        regLocation: null,
        realLocation: null,
        licensePic: null,
        corporationName: null,
        corporationPhoneNumber: null,
        corporationCertificateType: null,
        corporationCertificateValidityPeriod: null,
        corporationCertificateCode: null,
        corporationCertificateBack: null,
        corporationCertificateFront: null,
        corporationCertificateHand: null,
        buzType: '01',
        handlerName: null,
        handlerPhoneNumber: null,
        handlerOfficePhoneNumber: null,
        handlerEmail: null,
        handlerPermanentAddress: null,
        handlerCertificateType: null,
        handlerCertificateValidityPeriod: null,
        handlerCertificateCode: null,
        handlerCertificateBack: null,
        handlerCertificateFront: null,
        handlerCertificateHand: null,
        creditCode: null,
        areaCode: null,
        name: null
      };

      // 重置表单验证状态
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },

    // 切换模块折叠状态
    toggleSection(section) {
      this.$set(this.sectionCollapsed, section, !this.sectionCollapsed[section]);
    },

    // 获取单位主体详情
    getEnterpriseInfo() {
      this.loading = true;
      getEnterprise({ id: this.id }).then(response => {
        if (response.code == '200') {
          this.form = response.data;
          if (this.form.areaCode != null) {
            this.getPoliceList();
          }
        } else {
          this.form = {};
        }
        this.loading = false;
      });
    },

    // 获取所属区域
    getAreaList() {
      let params = {
        parentId: '110000'
      }
      listArea(params).then(response => {
        this.areaList = response.data;
      });
    },

    // 获取所属派出所
    getPoliceList() {
      let params = {
        parentId: this.form.areaCode
      }
      listArea(params).then(response => {
        this.policeList = response.data;
      });
    },

    // 提交表单
    submitForm() {
      this.form.flowNo = this.flowNo;

      if (this.form.handlerCertificateType == '') {
        this.form.handlerCertificateType = null;
      }
      if (this.form.corporationCertificateType == '') {
        this.form.corporationCertificateType = null;
      }

      this.$refs["form"].validate(valid => {
        if (valid) {
          this.loading = true;
          addEnterprise(this.form).then(res => {
            this.loading = false;
            this.$modal.msgSuccess(this.isEdit ? "修改成功" : "新增成功");
            this.resetForm()
            this.goBack();
          }).catch(() => {
            this.loading = false;
          })
        }
      });
    },

    // 返回
    goBack() {
      this.resetForm()
      this.$router.push('/enterprise/index');
    }
  }
}
</script>

<style scoped>
.page-header {
  background: #f6f7fb;
  padding: 16px 24px;
  margin-bottom: 16px;
  border-radius: 4px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.form-container {
  background: #fff;
  border-radius: 4px;
  padding: 24px;
}

.form-section {
  margin-bottom: 24px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.section-header {
  background: #f6f7fb;
  padding: 12px 16px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e4e7ed;
}

.section-title {
  display: flex;
  align-items: center;
}

.title-line {
  width: 4px;
  height: 16px;
  background: #4584ff;
  margin-right: 8px;
}

.title-text {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.section-toggle {
  display: flex;
  align-items: center;
  color: #606266;
  font-size: 14px;
}

.toggle-text {
  margin-right: 4px;
}

.section-content {
  padding: 24px;
}

.form-footer {
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid #e4e7ed;
  margin-top: 24px;
}

/* 表单标签不换行 */
::v-deep .el-form-item__label {
  white-space: nowrap !important;
}
</style>