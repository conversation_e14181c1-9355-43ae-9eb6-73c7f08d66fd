<template>
  <div class="app-container enterprise-archive">
    <section class="enterprise-archive-content">
      <div class="enterprise-header">
        <div class="header-left">

          <div class="search-container">
            <div class="complex-search">
              <el-select v-model="searchType" class="search-type">
                <el-option label="全部单位" value="all"></el-option>
                <el-option label="单位名称" value="name"></el-option>
                <el-option label="统一社会信用编码" value="creditCode"></el-option>
                <el-option label="法人" value="legalPerson"></el-option>
              </el-select>
              <el-input
                v-model="searchKeyword"
                placeholder="请输入关键词"
                clearable
                @keyup.enter.native="handleSearch"
              >
                <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
              </el-input>
            </div>
          </div>
        </div>
        <div class="header-right">
          <!-- 按钮已移至列表头部 -->
        </div>
      </div>

      <div class="broad-list" :class="{ 'is-loading': isLoading }">
        <broad-list
          :items="categoryItems"
          :horizontal="true"
          class="horizontal"
          :selectedIndex="activeCategory"
          @item-click="handleCategoryClick"
        ></broad-list>
        <div class="loading-overlay" v-if="isLoading">
          <i class="el-icon-loading"></i>
          <span>加载中...</span>
        </div>
      </div>

      <div class="enterprise-content">
      

        <div class="enterprise-list">
            <div class="list-header">
              <div class="left-section">
                <!-- 暂时隐藏全选按钮 -->
                <!-- <el-checkbox v-model="selectAll" @change="handleSelectAll">全选</el-checkbox> -->

                <!-- 添加操作按钮 -->
                <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAddEnterprise">新增企业</el-button>
                <el-button type="success" size="small" icon="el-icon-upload2" @click="handleImport">导入</el-button>
                <el-button type="warning" size="small" icon="el-icon-download" @click="handleExport">导出</el-button>
                <!-- <el-button type="danger" size="small" icon="el-icon-delete" @click="handleBatchDelete">批量删除</el-button> -->
              </div>
              <div class="right-section">
                <div class="batch-actions" v-if="selectedItems.length > 0">
                  <span class="selected-count">已选择 {{ selectedItems.length }} 项</span>
                </div>
                <!-- 添加设置和刷新按钮 -->
                <el-button
                  type="text"
                  icon="el-icon-setting"
                  @click="handleDisplayFieldsSettings"
                  class="action-button"
                >
                  <span class="action-text">设置显示字段</span>
                </el-button>
                <el-button
                  type="text"
                  icon="el-icon-refresh"
                  @click="handleRefreshList"
                  class="action-button"
                >
                  <span class="action-text">刷新列表</span>
                </el-button>
              </div>
            </div>
            <div class="enterprise-row"
              v-for="(item, index) in enterpriseList"
              :key="index"
              @click="handleRowClick(item)"
              :class="{'selected': item.selected}"
            >
              <!-- 暂时隐藏复选框 -->
              <div class="item-checkbox" @click.stop>
                  <el-checkbox v-model="item.selected" @change="(val) => handleItemSelect(val, item)"></el-checkbox>
              </div>
              <div class="enterprise-list-item">
                  <el-card class="enterprise-card" shadow="hover">
                      <div class="card-body">
                          <div class="card-left">
                              <div class="card-header">
                                  <span class="enterprise-name clickable" @click="handleView(item)">{{ item.name }}</span>
                                  <el-tag
                                    v-for="(tag, levelIndex) in getMatchedRiskTags(item)"
                                    :key="levelIndex"
                                    plain
                                    size="mini"
                                    :style="{ color: tag.tagColor, borderColor: tag.tagColor,background: '#fff'}"
                                    class="risk-tag"
                                  >
                                    {{ tag.label }}
                                  </el-tag>
                              </div>
                              <div class="card-content">
                                  <div class="info-item" v-if="displayFields.creditCode">
                                      <span class="label">{{ item.creditCodeLabel }}</span>
                                      <span class="value">{{ item.creditCode }}</span>
                                  </div>
                                  <div class="info-item" v-if="displayFields.legalPerson">
                                      <span class="label">{{ item.legalPersonLabel }}</span>
                                      <span class="value">{{ item.legalPerson }} ({{ formatPhone(item.contactPhone) }})</span>
                                  </div>
                                  <div class="info-item" v-if="displayFields.companyType">
                                      <span class="label">{{ item.companyTypeLabel }}</span>
                                      <span class="value">{{ item.companyType }}</span>
                                  </div>
                                  <div class="info-item" v-if="displayFields.manager">
                                      <span class="label">{{ item.managerLabel }}</span>
                                      <span class="value">{{ item.manager }} ({{ formatPhone(item.managerPhone) }})</span>
                                  </div>
                                  <div class="info-item" v-if="displayFields.status">
                                      <span class="label">{{ item.statusLabel }}</span>
                                      <span class="value" :style="{ color: getStateColor(item.state) }">{{ item.status }}</span>
                                  </div>
                                  <div class="info-item" v-if="displayFields.relatedOthers">
                                      <span class="label">{{ item.relatedOthersLabel }}</span>
                                      <span class="value">{{ item.relatedOthers }}</span>
                                  </div>
                                  <div class="info-item full-width" v-if="displayFields.address">
                                      <span class="label">{{ item.addressLabel }}</span>
                                      <span class="value">{{ item.address }}</span>
                                  </div>
                              </div>
                          </div>
                          <div class="card-footer">
                              <div class="button-with-badge">
                                <el-button type="text" icon="el-icon-edit" @click.stop="handleEditEnterprise(item)">修改</el-button>
                              </div>
                              <div class="button-with-badge">
                                <el-button type="text" icon="el-icon-delete" @click.stop="handleDisabledAction" disabled>删除</el-button>
                                <span class="upgrade-badge">升级中</span>
                              </div>
                              <el-button type="text" icon="el-icon-view" @click.stop="handleView(item)">查看详情</el-button>
                          </div>
                      </div>
                  </el-card>
              </div>
            </div>
        </div>

        <div class="pagination-container">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.currentPage"
            :page-sizes="[5, 10, 20]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total">
          </el-pagination>
        </div>
      </div>
    </section>
    <!-- 企业详情对话框 -->
    <el-dialog title="企业详情" :visible.sync="detailDialogVisible" width="70%">
      <enterprise-detail v-if="detailDialogVisible" :enterprise-id="currentEnterpriseId"></enterprise-detail>
    </el-dialog>

    <!-- 新增/编辑企业对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="formDialogVisible" width="50%">
      <enterprise-form
        v-if="formDialogVisible"
        :enterprise="currentEnterprise"
        @submit="handleFormSubmit"
        @cancel="formDialogVisible = false">
      </enterprise-form>
    </el-dialog>

    <!-- 显示字段设置组件 -->
    <field-display-settings
      :visible.sync="displayFieldsDialogVisible"
      :available-fields="availableDisplayFields"
      :current-fields="currentSelectedFields"
      :api-url="'/system/sysEnterprise/list'"
      @save-success="handleFieldSettingsSaveSuccess"
      @close="handleFieldSettingsClose"
    />

    <!-- 企业编辑抽屉 -->
    <enterprise-edit-drawer
      :visible.sync="editDrawerVisible"
      :enterprise-data="currentEditEnterprise"
      @save-success="handleEditSaveSuccess"
      @close="handleEditDrawerClose"
    ></enterprise-edit-drawer>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".zip"
        :headers="upload.headers"
        :action="upload.url + '?previewUrl=' + upload.previewUrl+ ''"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入zip格式文件。</span>
          <el-link type="danger" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import EnterpriseDetail from './components/EnterpriseDetail'
import EnterpriseForm from './components/EnterpriseForm'
import EnterpriseEditDrawer from '@/components/EnterpriseEditDrawer'
import BroadList from '@/components/broad-list.vue'
import FieldDisplaySettings from '@/components/FieldDisplaySettings'
import { getEnterpriseList, getEnterpriseCategoryStats, getRiskLevelOptions, addEnterprise, updateEnterprise, deleteEnterprise, batchDeleteEnterprise } from '@/api/enterprise'
import { getToken } from "@/utils/auth";
export default {
  name: 'EnterpriseArchive',
  components: {
    EnterpriseDetail,
    EnterpriseForm,
    EnterpriseEditDrawer,
    BroadList,
    FieldDisplaySettings
  },
  data() {
    return {
      searchKeyword: '',
      searchType: 'all',
      activeCategory: 0, // 默认选中第一个分类（全部单位）
      currentCategoryType: 'all', // 当前选中的分类类型
      isLoading: false, // 加载状态
      categoryItems: [
        {
          title: '全部单位',
          badge: '',// 这里是getEnterpriseCategoryStats返回值的totalNum
          backgroundColor: '#F5F7FA',
          backgroundColor_active: '#2A52D7',
          key: 'totalNum',
        },
        {
          title: '电子屏单位',
          badge: '',// 这里是getEnterpriseCategoryStats返回值的screenNum
          backgroundColor: '#F5F7FA',
          backgroundColor_active: '#60B8FF',
          key: 'screenNum',
        },
        {
          title: '等保备案单位',
          badge: '',
          backgroundColor: '#F5F7FA',
          backgroundColor_active: '#44C991',
          key: 'levelProjectNum',
        },
        {
          title: '网站备案单位',
          badge: '',
          backgroundColor: '#F5F7FA',
          backgroundColor_active: '#F5BC6C',
          key: 'websiteNum',
        },
        {
          title: '运营商单位',
          badge: '',
          backgroundColor: '#F5F7FA',
          backgroundColor_active: '#24A8BB',
          key: 'operatorNum',
        },
        {
          title: '网吧单位',
          badge: '',
          backgroundColor: '#F5F7FA',
          backgroundColor_active: '#FB6B2A',
          key: 'netbarNum',
        },
        {
          title: '非经营单位',
          badge: '',
          backgroundColor: '#F5F7FA',
          backgroundColor_active: '#D789D4',
          key: 'nonbusinessNum',
        },
        {
          title: '其他',
          badge: '',
          backgroundColor: '#F5F7FA',
          backgroundColor_active: '#95ABD4',
          key: 'otherNum',
        }
      ],
      filters: {
        industry: '',
        scale: '',
        riskLevel: '',
        region: ''
      },
      industryOptions: [
        { value: '制造业', label: '制造业' },
        { value: '服务业', label: '服务业' },
        { value: '建筑业', label: '建筑业' },
        { value: '金融业', label: '金融业' },
        { value: '其他', label: '其他' }
      ],
      scaleOptions: [
        { value: '大型', label: '大型' },
        { value: '中型', label: '中型' },
        { value: '小型', label: '小型' },
        { value: '微型', label: '微型' }
      ],

      queryParams: {
        pageNum: 1,
              pageSize: 10,
              name: null, 
              creditCode: null, 
              corporationName: null,
      },
      
      riskLevelOptions: {
        'netbarNum': { label: '网吧单位', tagColor: '#FB6B2A' },
        'screenNum':{  label: '电子屏单位', tagColor: '#60B8FF' },
         'levelprotectNum':{  label: '等保备案单位', tagColor: '#44C991'},
          'websiteNum':{  label: '网站备案单位', tagColor: '#F5BC6C' },
           'operatorNum':{ label: '运营商单位', tagColor: '#24A8BB' },
           'nonbusinessNum':{  label: '非经营单位', tagColor: '#D789D4' },
           'otherNum': {  label: '其他', tagColor: '#95ABD4' }

      },
     
      regionOptions: [
        { value: '通州区', label: '通州区' },
        { value: '朝阳区', label: '朝阳区' },
        { value: '海淀区', label: '海淀区' },
        { value: '丰台区', label: '丰台区' },
        { value: '其他', label: '其他' }
      ],
      enterpriseList: [],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      detailDialogVisible: false,
      formDialogVisible: false,
      currentEnterpriseId: null,
      currentEnterprise: null,
      dialogTitle: '新增企业',
      selectedItems: [], // 存储选中的企业ID
      selectAll: false, // 是否全选

      // 显示字段设置
      displayFieldsDialogVisible: false,
      displayFields: {
        creditCode: true,
        legalPerson: true,
        companyType: true,
        manager: true,
        status: true,
        relatedOthers: true,
        address: true
      },

      // 可用显示字段配置
      availableDisplayFields: [
        { key: 'creditCode', label: '统一社会信用编码' },
        { key: 'legalPerson', label: '法人及联系电话' },
        { key: 'companyType', label: '公司性质' },
        { key: 'manager', label: '负责人及联系电话' },
        { key: 'status', label: '状态' },
        { key: 'relatedOthers', label: '关联其他' },
        { key: 'address', label: '单位注册地址' }
      ],

      // 编辑抽屉相关
      editDrawerVisible: false,
      currentEditEnterprise: {},

      // 导入参数
      // 导入参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        previewUrl: process.env.VUE_APP_BASE_API + "/file/download/",
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/sysEnterprise/importData"
      },
    }
  },
  computed: {
    // 当前选中的字段
    currentSelectedFields() {
      return Object.keys(this.displayFields).filter(key => this.displayFields[key])
    }
  },
  created() {
    // 从本地存储加载显示字段设置
    const savedDisplayFields = localStorage.getItem('enterpriseDisplayFields');
    if (savedDisplayFields) {
      try {
        this.displayFields = JSON.parse(savedDisplayFields);
      } catch (e) {
        console.error('解析显示字段设置失败', e);
      }
    }

    // 确保默认选中"全部单位"标签
    this.activeCategory = 0;
    this.currentCategoryType = 'all';

    // 获取风险级别选项
    // this.fetchRiskLevelOptions();

    // 获取分类统计数据
    this.fetchCategoryStats();

    // 初始化时调用API获取数据
    this.fetchEnterpriseList();
  },
  methods: {
    // 获取企业列表
    fetchEnterpriseList() {
      try {
        // 确保使用 'all' 作为默认值
        const categoryType = this.currentCategoryType || 'all';
        console.log('fetchEnterpriseList - categoryType:', categoryType);

        // 构建新API格式的参数
        const params = {
          pageNum: this.pagination.currentPage.toString(),
          pageSize: this.pagination.pageSize.toString()
        };

        // 添加可选参数
        if (this.searchKeyword) {
          // 根据搜索类型设置对应的参数
          switch (this.searchType) {
            case 'name':
              params.name = this.searchKeyword;
              break;
            case 'creditCode':
              params.creditCode = this.searchKeyword;
              break;
            case 'legalPerson':
              params.corporationName = this.searchKeyword;
              break;
            case 'all':
            default:
              params.searchWord = this.searchKeyword;
              break;
          }
        }

        // 根据分类类型设置关联项参数
        if (categoryType !== 'all') {
          switch(categoryType) {
            case 'screen':
              params.relatedItem = 'screenNum';
              break;
            case 'levelProject':
              params.relatedItem = 'levelProjectNum';
              break;
            case 'website':
              params.relatedItem = 'websiteNum';
              break;
            case 'operator':
              params.relatedItem = 'operatorNum';
              break;
            case 'netbar':
              params.relatedItem = 'netbarNum';
              break;
            case 'nonbusiness':
              params.relatedItem = 'wifiNum';
              break;
            case 'other':
              params.relatedItem = 'otherNum';
              break;
          }
        }

        // 添加其他筛选条件
        if (this.filters.industry) {
          params.property = this.filters.industry;
        }

        console.log('API请求参数:', params);

        // 调用API获取企业列表数据
        getEnterpriseList(params).then(response => {
          console.log('API响应数据:', response);
          this.queryParams = params
          if (response.code === 200 && response.rows) {
            // 新API响应格式：{ code, msg, total, rows, fields }
            const total = response.total || 0;
            const list = response.rows || [];
            const fields = response.fields; // 获取API返回的fields字段

            // 处理fields字段，更新displayFields
            this.handleApiFields(fields);

            // 更新分页总数
            this.pagination.total = total;

            // 处理企业列表数据，映射新API字段到前端使用的字段
            this.enterpriseList = list.map(item => {
              return {
                ...item,
                // 映射字段名
                legalPerson: item.corporationName, // 法人姓名
                phone: item.corporationPhoneNumber, // 法人电话
                handlerName: item.handlerName, // 负责人姓名
                handlerPhone: item.handlerPhoneNumber, // 负责人电话
                address: item.regLocation, // 注册地址
                // 添加标签字段
                creditCodeLabel: '统一社会信用代码',
                legalPersonLabel: '法人及联系电话',
                companyTypeLabel: '公司性质',
                managerLabel: '负责人及联系电话',
                statusLabel: '状态',
                relatedOthersLabel: '关联其他',
                addressLabel: '单位注册地址',
                // 映射显示字段
                companyType: item.property, // 单位性质
                manager: item.handlerName, // 负责人姓名
                managerPhone: item.handlerPhoneNumber, // 负责人电话
                contactPhone: item.corporationPhoneNumber, // 法人电话
                status: item.statusLabel || item.status, // 状态
                state: item.status === '正常' ? 'valid' : 'invalid', // 状态类型
                relatedOthers: this.formatRelatedOthers(item), // 关联其他
                // 关联数量统计
                relatedCounts: {
                  screen: item.screenNum || 0,
                  levelProject: item.levelProjectNum || 0,
                  website: item.websiteNum || 0,
                  operator: item.operatorNum || 0,
                  netbar: item.netBarNum || 0,
                  wifi: item.wifiNum || 0
                },
                // 添加标签相关字段，用于matchRiskTags方法
                netbarNum: item.netBarNum || 0,
                screenNum: item.screenNum || 0,
                levelprotectNum: item.levelProjectNum || 0,
                websiteNum: item.websiteNum || 0,
                operatorNum: item.operatorNum || 0,
                nonbusinessNum: item.wifiNum || 0,
                otherNum: item.otherNum || 0,
                // 添加 selected 属性，用于复选框
                selected: this.selectedItems.includes(item.id),
                // 此处与broad-list容器内的卡片相呼应，筛选联动
                // riskLevels: this.generateRandomRiskLevels()
              };
            });

            // 重置全选状态
            this.selectAll = this.enterpriseList.length > 0 && this.selectedItems.length === this.enterpriseList.length;
          } else {
            console.warn('获取到的数据为空或格式不正确:', response);
            this.enterpriseList = [];
            this.pagination.total = 0;
          }
        }).catch(error => {
          console.error('获取企业列表失败:', error);
          this.$message.error('获取企业列表失败，请稍后重试');
          this.enterpriseList = [];
          this.pagination.total = 0;
        });
      } catch (error) {
        console.error('获取企业列表失败:', error);
        this.$message.error('获取企业列表失败，请稍后重试');
        this.enterpriseList = [];
        this.pagination.total = 0;
      }
    },

    // 筛选数据
    filterData(data) {
      const { industry, scale, riskLevel, region } = this.filters
      const keyword = this.searchKeyword.toLowerCase()
      const searchType = this.searchType
      const categoryType = this.currentCategoryType

      return data.filter(item => {
        let matchKeyword = true

        if (keyword) {
          switch (searchType) {
            case 'name':
              matchKeyword = item.name.toLowerCase().includes(keyword)
              break
            case 'creditCode':
              matchKeyword = item.creditCode.toLowerCase().includes(keyword)
              break
            case 'legalPerson':
              matchKeyword = (item.legalPerson || '').toLowerCase().includes(keyword)
              break
            case 'all':
            default:
              matchKeyword = item.name.toLowerCase().includes(keyword) ||
                item.creditCode.toLowerCase().includes(keyword) ||
                (item.legalPerson || '').toLowerCase().includes(keyword)
              break
          }
        }

        // 根据分类类型筛选
        let matchCategory = true
        if (categoryType !== 'all') {
          // 这里根据实际业务逻辑判断企业是否属于特定分类
          // 示例：假设企业数据中有 categories 字段表示所属分类
          switch(categoryType) {
            case 'screen':
              matchCategory = item.hasElectronicScreen === true
              break
            case 'levelProject':
              matchCategory = item.hasSecurityRecord === true
              break
            case 'website':
              matchCategory = item.hasWebsiteRecord === true
              break
            case 'operator':
              matchCategory = item.hasOperator === true
              break
            case 'netbar':
              matchCategory = item.hasNetbar === true
              break
            case 'nonbusiness':
              matchCategory = item.isNonBusiness === true
              break
            case 'other':
              // 不属于上述任何分类的企业
              matchCategory = !item.hasElectronicScreen &&
                             !item.hasSecurityRecord &&
                             !item.hasWebsiteRecord &&
                             !item.hasOperator &&
                             !item.hasNetbar &&
                             !item.isNonBusiness
              break
          }
        }

        const matchIndustry = !industry || item.industry === industry
        const matchScale = !scale || item.scale === scale
        const matchRiskLevel = !riskLevel || (item.riskLevels && item.riskLevels.includes(riskLevel))
        const matchRegion = !region || item.region === region

        return matchKeyword && matchIndustry && matchScale && matchRiskLevel && matchRegion && matchCategory
      })
    },

    // 根据风险等级获取标签类型
    getRiskLevelType(level) {
      // 查找对应的风险等级选项
      const option = this.riskLevelOptions.find(opt => opt.value === level);
      if (option) {
        return ''; // 返回空字符串，我们将使用自定义颜色
      }
      return 'info'; // 默认颜色
    },

    // 获取标签颜色（支持新的标签格式）
    getRiskLevelColor(riskTag) {
      // 如果是新格式的标签对象
      if (typeof riskTag === 'object' && riskTag.tagColor) {
        return riskTag.tagColor;
      }

      // 兼容旧格式
      const option = this.riskLevelOptions.find(opt => opt.value === riskTag);
      if (option) {
        return option.tagColor;
      }
      return '#909399'; // 默认颜色
    },

    // 获取标签文本（支持新的标签格式）
    getRiskLevelLabel(riskTag) {
      // 如果是新格式的标签对象
      if (typeof riskTag === 'object' && riskTag.label) {
        return riskTag.label;
      }

      // 兼容旧格式
      const option = this.riskLevelOptions.find(opt => opt.value === riskTag);
      if (option) {
        return option.label;
      }
      return '未知'; // 默认返回未知
    },

    // 处理搜索
    handleSearch() {
      this.pagination.currentPage = 1
      this.fetchEnterpriseList()
    },

    // 处理筛选
    handleFilter() {
      this.pagination.currentPage = 1
      this.fetchEnterpriseList()
    },

    // 重置筛选条件
    resetFilter() {
      this.filters = {
        industry: '',
        scale: '',
        riskLevel: '',
        region: ''
      }
      this.fetchEnterpriseList()
    },

    // 处理页码变化
    handleCurrentChange(currentPage) {
      this.pagination.currentPage = currentPage
      this.fetchEnterpriseList()
    },

    // 处理每页显示数量变化
    handleSizeChange(pageSize) {
      this.pagination.pageSize = pageSize
      this.fetchEnterpriseList()
    },

    // 处理行点击
    handleRowClick(item) {
      // 切换选中状态
      const selected = !item.selected;
      this.$set(item, 'selected', selected);
      this.handleItemSelect(selected, item);
    },

    // 处理禁用按钮点击
    handleDisabledAction() {
      this.$message({
        message: '功能升级中，敬请期待',
        type: 'info'
      });
    },

    // 处理查看详情
    handleView(item) {
      // 跳转到详情报告页面
      this.$router.push(`/enterprise/detail/${item.id}`)
    },


    // 处理编辑
    handleEdit(item) {
      this.currentEditEnterprise = { ...item }
      this.editDrawerVisible = true
    },

    // 处理删除
    handleDelete(item) {
      this.$confirm('确认删除该企业档案吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用API删除企业
        deleteEnterprise(item.id).then(response => {
          if (response.code === 200) {
            // 从列表中移除已删除的企业
            this.enterpriseList = this.enterpriseList.filter(enterprise => enterprise.id !== item.id);
            this.$message.success('删除成功');

            // 更新分类统计数据
            this.fetchCategoryStats();
          } else {
            this.$message.error(response.msg || '删除失败');
          }
        }).catch(error => {
          console.error('删除企业失败:', error);
          this.$message.error('删除失败，请稍后重试');
        });
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    // 处理新增企业
    handleAddEnterprise() {
      this.$router.push('/enterprise/enterpriseAdd');
    },

    // 处理编辑企业
    handleEditEnterprise(item) {
      this.$router.push({
        path: '/enterprise/enterpriseAdd',
        query: { id: item.id, mode: 'edit' }
      });
    },

    // 处理表单提交
    handleFormSubmit(formData) {
      if (formData.id) {
        // 编辑现有企业
        updateEnterprise(formData).then(response => {
          if (response.code === 200) {
            // 更新列表中的企业数据
            const index = this.enterpriseList.findIndex(item => item.id === formData.id);
            if (index !== -1) {
              this.enterpriseList.splice(index, 1, formData);
            }
            this.$message.success('更新成功');

            // 更新分类统计数据
            this.fetchCategoryStats();
          } else {
            this.$message.error(response.msg || '更新失败');
          }
        }).catch(error => {
          console.error('更新企业失败:', error);
          this.$message.error('更新失败，请稍后重试');
        });
      } else {
        // 新增企业
        addEnterprise(formData).then(response => {
          if (response.code === 200) {
            // 添加新企业到列表
            const newEnterprise = {
              id: Date.now(), // 实际应该使用后端返回的ID
              ...formData
            };
            this.enterpriseList.unshift(newEnterprise);
            this.$message.success('添加成功');

            // 更新分类统计数据
            this.fetchCategoryStats();
          } else {
            this.$message.error(response.msg || '添加失败');
          }
        }).catch(error => {
          console.error('添加企业失败:', error);
          this.$message.error('添加失败，请稍后重试');
        });
      }
      this.formDialogVisible = false;
    },

    // 处理导入
    handleImport() {
      this.upload.title = "数据导入";
      this.upload.open = true;
    },

    // 提交上传文件
    submitFileForm() {
      this.upload.open = false;
      this.$refs.upload.submit();
    },

    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      console.log('----handleFileUploadProgress--');
      this.loading = true;
      this.upload.isUploading = true;
    },

    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      let that = this;
      that.loading = false;
      that.upload.open = false;
      that.upload.isUploading = false;
      that.$refs.upload.clearFiles();
      that.importLog = response.msg;

      let msgTitle = response.msg;
      if (response.code == 200) {
        that.$modal.msgSuccess(msgTitle);
        that.fetchEnterpriseList();
        that.fetchCategoryStats();
      } else {
        that.$modal.msgError(msgTitle);
      }
    },

    /** 下载模板操作 */
    async importTemplate() {
      const url = process.env.VUE_APP_BASE_API+'/file/download/'+vueConfig.template_path_enterprise; // 替换成你要下载的文件的URL
      const fileName = '企业主体信息导入模板.xlsx'; // 自定义文件名

      const response = await fetch(url);
      const blob = await response.blob();
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('/system/sysEnterprise/export', {
        ...this.queryParams
      }, `单位主体_${new Date().getTime()}.xlsx`)
    },

    // 格式化电话号码，中间4位用星号代替
    formatPhone(phone) {
      if (!phone) return '';
      return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    },

    // 获取状态颜色
    getStateColor(state) {
      if (state === 'valid') {
        return '#4584FF'; // 有效状态为蓝色
      } else if (state === 'invalid') {
        return '#F56C6C'; // 无效状态为红色
      }
      return '#909399'; // 默认颜色
    },

    // 获取变浅的标签背景色
    getLightenedColor(level) {
      const option = this.riskLevelOptions.find(opt => opt.value === level);
      if (option) {
        // 直接在这里实现 lightenColor 方法的功能
        const hexColor = option.tagColor.replace(/^#/, "");
        const r = parseInt(hexColor.substring(0, 2), 16);
        const g = parseInt(hexColor.substring(2, 4), 16);
        const b = parseInt(hexColor.substring(4, 6), 16);

        // 计算变浅后的RGB值（90%变浅）
        const percent = 90;
        const newR = Math.round(r + (255 - r) * (percent / 100));
        const newG = Math.round(g + (255 - g) * (percent / 100));
        const newB = Math.round(b + (255 - b) * (percent / 100));

        // 将RGB转换回16进制
        const toHex = (value) => {
          const hex = value.toString(16);
          return hex.length === 1 ? "0" + hex : hex;
        };

        return `#${toHex(newR)}${toHex(newG)}${toHex(newB)}`;
      }
      return "#F5F7FA"; // 默认背景色
    },




    // 格式化关联其他信息
    formatRelatedOthers(item) {
      const relatedItems = [];
      if (item.screenNum > 0) relatedItems.push(`电子屏${item.screenNum}个`);
      if (item.levelProjectNum > 0) relatedItems.push(`等保备案${item.levelProjectNum}个`);
      if (item.websiteNum > 0) relatedItems.push(`网站备案${item.websiteNum}个`);
      if (item.operatorNum > 0) relatedItems.push(`运营商${item.operatorNum}个`);
      if (item.netBarNum > 0) relatedItems.push(`网吧${item.netBarNum}个`);
      if (item.wifiNum > 0) relatedItems.push(`非经营${item.wifiNum}个`);

      return relatedItems.length > 0 ? relatedItems.join('、') : '无';
    },

    // 格式化关联其他信息
    formatRelatedOthers(item) {
      const relatedItems = [];
      if (item.screenNum > 0) relatedItems.push(`电子屏${item.screenNum}个`);
      if (item.levelProjectNum > 0) relatedItems.push(`等保备案${item.levelProjectNum}个`);
      if (item.websiteNum > 0) relatedItems.push(`网站备案${item.websiteNum}个`);
      if (item.operatorNum > 0) relatedItems.push(`运营商${item.operatorNum}个`);
      if (item.netBarNum > 0) relatedItems.push(`网吧${item.netBarNum}个`);
      if (item.wifiNum > 0) relatedItems.push(`非经营${item.wifiNum}个`);

      return relatedItems.length > 0 ? relatedItems.join('、') : '无';
    },



    // 根据API数据生成风险标签（数值大于0的才显示）
    generateRiskLevelsFromApiData(item) {
      const riskTags = [];

      // 遍历riskLevelOptions，根据key值匹配API数据
      Object.keys(this.riskLevelOptions).forEach(fieldName => {
        const value = item[fieldName];
        if (value && value > 0) {
          const config = this.riskLevelOptions[fieldName];
          riskTags.push({
            fieldName: fieldName,
            label: config.label,
            value: value,
            tagColor: config.tagColor
          });
        }
      });

      return riskTags;
    },

    // 调用接口获取企业列表数据
    fetchEnterpriseListByApi(categoryType = 'all') {
      console.warn('fetchEnterpriseListByApi 方法已废弃，请使用 fetchEnterpriseList 方法');
      this.fetchEnterpriseList();
    },

    // 格式化电话号码，中间4位用星号代替
    formatPhone(phone) {
      if (!phone) return '';
      return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    },

    // 获取状态颜色
    getStateColor(state) {
      if (state === 'valid') {
        return '#4584FF'; // 有效状态为蓝色
      } else if (state === 'invalid') {
        return '#F56C6C'; // 无效状态为红色
      }
      return '#909399'; // 默认颜色
    },

    // 获取变浅的标签背景色
    getLightenedColor(riskTag) {
      let tagColor = '#909399'; // 默认颜色

      // 如果是新格式的标签对象
      if (typeof riskTag === 'object' && riskTag.tagColor) {
        tagColor = riskTag.tagColor;
      } else if (typeof riskTag === 'string') {
        // 兼容旧格式，查找对应的配置
        const fieldNames = Object.keys(this.riskLevelOptions);
        for (const fieldName of fieldNames) {
          const config = this.riskLevelOptions[fieldName];
          if (config.label === riskTag) {
            tagColor = config.tagColor;
            break;
          }
        }
      }

      // 直接在这里实现 lightenColor 方法的功能
      const hexColor = tagColor.replace(/^#/, "");
      const r = parseInt(hexColor.substring(0, 2), 16);
      const g = parseInt(hexColor.substring(2, 4), 16);
      const b = parseInt(hexColor.substring(4, 6), 16);

      // 计算变浅后的RGB值（90%变浅）
      const percent = 90;
      const newR = Math.round(r + (255 - r) * (percent / 100));
      const newG = Math.round(g + (255 - g) * (percent / 100));
      const newB = Math.round(b + (255 - b) * (percent / 100));

      // 将RGB转换回16进制
      const toHex = (value) => {
        const hex = value.toString(16);
        return hex.length === 1 ? "0" + hex : hex;
      };

      return `#${toHex(newR)}${toHex(newG)}${toHex(newB)}`;
    },

    // 根据分类类型获取分类标题
    getCategoryTitle(categoryType) {
      // 根据categoryType查找对应的categoryItems中的标题
      switch(categoryType) {
        case 'total': return '全部单位';
        case 'screen': return '电子屏单位';
        case 'levelProject': return '等保备案单位';
        case 'website': return '网站备案单位';
        case 'operator': return '运营商单位';
        case 'netbar': return '网吧单位';
        case 'nonbusiness': return '非经营单位';
        case 'other': return '其他';
        default: return '';
      }
    },

    // 处理分类点击
    handleCategoryClick(item, index) {
      // 更新选中的分类索引
      this.activeCategory = index;

      // 根据分类类型设置筛选条件
      let filterType = '';
      switch(item.title) {
        case '全部单位':
          // 不设置特定筛选条件，显示所有单位
          filterType = 'all';
          break;
        case '电子屏单位':
          filterType = 'screen';
          break;
        case '等保备案单位':
          filterType = 'levelProject';
          break;
        case '网站备案单位':
          filterType = 'website';
          break;
        case '运营商单位':
          filterType = 'operator';
          break;
        case '网吧单位':
          filterType = 'netbar';
          break;
        case '非经营单位':
          filterType = 'nonbusiness';
          break;
        case '其他':
          filterType = 'other';
          break;
      }

      console.log('handleCategoryClick - 切换到分类:', filterType);

      // 设置当前选中的分类类型
      this.currentCategoryType = filterType;

      // 重置分页
      this.pagination.currentPage = 1;

      // 调用接口获取数据
      this.fetchEnterpriseList();
    },

    // 处理选择单个项目
    handleItemSelect(selected, item) {
      if (selected) {
        // 添加到选中列表
        if (!this.selectedItems.includes(item.id)) {
          this.selectedItems.push(item.id);
        }
      } else {
        // 从选中列表中移除
        this.selectedItems = this.selectedItems.filter(id => id !== item.id);
      }

      // 检查是否所有项目都被选中
      this.selectAll = this.enterpriseList.length > 0 && this.selectedItems.length === this.enterpriseList.length;
    },

    // 处理全选/取消全选
    handleSelectAll(val) {
      if (val) {
        // 全选
        this.selectedItems = this.enterpriseList.map(item => item.id);
        this.enterpriseList.forEach(item => {
          this.$set(item, 'selected', true);
        });
      } else {
        // 取消全选
        this.selectedItems = [];
        this.enterpriseList.forEach(item => {
          this.$set(item, 'selected', false);
        });
      }
    },

    // 处理设置显示字段
    handleDisplayFieldsSettings() {
      this.displayFieldsDialogVisible = true;
    },

    // 保存显示字段设置
    saveDisplayFieldsSettings() {
      this.displayFieldsDialogVisible = false;
      // 可以在这里保存设置到本地存储或后端
      localStorage.setItem('enterpriseDisplayFields', JSON.stringify(this.displayFields));
      this.$message.success('显示字段设置已保存');
    },

    // 处理字段设置保存成功
    handleFieldSettingsSaveSuccess(selectedFields) {
      // 更新displayFields对象
      Object.keys(this.displayFields).forEach(key => {
        this.displayFields[key] = selectedFields.includes(key)
      })

      // 保存到本地存储
      localStorage.setItem('enterpriseDisplayFields', JSON.stringify(this.displayFields));
      this.$message.success('显示字段设置已保存');
    },

    // 处理字段设置弹窗关闭
    handleFieldSettingsClose() {
      this.displayFieldsDialogVisible = false;
    },

    // 处理API返回的fields字段
    handleApiFields(fields) {
      console.log('API返回的fields字段:', fields);

      if (fields && typeof fields === 'string') {
        // 解析fields字符串，例如 "creditCode|legalPerson|companyType|manager|status|address"
        const fieldsArray = fields.split('|');
        console.log('解析后的字段数组:', fieldsArray);

        // 重置所有字段为false
        Object.keys(this.displayFields).forEach(key => {
          this.displayFields[key] = false;
        });

        // 根据API返回的字段设置为true
        fieldsArray.forEach(field => {
          if (this.displayFields.hasOwnProperty(field)) {
            this.displayFields[field] = true;
          }
        });

        console.log('更新后的displayFields:', this.displayFields);
      } else if (!fields) {
        // 如果fields为null、空或undefined，显示所有字段
        console.log('fields为空，显示所有字段');
        Object.keys(this.displayFields).forEach(key => {
          this.displayFields[key] = true;
        });
      }
    },

    // 处理刷新列表
    handleRefreshList() {
      // 使用当前选中的分类类型刷新数据
      this.fetchEnterpriseList();
      this.fetchCategoryStats();
    },

    // 获取风险级别选项
    fetchRiskLevelOptions() {
      getRiskLevelOptions().then(response => {
        if (response.code === 200 && response.data) {
          this.riskLevelOptions = response.data;
        } else {
          console.warn('获取风险级别选项失败');
        }
      }).catch(error => {
        console.error('获取风险级别选项失败:', error);
      });
    },

    // 获取分类统计数据
    fetchCategoryStats() {
      getEnterpriseCategoryStats().then(response => {
        console.log('统计API响应数据:', response);

        if (response.code === 200 && response.data) {
          // 新API响应格式：{ code, msg, data: { enterpriseNum, screenNum, ... } }
          const statsData = response.data;

          // 更新分类统计数据，映射新API字段
          this.categoryItems = this.categoryItems.map(item => {
            let count = 0;
            switch(item.key) {
              case 'totalNum':
                count = statsData.totalNum || 0;
                break;
              case 'screenNum':
                count = statsData.screenNum || 0;
                break;
              case 'levelProjectNum':
                count = statsData.levelProjectNum || 0;
                break;
              case 'websiteNum':
                count = statsData.websiteNum || 0;
                break;
              case 'operatorNum':
                count = statsData.operatorNum || 0;
                break;
              case 'netbarNum':
                count = statsData.netbarNum || 0;
                break;
              case 'nonbusinessNum':
                count = statsData.wifiNum || 0;
                break;
              case 'otherNum':
                count = statsData.otherNum || 0;
                break;
              default:
                count = 0;
            }

            console.log(`映射 ${item.key}: ${count} (来源: ${item.title})`);

            return {
              ...item,
              badge: count.toString()
            };
          });

          console.log('更新后的categoryItems:', this.categoryItems);
        } else {
          console.warn('获取分类统计数据失败');
        }
      }).catch(error => {
        console.error('获取分类统计数据失败:', error);
      });
    },

    // 处理批量删除
    handleBatchDelete() {
      // 如果没有选中项，提示用户但不阻止操作
      const confirmMessage = this.selectedItems.length > 0
        ? `确认删除选中的 ${this.selectedItems.length} 项企业档案吗？`
        : '确认批量删除所有企业档案吗？';

      this.$confirm(confirmMessage, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用API批量删除企业
        const ids = this.selectedItems.length > 0 ? this.selectedItems : null;

        batchDeleteEnterprise(ids).then(response => {
          if (response.code === 200) {
            if (this.selectedItems.length > 0) {
              // 删除选中的企业
              this.enterpriseList = this.enterpriseList.filter(item => !this.selectedItems.includes(item.id));
              this.$message.success(`已删除 ${this.selectedItems.length} 项企业档案`);
            } else {
              // 删除所有企业
              const totalCount = this.enterpriseList.length;
              this.enterpriseList = [];
              this.$message.success(`已删除全部 ${totalCount} 项企业档案`);
            }

            // 清空选中状态
            this.selectedItems = [];
            this.selectAll = false;

            // 更新分类统计数据
            this.fetchCategoryStats();
          } else {
            this.$message.error(response.msg || '批量删除失败');
          }
        }).catch(error => {
          console.error('批量删除企业失败:', error);
          this.$message.error('批量删除失败，请稍后重试');
        });
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    // 处理编辑抽屉保存成功
    handleEditSaveSuccess(formData) {
      // 更新列表中的企业数据
      const index = this.enterpriseList.findIndex(item => item.id === formData.id);
      if (index !== -1) {
        this.enterpriseList.splice(index, 1, { ...formData, selected: this.enterpriseList[index].selected });
      }
      this.$message.success('企业信息更新成功');

      // 更新分类统计数据
      this.fetchCategoryStats();
    },

    // 处理编辑抽屉关闭
    handleEditDrawerClose() {
      this.editDrawerVisible = false;
      this.currentEditEnterprise = {};
    },

    /**
     * 根据传入的数据对象匹配标签
     * @param {Object} dataObj - 包含各种数量字段的数据对象
     * @returns {Array} 返回匹配的标签数组，包含 label、value、tagColor
     */
    getMatchedRiskTags(dataObj) {
      const matchedTags = []

      // 遍历数据对象的每个字段
      Object.keys(dataObj).forEach(dataKey => {
        const dataValue = dataObj[dataKey]

        // 只处理大于0的数值
        if (dataValue > 0) {
          // 在riskLevelOptions中查找匹配的key
          Object.keys(this.riskLevelOptions).forEach(optionKey => {
            // 使用indexOf进行模糊匹配
            if (dataKey.indexOf(optionKey.replace('Num', '')) > -1 ||
                optionKey.indexOf(dataKey.replace('Num', '')) > -1) {
              const option = this.riskLevelOptions[optionKey]
              matchedTags.push({
                label: option.label,
                value: dataValue,
                tagColor: option.tagColor
              })
            }
          })
        }
      })

      return matchedTags
    }
  }
}
</script>

<style lang="scss" scoped>
.enterprise-archive-content {
  width: 90%;
  margin: 0 auto;
}
.enterprise-archive {
  position: relative;
  min-height:90vh;
  background-image: url('../../assets/images/tz/u8.png');
  background-size: cover;
 
  .enterprise-header {
    display: flex;
    justify-content: center;
    align-items: center;
     margin-bottom: 20px;
    margin-bottom: 20px;

    .header-left {
      display: flex;
      align-items: center;

      h2 {
        margin: 0 20px 0 0;
        font-size: 20px;
        font-weight: 600;
      }

      .search-container {
        width: 550px;

        .complex-search {
          display: flex;

          .search-type {
            width: 150px;
            margin-right: -1px;

            ::v-deep .el-input__inner {
              border-top-right-radius: 0;
              border-bottom-right-radius: 0;
            }
          }

          .el-input {
            flex: 1;

            ::v-deep .el-input__inner {
              border-top-left-radius: 0;
              border-bottom-left-radius: 0;
            }
          }
        }
      }
    }

    .header-right {
      display: flex;
      gap: 10px;
    }

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;

      .header-left {
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 15px;

        h2 {
          margin-bottom: 15px;
        }

        .search-container {
          width: 100%;

          .complex-search {
            flex-direction: column;

            .search-type {
              width: 100%;
              margin-right: 0;
              margin-bottom: 10px;

              ::v-deep .el-input__inner {
                border-radius: 4px;
              }
            }

            .el-input {
              ::v-deep .el-input__inner {
                border-radius: 4px;
              }
            }
          }
        }
      }

      .header-right {
        justify-content: flex-end;
      }
    }
  }

  .broad-list {
    margin-bottom: 20px;
    padding: 0 10px;
    position: relative;

    &.is-loading {
      pointer-events: none;
      opacity: 0.7;
    }

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.7);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 10;

      i {
        font-size: 32px;
        color: #409EFF;
        margin-bottom: 10px;
      }

      span {
        font-size: 14px;
        color: #606266;
      }
    }

    ::v-deep .broad-list-container {
      margin-left: 10px;
      .broad-list {
        padding: 10px 0;

        .broad-list-item {
          height: 100px;
          display: flex;
          align-items: center;
          justify-content: center;

          .item-content {
            .item-badge {
              font-size: 28px;
              font-weight: bold;
              margin-bottom: 10px;
               &.active {
                    color: #ffffff;
                }
            }

            .item-title {
              font-weight: 500;
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  .enterprise-content {
    .filter-container {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f5f7fa;
      border-radius: 4px;

      .filter-item {
        display: flex;
        align-items: center;

        .label {
          margin-right: 8px;
          white-space: nowrap;
        }

        .el-select {
          width: 120px;
        }
      }
    }

    .enterprise-list {
      margin-bottom: 20px;

      .enterprise-row {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        position: relative;
        cursor: pointer;

        &:hover {
          .enterprise-card {
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
          }
        }

        &.selected {
          .enterprise-card {
            border: 1px solid #409EFF;
            box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.2);
          }
        }

        .item-checkbox {
          margin-right: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .enterprise-list-item {
          flex: 1;

          .enterprise-card {
            transition: all 0.3s;

            .card-body {
              display: flex;
              justify-content: space-between;
              align-items: stretch;

              .card-left {
                flex: 1;

                .card-header {
                  display: flex;
                  flex-wrap: wrap;
                  justify-content: flex-start;
                  align-items: center;
                  margin-bottom: 15px;
                  padding-bottom: 10px;
                  border-bottom: 1px solid #ebeef5;

                  .enterprise-name {
                    font-size: 16px;
                    font-weight: 600;
                    color: #303133;
                    margin-right: 10px;
                  }

                  .enterprise-name.clickable {
                    color: #4584FF;
                    cursor: pointer;
                    transition: color 0.3s ease;
                  }

                  .enterprise-name.clickable:hover {
                    color: #2A52D7;
                    text-decoration: underline;
                  }

                  .risk-tag {
                    margin-right: 5px;
                    margin-bottom: 5px;
                  }
                }

                .card-content {
                  display: flex;
                  flex-wrap: wrap;
                  width: 100%;
                  padding: 10px 0;

                  .info-item {
                    margin-bottom: 8px;
                    font-size: 13px;
                    margin-right: 20px;
                    display: flex;
                    align-items: center;
                    flex: 0 0 auto;
                    min-width: 200px;

                    &.full-width {
                      width: 100%;
                      flex-basis: 100%;
                      margin-top: 5px;
                    }

                    .label {
                      color: #909399;
                      white-space: nowrap;
                    }

                    .value {
                      color: #606266;
                      margin-left: 5px;
                      white-space: normal;
                      word-break: break-all;
                    }
                  }
                }
              }

              .card-footer {
                display: flex;
                flex-direction: row;
                justify-content: flex-end;
                align-items: center;
                padding-left: 20px;
                border-left: 1px solid #ebeef5;
                margin-left: 20px;

                .button-with-badge {
                  position: relative;
                  margin: 0 5px;

                  .upgrade-badge {
                    position: absolute;
                    top: -8px;
                    right: -10px;
                    background: #ff4444;
                    color: white;
                    font-size: 10px;
                    padding: 2px 6px;
                    border-radius: 8px;
                    white-space: nowrap;
                    z-index: 10;
                  }
                }

                .el-button {
                  margin: 0 5px;

                  &:first-child {
                    margin-left: 0;
                  }

                  &:last-child {
                    margin-right: 0;
                  }
                }
              }
            }
          }
        }
      }

      .list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding: 10px 15px;
        background-color: #f5f7fa;
        border-radius: 4px;
        margin-left:27px;

        .left-section {
          display: flex;
          align-items: center;

          .el-button {
            margin-right: 10px;

            &:last-child {
              margin-right: 0;
            }
          }
        }

        .right-section {
          display: flex;
          align-items: center;

          .batch-actions {
            display: flex;
            align-items: center;
            margin-right: 15px;

            .selected-count {
              color: #606266;
            }
          }

          .action-button {
            font-size: 14px;
            padding: 0;
            margin-left: 15px;
            color: #606266;
            display: flex;
            align-items: center;

            i {
              font-size: 16px;
              margin-right: 4px;
            }

            .action-text {
              font-size: 13px;
            }

            &:hover {
              color: #409EFF;
            }
          }
        }
      }

      .list-items {
        .list-item {
          display: flex;
          align-items: stretch;
          margin-bottom: 15px;
          border-radius: 4px;
          border: 1px solid #ebeef5;
          transition: all 0.3s;

          &:hover {
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
          }

          &.selected {
            background-color: #f0f9eb;
            border-color: #67c23a;
          }

          .item-checkbox {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 15px;
            border-right: 1px solid #ebeef5;
          }

          .item-content {
            flex: 1;
            padding: 15px;
            cursor: pointer;

            .item-main {
              display: flex;
              flex-direction: column;
              margin-bottom: 10px;
              padding-bottom: 10px;
              border-bottom: 1px solid #ebeef5;

              .item-name {
                font-size: 16px;
                font-weight: 600;
                color: #303133;
                margin-bottom: 8px;

                .risk-tag {
                  margin-left: 10px;
                }
              }

              .item-relations {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;

                .relation-tag {
                  font-size: 12px;
                  color: #606266;
                  background-color: #f5f7fa;
                  padding: 2px 8px;
                  border-radius: 2px;
                }
              }
            }

            .item-details {
              .detail-row {
                display: flex;
                margin-bottom: 8px;

                .detail-item {
                  flex: 1;
                  display: flex;
                  font-size: 13px;

                  &.full-width {
                    flex: 0 0 100%;
                  }

                  .label {
                    color: #909399;
                    margin-right: 5px;
                    white-space: nowrap;
                  }

                  .value {
                    color: #606266;
                  }
                }
              }
            }
          }

          .item-actions {
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 0 15px;
            border-left: 1px solid #ebeef5;

            .el-button {
              margin-left: 0;
              margin-right: 0;
              padding: 8px 0;

              & + .el-button {
                margin-top: 5px;
              }
            }
          }
        }
      }

      .empty-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 50px 0;
        color: #909399;

        i {
          font-size: 48px;
          margin-bottom: 15px;
        }

        p {
          font-size: 14px;
        }
      }
    }

    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 30px;
    }
  }

  .display-fields-container {
    display: flex;
    flex-direction: column;

    .el-checkbox {
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  @media (max-width: 768px) {
    .enterprise-content {
      .enterprise-list {
        .list-header {
          flex-direction: column;

          .left-section {
            margin-bottom: 10px;
            flex-wrap: wrap;

            .el-button {
              margin-bottom: 5px;
            }
          }

          .right-section {
            justify-content: flex-end;

            .action-button {
              margin-left: 10px;
            }
          }
        }
        .list-item {
          flex-direction: column;

          .item-checkbox {
            padding: 10px;
            border-right: none;
            border-bottom: 1px solid #ebeef5;
            justify-content: flex-start;
          }

          .item-content {
            .card-content {
              flex-direction: column;

              .info-item {
                width: 100%;
                margin-right: 0;
                min-width: auto;
              }
            }
          }

          .item-actions {
            flex-direction: row;
            padding: 10px;
            border-left: none;
            border-top: 1px solid #ebeef5;

            .el-button {
              flex: 1;

              & + .el-button {
                margin-top: 0;
                margin-left: 10px;
              }
            }
          }
        }
      }
    }
  }
}
//  .enterprise-archive::after {
//   content: "";
//   position: absolute;
//   top: 0;
//   left: 0;
//   right: 0;
//   bottom: 0;
//   background: rgba(255, 255, 255, 0.5); /* 白色，50% 透明度 */
//   mix-blend-mode: overlay; /* 与背景图混合 */
// }
</style>
