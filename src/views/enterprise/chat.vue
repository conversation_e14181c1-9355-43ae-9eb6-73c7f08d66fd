<template>
  <div class="app-container enterprise-chat-page">
    <!-- 聊天面板 -->
    <section class="chat-container">
      <!-- 聊天头部 -->
      <div class="chat-header">
        <div class="header-left">
          <el-button
            class="new-chat-btn"
            @click="startNewConversation"
            icon="el-icon-plus"
            size="small"
            type="primary"
            plain
          >
            新建对话
          </el-button>
        </div>
        <div class="header-right">
          <el-button
            class="tool-button disabled"
            :disabled="true"
            size="small"
          >
            编辑
            <span class="upgrade-badge">功能升级中</span>
          </el-button>
          <el-button
            class="tool-button disabled"
            :disabled="true"
            size="small"
          >
            下载
            <span class="upgrade-badge">功能升级中</span>
          </el-button>
          <el-button
            class="tool-button disabled"
            :disabled="true"
            size="small"
          >
            打印
            <span class="upgrade-badge">功能升级中</span>
          </el-button>
        </div>
      </div>

      <!-- 聊天内容区域 -->
      <div class="chat-content">
        <div class="chatBox">
          <div v-if="errorMessage" class="error-message">
            {{ errorMessage }}
          </div>
          <div class="chat-messages" ref="chatMessages">
            <!-- 欢迎消息 -->
            <div v-if="messages.length === 0" class="welcome-message">
              <div class="welcome-content">
                <i class="el-icon-office-building welcome-icon"></i>
                <h3>欢迎使用一企一档智能助手</h3>
                <p>请输入企业名称或关键词，我将为您查找相关企业档案信息</p>
              </div>
            </div>

            <!-- 对话消息列表 -->
            <div v-for="(message, index) in messages" :key="index" class="message">
              <div v-if="message.type === 'user'" class="user-message-container">
                <article class="message-content user-message">{{ message.content }}</article>
                <img :src="userAvatar" class="avatar user-avatar" alt="用户头像">
              </div>
              <div v-else class="bot-message-container">
                <img src="@/assets/images/tz/u117.png" class="avatar bot-avatar" alt="机器人头像">
                <article class="message-content bot-message">
                  <!-- 思考中状态 -->
                  <div v-if="message.thinking" class="thinking-indicator">
                    <i class="el-icon-loading"></i>
                    <span>思考中...</span>
                  </div>

                  <!-- 企业搜索结果 -->
                  <div v-else-if="message.searchResults" class="search-results">
                    <div class="search-title">为您找到以下企业：</div>
                    <div class="enterprise-list">
                      <div
                        v-for="enterprise in message.searchResults"
                        :key="enterprise.id"
                        class="enterprise-item"
                        @click="selectEnterprise(enterprise)"
                      >
                        <div class="enterprise-name">{{ enterprise.name }}</div>
                        <div class="enterprise-info">
                          <span class="credit-code">{{ enterprise.creditCode }}</span>
                          <span class="address">{{ enterprise.address }}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 企业档案详情 -->
                  <div v-else-if="message.enterpriseDetail" class="enterprise-detail">
                    <div class="detail-title">
                      以下为 "{{ message.enterpriseDetail.name }}" 企业档案
                    </div>
                    <div class="detail-container">
                      <!-- 分批次渲染的组件 -->
                      <div v-for="step in message.renderedSteps" :key="step.name" class="detail-step">
                        <div class="step-title" v-if="step.showTitle !== false">
                          <div class="step-indicator"></div>
                          <span>{{ step.title }}</span>
                        </div>
                        <div class="step-content">
                          <!-- 企业主体信息 -->
                          <enterprise-overview
                            v-if="step.name === 'overview'"
                            :enterprise-data="message.enterpriseDetail"
                            :show-actions="false"
                          />
                          <!-- 企业基础信息 -->
                          <enterprise-basic-info
                            v-else-if="step.name === 'basic'"
                            :enterprise-data="message.enterpriseDetail"
                            :legal-person="message.enterpriseDetail.legalPerson || {}"
                            :responsible-person="message.enterpriseDetail.responsiblePerson || {}"
                            :show-edit="false"
                          />
                          <!-- 企业业务信息 -->
                          <record-cards
                            v-else-if="step.name === 'business'"
                            :record-cards="message.enterpriseDetail.recordCards || {}"
                          />
                          <!-- 关联数据列表 -->
                          <related-data-lists
                            v-else-if="step.name === 'related'"
                            :enterprise-data="message.enterpriseDetail"
                          />
                          <!-- 网络安全评估 -->
                          <security-assessment
                            v-else-if="step.name === 'security'"
                            :enterprise-data="message.enterpriseDetail"
                            chart-id="chatSecurityChart"
                          />
                          <!-- 历史合规记录 -->
                          <compliance-records
                            v-else-if="step.name === 'compliance'"
                            :enterprise-data="message.enterpriseDetail"
                          />
                          <!-- 数字资产清单 -->
                          <digital-assets
                            v-else-if="step.name === 'assets'"
                            :enterprise-data="message.enterpriseDetail"
                          />
                          <!-- 月度更新情况 -->
                          <monthly-status-module
                            v-else-if="step.name === 'monthly'"
                            :enterprise-data="message.enterpriseDetail"
                          />
                          <!-- 资产变化情况 -->
                          <asset-change-module
                            v-else-if="step.name === 'changes'"
                            :enterprise-data="message.enterpriseDetail"
                          />
                        </div>
                      </div>

                      <!-- 正在生成的步骤 -->
                      <div v-if="message.isGenerating && message.currentGeneratingStep" class="generating-step">
                        <div class="step-title">
                          <div class="step-indicator generating"></div>
                          <span>{{ message.currentGeneratingStep.title }}</span>
                        </div>
                        <div class="step-content generating">
                          <div class="generating-indicator">
                            <i class="el-icon-loading"></i>
                            <span>正在生成{{ message.currentGeneratingStep.title }}...</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 普通文本消息 -->
                  <div v-else>
                    {{ message.content }}
                  </div>
                </article>
              </div>
            </div>
          </div>

          <!-- 输入区域 -->
          <div class="chat-input">
            <textarea
              v-model="inputText"
              placeholder="请输入企业名称或关键词..."
              @keyup.enter.exact="handleSend"
              rows="3"
              :disabled="isLoading"
            />
            <button
                  @click="handleSend"
                  :disabled="!inputText.trim() || isLoading"
                  class="send-btn"
                >
                  <i class="el-icon-s-promotion"></i>
                </button>
            <!-- <div class="input-actions">
              <div class="action-buttons">
                
              </div>
            </div> -->
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { getEnterpriseDetail } from '@/api/enterprise'
import request from '@/utils/request'
import EnterpriseOverview from '@/components/enterprise/EnterpriseOverview'
import EnterpriseBasicInfo from '@/components/enterprise/EnterpriseBasicInfo'
import RecordCards from '@/components/enterprise/RecordCards'
import SecurityAssessment from '@/components/enterprise/SecurityAssessment'
import ComplianceRecords from '@/components/enterprise/ComplianceRecords'
import DigitalAssets from '@/components/enterprise/DigitalAssets'
import RelatedDataLists from '@/components/enterprise/RelatedDataLists'
import AssetChangeModule from '@/components/AssetChangeModule'
import MonthlyStatusModule from '@/components/MonthlyStatusModule'
import { mapGetters } from 'vuex'

export default {
  name: 'EnterpriseChat',
  components: {
    EnterpriseOverview,
    EnterpriseBasicInfo,
    RecordCards,
    SecurityAssessment,
    ComplianceRecords,
    DigitalAssets,
    RelatedDataLists,
    AssetChangeModule,
    MonthlyStatusModule
  },
  data() {
    return {
      inputText: '',
      isLoading: false,
      messages: [],
      errorMessage: '',
      // 分批次渲染相关状态
      currentRenderingEnterprise: null,
      renderingSteps: [
        { name: 'overview', component: 'EnterpriseOverview', title: '企业主体信息', delay: 1000 },
        { name: 'basic', component: 'EnterpriseBasicInfo', title: '企业基础信息', delay: 1500 },
        { name: 'business', component: 'RecordCards', title: '企业业务信息', delay: 2000 },
        { name: 'related', component: 'RelatedDataLists', title: '关联数据列表', delay: 2500 },
        { name: 'security', component: 'SecurityAssessment', title: '网络安全评估', delay: 3000 },
        { name: 'compliance', component: 'ComplianceRecords', title: '历史合规记录', delay: 3500 },
        { name: 'assets', component: 'DigitalAssets', title: '数字资产清单', delay: 4000 },
        { name: 'monthly', component: 'MonthlyStatusModule', title: '月度更新情况', delay: 4500 },
        { name: 'changes', component: 'AssetChangeModule', title: '资产变化情况', delay: 5000 }
      ],
      currentStep: 0,
      renderedComponents: [],
      isGeneratingReport: false
    }
  },
  computed: {
    ...mapGetters(['avatar']),
    userAvatar() {
      return this.avatar || '@/assets/images/profile.jpg'
    }
  },
  mounted() {
    // 获取从首页传递过来的搜索词
    const searchWord = this.$route.query.searchWord
    if (searchWord) {
      this.inputText = searchWord
      // 自动发送搜索
      this.$nextTick(() => {
        this.handleSend()
      })
    }
  },
  methods: {
    // 处理发送消息
    async handleSend() {
      if (!this.inputText.trim() || this.isLoading) return
      
      const userMessage = this.inputText.trim()
      this.inputText = ''
      
      // 添加用户消息
      this.messages.push({
        type: 'user',
        content: userMessage,
        timestamp: new Date()
      })
      
      // 滚动到底部
      this.scrollToBottom()
      
      // 搜索企业
      await this.searchEnterprises(userMessage)
    },
    
    // 搜索企业
    async searchEnterprises(searchWord) {
      this.isLoading = true

      try {
        // 使用新的AI搜索接口
        const response = await this.queryByAI(searchWord)

        console.log('Chat搜索响应:', response)
        console.log('响应数据:', response.data)
        console.log('数据类型:', typeof response.data)
        console.log('是否为数组:', Array.isArray(response.data))

        if (response.code === 200 && response.data) {
          let dataArray = []

          // 处理不同的数据格式
          if (Array.isArray(response.data)) {
            // 如果是数组，直接使用
            dataArray = response.data
            console.log('数据是数组格式，长度:', dataArray.length)
          } else if (response.data.creditCode) {
            // 如果是单个对象且有creditCode字段，转换为数组
            dataArray = [response.data]
            console.log('数据是单个对象，有creditCode，转换为数组:', dataArray)
          } else {
            console.log('数据格式不正确，没有creditCode字段')
          }

          // 如果有有效数据
          if (dataArray.length > 0) {
            // 如果只有一条结果，自动选择
            if (dataArray.length === 1) {
              console.log('单条结果，自动选择:', dataArray[0])
              // 直接选择这条企业数据
              await this.selectEnterprise(dataArray[0])
            } else {
              console.log('多条结果，显示列表:', dataArray)
              // 多条结果时显示搜索结果列表
              this.messages.push({
                type: 'bot',
                searchResults: dataArray,
                timestamp: new Date()
              })
            }
          } else {
            console.log('没有找到有效的企业数据')
            // 没有找到结果
            this.messages.push({
              type: 'bot',
              content: `抱歉，没有找到与"${searchWord}"相关的企业信息，请尝试其他关键词。`,
              timestamp: new Date()
            })
          }
        } else {
          console.log('接口返回错误或无数据')
          // 没有找到结果
          this.messages.push({
            type: 'bot',
            content: `抱歉，没有找到与"${searchWord}"相关的企业信息，请尝试其他关键词。`,
            timestamp: new Date()
          })
        }
      } catch (error) {
        console.error('搜索企业失败:', error)
        this.messages.push({
          type: 'bot',
          content: '搜索过程中出现错误，请稍后重试。',
          timestamp: new Date()
        })
      } finally {
        this.isLoading = false
        this.scrollToBottom()
      }
    },
    
    // 选择企业
    async selectEnterprise(enterprise) {
      // 添加思考中消息
      this.messages.push({
        type: 'bot',
        thinking: true,
        timestamp: new Date()
      })

      this.scrollToBottom()

      try {
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 1500))

        const response = await getEnterpriseDetail(enterprise.id)

        if (response.code === 200 && response.data) {
          // 移除思考中消息
          this.messages.pop()

          // 开始分批次渲染企业档案
          await this.renderEnterpriseDetailStepByStep(response.data)
        } else {
          // 移除思考中消息
          this.messages.pop()

          this.messages.push({
            type: 'bot',
            content: '获取企业详情失败，请稍后重试。',
            timestamp: new Date()
          })
        }
      } catch (error) {
        console.error('获取企业详情失败:', error)

        // 移除思考中消息
        this.messages.pop()

        this.messages.push({
          type: 'bot',
          content: '获取企业详情过程中出现错误，请稍后重试。',
          timestamp: new Date()
        })
      } finally {
        this.scrollToBottom()
      }
    },

    // 分批次渲染企业详情
    async renderEnterpriseDetailStepByStep(enterpriseData) {
      // 处理企业数据，添加必要的字段
      const processedData = this.processEnterpriseData(enterpriseData)

      // 创建企业详情消息
      const detailMessage = {
        type: 'bot',
        enterpriseDetail: processedData,
        renderedSteps: [],
        isGenerating: true,
        currentGeneratingStep: null,
        timestamp: new Date()
      }

      this.messages.push(detailMessage)
      this.scrollToBottom()

      // 逐步渲染每个组件
      for (let i = 0; i < this.renderingSteps.length; i++) {
        const step = this.renderingSteps[i]

        // 设置当前正在生成的步骤
        detailMessage.currentGeneratingStep = step
        this.scrollToBottom()

        // 等待指定的延迟时间
        await new Promise(resolve => setTimeout(resolve, step.delay))

        // 添加已渲染的步骤
        detailMessage.renderedSteps.push(step)

        // 1秒后隐藏step-title
        setTimeout(() => {
          const renderedStep = detailMessage.renderedSteps.find(s => s.name === step.name)
          if (renderedStep) {
            this.$set(renderedStep, 'showTitle', false)
          }
        }, 1000)

        // 如果是最后一步，标记生成完成
        if (i === this.renderingSteps.length - 1) {
          detailMessage.isGenerating = false
          detailMessage.currentGeneratingStep = null
        }

        this.scrollToBottom()
      }
    },

    // 处理企业数据，添加必要的字段
    processEnterpriseData(apiData) {
      console.log('EnterpriseChat - 处理企业数据:', apiData)
      console.log('EnterpriseChat - 原始creditCode:', apiData.creditCode)
      const processedData = {
        // 基本信息
        id: apiData.id,
        name: apiData.name,
        creditCode: apiData.creditCode,
        property: apiData.property,
        status: apiData.status,
        statusLabel: apiData.statusLabel,
        createTime: apiData.createTime,
        regLocation: apiData.regLocation,
        realLocation: apiData.realLocation,

        // 法人信息
        legalPerson: {
          name: apiData.corporationName,
          phone: apiData.corporationPhoneNumber,
          certificateCode: apiData.corporationCertificateCode,
          certificateType: apiData.corporationCertificateType,
          certificateValidityPeriod: apiData.corporationCertificateValidityPeriod,
          certificateFront: apiData.corporationCertificateFront,
          certificateBack: apiData.corporationCertificateBack,
          certificateHand: apiData.corporationCertificateHand
        },

        // 负责人信息
        responsiblePerson: {
          name: apiData.handlerName,
          phone: apiData.handlerPhoneNumber,
          officePhone: apiData.handlerOfficePhoneNumber,
          email: apiData.handlerEmail,
          address: apiData.handlerPermanentAddress,
          certificateCode: apiData.handlerCertificateCode,
          certificateType: apiData.handlerCertificateType,
          certificateValidityPeriod: apiData.handlerCertificateValidityPeriod,
          certificateFront: apiData.handlerCertificateFront,
          certificateBack: apiData.handlerCertificateBack,
          handlerCertificateType:apiData.handlerCertificateType,
          certificateHand: apiData.handlerCertificateHand
        },

        // 营业执照图片
        licensePic: apiData.licensePic, // 营业执照图片

        // 关联数量统计
        screenNum: apiData.screenNum || 0,
        levelProjectNum: apiData.levelProjectNum || 0,
        websiteNum: apiData.websiteNum || 0,
        operatorNum: apiData.operatorNum || 0,
        netBarNum: apiData.netBarNum || 0,
        wifiNum: apiData.wifiNum || 0,

        // 为组件添加模拟数据
        recordCards: {
          securityRecord: {
            level: '三级',
            expiryDate: '2022.08.18 15:20',
            result: '通过'
          },
          appRecord: {
            recordNumber: 'xxxxx',
            operationStatus: '正常运营',
            updateTime: '2022.08.18 15:20'
          }
        },

        securityAssessment: {
          totalScore: 85,
          lastMonthChange: 5,
          aboveAverage: 12,
          riskLevel: '低风险',
          starCount: 4,
          radarData: [
            { name: '资质与合规情况', value: 3.2, lastMonthChange: -0.4, status: '需持续关注' },
            { name: '历史安全事件与威胁情报', value: 2.4, lastMonthChange: -4.4, status: '请重点关注' }
          ]
        },

        complianceRecords: [
          {
            id: 1,
            type: '立即现场检查记录',
            totalCount: 12,
            recentPeriod: '近半年两次',
            lastCheckTime: '2023-10-15',
            problemCount: 8,
            unresolved: 2,
            resolved: 6
          }
        ],

        complianceDetailList: [
          {
            id: 1,
            decisionNumber: '京网安罚字2018第001号',
            decisionDate: '2018-07-15',
            caseDescription: '未履行用户隐私保护责任，导致用户数据泄露。'
          }
        ],

        digitalAssetList: [
          { name: '上级接入商', count: 3, color: '#E3F2FD', borderColor: '#1976D2', textColor: '#1976D2' },
          { name: 'IP地址段', count: 101, color: '#F3E5F5', borderColor: '#7B1FA2', textColor: '#7B1FA2' }
        ]
      }

      console.log('EnterpriseChat - 处理后的企业数据:', processedData)
      return processedData
    },
    
    // 新建对话
    startNewConversation() {
      this.messages = []
      this.inputText = ''
      this.errorMessage = ''
      this.$message.success('已开始新对话')
    },

    // AI搜索接口
    async queryByAI(searchWord) {
      return request({
        url: '/system/sysEnterprise/queryByAI',
        method: 'get',
        params: {
          searchWord: searchWord
        }
      })
    },

    // 滚动到底部
    scrollToBottom() {
      this.$nextTick(() => {
        const chatMessages = this.$refs.chatMessages
        if (chatMessages) {
          chatMessages.scrollTop = chatMessages.scrollHeight
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.enterprise-chat-page {
  display: flex;
  width: 100%;
  height: calc(100vh - 120px); /* 减去头部导航的高度 */
  background-color: white;
  position: relative;
}

/* 聊天面板 */
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: white;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 20px 15px;
  border-bottom: 1px solid #e0e0e0;
  margin-top: -15px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 15px;

    h2 {
      margin: 0;
      font-size: 18px;
      color: #333;
    }

    .new-chat-btn {
      font-size: 13px;
      border-radius: 16px;
    }
  }

  .header-right {
    display: flex;
    gap: 8px;

    .tool-button {
      position: relative;
      border: 1px solid #d1d5db;
      background: #fff;
      color: #6b7280;
      border-radius: 16px;

      &.disabled {
        cursor: not-allowed;
        opacity: 0.6;
      }

      .upgrade-badge {
        position: absolute;
        top: -6px;
        right: -6px;
        background: #ef4444;
        color: #fff;
        font-size: 10px;
        padding: 2px 4px;
        border-radius: 6px;
        white-space: nowrap;
        line-height: 1;
      }
    }
  }
}

.chat-content {
  flex: 1;
  padding: 0;
  display: flex;
  flex-direction: column;
  height: calc(100% - 20px); /* 减去头部的高度 */
}

.chatBox {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
  background-color: white;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px;
  margin: 12px;
  border-radius: 8px;
  text-align: center;
}

.welcome-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px;

  .welcome-content {
    text-align: center;
    color: #666;

    .welcome-icon {
      font-size: 48px;
      color: #2196f3;
      margin-bottom: 16px;
    }

    h3 {
      margin: 0 0 8px 0;
      font-size: 20px;
      color: #333;
    }

    p {
      margin: 0;
      font-size: 14px;
      color: #666;
    }
  }
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #F6F7FB;
}

.message {
  margin-bottom: 20px;
  display: flex;
  align-items: flex-start;
}

.user-message-container {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  align-items: flex-start;
  gap: 12px;
}

.bot-message-container {
  display: flex;
  justify-content: flex-start;
  width: 100%;
  align-items: flex-start;
  gap: 12px;
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

// .user-avatar {
//   border: 2px solid #2196f3;
// }

.bot-avatar {
  border: 1px solid #4caf50;
}

.message-content {
  max-width: calc(95% - 50px);
  padding: 12px 16px;
  border-radius: 18px;
  line-height: 1.5;
  word-wrap: break-word;
  font-size: 15px;
}

.user-message {
  background-color: #e3f2fd;
  color: #0d47a1;
  border-top-right-radius: 4px;
}

.bot-message {
  background-color: white;
  color: #333;
  border-top-left-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

// 思考中指示器
.thinking-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;

  i {
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

// 搜索结果样式
.search-results {
  .search-title {
    font-weight: 600;
    margin-bottom: 12px;
    color: #1a1a1a;
  }

  .enterprise-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .enterprise-item {
    padding: 12px;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    background: #fafafa;

    &:hover {
      border-color: #2196f3;
      background: #f0f8ff;
    }

    .enterprise-name {
      font-weight: 600;
      color: #1a1a1a;
      margin-bottom: 4px;
    }

    .enterprise-info {
      display: flex;
      gap: 16px;
      font-size: 14px;
      color: #6b7280;

      .credit-code {
        font-family: monospace;
      }
    }
  }
}

// 企业详情样式
.enterprise-detail {
  .detail-title {
    font-weight: 600;
    margin-bottom: 16px;
    color: #1a1a1a;
    font-size: 16px;
  }

  .detail-container {
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    background: #F8FBFF;
    padding: 16px;

    // 覆盖企业组件的样式，使其适应对话框
    ::v-deep .enterprise-overview,
    ::v-deep .enterprise-basic-info,
    ::v-deep .record-cards,
    ::v-deep .security-assessment,
    ::v-deep .compliance-records,
    ::v-deep .digital-assets,
    ::v-deep .related-data-lists,
    ::v-deep .asset-change-module,
    ::v-deep .monthly-status-module {
      margin-bottom: 0;
      box-shadow: none;
      border: none;
    }
  }

  .detail-step {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    .step-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      font-weight: 600;
      color: #333;

      .step-indicator {
        width: 8px;
        height: 8px;
        background: #2196f3;
        border-radius: 50%;

        &.generating {
          background: #ff9800;
          animation: pulse 1.5s infinite;
        }
      }
    }

    .step-content {
      padding-left: 16px;

      &.generating {
        .generating-indicator {
          display: flex;
          align-items: center;
          gap: 8px;
          color: #ff9800;
          font-style: italic;

          i {
            animation: spin 1s linear infinite;
          }
        }
      }
    }
  }

  .generating-step {
    margin-bottom: 20px;

    .step-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      font-weight: 600;
      color: #333;

      .step-indicator.generating {
        width: 8px;
        height: 8px;
        background: #ff9800;
        border-radius: 50%;
        animation: pulse 1.5s infinite;
      }
    }

    .step-content.generating {
      padding-left: 16px;

      .generating-indicator {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #ff9800;
        font-style: italic;

        i {
          animation: spin 1s linear infinite;
        }
      }
    }
  }
}

.chat-input {
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 20px;
  border-top: 1px solid #e0e0e0;
  background-color: #fff;
}

.chat-input textarea {
  width: 100%;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 10px;
  resize: none;
  min-height: 60px;
  max-height: 150px;
  font-family: inherit;
  font-size: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  &:focus {
    outline: none;
    border-color: #2196f3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
  }
}



.action-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
}

.send-btn {
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  position: absolute;
  right: 30px;
  top:30px;

  &:hover {
    background-color: #1976d2;
  }

  &:disabled {
    background-color: #e0e0e0;
    color: #9e9e9e;
    cursor: not-allowed;
  }

  i {
    font-size: 18px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .enterprise-chat-page {
    height: calc(100vh - 100px);
  }

  .chat-header {
    padding: 12px 16px;

    .header-left {
      gap: 10px;
      .el-button {
        border-radius: 16px !important;
      }

      h2 {
        font-size: 16px;
      }

      .new-chat-btn {
        font-size: 12px;
        padding: 4px 8px;
      }
    }

    .header-right {
      gap: 6px;

      .tool-button {
        padding: 6px 10px;
        font-size: 11px;

        .upgrade-badge {
          font-size: 9px;
          padding: 1px 3px;
        }
      }
    }
  }

  .chat-messages {
    padding: 15px;
  }

  .message {
    margin-bottom: 15px;
  }

  .user-message-container,
  .bot-message-container {
    gap: 8px;
  }

  .avatar {
    width: 32px;
    height: 32px;
  }

  .message-content {
    max-width: calc(95% - 40px);
    padding: 10px 12px;
    font-size: 14px;
  }

  .chat-input {
    padding: 15px;
    position: relative;
    textarea {
      padding: 12px;
      font-size: 14px;
      min-height: 50px;
    }
  }

  .send-btn {
    width: 36px;
    height: 36px;

    i {
      font-size: 16px;
    }
  }

  .enterprise-item {
    padding: 10px;

    .enterprise-name {
      font-size: 14px;
    }

    .enterprise-info {
      font-size: 12px;
      gap: 12px;
    }
  }
}

@media (max-width: 480px) {
  .header-left {
    .new-chat-btn {
      span {
        display: none;
      }
    }
  }

  .header-right {
    .tool-button {
      span:not(.upgrade-badge) {
        display: none;
      }
    }
  }

  .message-content {
    max-width: calc(95% - 40px);
  }
}
</style>
