<template>
  <div class="app-container home">
    <el-row type="flex" justify="center">
       <el-col :xs="24" :sm="24" :md="20" :lg="20" >
        <div class="content-title">
          <div class="title-left">
            <img src="@/assets/images/tz/u117.png" alt="标题图标">
          </div>
          <div class="title-right">
            <h1>我是瑞霖捜，你的警务AI助手！</h1>
            <p>让我们开启全新的办公模式吧~</p>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="20">

      <section class="chat-container">
        <div class="chat-input-container">
          <div class="input-wrapper">
            <textarea
              v-model="inputMessage"
              placeholder="请输入您的问题"
              @keydown.enter.exact="handleEnterKey"
              rows="6"
              class="main-input"
            />
            <div class="input-options">
              <div class="button-group">
                <button class="option-btn" :class="{ 'active': activeOption === 'ds' }" @click="setActiveOption('ds')">
                  <i class="el-icon-cpu"></i> 深度思考(DeepSeek R1)
                </button>
                <button class="option-btn" :class="{ 'active': activeOption === 'qs' }" @click="setActiveOption('qs')">
                  <i class="el-icon-office-building"></i> 一企一档
                </button>
                <button class="option-btn" :disabled="true" :class="{ 'active': activeOption === 'hs' }" @click="setActiveOption('hs')">
                  <i class="el-icon-time"></i> 历史对话
                </button>
              </div>
              <div class="input-actions">
                <button
                  @click="navigateToActiveOption"
                  :disabled="!inputMessage.trim() && activeOption !== 'hs'"
                  class="send-btn"
                >
                  <i class="el-icon-s-promotion"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

    </el-row>
    <el-row  :gutter="20">
      <section class="task-block">
        <el-col :xs="24" :sm="24" :md="13" :lg="13" style="padding-left: 0px;padding-right: 0px;">
          <el-card class="update-log" style="margin-right: 15px;">
            <div slot="header" class="clearfix">
              <span class="card-top-tit">待办任务</span>
            </div>
            <div class="task-list">
              <el-tabs v-model="activeTaskTab">
                <el-tab-pane :label="'基础数据更新提醒 (' + superiorTasks.length + ')'" name="superior">
                  <ul class="task-items" v-if="superiorTasks.length > 0">
                    <li
                      v-for="(task, index) in superiorTasks"
                      :key="'superior-' + index"
                      class="reminder-item"
                      @click="handleReminderClick(task)"
                    >
                      截止目前 <span class="data-name">{{ task.name }}</span> 有
                      <span v-if="task.pendingUpdateNum > 0" class="pending-count">{{ task.pendingUpdateNum }}条数据待更新</span>
                      <span v-if="task.pendingUpdateNum > 0 && task.overdueNum > 0">，有 </span>
                      <span v-if="task.overdueNum > 0" class="overdue-count">{{ task.overdueNum }}条数据已超期</span>
                      ，请关注。
                    </li>
                  </ul>
                  <div v-else class="empty-task">
                    <i class="el-icon-s-order"></i>
                    <span>暂无基础数据更新提醒</span>
                  </div>
                </el-tab-pane>
                <!-- <el-tab-pane :label="'下级反馈任务 (' + Math.min(subordinateTasks.length, 5) + ')'" name="subordinate">
                  <ul class="task-items" v-if="subordinateTasks.length > 0">
                    <li
                      v-for="(task, index) in subordinateTasks.slice(0, 5)"
                      :key="'subordinate-' + index"
                      class="task-item"
                      @click="goTarget(task.link)"
                    >
                      <div class="task-index">{{ index + 1 }}</div>
                      <div class="task-content" :title="task.content">
                        {{ task.content }}
                        <span v-if="task.isNew" class="new-badge">NEW</span>
                      </div>
                      <div class="task-source">{{ task.source }}</div>
                      <div class="task-time">{{ formatDateTime(task.time) }}</div>
                    </li>
                  </ul>
                  <div v-else class="empty-task">
                    <i class="el-icon-s-order"></i>
                    <span>暂无下级反馈任务</span>
                  </div>
                </el-tab-pane> -->
              </el-tabs>
            </div>

          </el-card>
        </el-col>
        <el-col :xs="24" :sm="24" :md="13" :lg="11"  style="padding-left: 0px;padding-right: 0px;">
          <section class="business-block" style="margin-left: 15px;">
            <div class="flex-row">
              <h3>自主业务区块</h3>
              <!-- <p class="data-warehouse-btn disabled">
                数据视仓 >
                <span class="upgrade-badge">升级中</span>
              </p> -->
            </div>
            <div class="card-con">
              <!-- 卡片行 -->
              <div
                class="card-row"
                v-for="(row, rowIndex) in cardRows"
                :key="'row-' + rowIndex"
              >
                <!-- 卡片项 -->
                <div
                  class="card-item"
                  :class="{ 'disabled': card.title !== '一企一档' && card.title !== '基础数据管理' && card.title !== '行政监管' }"
                  v-for="(card, cardIndex) in row"
                  :key="'card-' + rowIndex + '-' + cardIndex"
                  @click="handleCardClick(card)"
                  :title="card.title !== '一企一档' && card.title !== '基础数据管理' && card.title !== '行政监管' ? '功能升级中' : ''"
                >
                  <!-- 卡片左侧图标 -->
                  <div class="card-left">
                    <img
                      :src="require('@/assets/images/tz/' + card.image)"
                      :alt="card.title + ' 图标'"
                    >
                  </div>

                  <!-- 卡片右侧内容 -->
                  <div class="card-right">
                    <!-- 卡片标题 -->
                    <div class="card-title">{{ card.title }}</div>

                    <!-- 卡片副标题和数字 -->
                    <div
                      class="card-subtitle"
                      v-if="card.subtit || card.subtitNum"
                    >
                      {{ card.subtit }}
                      <span
                        v-if="card.subtitNum"
                        class="subtitle-num"
                      >
                        {{ card.subtitNum }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </el-col>
      </section>
    </el-row>
  </div>
</template>

<script>
import { getDataUpdateReminderStat } from '@/api/data-management'

export default {
  name: "Index",
  computed: {
    cardRows() {
      // 将卡片列表分成每行两个卡片
      const rows = [];
      for (let i = 0; i < this.cardList.length; i += 2) {
        rows.push(this.cardList.slice(i, i + 2));
      }
      return rows;
    }
  },
  data() {
    return {
      // 版本号
      version: "3.6.5",
      inputMessage: "",
      isStreaming: false,
      // 当前激活的选项
      activeOption: 'ds',
      // 当前激活的任务选项卡
      activeTaskTab: 'superior',
      // 基础数据更新提醒数据
      superiorTasks: [],
      // 下级反馈任务数据
      subordinateTasks: [
        {
          content: '市场调研报告已完成，等待审核',
          source: '市场部',
          time: new Date('2023-10-15 15:30:00'),
          isNew: true,
          link: '/task/feedback/2001'
        },
        {
          content: '新员工培训计划已制定完毕',
          source: '培训部',
          time: new Date('2023-10-16 09:45:00'),
          isNew: false,
          link: '/task/feedback/2002'
        },
        {
          content: '客户满意度调查结果分析',
          source: '客服部',
          time: new Date('2023-10-17 13:20:00'),
          isNew: true,
          link: '/task/feedback/2003'
        },
        {
          content: '产品原型设计已完成',
          source: '设计部',
          time: new Date('2023-10-18 11:30:00'),
          isNew: true,
          link: '/task/feedback/2004'
        },
        {
          content: '销售数据周报已生成',
          source: '销售部',
          time: new Date('2023-10-19 17:00:00'),
          isNew: false,
          link: '/task/feedback/2005'
        }
      ],
      cardList: [
        {
          title: '一企一档',
          image: 'u155.png',
          time: new Date('2023-10-19 17:00:00'),
          subtit: '',
          subtitNum: '',
          link: '/enterprise/index'
        },
        {
          title: '数据视仓',
          image: 'u155.png',
          time: new Date('2023-10-19 17:00:00'),
          subtit: '',
          subtitNum: '',
          link: '/task/feedback/2005'
        },
        {
          title: '基础数据管理',
          image: 'u58.png',
          time: new Date('2023-10-19 17:00:00'),
          // subtit: '企业上报',
          subtitNum: '',
          link: '/data-management/index'
        },
        {
          title: '通知公告',
          image: 'u155.png',
          time: new Date('2023-10-19 17:00:00'),
          subtit: '',
          subtitNum: '',
          link: '/task/feedback/2005'
        },
        {
          title: '监督检查',
          image: 'u155.png',
          time: new Date('2023-10-19 17:00:00'),
          subtit: '',
          subtitNum: '',
          link: '/task/feedback/2005'
        },
        {
          title: '行政监管',
          image: 'u80.png',
          time: new Date('2023-10-19 17:00:00'),
          subtit: '未处理',
          subtitNum: '',
          link: '/data-management/scene'
        },
        {
          title: '任务中心',
          image: 'u155.png',
          time: new Date('2023-10-19 17:00:00'),
          subtit: '',
          subtitNum: '',
          link: '/task/feedback/2005'
        },
        {
          title: '人员虚实档案',
          image: 'u155.png',
          time: new Date('2023-10-19 17:00:00'),
          subtit: '',
          subtitNum: '',
          link: '/task/feedback/2005'
        },

      ],
      // 单位主体 ENTERPRISE，等保：LEVEL_PROTECT，运营商：OPERATOR_INFO，网吧：SITE_BASE，网站：WEB_SITE，app：APP，小程序：APPLET，电子屏：SCREEN 非经营：WIFI
      routeMap:{
        'SCREEN': '/data-management/screen',
        'LEVEL_PROTECT': '/data-management/levelprotect',
        'WEB_SITE': '/data-management/website',
        'APP': '/data-management/website/app',
        'APPLET': '/data-management/website/applet',
        'SITE_BASE': '/data-management/netbar',
        'OPERATOR_INFO': '/data-management/operator/baseinfo',
        'WIFI': '/data-management/wifi/site'
      }
    }
  },
  created() {
    // 默认激活深度思考选项
    this.activeOption = 'ds';
    // 加载基础数据更新提醒
    this.loadDataUpdateReminder();
  },
  methods: {
    // 加载基础数据更新提醒
    async loadDataUpdateReminder() {
      try {
        const response = await getDataUpdateReminderStat()
        if (response.code === 200 && response.data) {
          this.superiorTasks = response.data
        }
      } catch (error) {
        console.error('加载基础数据更新提醒失败:', error)
      }
    },

    // 处理回车键
    handleEnterKey(event) {
      event.preventDefault(); // 阻止默认的换行行为
      this.navigateToActiveOption();
    },



    // 设置激活选项
    setActiveOption(option) {
      this.activeOption = option;
    },

    // 根据当前激活选项进行跳转
    navigateToActiveOption() {
      const query = {};

      if (this.activeOption === 'ds') {
        // 深度思考(DeepSeek R1) - 跳转到 /ai/demo
        if (!this.inputMessage.trim()) {
          this.$message({
            message: '请输入问题后再点击',
            type: 'warning'
          });
          return;
        }
        query.message = this.inputMessage.trim();
        const url = this.$router.resolve({ path: '/ai/demo', query }).href;
        window.open(url, '_blank');
      } else if (this.activeOption === 'qs') {
        // 一企一档 - 跳转到 /enterprise/chat
        if (!this.inputMessage.trim()) {
          this.$message({
            message: '请输入企业名称或关键词后再点击',
            type: 'warning'
          });
          return;
        }
        query.searchWord = this.inputMessage.trim();
        const url = this.$router.resolve({ path: '/enterprise/chat', query }).href;
        window.open(url, '_blank');
      } else if (this.activeOption === 'hs') {
        // 历史对话 - 直接跳转到 /ai/demo
        const url = this.$router.resolve({ path: '/ai/demo' }).href;
        window.open(url, '_blank');
      }
    },

    handleTaskLine (item) {
      // if(!item)
      let path = this.routeMap[item.code]
      this.$router.push({path: path}).then(() => {
        // 路由跳转后滚动到顶部
        this.$nextTick(() => {
          window.scrollTo({
            top: 0,
            left: 0,
            behavior: 'smooth'
          });

          // 同时处理可能存在的其他滚动容器
          const containers = document.querySelectorAll('.app-main, .main-container, .page-container');
          containers.forEach(container => {
            container.scrollTop = 0;
          });
        });
      });
    },

    // 处理提醒项点击
    handleReminderClick(task) {
      // 根据code确定跳转路径
      let path = '';
      let overTimeQuery = 0;

      // 确定overTimeQuery参数
      if (task.pendingUpdateNum > 0 && task.overdueNum > 0) {
        // 如果两者都有，优先跳转到待更新
        overTimeQuery = 1;
      } else if (task.pendingUpdateNum > 0) {
        overTimeQuery = 1; // 待更新
      } else if (task.overdueNum > 0) {
        overTimeQuery = 2; // 已超期
      }
      // 根据code确定路径
      switch (task.code) {
        case 'SCREEN':
          path = '/data-management/screen';
          break;
        case 'SITE_BASE':
          path = '/data-management/netbar';
          break;
        case 'ENTERPRISE':
          path = '/data-management/screen/enterprise';
          break;
        case 'LEVEL_PROTECT':
          path = '/data-management/levelprotect';
          break;
        case 'OPERATOR_INFO':
          path = '/data-management/operator/baseinfo';
          break;
        case 'WEB_SITE':
          path = '/data-management/website';
          break;
        case 'APP':
          path = '/data-management/website/app';
          break;
        case 'APPLET':
          path = '/data-management/website/applet';
          break;
        case 'WIFI':
          path = '/data-management/wifi/site';
          break;
        default:
          this.$message.warning('未找到对应的页面');
          return;
      }
      console.log('--------path--------', path, overTimeQuery)
      // 跳转到对应页面并传递参数
      this.$router.push({
        path: path,
        query: {
          overTimeQuery: overTimeQuery
        }
      }).then(() => {
        // 路由跳转后滚动到顶部
        this.$nextTick(() => {
          window.scrollTo({
            top: 0,
            left: 0,
            behavior: 'smooth'
          });

          // 同时处理可能存在的其他滚动容器
          const containers = document.querySelectorAll('.app-main, .main-container, .page-container');
          containers.forEach(container => {
            container.scrollTop = 0;
          });
        });
      });
    },
    // 处理卡片点击
    handleCardClick(card) {
      if (card.title === '一企一档' || card.title === '基础数据管理' || card.title === '行政监管') {
        this.$router.push({path: card.link}).then(() => {
          // 路由跳转后滚动到顶部
          this.$nextTick(() => {
            window.scrollTo({
              top: 0,
              left: 0,
              behavior: 'smooth'
            });

            // 同时处理可能存在的其他滚动容器
            const containers = document.querySelectorAll('.app-main, .main-container, .page-container');
            containers.forEach(container => {
              container.scrollTop = 0;
            });
          });
        });
        // this.goTarget(card.link);
      } else {
        this.$message({
          message: '功能升级中，敬请期待',
          type: 'info'
        });
      }
    },

    goTarget(href) {
      window.open(href, "_blank")
    },





    // 格式化日期时间
    formatDateTime(date) {
      if (!date) return '';

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  margin-top:-8px;
  /* 添加背景图片 */
  background-image: url('~@/assets/images/tz/index_bg.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  position: relative;
  min-height: 100vh;
  padding: 20px;

  /* 添加背景遮罩，使内容更易读 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.85); /* 半透明白色背景 */
    z-index: -1;
  }

  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }

  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }

  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
    list-style-type: none;
  }

  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
    .card-top-tit {
      font-size: 18px;
      font-weight: 500;
      color: #303133;
    }
  }

  .task-list {
    padding: 0;

    .el-tabs__header {
      margin-bottom: 15px;
    }

    .task-items {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .reminder-item {
      background-color: #d8e7ff;
      border-radius: 12px;
      padding: 12px 16px;
      margin-bottom: 12px;
      font-size: 14px;
      line-height: 1.5;
      color: #333;
      cursor: pointer;
      transition: all 0.3s ease;

      &:last-child {
        margin-bottom: 0;
      }

      &:hover {
        background-color: #c6ddff;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(69, 132, 255, 0.2);
      }

      .data-name {
        font-weight: 600;
        color: #303133;
      }

      .pending-count {
        color: #FE9400;
        font-weight: 600;
      }

      .overdue-count {
        color: #DF2020;
        font-weight: 600;
      }
    }

    .task-item {
      display: flex;
      align-items: center;
      padding: 10px 0;
      border-bottom: 1px solid #ebeef5;
      cursor: pointer;
      transition: background-color 0.2s;

      /* 在移动端优化任务项布局 */
      @media (max-width: 768px) {
        flex-wrap: wrap;
        padding: 12px 0;
      }

      &:hover {
        background-color: #f5f7fa;
      }

      &:active {
        background-color: #D8E7FF;
      }

      &:last-child {
        border-bottom: none;
      }

      .task-index {
        width: 50px;
        text-align: center;
        color: #606266;
        font-size: 13px;

        /* 在移动端优化序号显示 */
        @media (max-width: 768px) {
          width: 30px;
          font-size: 12px;
        }
      }

      .task-content {
        flex: 1;
        padding: 0 10px;
        color: #303133;
        font-size: 13px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 50%;
        position: relative;

        /* 在移动端优化任务内容显示 */
        @media (max-width: 768px) {
          max-width: 100%;
          padding: 0 5px;
        }

        .new-badge {
          display: inline-block;
          padding: 0 5px;
          font-size: 10px;
          line-height: 16px;
          color: #fff;
          background-color: #f56c6c;
          border-radius: 10px;
          margin-left: 5px;
          vertical-align: top;
        }
      }

      .task-source {
        width: 100px;
        text-align: center;
        color: #606266;
        font-size: 13px;

        /* 在移动端优化任务来源显示 */
        @media (max-width: 768px) {
          width: auto;
          margin-right: 10px;
          margin-left: auto;
        }
      }

      .task-time {
        width: 150px;
        text-align: center;
        color: #909399;
        font-size: 13px;

        /* 在移动端优化时间显示 */
        @media (max-width: 768px) {
          width: 100%;
          text-align: right;
          margin-top: 5px;
          font-size: 12px;
        }
      }
    }

    .empty-task {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 30px 0;
      color: #909399;

      i {
        font-size: 32px;
        margin-bottom: 10px;
      }

      span {
        font-size: 14px;
      }
    }
  }
}
.task-block {
   width: 78%;
   display: flex;
   flex-wrap: nowrap;
   margin: 0 auto;
}
// 输入容器样式
.chat-input-container {
  display: flex;
  width: 88%;
  border: 1px solid rgba(224, 224, 224, 0.5);
  border-radius: 12px;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  margin: 0 auto;
}

.input-wrapper {
  width: 100%;
  padding: 15px 15px 0px;
  box-sizing: border-box;
}

.main-input {
  width: 100%;
  padding: 15px;
  border: none;
  border-radius: 8px;
  resize: none;
  font-family: inherit;
  font-size: 15px;
  background-color: #f9f9f9;
  border: 1px solid #e0e0e0;
  margin-bottom: 15px;

  &:focus {
    outline: none;
    border-color: #2196f3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
  }
}

.input-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 10px;
}

.send-btn {
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: #1976d2;
  }

  &:disabled {
    background-color: #e0e0e0;
    color: #9e9e9e;
    cursor: not-allowed;
  }

  i {
    font-size: 18px;
  }
}

.input-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;

  .button-group {
    display: flex;
    gap: 10px;
    flex: 1;
  }

  .input-actions {
    margin-left: 15px;
  }
  .option-btn {
    border-radius: 24px;
  }
}

.button-group {
  display: flex;
  align-items: center;
  gap: 10px;

  /* 在移动端优化按钮组布局 */
  @media (max-width: 768px) {
    flex-wrap: wrap;
    justify-content: center;
    gap: 8px;
  }
}

.option-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  color: #666;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;

  /* 在移动端优化按钮样式 */
  @media (max-width: 768px) {
    padding: 5px 10px;
    font-size: 12px;
    white-space: nowrap;
  }

  &:hover {
    background-color: #e9e9e9;
    border-color: #2196f3;
    color: #2196f3;
  }

  &.active {
    background-color: #e3f2fd;
    border-color: #2196f3;
    color: #2196f3;
  }

  &:disabled {
    background-color: #f5f5f5;
    border-color: #e0e0e0;
    color: #c0c4cc;
    cursor: not-allowed;
    opacity: 0.6;

    &:hover {
      background-color: #f5f5f5;
      border-color: #e0e0e0;
      color: #c0c4cc;
    }
  }

  i {
    font-size: 14px;
  }
}

.input-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}


/* 任务列表样式已更新，使用 ul 和 li 替代 el-table */

/* 卡片样式 */
/* 标题行 */
.flex-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 5px;

  /* 标题 */
  h3 {
    margin: 0;
    font-size: 18px;
    color: #303133;
    font-weight: 600;
    position: relative;
    padding-left: 12px;

    /* 左侧装饰线 */
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background-color: #2196f3;
      border-radius: 2px;
    }
  }

  /* "更多"链接 */
  p {
    margin: 0;
    font-size: 14px;
    color: #2196f3;
    cursor: pointer;
    transition: color 0.2s ease;

    &:hover {
      color: #1976d2;
      text-decoration: underline;
    }

    /* 数据视仓按钮禁用样式 */
    &.data-warehouse-btn.disabled {
      color: #c0c4cc;
      cursor: not-allowed;
      opacity: 0.6;
      position: relative;

      &:hover {
        color: #c0c4cc;
        text-decoration: none;
      }

      .upgrade-badge {
        display: inline-block;
        background-color: #f56c6c;
        color: #fff;
        font-size: 10px;
        padding: 1px 6px;
        border-radius: 8px;
        margin-left: 8px;
        vertical-align: middle;
        font-weight: 500;
      }
    }
  }
}

/* 卡片容器 */
.card-con {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 5px;
}

/* 卡片行 */
.card-row {
  display: flex;
  gap: 20px;

  /* 响应式布局 - 在小屏幕上垂直排列 */
  @media (max-width: 768px) {
    flex-direction: column;
  }
}

/* 卡片项 */
.card-item {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  border: 1px solid rgba(224, 224, 224, 0.5);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);

  /* 悬停效果 */
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    border-color: rgba(33, 150, 243, 0.4);
    background-color: rgba(255, 255, 255, 0.95);
  }

  /* 点击效果 */
  &:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  /* 禁用状态 */
  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: rgba(245, 245, 245, 0.9);

    &:hover {
      transform: none;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      border-color: rgba(224, 224, 224, 0.5);
      background-color: rgba(245, 245, 245, 0.9);
    }

    &:active {
      transform: none;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .card-left img {
      filter: grayscale(100%);
    }

    .card-right {
      color: #999;
    }
  }
}

/* 卡片左侧图标 */
.card-left {
  width: 65px;
  height: 65px;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform 0.2s ease;

    /* 图标悬停效果 */
    .card-item:hover & {
      transform: scale(1.05);
    }
  }
}

/* 卡片右侧内容 */
.card-right {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
  min-width: 0; /* 确保文本可以正确截断 */
}

/* 卡片标题 */
.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 卡片副标题 */
.card-subtitle {
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;

  /* 副标题数字标记 */
  .subtitle-num {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #f56c6c;
    color: #fff;
    border-radius: 10px;
    padding: 0 6px;
    height: 18px;
    margin-left: 6px;
    font-size: 12px;
    font-weight: 500;
  }
}

/* 标题区域样式 */
.content-title {
  display: flex;
  align-items: center;
  padding: 20px;
  margin-bottom: 20px;
  // background-color: rgba(255, 255, 255, 0.9);
  // border-radius: 12px;
  // box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  // backdrop-filter: blur(5px);
  // -webkit-backdrop-filter: blur(5px);
  // border: 1px solid rgba(224, 224, 224, 0.5);

  /* 在移动端优化标题区域布局 */
  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
    padding: 15px;
  }

  .title-left {
    flex: 0 0 auto;
    margin-right: 20px;

    /* 在移动端优化图片布局 */
    @media (max-width: 768px) {
      margin-right: 0;
      margin-bottom: 15px;
    }

    img {
      width: 120px;
      height: 120px;
      object-fit: contain;
      // border-radius: 50%;
      // box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      padding: 5px;
      // background-color: #fff;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }

      /* 在移动端优化图片大小 */
      @media (max-width: 768px) {
        width: 70px;
        height: 70px;
      }
    }
  }

  .title-right {
    flex: 1;

    /* 在移动端优化文字布局 */
    @media (max-width: 768px) {
      width: 100%;
    }

    h1 {
      margin: 0 0 10px 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;

      /* 在移动端优化标题字体大小 */
      @media (max-width: 768px) {
        font-size: 20px;
        margin-bottom: 8px;
      }
    }

    p {
      margin: 0;
      font-size: 16px;
      color: #606266;

      /* 在移动端优化副标题字体大小 */
      @media (max-width: 768px) {
        font-size: 14px;
      }
    }
  }
}
.update-log {
  padding: 15px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(224, 224, 224, 0.5);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%; /* 确保两个区块高度一致 */
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  }
}

.business-block {
  padding: 15px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(224, 224, 224, 0.5);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%; /* 确保两个区块高度一致 */
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  }
}

/* 确保在小屏幕上有适当的间距和垂直排列 */
@media (max-width: 991px) {
  .update-log, .business-block {
    margin-bottom: 20px;
  }

  /* 在小屏幕上强制垂直排列 */
  .el-row {
    display: block !important;
  }

  .el-col {
    width: 100% !important;
  }
}

/* 确保在大屏幕上两个区块并排显示且高度一致 */
@media (min-width: 1051px) {
  .el-row {
    display: flex;
    flex-wrap: nowrap; /* 防止换行 */

    .el-col {
      display: flex;
      flex-direction: column;
      float: none; /* 覆盖 Element UI 的默认浮动 */
    }
    .chat-container {
      width: 89%;
      margin:-30px auto 30px;
    }

    .el-card, .business-block {
      flex: 1;
      display: flex;
      flex-direction: column;
      height: 100%; /* 确保高度一致 */
    }

    .el-card .el-card__body {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto; /* 内容过多时可滚动 */
    }

    .task-list, .card-con {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }

  /* 确保宽度比例正确 */
  .el-col[class*=md-13] {
    width: 56% !important;
  }

  .el-col[class*=md-11] {
    width: 44% !important;
  }
}


@media (max-width: 1050px) {
  .chat-container{
    width: 96%;
    margin:-15px auto 15px;
  }
  .chat-input-container {
    width: 100%;
  }
  .task-block {
     width: 96%;
  }
}
</style>

