控制台报错：
Vue warn]: Error in directive copy-on-dblclick unbind hook: "TypeError: 'caller', 'callee', and 'arguments' properties may not be accessed on strict mode functions or the arguments objects for calls to them"

found in

---> <NetbarIndex> at src/views/data-management/netbar/index.vue
       <AppMain> at src/layout/components/AppMain.vue
         <Layout> at src/layout/index.vue
           <App> at src/App.vue
             <Root>
warn @ vue.runtime.esm.js:620
logError @ vue.runtime.esm.js:1883
globalHandleError @ vue.runtime.esm.js:1878
handleError @ vue.runtime.esm.js:1838
callHook$1 @ vue.runtime.esm.js:6672
_update @ vue.runtime.esm.js:6631
updateDirectives @ vue.runtime.esm.js:6572
unbindDirectives @ vue.runtime.esm.js:6566
invokeDestroyHook @ vue.runtime.esm.js:6105
patch @ vue.runtime.esm.js:6457
Vue.$destroy @ vue.runtime.esm.js:3995
destroy @ vue.runtime.esm.js:3156
invokeDestroyHook @ vue.runtime.esm.js:6104
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
patch @ vue.runtime.esm.js:6457
Vue.$destroy @ vue.runtime.esm.js:3995
destroy @ vue.runtime.esm.js:3156
invokeDestroyHook @ vue.runtime.esm.js:6104
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
patch @ vue.runtime.esm.js:6457
Vue.$destroy @ vue.runtime.esm.js:3995
destroy @ vue.runtime.esm.js:3156
invokeDestroyHook @ vue.runtime.esm.js:6104
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
patch @ vue.runtime.esm.js:6457
Vue.$destroy @ vue.runtime.esm.js:3995
destroy @ vue.runtime.esm.js:3156
invokeDestroyHook @ vue.runtime.esm.js:6104
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
patch @ vue.runtime.esm.js:6457
Vue.$destroy @ vue.runtime.esm.js:3995
destroy @ vue.runtime.esm.js:3156
invokeDestroyHook @ vue.runtime.esm.js:6104
removeVnodes @ vue.runtime.esm.js:6120
patch @ vue.runtime.esm.js:6548
Vue._update @ vue.runtime.esm.js:3942
updateComponent @ vue.runtime.esm.js:4060
get @ vue.runtime.esm.js:4473
run @ vue.runtime.esm.js:4548
flushSchedulerQueue @ vue.runtime.esm.js:4304
eval @ vue.runtime.esm.js:1979
flushCallbacks @ vue.runtime.esm.js:1905
Promise.then
timerFunc @ vue.runtime.esm.js:1932
nextTick @ vue.runtime.esm.js:1989
queueWatcher @ vue.runtime.esm.js:4396
update @ vue.runtime.esm.js:4538
notify @ vue.runtime.esm.js:731
reactiveSetter @ vue.runtime.esm.js:1056
eval @ vue-router.esm.js:2933
eval @ vue-router.esm.js:2932
updateRoute @ vue-router.esm.js:2353
eval @ vue-router.esm.js:2207
eval @ vue-router.esm.js:2341
step @ vue-router.esm.js:1945
step @ vue-router.esm.js:1952
runQueue @ vue-router.esm.js:1956
eval @ vue-router.esm.js:2336
step @ vue-router.esm.js:1945
eval @ vue-router.esm.js:1949
eval @ vue-router.esm.js:2323
eval @ vue-router.esm.js:2071
eval @ vue-router.esm.js:2147
Promise.then
eval @ vue-router.esm.js:2094
eval @ vue-router.esm.js:2115
eval @ vue-router.esm.js:2115
flatMapComponents @ vue-router.esm.js:2114
eval @ vue-router.esm.js:2050
iterator @ vue-router.esm.js:2301
step @ vue-router.esm.js:1948
step @ vue-router.esm.js:1952
step @ vue-router.esm.js:1952
eval @ vue-router.esm.js:1949
eval @ vue-router.esm.js:2323
eval @ permission.js:46
iterator @ vue-router.esm.js:2301
step @ vue-router.esm.js:1948
step @ vue-router.esm.js:1952
runQueue @ vue-router.esm.js:1956
confirmTransition @ vue-router.esm.js:2331
transitionTo @ vue-router.esm.js:2204
push @ vue-router.esm.js:2545
eval @ vue-router.esm.js:2964
push @ vue-router.esm.js:2963
push @ index.js:640
handleMenuSelect @ cjs.js?!./node_modules/babel-loader/lib/index.js!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/components/BasicDataSidebar/index.vue?vue&type=script&lang=js:181
invokeWithErrorHandling @ vue.runtime.esm.js:1853
invoker @ vue.runtime.esm.js:2178
invokeWithErrorHandling @ vue.runtime.esm.js:1853
Vue.$emit @ vue.runtime.esm.js:3882
handleItemClick @ element-ui.common.js:3367
invokeWithErrorHandling @ vue.runtime.esm.js:1853
Vue.$emit @ vue.runtime.esm.js:3882
dispatch @ emitter.js:29
handleClick @ element-ui.common.js:4096
invokeWithErrorHandling @ vue.runtime.esm.js:1853
invoker @ vue.runtime.esm.js:2178
original._wrapper @ vue.runtime.esm.js:6907
permission.js:46 TypeError: 'caller', 'callee', and 'arguments' properties may not be accessed on strict mode functions or the arguments objects for calls to them
    at unbind (copy.js:79:1)
    at callHook$1 (vue.runtime.esm.js:6670:7)
    at _update (vue.runtime.esm.js:6631:9)
    at updateDirectives (vue.runtime.esm.js:6572:5)
    at Array.unbindDirectives (vue.runtime.esm.js:6566:5)
    at invokeDestroyHook (vue.runtime.esm.js:6105:64)
    at VueComponent.patch [as __patch__] (vue.runtime.esm.js:6457:30)
    at Vue.$destroy (vue.runtime.esm.js:3995:8)
    at destroy (vue.runtime.esm.js:3156:27)
    at invokeDestroyHook (vue.runtime.esm.js:6104:59)
logError @ vue.runtime.esm.js:1887
globalHandleError @ vue.runtime.esm.js:1878
handleError @ vue.runtime.esm.js:1838
callHook$1 @ vue.runtime.esm.js:6672
_update @ vue.runtime.esm.js:6631
updateDirectives @ vue.runtime.esm.js:6572
unbindDirectives @ vue.runtime.esm.js:6566
invokeDestroyHook @ vue.runtime.esm.js:6105
patch @ vue.runtime.esm.js:6457
Vue.$destroy @ vue.runtime.esm.js:3995
destroy @ vue.runtime.esm.js:3156
invokeDestroyHook @ vue.runtime.esm.js:6104
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
patch @ vue.runtime.esm.js:6457
Vue.$destroy @ vue.runtime.esm.js:3995
destroy @ vue.runtime.esm.js:3156
invokeDestroyHook @ vue.runtime.esm.js:6104
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
patch @ vue.runtime.esm.js:6457
Vue.$destroy @ vue.runtime.esm.js:3995
destroy @ vue.runtime.esm.js:3156
invokeDestroyHook @ vue.runtime.esm.js:6104
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
patch @ vue.runtime.esm.js:6457
Vue.$destroy @ vue.runtime.esm.js:3995
destroy @ vue.runtime.esm.js:3156
invokeDestroyHook @ vue.runtime.esm.js:6104
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
patch @ vue.runtime.esm.js:6457
Vue.$destroy @ vue.runtime.esm.js:3995
destroy @ vue.runtime.esm.js:3156
invokeDestroyHook @ vue.runtime.esm.js:6104
removeVnodes @ vue.runtime.esm.js:6120
patch @ vue.runtime.esm.js:6548
Vue._update @ vue.runtime.esm.js:3942
updateComponent @ vue.runtime.esm.js:4060
get @ vue.runtime.esm.js:4473
run @ vue.runtime.esm.js:4548
flushSchedulerQueue @ vue.runtime.esm.js:4304
eval @ vue.runtime.esm.js:1979
flushCallbacks @ vue.runtime.esm.js:1905
Promise.then
timerFunc @ vue.runtime.esm.js:1932
nextTick @ vue.runtime.esm.js:1989
queueWatcher @ vue.runtime.esm.js:4396
update @ vue.runtime.esm.js:4538
notify @ vue.runtime.esm.js:731
reactiveSetter @ vue.runtime.esm.js:1056
eval @ vue-router.esm.js:2933
eval @ vue-router.esm.js:2932
updateRoute @ vue-router.esm.js:2353
eval @ vue-router.esm.js:2207
eval @ vue-router.esm.js:2341
step @ vue-router.esm.js:1945
step @ vue-router.esm.js:1952
runQueue @ vue-router.esm.js:1956
eval @ vue-router.esm.js:2336
step @ vue-router.esm.js:1945
eval @ vue-router.esm.js:1949
eval @ vue-router.esm.js:2323
eval @ vue-router.esm.js:2071
eval @ vue-router.esm.js:2147
Promise.then
eval @ vue-router.esm.js:2094
eval @ vue-router.esm.js:2115
eval @ vue-router.esm.js:2115
flatMapComponents @ vue-router.esm.js:2114
eval @ vue-router.esm.js:2050
iterator @ vue-router.esm.js:2301
step @ vue-router.esm.js:1948
step @ vue-router.esm.js:1952
step @ vue-router.esm.js:1952
eval @ vue-router.esm.js:1949
eval @ vue-router.esm.js:2323
eval @ permission.js:46
iterator @ vue-router.esm.js:2301
step @ vue-router.esm.js:1948
step @ vue-router.esm.js:1952
runQueue @ vue-router.esm.js:1956
confirmTransition @ vue-router.esm.js:2331
transitionTo @ vue-router.esm.js:2204
push @ vue-router.esm.js:2545
eval @ vue-router.esm.js:2964
push @ vue-router.esm.js:2963
push @ index.js:640
handleMenuSelect @ cjs.js?!./node_modules/babel-loader/lib/index.js!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/components/BasicDataSidebar/index.vue?vue&type=script&lang=js:181
invokeWithErrorHandling @ vue.runtime.esm.js:1853
invoker @ vue.runtime.esm.js:2178
invokeWithErrorHandling @ vue.runtime.esm.js:1853
Vue.$emit @ vue.runtime.esm.js:3882
handleItemClick @ element-ui.common.js:3367
invokeWithErrorHandling @ vue.runtime.esm.js:1853
Vue.$emit @ vue.runtime.esm.js:3882
dispatch @ emitter.js:29
handleClick @ element-ui.common.js:4096
invokeWithErrorHandling @ vue.runtime.esm.js:1853
invoker @ vue.runtime.esm.js:2178
original._wrapper @ vue.runtime.esm.js:6907
permission.js:46 [Vue warn]: Error in directive co

控制台报错：
[Vue warn]: Error in directive copy-on-dblclick unbind hook: "TypeError: 'caller', 'callee', and 'arguments' properties may not be accessed on strict mode functions or the arguments objects for calls to them"

found in

---> <NetbarIndex> at src/views/data-management/netbar/index.vue
       <AppMain> at src/layout/components/AppMain.vue
         <Layout> at src/layout/index.vue
           <App> at src/App.vue
             <Root>
warn @ vue.runtime.esm.js:620
logError @ vue.runtime.esm.js:1883
globalHandleError @ vue.runtime.esm.js:1878
handleError @ vue.runtime.esm.js:1838
callHook$1 @ vue.runtime.esm.js:6672
_update @ vue.runtime.esm.js:6631
updateDirectives @ vue.runtime.esm.js:6572
unbindDirectives @ vue.runtime.esm.js:6566
invokeDestroyHook @ vue.runtime.esm.js:6105
patch @ vue.runtime.esm.js:6457
Vue.$destroy @ vue.runtime.esm.js:3995
destroy @ vue.runtime.esm.js:3156
invokeDestroyHook @ vue.runtime.esm.js:6104
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
patch @ vue.runtime.esm.js:6457
Vue.$destroy @ vue.runtime.esm.js:3995
destroy @ vue.runtime.esm.js:3156
invokeDestroyHook @ vue.runtime.esm.js:6104
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
patch @ vue.runtime.esm.js:6457
Vue.$destroy @ vue.runtime.esm.js:3995
destroy @ vue.runtime.esm.js:3156
invokeDestroyHook @ vue.runtime.esm.js:6104
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
patch @ vue.runtime.esm.js:6457
Vue.$destroy @ vue.runtime.esm.js:3995
destroy @ vue.runtime.esm.js:3156
invokeDestroyHook @ vue.runtime.esm.js:6104
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
invokeDestroyHook @ vue.runtime.esm.js:6109
patch @ vue.runtime.esm.js:6457
Vue.$destroy @ vue.runtime.esm.js:3995
destroy @ vue.runtime.esm.js:3156
invokeDestroyHook @ vue.runtime.esm.js:6104
removeVnodes @ vue.runtime.esm.js:6120
patch @ vue.runtime.esm.js:6548
Vue._update @ vue.runtime.esm.js:3942
updateComponent @ vue.runtime.esm.js:4060
get @ vue.runtime.esm.js:4473
run @ vue.runtime.esm.js:4548
flushSchedulerQueue @ vue.runtime.esm.js:4304
eval @ vue.runtime.esm.js:1979
flushCallbacks @ vue.runtime.esm.js:1905
Promise.then
timerFunc @ vue.runtime.esm.js:1932
nextTick @ vue.runtime.esm.js:1989
queueWatcher @ vue.runtime.esm.js:4396
update @ vue.runtime.esm.js:4538
notify @ vue.runtime.esm.js:731
reactiveSetter @ vue.runtime.esm.js:1056
eval @ vue-router.esm.js:2933
eval @ vue-router.esm.js:2932
updateRoute @ vue-router.esm.js:2353
eval @ vue-router.esm.js:2207
eval @ vue-router.esm.js:2341
step @ vue-router.esm.js:1945
step @ vue-router.esm.js:1952
runQueue @ vue-router.esm.js:1956
eval @ vue-router.esm.js:2336
step @ vue-router.esm.js:1945
eval @ vue-router.esm.js:1949
eval @ vue-router.esm.js:2323
eval @ vue-router.esm.js:2071
eval @ vue-router.esm.js:2147
Promise.then
eval @ vue-router.esm.js:2094
eval @ vue-router.esm.js:2115
eval @ vue-router.esm.js:2115
flatMapComponents @ vue-router.esm.js:2114
eval @ vue-router.esm.js:2050
iterator @ vue-router.esm.js:2301
step @ vue-router.esm.js:1948
step @ vue-router.esm.js:1952
step @ vue-router.esm.js:1952
eval @ vue-router.esm.js:1949
eval @ vue-router.esm.js:2323
eval @ permission.js:46
iterator @ vue-router.esm.js:2301
step @ vue-router.esm.js:1948
step @ vue-router.esm.js:1952
runQueue @ vue-router.esm.js:1956
confirmTransition @ vue-router.esm.js:2331
transitionTo @ vue-router.esm.js:2204
push @ vue-router.esm.js:2545
eval @ vue-router.esm.js:2964
push @ vue-router.esm.js:2963
push @ index.js:640
handleMenuSelect @ cjs.js?!./node_modules/babel-loader/lib/index.js!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/components/BasicDataSidebar/index.vue?vue&type=script&lang=js:181
invokeWithErrorHandling @ vue.runtime.esm.js:1853
invoker @ vue.runtime.esm.js:2178
invokeWithErrorHandling @ vue.runtime.esm.js:1853
Vue.$emit @ vue.runtime.esm.js:3882
handleItemClick @ element-ui.common.js:3367
invokeWithErrorHandling @ vue.runtime.esm.js:1853
Vue.$emit @ vue.runtime.esm.js:3882
dispatch @ emitter.js:29
handleClick @ element-ui.common.js:4096
invokeWithErrorHandling @ vue.runtime.esm.js:1853
invoker @ vue.runtime.esm.js:2178
original._wrapper @ vue.runtime.esm.js:6907
