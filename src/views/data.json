//maxkb 获取历史记录接口地址 api/application/9e4a8c50-6173-11f0-b28f-0242ac120003/chat/cc8d3226-617c-11f0-b28f-0242ac120003/chat_record/1/20?order_asc=false
// 接口返回示例如下
// {
//     "code": 200,
//     "message": "成功",
//     "data": {
//         "total": 2,
//         "records": [
//             {
//                 "id": "d3103b7a-617c-11f0-9e33-0242ac120003",
//                 "chat_id": "cc8d3226-617c-11f0-b28f-0242ac120003",
//                 "vote_status": "-1",
//                 "problem_text": "今天天气怎么样？",
//                 "answer_text": "\n我无法直接获取实时天气信息，但你可以通过以下方式快速了解：\n\n1. 打开手机上的天气应用或网站（如中国天气网、墨迹天气等）\n2. 或使用智能音箱询问当前气温和天气状况\n\n如果你告诉我具体的城市名称，我可以提供该地区的近期天气趋势分析哦！😊",
//                 "message_tokens": 140,
//                 "answer_tokens": 911,
//                 "const": 0,
//                 "improve_paragraph_id_list": [],
//                 "run_time": 12.19234848022461,
//                 "index": 1,
//                 "answer_text_list": [
//                     [
//                         {
//                             "content": "\n我无法直接获取实时天气信息，但你可以通过以下方式快速了解：\n\n1. 打开手机上的天气应用或网站（如中国天气网、墨迹天气等）\n2. 或使用智能音箱询问当前气温和天气状况\n\n如果你告诉我具体的城市名称，我可以提供该地区的近期天气趋势分析哦！😊",
//                             "view_type": "ai-chat-node",
//                             "child_node": {},
//                             "real_node_id": "ai-chat-node",
//                             "chat_record_id": "ai-chat-node",
//                             "runtime_node_id": "ai-chat-node",
//                             "reasoning_content": "\n嗯，用户问了一个简单的天气查询问题。虽然看起来是个日常问候，但可能隐含了出行计划或健康考量的需求——比如是否需要带伞，或者是否适合户外活动。\n\n作为DeepSeek开发的AI助手，我无法直接获取实时天气数据，这涉及到第三方服务接入和隐私协议限制。不过用户可能并不清楚这一点，ta期待的是一个即时准确的回答。\n\n考虑到用户没有提供具体地点，回复时应该保持中立但友好的态度，并主动引导补充信息。用“😊”表情可以软化语气，避免让用户觉得被敷衍。同时给出明确的行动建议（查询天气应用），既解决了表面问题又暗示了我的能力边界。\n\n这种情况下最重要的是平衡：既要诚实说明限制，又要提供有价值的替代方案。用户如果真有需求，应该会顺着这个方向继续提问。\n"
//                         }
//                     ]
//                 ],
//                 "create_time": "2025-07-15T21:08:40.840184+08:00",
//                 "update_time": "2025-07-15T21:08:40.840195+08:00",
//                 "padding_problem_text": "今天天气怎么样？",
//                 "dataset_list": [],
//                 "paragraph_list": [],
//                 "execution_details": [
//                     {
//                         "cost": 0,
//                         "model_id": "59bb1fa0-6173-11f0-b039-0242ac120003",
//                         "run_time": 1.6016120910644531,
//                         "step_type": "chat_step",
//                         "message_list": [
//                             {
//                                 "role": "ai",
//                                 "content": "警务Ai助手"
//                             },
//                             {
//                                 "role": "user",
//                                 "content": "今天天气怎么样？"
//                             },
//                             {
//                                 "role": "ai",
//                                 "content": "<think>\n嗯，用户问了一个简单的天气查询问题。虽然看起来是个日常问候，但可能隐含了出行计划或健康考量的需求——比如是否需要带伞，或者是否适合户外活动。\n\n作为DeepSeek开发的AI助手，我无法直接获取实时天气数据，这涉及到第三方服务接入和隐私协议限制。不过用户可能并不清楚这一点，ta期待的是一个即时准确的回答。\n\n考虑到用户没有提供具体地点，回复时应该保持中立但友好的态度，并主动引导补充信息。用“😊”表情可以软化语气，避免让用户觉得被敷衍。同时给出明确的行动建议（查询天气应用），既解决了表面问题又暗示了我的能力边界。\n\n这种情况下最重要的是平衡：既要诚实说明限制，又要提供有价值的替代方案。用户如果真有需求，应该会顺着这个方向继续提问。\n</think>\n我无法直接获取实时天气信息，但你可以通过以下方式快速了解：\n\n1. 打开手机上的天气应用或网站（如中国天气网、墨迹天气等）\n2. 或使用智能音箱询问当前气温和天气状况\n\n如果你告诉我具体的城市名称，我可以提供该地区的近期天气趋势分析哦！😊"
//                             }
//                         ],
//                         "answer_tokens": 896,
//                         "message_tokens": 31
//                     },
//                     {
//                         "cost": 0,
//                         "run_time": 0.0007097721099853516,
//                         "step_type": "search_step",
//                         "model_name": null,
//                         "problem_text": "今天天气怎么样？",
//                         "answer_tokens": 0,
//                         "message_tokens": 0,
//                         "paragraph_list": []
//                     },
//                     {
//                         "cost": 0,
//                         "model_id": "59bb1fa0-6173-11f0-b039-0242ac120003",
//                         "run_time": 10.589271306991577,
//                         "step_type": "problem_padding",
//                         "problem_text": "今天天气怎么样？",
//                         "answer_tokens": 15,
//                         "message_tokens": 109,
//                         "padding_problem_text": "今天天气怎么样？"
//                     }
//                 ]
//             },
//             {
//                 "id": "fc10b220-617c-11f0-a844-0242ac120003",
//                 "chat_id": "cc8d3226-617c-11f0-b28f-0242ac120003",
//                 "vote_status": "-1",
//                 "problem_text": "14的平方是多少？",
//                 "answer_text": "\n14 的平方是 **196**。  \n\n如果你需要其他数字的平方计算或数学公式帮助，随时告诉我！ 😊",
//                 "message_tokens": 661,
//                 "answer_tokens": 870,
//                 "const": 0,
//                 "improve_paragraph_id_list": [],
//                 "run_time": 5.476612567901611,
//                 "index": 2,
//                 "answer_text_list": [
//                     [
//                         {
//                             "content": "\n14 的平方是 **196**。  \n\n如果你需要其他数字的平方计算或数学公式帮助，随时告诉我！ 😊",
//                             "view_type": "ai-chat-node",
//                             "child_node": {},
//                             "real_node_id": "ai-chat-node",
//                             "chat_record_id": "ai-chat-node",
//                             "runtime_node_id": "ai-chat-node",
//                             "reasoning_content": "\n嗯，用户突然从问天气切换到数学问题了。可能ta正在快速测试我的功能边界，或者单纯想确认基础计算能力。考虑到之前对话是关于实时信息查询，现在这个问题更偏向知识型需求。\n\n这个计算很简单，但需要确保准确性。14的平方确实是196，我记得初中代数里有这么个知识点：任何整数n的平方都是n乘以自身的结果。不过用户可能只是想要一个快速答案，不需要深入讲解。\n\nta连续问了两个不同领域的问题，或许在评估我的响应速度和可靠性？也可能是在为某个项目收集基础数据。既然问题本身不复杂，保持简洁明了的回答最合适，但可以主动提出扩展帮助——比如计算其他数字的平方或提供公式说明，这样既完成当前任务又预留了延伸空间。\n\n要注意避免让用户觉得我在敷衍，所以除了给出答案外，还应该展示出愿意提供更多相关信息的姿态。毕竟有些人问完基础问题后可能需要进阶知识。\n"
//                         }
//                     ]
//                 ],
//                 "create_time": "2025-07-15T21:09:49.607487+08:00",
//                 "update_time": "2025-07-15T21:09:49.607500+08:00",
//                 "padding_problem_text": "14的平方是多少？",
//                 "dataset_list": [],
//                 "paragraph_list": [],
//                 "execution_details": [
//                     {
//                         "cost": 0,
//                         "model_id": "59bb1fa0-6173-11f0-b039-0242ac120003",
//                         "run_time": 1.5797221660614014,
//                         "step_type": "chat_step",
//                         "message_list": [
//                             {
//                                 "role": "ai",
//                                 "content": "警务Ai助手"
//                             },
//                             {
//                                 "role": "user",
//                                 "content": "今天天气怎么样？"
//                             },
//                             {
//                                 "role": "ai",
//                                 "content": "\n我无法直接获取实时天气信息，但你可以通过以下方式快速了解：\n\n1. 打开手机上的天气应用或网站（如中国天气网、墨迹天气等）\n2. 或使用智能音箱询问当前气温和天气状况\n\n如果你告诉我具体的城市名称，我可以提供该地区的近期天气趋势分析哦！😊"
//                             },
//                             {
//                                 "role": "user",
//                                 "content": "14的平方是多少？"
//                             },
//                             {
//                                 "role": "ai",
//                                 "content": "<think>\n嗯，用户突然从问天气切换到数学问题了。可能ta正在快速测试我的功能边界，或者单纯想确认基础计算能力。考虑到之前对话是关于实时信息查询，现在这个问题更偏向知识型需求。\n\n这个计算很简单，但需要确保准确性。14的平方确实是196，我记得初中代数里有这么个知识点：任何整数n的平方都是n乘以自身的结果。不过用户可能只是想要一个快速答案，不需要深入讲解。\n\nta连续问了两个不同领域的问题，或许在评估我的响应速度和可靠性？也可能是在为某个项目收集基础数据。既然问题本身不复杂，保持简洁明了的回答最合适，但可以主动提出扩展帮助——比如计算其他数字的平方或提供公式说明，这样既完成当前任务又预留了延伸空间。\n\n要注意避免让用户觉得我在敷衍，所以除了给出答案外，还应该展示出愿意提供更多相关信息的姿态。毕竟有些人问完基础问题后可能需要进阶知识。\n</think>\n14 的平方是 **196**。  \n\n如果你需要其他数字的平方计算或数学公式帮助，随时告诉我！ 😊"
//                             }
//                         ],
//                         "answer_tokens": 857,
//                         "message_tokens": 291
//                     },
//                     {
//                         "cost": 0,
//                         "run_time": 0.0007848739624023438,
//                         "step_type": "search_step",
//                         "model_name": null,
//                         "problem_text": "14的平方是多少？",
//                         "answer_tokens": 0,
//                         "message_tokens": 0,
//                         "paragraph_list": []
//                     },
//                     {
//                         "cost": 0,
//                         "model_id": "59bb1fa0-6173-11f0-b039-0242ac120003",
//                         "run_time": 3.8952369689941406,
//                         "step_type": "problem_padding",
//                         "problem_text": "14的平方是多少？",
//                         "answer_tokens": 13,
//                         "message_tokens": 370,
//                         "padding_problem_text": "14的平方是多少？"
//                     }
//                 ]
//             }
//         ],
//         "current": 1,
//         "size": 20
//     }
// }


// 接口请求地址 http://localhost:1024/ai-api/api/application/chat_message/7a7eb876-6184-11f0-aa5e-0242ac120003

// 接口返回示例如下
// {
//     "code": 200,
//     "message": "成功",
//     "data": {
//         "chat_id": "7a7eb876-6184-11f0-aa5e-0242ac120003",
//         "id": "976756be-6184-11f0-93c3-0242ac120003",
//         "operate": true,
//         "content": "",
//         "is_end": true,
//         "reasoning_content": "",
//         "answer_list": [
//             {
//                 "content": "",
//                 "reasoning_content": ""
//             }
//         ],
//         "completion_tokens": 2533,
//         "prompt_tokens": 0
//     }
// }

/api/application/9e4a8c50-6173-11f0-b28f-0242ac120003/chat/4eec1e52-6197-11f0-9e33-0242ac120003/chat_record/1/20?order_asc=false
请求参数为：
{
    "message": "帮我写一个js方法实现某个数对另一个数的求余功能",
    "re_chat": false,
    "stream": true
}

接口返回为：

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u60a8\u53ef\u4ee5\u4f7f\u7528\u4e0b\u9762", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u7684", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "JavaScript", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u51fd\u6570", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u6765", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u5b9e\u73b0", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u8fd9\u4e2a", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u9700\u6c42", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\uff1a\n\n", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "```", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "javascript", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\n", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "function", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": " calculate", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "Rem", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "ain", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "der", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "(num", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "1", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": ",", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": " num", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "2", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": ")", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": " {\n", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "   ", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": " return", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": " num", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "1", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": " %", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": " num", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "2", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": ";\n", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "}\n", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "``", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "`\n", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u8fd9\u4e2a", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u51fd\u6570", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u63a5\u53d7", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u4e24\u4e2a", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u53c2\u6570", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "`", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "num", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "1", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "`", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u548c", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "`", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "num", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "2", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "`", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\uff0c", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u7136\u540e", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u901a\u8fc7", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u4f7f\u7528", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u6a21", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u6570", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u8fd0\u7b97", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u7b26", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\uff08", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "%", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\uff09", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u8ba1\u7b97", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "`", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "num", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "1", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "`", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u9664", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u4ee5", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "`", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "num", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "2", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "`", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u7684", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u4f59", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u6570", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\uff0c\u5e76", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u8fd4\u56de", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u8fd9\u4e2a", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u7ed3\u679c", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u3002", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u4f8b\u5982", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\uff0c", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "`", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "calculate", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "Rem", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "ain", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "der", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "(", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "9", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": ",", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": " ", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "5", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": ")`", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u4f1a", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u8fd4\u56de", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "`", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "4", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "`", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "\u3002", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": false, "view_type": "many_view", "node_type": "ai-chat-node", "real_node_id": "ai-chat-node", "reasoning_content": ""}

data: {"chat_id": "4eec1e52-6197-11f0-9e33-0242ac120003", "chat_record_id": "62b5a254-6198-11f0-ba60-0242ac120003", "operate": true, "content": "", "node_id": "ai-chat-node", "up_node_id_list": [], "is_end": true, "usage": {"completion_tokens": 1074, "prompt_tokens": 219, "total_tokens": 1293}, "node_is_end": true, "view_type": "many_view", "node_type": "ai-chat-node"}

