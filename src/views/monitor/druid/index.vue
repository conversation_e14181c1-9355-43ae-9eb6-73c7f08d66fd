<template>
  <div class="app-container">
    <div class="page-header">
      <h2>数据监控</h2>
      <p>这是系统监控 > 数据监控页面</p>
    </div>
    
    <div class="content-area">
      <el-card>
        <div slot="header">
          <span>Druid数据源监控</span>
        </div>
        <div class="demo-content">
          <p>这是一个演示页面，用于测试数据监控功能。</p>
          <p>在实际项目中，这里会显示Druid数据源的监控信息。</p>
          
          <el-button type="primary" @click="goBack">返回主菜单</el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MonitorDruid',
  data() {
    return {
      
    }
  },
  methods: {
    goBack() {
      // 退出系统菜单模式
      this.$store.dispatch('systemMenu/exitSystemMenuMode')
      // 跳转到首页
      this.$router.push('/')
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    h2 {
      margin: 0 0 10px 0;
      color: #303133;
    }
    
    p {
      margin: 0;
      color: #666;
    }
  }
  
  .content-area {
    .demo-content {
      padding: 20px;
      text-align: center;
      
      p {
        margin-bottom: 15px;
        color: #666;
        line-height: 1.6;
      }
    }
  }
}
</style>
