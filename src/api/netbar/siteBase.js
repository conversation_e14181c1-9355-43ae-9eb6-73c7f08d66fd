import request from '@/utils/request'

// 查询网吧列表
export function getSiteBaseList(query) {
  return request({
    url: '/netbar/siteBase/getSiteBaseList',
    method: 'get',
    params: query
  })
}

// 查询网吧详细
export function getSiteBase(id) {
  return request({
    url: '/netbar/siteBase/' + id,
    method: 'get'
  })
}

// 新增网吧
export function addSiteBase(data) {
  return request({
    url: '/netbar/siteBase',
    method: 'post',
    data: data
  })
}

// 修改网吧
export function updateSiteBase(data) {
  return request({
    url: '/netbar/siteBase',
    method: 'put',
    data: data
  })
}

// 删除网吧
export function delSiteBase(id) {
  return request({
    url: '/netbar/siteBase/' + id,
    method: 'delete'
  })
}
