import request from '@/utils/request'

// 查询 列表
export function listNetbar(query) {
    return request({
      url: '/netbar/siteBase/list',
      method: 'get',
      params: query
    })
}

// 查询 详情
export function getNetbar(query) {
  return request({
    url: '/netbar/siteBase/detail',
    method: 'get',
    params: query
  })
}

// 添加
export function addNetbar(data) {
  return request({
    url: '/netbar/siteBase/add',
    method: 'post',
    data: data
  })
}

// 修改
export function updateNetbar(data) {
  return request({
    url: '/netbar/siteBase/update',
    method: 'put',
    data: data
  })
}

// 获取网吧备案台账总数
export function getNetbarNum() {
  return request({
    url: '/netbar/siteBase/stat' ,
    method: 'get'
  })
}
// 删除网吧信息
export function delNetbar(netbarId) {
  return request({
    url: '/operator/operatorInfo/del/' + netbarId,
    method: 'delete'
  })
}

