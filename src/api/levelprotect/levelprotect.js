import request from '@/utils/request'

// 查询等级保护台账列表
export function listLevelprotect(query) {
  return request({
    url: '/levelprotect/levelprotect/list',
    method: 'get',
    params: query
  })
}

// 查询等级保护台账详细
export function getLevelprotect(levelProtectId) {
  return request({
    url: '/levelprotect/levelprotect/' + levelProtectId,
    method: 'get'
  })
}

// 新增等级保护台账
export function addLevelprotect(data) {
  return request({
    url: '/levelprotect/levelprotect',
    method: 'post',
    data: data
  })
}

// 修改等级保护台账
export function updateLevelprotect(data) {
  return request({
    url: '/levelprotect/levelprotect',
    method: 'put',
    data: data
  })
}

// 删除等级保护台账
export function delLevelprotect(levelProtectId) {
  return request({
    url: '/levelprotect/levelprotect/' + levelProtectId,
    method: 'delete'
  })
}
