import request from '@/utils/request'

// 获取流水号
export function getFlowNo(query) {
  return request({
    url: '/system/sysEnterpriseFlow/getFlowNo',
    method: 'get',
    params: query
  })
}
// 企业主体提交
export function addEnterprise(data) {
  return request({
    url: '/system/sysEnterprise/addEnterprise',
    method: 'post',
    data: data
  })
}
// 删除企业主体信息
export function delEnterprise(erpIds) {
  return request({
    url: '/system/sysEnterprise/del/' + erpIds,
    method: 'delete'
  })
}
// 批量删除企业主体信息
export function delBatchEnterprise(erpIds) {
  return request({
    url: '/system/sysEnterprise/delBatch',
    method: 'post',
    data: data
  })
}

// 企业主体详情
export function getEnterprise(query) {
  return request({
    url: '/system/sysEnterprise/detail',
    method: 'get',
    params: query
  })
}
// 企业主体详情统一社会信用代码
export function getEnterpriseByCC(query) {
  return request({
    url: '/system/sysEnterprise/getByCreditCode',
    method: 'get',
    params: query
  })
} 

// 企业主体列表
export function getEnterpriseList(query) {
  return request({
    url: '/system/sysEnterprise/list',
    method: 'get',
    params: query
  })
}

// 获取单位主体台账总数
export function getEnterpriseNum() {
  return request({
    url: '/system/sysEnterprise/stat' ,
    method: 'get'
  })
}