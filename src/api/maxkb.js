import axios from 'axios'

/**
 * 解码对象中的Unicode转义序列
 * @param {any} obj - 需要解码的对象
 * @returns {any} - 解码后的对象
 */
function decodeUnicodeInObject(obj) {
  if (typeof obj === 'string') {
    // 解码Unicode转义序列 (如 \u4e2d\u6587 -> 中文)
    return obj.replace(/\\u[\dA-Fa-f]{4}/g, function(match) {
      return String.fromCharCode(parseInt(match.replace(/\\u/g, ''), 16))
    })
  } else if (Array.isArray(obj)) {
    // 递归处理数组
    return obj.map(item => decodeUnicodeInObject(item))
  } else if (obj !== null && typeof obj === 'object') {
    // 递归处理对象
    const decoded = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        decoded[decodeUnicodeInObject(key)] = decodeUnicodeInObject(obj[key])
      }
    }
    return decoded
  }
  return obj
}

// maxKB AI助手API配置
const MAXKB_CONFIG = {
  baseURL: '/ai-api',
  apiKey: process.env.VUE_APP_MAXKB_API_KEY,
  applicationId: process.env.VUE_APP_MAXKB_APPLICATION_ID,
  maxkbId: process.env.VUE_APP_MAXKB_ID
}

// 创建专用于maxKB的axios实例，避免被系统拦截器影响
const maxkbRequest = axios.create({
  baseURL: MAXKB_CONFIG.baseURL,
  // timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// maxKB专用请求拦截器
maxkbRequest.interceptors.request.use(config => {
  // 为maxKB API添加专用的Authorization头
  if (MAXKB_CONFIG.apiKey) {
    config.headers['Authorization'] = `${MAXKB_CONFIG.apiKey}`
  }

  const fullUrl = `${config.baseURL}${config.url}`
  console.log(`[maxKB API] 请求: ${config.method?.toUpperCase()} ${fullUrl}`)
  console.log(`[maxKB API] Authorization: ${MAXKB_CONFIG.apiKey}`)
  console.log(`[maxKB API] 请求数据:`, config.data)

  return config
}, error => {
  console.error('[maxKB API] 请求错误:', error)
  return Promise.reject(error)
})

// maxKB专用响应拦截器
maxkbRequest.interceptors.response.use(response => {
  const fullUrl = `${response.config.baseURL}${response.config.url}`
  console.log(`[maxKB API] 响应成功: ${response.status} ${fullUrl}`)

  // 处理响应数据，确保中文字符正确显示
  let responseData = response.data

  // 如果响应数据是字符串且包含Unicode转义序列，进行解码
  if (typeof responseData === 'string') {
    try {
      // 尝试解析JSON字符串
      responseData = JSON.parse(responseData)
    } catch (e) {
      // 如果不是JSON，直接使用原字符串
      console.log(`[maxKB API] 响应为纯文本:`, responseData)
    }
  }

  // 递归处理对象中的Unicode转义序列
  // responseData = decodeUnicodeInObject(responseData)

  console.log(`[maxKB API] 处理后的响应数据:`, responseData)
  return responseData
}, error => {
  const fullUrl = error.config ? `${error.config.baseURL}${error.config.url}` : '未知地址'
  console.error(`[maxKB API] 响应错误: ${fullUrl}`)
  console.error('[maxKB API] 错误详情:', error)

  if (error.response) {
    const { status, data, config } = error.response
    console.error(`[maxKB API] HTTP ${status} 错误:`)
    console.error(`[maxKB API] 接口地址: ${config.baseURL}${config.url}`)
    console.error(`[maxKB API] 请求方法: ${config.method?.toUpperCase()}`)
    console.error(`[maxKB API] 请求头:`, config.headers)
    console.error(`[maxKB API] 请求数据:`, config.data)
    console.error(`[maxKB API] 响应数据:`, data)

    if (status === 401) {
      throw new Error(`maxKB API认证失败 (${fullUrl})，请检查API Key`)
    } else if (status === 404) {
      throw new Error(`maxKB API端点不存在 (${fullUrl})`)
    } else if (status >= 500) {
      throw new Error(`maxKB服务器错误 (${status}) - ${fullUrl}`)
    } else {
      throw new Error(`maxKB API请求失败 (${status}) - ${fullUrl}: ${data?.message || data?.error || '未知错误'}`)
    }
  } else if (error.request) {
    console.error(`[maxKB API] 网络错误: 无法连接到 ${fullUrl}`)
    throw new Error(`无法连接到maxKB服务 (${fullUrl})`)
  } else {
    console.error(`[maxKB API] 请求配置错误:`, error.message)
    throw new Error(`maxKB API请求配置错误: ${error.message}`)
  }
})

/**
 * 步骤2: 打开会话，获取会话ID (根据maxKB官方文档)
 * @param {string} applicationId - 应用ID
 * @returns {Promise}
 */
export function openChat(applicationId) {
  return maxkbRequest({
    url: `/api/application/${applicationId}/chat/open`,
    method: 'get'
  })
}

/**
 * 步骤3: 发送消息到maxKB AI助手 (根据官方文档)
 * @param {Object} params
 * @param {string} params.chat_id - 会话ID (必填)
 * @param {string} params.message - 用户消息
 * @param {boolean} params.re_chat - 是否重新对话
 * @param {boolean} params.stream - 是否流式输出
 * @returns {Promise}
 */
export function sendMessage(params) {
  if (!params.chat_id) {
    const error = new Error('chat_id is required')
    console.error('[maxKB API] sendMessage 参数错误:', error.message)
    throw error
  }

  const requestData = {
    message: params.message || '',
    re_chat: params.re_chat || false,
    stream: params.stream || false
  }

  console.log(`[maxKB API] sendMessage 调用参数:`, {
    chat_id: params.chat_id,
    requestData: requestData,
    isStream: params.stream
  })

  // 如果是流式请求，使用fetch API处理SSE
  if (params.stream) {
    return sendStreamMessage(params.chat_id, requestData)
  }

  // 非流式请求使用原来的方式
  return maxkbRequest({
    url: `/api/application/chat_message/${params.chat_id}`,
    method: 'post',
    data: requestData
  })
}

/**
 * 发送流式消息（使用axios处理，通过代理避免CORS问题）
 * @param {string} chatId - 会话ID
 * @param {Object} requestData - 请求数据
 * @returns {Promise}
 */
async function sendStreamMessage(chatId, requestData) {
  console.log('[maxKB API] 开始流式请求')

  try {
    // 使用axios发送请求，但设置特殊的响应类型
    const response = await maxkbRequest({
      url: `/api/application/chat_message/${chatId}`,
      method: 'post',
      data: requestData,
      // 设置响应类型为text，以便处理流式数据
      responseType: 'text',
      // 增加超时时间
      timeout: 60000,
      // 添加流式请求的特殊头
      headers: {
        'Accept': 'text/event-stream, text/plain, */*'
      }
    })

    console.log('[maxKB API] 流式响应接收完成，数据长度:', response.data ? response.data.length : 0)

    // 返回响应数据
    return response.data || response
  } catch (error) {
    console.error('[maxKB API] 流式请求失败:', error)

    // 如果流式请求失败，尝试普通请求
    console.log('[maxKB API] 尝试降级到普通请求')
    const fallbackData = { ...requestData, stream: false }

    try {
      const fallbackResponse = await maxkbRequest({
        url: `/api/application/chat_message/${chatId}`,
        method: 'post',
        data: fallbackData
      })

      console.log('[maxKB API] 降级请求成功')
      return fallbackResponse
    } catch (fallbackError) {
      console.error('[maxKB API] 降级请求也失败:', fallbackError)
      throw error // 抛出原始错误
    }
  }
}





/**
 * 创建新会话 (按照maxKB正确流程)
 * @returns {Promise}
 */
export async function createConversation() {
  try {
    console.log('[maxKB] 开始创建会话流程...')

    // 步骤1: 获取应用信息
    console.log('[maxKB] 步骤1: 调用 getApplicationProfile...')
    const appProfile = await getApplicationProfile()
    console.log('[maxKB] 步骤1 - 获取应用信息成功:', appProfile)

    if (!appProfile) {
      const error = new Error('getApplicationProfile 返回空数据')
      console.error('[maxKB] 步骤1 失败:', error.message)
      throw error
    }

    if (!appProfile.id) {
      const error = new Error('应用信息中缺少 id 字段')
      console.error('[maxKB] 步骤1 失败:', error.message)
      console.error('[maxKB] 应用信息结构:', appProfile)
      throw error
    }

    // 步骤2: 打开会话
    console.log(`[maxKB] 步骤2: 调用 openChat(${appProfile.id})...`)
    const chatResponse = await openChat(appProfile.id)
    console.log('[maxKB] 步骤2 - 打开会话响应:', chatResponse)

    if (!chatResponse) {
      const error = new Error('openChat 返回空数据')
      console.error('[maxKB] 步骤2 失败:', error.message)
      throw error
    }

    // 根据您提供的响应格式：{ "code": 200, "message": "成功", "data": "f2756af8-41dd-11f0-b546-0242ac120003" }
    // chat_id 在 data 字段中
    let chatId = null
    if (chatResponse.data) {
      chatId = chatResponse.data
    } else if (chatResponse.id) {
      // 兼容其他可能的响应格式
      chatId = chatResponse.id
    } else if (typeof chatResponse === 'string') {
      // 如果直接返回字符串
      chatId = chatResponse
    }

    if (!chatId) {
      const error = new Error('会话响应中缺少 chat_id')
      console.error('[maxKB] 步骤2 失败:', error.message)
      console.error('[maxKB] 会话响应结构:', chatResponse)
      throw error
    }

    const result = {
      id: chatId,
      applicationId: appProfile.id,
      applicationName: appProfile.name || 'maxKB AI助手'
    }

    console.log('[maxKB] 会话创建成功:', result)
    return result

  } catch (error) {
    console.error('[maxKB] 创建会话失败:')
    console.error('[maxKB] 错误类型:', error.constructor.name)
    console.error('[maxKB] 错误消息:', error.message)
    console.error('[maxKB] 错误堆栈:', error.stack)
    throw error
  }
}

/**
 * 步骤1: 获取应用配置信息 (根据maxKB官方文档) application-ce580c6f7987cb4b4abfcc35ccff0dad
 * 接口: GET /api/application/profile
 * @returns {Promise}
 */
export function getApplicationProfile() {
  return maxkbRequest({
    url: '/api/application/profile',
    method: 'get'
  })
}

/**
 * 获取应用信息 (兼容旧版本)
 * @returns {Promise}
 */
export function getApplicationInfo() {
  return getApplicationProfile()
}

/**
 * 获取聊天历史记录
 * @param {string} applicationId - 应用ID
 * @param {string} chatId - 会话ID
 * @param {string} maxkbId - maxkbId 用于获取历史对话
 * @param {number} current - 当前页码
 * @param {number} pageSize - 每页数据量
 * @returns {Promise}
 */
export function getChatHistory(chatId, current = 1, pageSize = 20) {
  return maxkbRequest({
    // url: `/api/application/${applicationId}/chat/${chatId}/chat_record/${current}/${pageSize}?order_asc=false`,
    url: `/api/application/${MAXKB_CONFIG.maxkbId}/chat/${chatId}/chat_record/1/20?order_asc=false`,
    method: 'get'
  })
}

// 导出配置，供其他模块使用
export { MAXKB_CONFIG }
