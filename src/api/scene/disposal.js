import request from '@/utils/request'

// 查询行政处置列表
export function listDisposal(query) {
  return request({
    url: '/scene/disposal/list',
    method: 'get',
    params: query
  })
}

// 查询行政处置详细
export function getDisposal(id) {
  return request({
    url: '/scene/disposal/' + id,
    method: 'get'
  })
}

// 新增行政处置
export function addDisposal(data) {
  return request({
    url: '/scene/disposal',
    method: 'post',
    data: data
  })
}

// 修改行政处置
export function updateDisposal(data) {
  return request({
    url: '/scene/disposal',
    method: 'put',
    data: data
  })
}

// 删除行政处置
export function delDisposal(id) {
  return request({
    url: '/scene/disposal/' + id,
    method: 'delete'
  })
}
