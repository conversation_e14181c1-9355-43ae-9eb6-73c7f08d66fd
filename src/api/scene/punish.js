import request from '@/utils/request'

// 查询处罚记录列表
export function listPunish(query) {
  return request({
    url: '/scene/punish/list',
    method: 'get',
    params: query
  })
}

// 查询处罚记录详细
export function getPunish(id) {
  return request({
    url: '/scene/punish/' + id,
    method: 'get'
  })
}

// 新增处罚记录
export function addPunish(data) {
  return request({
    url: '/scene/punish',
    method: 'post',
    data: data
  })
}

// 修改处罚记录
export function updatePunish(data) {
  return request({
    url: '/scene/punish',
    method: 'put',
    data: data
  })
}

// 删除处罚记录
export function delPunish(id) {
  return request({
    url: '/scene/punish/' + id,
    method: 'delete'
  })
}
