import request from '@/utils/request'

// 查询APP信息管理列表
export function listApp(query) {
  return request({
    url: '/website/app/list',
    method: 'get',
    params: query
  })
}

// 查询APP信息管理详细
export function getApp(registerNum) {
  return request({
    url: '/website/app/' + registerNum,
    method: 'get'
  })
}

// 新增APP信息管理
export function addApp(data) {
  return request({
    url: '/website/app',
    method: 'post',
    data: data
  })
}

// 修改APP信息管理
export function updateApp(data) {
  return request({
    url: '/website/app',
    method: 'put',
    data: data
  })
}

// 删除APP信息管理
export function delApp(registerNum) {
  return request({
    url: '/website/app/' + registerNum,
    method: 'delete'
  })
}
