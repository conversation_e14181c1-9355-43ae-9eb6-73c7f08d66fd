import request from '@/utils/request'

// 查询网站信息审核列表
export function listWebsite(query) {
  return request({
    url: '/website/website/list',
    method: 'get',
    params: query
  })
}

// 查询网站信息审核详细
export function getWebsite(websiteId) {
  return request({
    url: '/website/website/' + websiteId,
    method: 'get'
  })
}

// 新增网站信息审核
export function addWebsite(data) {
  return request({
    url: '/website/website',
    method: 'post',
    data: data
  })
}

// 修改网站信息审核
export function updateWebsite(data) {
  return request({
    url: '/website/website',
    method: 'put',
    data: data
  })
}

// 删除网站信息审核
export function delWebsite(websiteId) {
  return request({
    url: '/website/website/' + websiteId,
    method: 'delete'
  })
}
