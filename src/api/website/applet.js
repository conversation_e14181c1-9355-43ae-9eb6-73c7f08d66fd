import request from '@/utils/request'

// 查询小程序信息审核列表
export function listApplet(query) {
  return request({
    url: '/website/applet/list',
    method: 'get',
    params: query
  })
}

// 查询小程序信息审核详细
export function getApplet(registerNum) {
  return request({
    url: '/website/applet/' + registerNum,
    method: 'get'
  })
}

// 新增小程序信息审核
export function addApplet(data) {
  return request({
    url: '/website/applet',
    method: 'post',
    data: data
  })
}

// 修改小程序信息审核
export function updateApplet(data) {
  return request({
    url: '/website/applet',
    method: 'put',
    data: data
  })
}

// 删除小程序信息审核
export function delApplet(registerNum) {
  return request({
    url: '/website/applet/' + registerNum,
    method: 'delete'
  })
}
