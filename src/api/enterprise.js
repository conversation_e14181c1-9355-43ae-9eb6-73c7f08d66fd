import request from '@/utils/request'

/**
 * 企业管理相关接口
 *
 * 注意：这些API路径需要与后端API路径保持一致
 * 在开发环境中，这些请求会被 mockInterceptor.js 拦截并返回模拟数据
 * 在生产环境中，这些请求会直接发送到真实后端API
 */

// 获取企业列表数据
export function getEnterpriseList(params) {
  return request({
    url: '/system/sysEnterprise/list',
    method: 'get',
    params
  })
}

// 获取企业详情
export function getEnterpriseDetail(id) {
  return request({
    url: `/system/sysEnterprise/detail`,
    method: 'get',
    params: { id }
  })
}

// 获取企业网络安全评估总数据
export function getErpAssessCurrent(creditCode) {
  return request({
    url: '/system/riskAssess/getErpAssessCurrent',
    method: 'get',
    params: { creditCode }
  })
}

// 获取企业网络安全评估详细信息
export function getErpAssessInfo(params) {
  return request({
    url: '/system/riskAssess/getErpAssessInfo',
    method: 'get',
    params
  })
}

// 获取历史合规记录统计数据
export function getComplianceStatistics(creditCode) {
  return request({
    url: `/scene/statistics/synthetic/${creditCode}`,
    method: 'get'
  })
}

// 获取处罚决定书列表
export function getPunishList(params) {
  return request({
    url: '/scene/punish/list',
    method: 'get',
    params
  })
}

// 获取数字资产统计数据
export function getDataAssetStat(creditCode) {
  return request({
    url: '/operator/operatorInfo/getDataAssetStat',
    method: 'get',
    params: { creditCode }
  })
}

// 获取数字资产月度统计数据
export function getDataAssetMonthlyStat(creditCode) {
  return request({
    url: '/operator/operatorInfo/getDataAssetMonthlyStat',
    method: 'get',
    params: { creditCode }
  })
}

// 获取资产变更月度趋势数据
export function getDataAssetMonthlyTrend(creditCode) {
  return request({
    url: '/operator/operatorInfo/getDataAssetMonthlyTrend',
    method: 'get',
    params: { creditCode }
  })
}

// // 获取数字资产分布情况数据


// 获取数字资产同比增长趋势数据
export function getDataAssetGrowthTrend(creditCode) {
  return request({
    url: '/operator/operatorInfo/getDataAssetGrowthTrend',
    method: 'get',
    params: { creditCode }
  })
}

// 添加企业
export function addEnterprise(data) {
  return request({
    url: '/enterprise/add',
    method: 'post',
    data: data
  })
}



// 删除企业
export function deleteEnterprise(id) {
  return request({
    url: `/enterprise/delete/${id}`,
    method: 'delete'
  })
}

// 批量删除企业
export function batchDeleteEnterprise(ids) {
  return request({
    url: '/enterprise/batchDelete',
    method: 'delete',
    data: { ids }
  })
}

// 获取企业风险级别选项
export function getRiskLevelOptions() {
  return request({
    url: '/enterprise/riskLevelOptions',
    method: 'get'
  })
}

// 获取企业分类统计数据
export function getEnterpriseCategoryStats() {
  return request({
    url: '/system/sysEnterprise/stat',
    method: 'get'
  })
}

// 更新企业信息（用于编辑抽屉）
export function updateEnterprise(data) {
  return request({
    url: `/enterprise/${data.id}`,
    method: 'put',
    data
  })
}

// 获取关联电子屏列表
export function getScreenList(params) {
  return request({
    url: '/screen/screen/list',
    method: 'get',
    params
  })
}

// 获取网吧列表
export function getNetbarList(params) {
  return request({
    url: '/netbar/siteBase/list',
    method: 'get',
    params
  })
}

// 获取等保备案列表
export function getLevelProtectList(params) {
  return request({
    url: '/levelprotect/levelprotect/list',
    method: 'get',
    params
  })
}

// 获取网站备案列表
export function getWebsiteList(params) {
  return request({
    url: '/website/website/list',
    method: 'get',
    params
  })
}

// 获取APP列表
export function getAppList(params) {
  return request({
    url: '/website/app/list',
    method: 'get',
    params
  })
}

// 获取小程序列表
export function getAppletList(params) {
  return request({
    url: '/website/applet/list',
    method: 'get',
    params
  })
}

// 获取运营商列表
export function getOperatorList(params) {
  return request({
    url: '/operator/operatorInfo/list',
    method: 'get',
    params
  })
}

// 获取非经营场所列表
export function getWifiSiteList(params) {
  return request({
    url: '/wifi/site/list',
    method: 'get',
    params
  })
}