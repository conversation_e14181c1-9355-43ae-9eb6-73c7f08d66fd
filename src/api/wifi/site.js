import request from '@/utils/request'

// 查询非经营场所列表
export function listWifiSite(query) {
  return request({
    url: '/wifi/site/list',
    method: 'get',
    params: query
  })
}

// 查询非经营场所详细
export function getWifiSite(serviceCode) {
  return request({
    url: '/wifi/site/' + serviceCode,
    method: 'get'
  })
}

// 新增非经营场所
export function addWifiSite(data) {
  return request({
    url: '/wifi/site',
    method: 'post',
    data: data
  })
}

// 修改非经营场所
export function updateWifiSite(data) {
  return request({
    url: '/wifi/site',
    method: 'put',
    data: data
  })
}

// 删除非经营场所
export function delWifiSite(serviceCode) {
  return request({
    url: '/wifi/site/' + serviceCode,
    method: 'delete'
  })
}

// 导出非经营场所
export function exportWifiSite(query) {
  return request({
    url: '/wifi/site/export',
    method: 'get',
    params: query
  })
}

// 查询非经营场所POI列表
export function poiList(query) {
  return request({
    url: '/wifi/site/poiList',
    method: 'get',
    params: query
  })
}
