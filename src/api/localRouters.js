// 本地路由数据，基于chat.json中的真实API数据
export const localRoutersData = {
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "name": "System",
      "path": "/system",
      "hidden": false,
      "redirect": "noRedirect",
      "component": "Layout",
      "alwaysShow": true,
      "meta": {
        "title": "系统管理",
        "icon": "system",
        "noCache": false,
        "link": null
      },
      "children": [
        {
          "name": "User",
          "path": "user",
          "hidden": false,
          "component": "system/user/index",
          "meta": {
            "title": "用户管理",
            "icon": "user",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Dataroles",
          "path": "dataroles",
          "hidden": false,
          "component": "system/datarole/index",
          "meta": {
            "title": "数据角色管理",
            "icon": "client",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Role",
          "path": "role",
          "hidden": false,
          "component": "system/role/index",
          "meta": {
            "title": "功能角色管理",
            "icon": "peoples",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Menu",
          "path": "menu",
          "hidden": false,
          "component": "system/menu/index",
          "meta": {
            "title": "菜单管理",
            "icon": "tree-table",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Dept",
          "path": "dept",
          "hidden": false,
          "component": "system/dept/index",
          "meta": {
            "title": "部门管理",
            "icon": "tree",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Post",
          "path": "post",
          "hidden": false,
          "component": "system/post/index",
          "meta": {
            "title": "岗位管理",
            "icon": "post",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Dict",
          "path": "dict",
          "hidden": false,
          "component": "system/dict/index",
          "meta": {
            "title": "字典管理",
            "icon": "dict",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Config",
          "path": "Config",
          "hidden": false,
          "component": "system/config/index",
          "meta": {
            "title": "参数设置",
            "icon": "edit",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Notice",
          "path": "notice",
          "hidden": false,
          "component": "system/notice/index",
          "meta": {
            "title": "通知公告",
            "icon": "message",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Log",
          "path": "log",
          "hidden": false,
          "redirect": "noRedirect",
          "component": "ParentView",
          "alwaysShow": true,
          "meta": {
            "title": "日志管理",
            "icon": "log",
            "noCache": false,
            "link": null
          },
          "children": [
            {
              "name": "Operlog",
              "path": "operlog",
              "hidden": false,
              "component": "system/operlog/index",
              "meta": {
                "title": "操作日志",
                "icon": "form",
                "noCache": false,
                "link": null
              }
            },
            {
              "name": "Logininfor",
              "path": "logininfor",
              "hidden": false,
              "component": "system/logininfor/index",
              "meta": {
                "title": "登录日志",
                "icon": "logininfor",
                "noCache": false,
                "link": null
              }
            }
          ]
        }
      ]
    },
    {
      "name": "Monitor",
      "path": "/monitor",
      "hidden": false,
      "redirect": "noRedirect",
      "component": "Layout",
      "alwaysShow": true,
      "meta": {
        "title": "系统监控",
        "icon": "monitor",
        "noCache": false,
        "link": null
      },
      "children": [
        {
          "name": "Online",
          "path": "online",
          "hidden": false,
          "component": "monitor/online/index",
          "meta": {
            "title": "在线用户",
            "icon": "online",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Job",
          "path": "job",
          "hidden": false,
          "component": "monitor/job/index",
          "meta": {
            "title": "定时任务",
            "icon": "job",
            "noCache": false,
            "link": null
          }
        }
      ]
    }
  ]
}
