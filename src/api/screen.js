import request from '@/utils/request'

// 查询电子屏信息列表
export function listScreen(query) {
  return request({
    url: '/screen/screen/list',
    method: 'get',
    params: query
  })
}

// 查询电子屏信息详细
export function getScreen(screenId) {
  return request({
    url: '/screen/screen/' + screenId,
    method: 'get'
  })
}

// 新增电子屏信息
export function addScreen(data) {
  return request({
    url: '/screen/screen',
    method: 'post',
    data: data
  })
}

// 修改电子屏信息
export function updateScreen(data) {
  return request({
    url: '/screen/screen',
    method: 'put',
    data: data
  })
}

// 删除电子屏信息
export function delScreen(screenId) {
  return request({
    url: '/screen/screen/' + screenId,
    method: 'delete'
  })
}