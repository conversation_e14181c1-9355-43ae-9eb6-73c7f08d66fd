import request from '@/utils/request'

// 查询参数列表
export function listUserArea() {
  return request({
    url: '/system/area/getUserAreaList',
    method: 'get'
  })
}
// 查询参数列表
export function listArea(query) {
  return request({
    url: '/system/area/getAreaList',
    method: 'get',
    params: query
  })
}
export function treeArea(query) {
  return request({
    url: '/system/area/getAreaTree',
    method: 'get',
    params: query
  })
}
