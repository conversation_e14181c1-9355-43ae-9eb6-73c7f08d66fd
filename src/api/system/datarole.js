import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询数据角色列表
export function listDataRole(query) {
  return request({
    url: '/system/sysDataRole/list',
    method: 'get',
    params: query
  })
}

// 查询数据角色详细
export function getDataRole(roleId) {
  
  return request({
    url: '/system/sysDataRole/findById/' + parseStrEmpty(roleId),
    method: 'get' 
  })
}

// 新增角色
export function addOrUpdateDataRole(data) {
  return request({
    url: '/system/sysDataRole/saveOrUpdate',
    method: 'post',
    data: data
  })
}
 

// 角色状态修改
export function changeDataRoleStatus(roleId) {
  // console.log('----dataRoleId =', roleId )
  let dataJson = {
    "id":roleId
  }
  return request({
    url: '/system/sysDataRole/toggle/',
    method: 'post',
    params:dataJson
  })
}

// 删除角色
export function delDataRole(roleId) {
  return request({
    url: '/system/sysDataRole/del/' + roleId,
    method: 'post'
  })
}
 