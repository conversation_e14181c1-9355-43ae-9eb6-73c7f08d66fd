import request from '@/utils/request'

// 查询重点区域列表
export function listKeyarea(query) {
  return request({
    url: '/screen/keyarea/list',
    method: 'get',
    params: query
  })
}

// 查询重点区域详细
export function getKeyarea(keyareaId) {
  return request({
    url: '/screen/keyarea/' + keyareaId,
    method: 'get'
  })
}

// 新增重点区域
export function addKeyarea(data) {
  return request({
    url: '/screen/keyarea',
    method: 'post',
    data: data
  })
}

// 修改重点区域
export function updateKeyarea(data) {
  return request({
    url: '/screen/keyarea',
    method: 'put',
    data: data
  })
}

// 删除重点区域
export function delKeyarea(keyareaId) {
  return request({
    url: '/screen/keyarea/' + keyareaId,
    method: 'delete'
  })
}
