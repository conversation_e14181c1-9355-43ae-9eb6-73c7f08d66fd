import request from '@/utils/request'

// 查询专项列表
export function listSpecial(query) {
  return request({
    url: '/screen/special/list',
    method: 'get',
    params: query
  })
}

// 查询专项详细
export function getSpecial(specialId) {
  return request({
    url: '/screen/special/' + specialId,
    method: 'get'
  })
}

// 新增专项
export function addSpecial(data) {
  return request({
    url: '/screen/special',
    method: 'post',
    data: data
  })
}

// 修改专项
export function updateSpecial(data) {
  return request({
    url: '/screen/special',
    method: 'put',
    data: data
  })
}

// 删除专项
export function delSpecial(specialId) {
  return request({
    url: '/screen/special/' + specialId,
    method: 'delete'
  })
}
