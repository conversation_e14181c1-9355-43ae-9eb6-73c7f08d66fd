import request from '@/utils/request'
import pgsql from 'highlight.js/lib/languages/pgsql'

// 查询获取当前用户 功能菜单列表 
export function getMenuTree() {
  return request({
    url: '/system/shortcutMenu/menuTree',
    method: 'get' 
  })
}

// 桌面菜单图标添加
export function addMenuDesktopIcon(query) {
  // console.log('query=',query)
  return request({
    url: '/system/shortcutMenu/add/',
    method: 'post',
    data: query 
  })
}

// 桌面菜单图标点击
export function desktopIconClick(query) {
  // console.log('query=',query)
  return request({
    url: '/system/menu/click/'+query,
    method: 'put', 
  })
}



// 获取当前登录用户设置的 工作台图标列表 
export function getUserCurrentDesktopIconList(query) {
  // console.log('query=',query)
  return request({
    url: '/system/shortcutMenu/list',
    method: 'get'
  })
}

// 获取当前登录用户最近访问 工作台图标列表 
export function getUserAccessDesktopIconList(query) {
  // console.log('query=',query)
  return request({
    url: '/system/menu/listTop10',
    method: 'get'
  })
}


