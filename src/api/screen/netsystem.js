import request from '@/utils/request'

// 查询联网系统列表
export function listNetsystem(query) {
  return request({
    url: '/screen/netsystem/list',
    method: 'get',
    params: query
  })
}

// 查询联网系统详细
export function getNetsystem(netsystemId) {
  return request({
    url: '/screen/netsystem/' + netsystemId,
    method: 'get'
  })
}

// 新增联网系统
export function addNetsystem(data) {
  return request({
    url: '/screen/netsystem',
    method: 'post',
    data: data
  })
}

// 修改联网系统
export function updateNetsystem(data) {
  return request({
    url: '/screen/netsystem',
    method: 'put',
    data: data
  })
}

// 删除联网系统
export function delNetsystem(netsystemId) {
  return request({
    url: '/screen/netsystem/' + netsystemId,
    method: 'delete'
  })
}
