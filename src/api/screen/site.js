import request from '@/utils/request'

// 查询归属场所列表
export function listSite(query) {
  return request({
    url: '/screen/site/list',
    method: 'get',
    params: query
  })
}

// 查询归属场所详细
export function getSite(siteCode) {
  return request({
    url: '/screen/site/' + siteCode,
    method: 'get'
  })
}

// 新增归属场所
export function addSite(data) {
  return request({
    url: '/screen/site',
    method: 'post',
    data: data
  })
}

// 修改归属场所
export function updateSite(data) {
  return request({
    url: '/screen/site',
    method: 'put',
    data: data
  })
}

// 删除归属场所
export function delSite(siteCode) {
  return request({
    url: '/screen/site/' + siteCode,
    method: 'delete'
  })
}
