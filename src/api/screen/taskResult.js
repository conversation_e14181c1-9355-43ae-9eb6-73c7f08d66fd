import request from '@/utils/request'

// 查询检查任务结果列表
export function listTaskResult(query) {
  return request({
    url: '/screen/taskResult/list',
    method: 'get',
    params: query
  })
}

// 查询检查任务结果详细
export function getTaskResult(resultId) {
  return request({
    url: '/screen/taskResult/' + resultId,
    method: 'get'
  })
}

// 新增检查任务结果
export function addTaskResult(data) {
  return request({
    url: '/screen/taskResult',
    method: 'post',
    data: data
  })
}

// 修改检查任务结果
export function updateTaskResult(data) {
  return request({
    url: '/screen/taskResult',
    method: 'put',
    data: data
  })
}

// 删除检查任务结果
export function delTaskResult(resultId) {
  return request({
    url: '/screen/taskResult/' + resultId,
    method: 'delete'
  })
}
