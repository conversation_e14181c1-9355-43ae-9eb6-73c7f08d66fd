import request from '@/utils/request'

// 查询责任单位列表
export function listEnterprise(query) {
  return request({
    url: '/screen/enterprise/list',
    method: 'get',
    params: query
  })
}

// 查询责任单位详细
export function getEnterprise(enterpriseId) {
  return request({
    url: '/screen/enterprise/' + enterpriseId,
    method: 'get'
  })
}

// 新增责任单位
export function addEnterprise(data) {
  return request({
    url: '/screen/enterprise',
    method: 'post',
    data: data
  })
}

// 修改责任单位
export function updateEnterprise(data) {
  return request({
    url: '/screen/enterprise',
    method: 'put',
    data: data
  })
}

// 删除责任单位
export function delEnterprise(enterpriseId) {
  return request({
    url: '/screen/enterprise/' + enterpriseId,
    method: 'delete'
  })
}
