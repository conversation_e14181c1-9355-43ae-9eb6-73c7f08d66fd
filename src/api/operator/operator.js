import request from '@/utils/request'

// 查询 列表
export function listOperator(query) {
    return request({
      url: '/operator/operatorInfo/list',
      method: 'get',
      params: query
    })
}

// 查询 详情
export function getOperator(query) {
  return request({
    url: '/operator/operatorInfo/detail',
    method: 'get',
    params: query
  })
}

// 添加
export function addOperator(data) {
  return request({
    url: '/operator/operatorInfo/add',
    method: 'post',
    data: data
  })
}

// 修改
export function updateOperator(data) {
  return request({
    url: '/operator/operatorInfo/update',
    method: 'put',
    data: data
  })
}

// 获取运营商备案台账总数
export function getOperatorNum() {
  return request({
    url: '/operator/operatorInfo/stat' ,
    method: 'get'
  })
}

// 删除运营商信息
export function delOperator(creditCode) {
  return request({
    url: '/operator/operatorInfo/del/' + creditCode,
    method: 'delete'
  })
}


