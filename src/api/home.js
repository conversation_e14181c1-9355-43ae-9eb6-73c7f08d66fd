import request from '@/utils/request'

// 查询等保备案统计相关信息
export function levelprotectStatistics() {
  return request({
    url: '/screen/statistics/index',
    method: 'get' 
  })
}

// 查询等保备案统计相关信息
export function levelprotectStatisticsType(query) {
  // console.log('query=',query)
  return request({
    url: '/screen/statistics/type',
    method: 'get',
    params: query
  })
}
 


// 等保备案一键搜类型
export function levelprotectSearchType(query) {
  let randomNumber = Math.floor(Math.random() * 100) + 1;
  return request({
    url: '/system/search/getIndexList',
    method: 'get',
    params: query
  })
}
 
// 等保备案一键搜
export function levelprotectSearchOneClick(query) {
  // console.log('query=',query)
  return request({
    url: '/system/search/oneClick',
    method: 'get',
    params:  query
  })
}