import request from '@/utils/request'

// 根据业务类型获取表格列表
export function getTableListByBiz(biz) {
  return request({
    url: '/system/dataUpdateReminder/getTableListByBiz',
    method: 'get',
    params: {
      biz
    }
  })
}

// 根据表信息获取字段元数据
export function getFieldMetadataByTable(data) {
  return request({
    url: '/system/dataUpdateReminder/getFieldMetadataByTable',
    method: 'get',
    params: data
  })
}

// 根据表信息获取字段元数据
export function getReminderRuleListByBiz(data) {
  return request({
    url: '/system/dataUpdateReminder/getReminderRuleListByBiz',
    method: 'get',
    params: data
  })
}
// 批量添加提醒规则
export function batchAddReminder(data) {
  return request({
    url: '/system/dataUpdateReminder/batchAdd',
    method: 'post',
    data
  })
}
