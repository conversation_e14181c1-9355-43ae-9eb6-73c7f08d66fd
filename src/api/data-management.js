import request from '@/utils/request'

// 获取基础数据台账统计 - 企业信息统计扩展
export function getDataStatistics() {
  return request({
    url: '/system/sysEnterprise/statExtend',
    method: 'get'
  })
}

// 获取数据更新提醒统计
export function getDataUpdateReminderStat() {
  return request({
    url: '/system/dataUpdateReminder/stat',
    method: 'get'
  })
}

// 获取全量数据变化情况
export function getDataChangeHistory(dateType = 'day') {
  return request({
    url: '/system/changeData/trend',
    method: 'get',
    params: { dateType }
  })
}

// 获取各派出所数据贡献情况
export function getPoliceStationContribution() {
  return request({
    url: '/system/changeData/police/contribute',
    method: 'get'
  })
}

// 搜索数据
export function searchData(params) {
  return request({
    url: '/data-management/search',
    method: 'get',
    params
  })
}

// 数据维护
export function getChangeData(params) {
  return request({
    url: '/system/changeData/list',
    method: 'get',
    params
  })
}
