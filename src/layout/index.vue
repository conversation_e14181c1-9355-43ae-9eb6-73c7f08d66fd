<template>
  <div :class="classObj" class="app-wrapper" :style="{'--current-color': theme}">
    <div v-if="device==='mobile'&&sidebar.opened" class="drawer-bg" @click="handleClickOutside"/>
    <sidebar v-if="showSidebar" class="sidebar-container"/>
    <div :class="{hasTagsView:needTagsView,sidebarHide:sidebar.hide}" class="main-container">
      <div :class="{'fixed-header':fixedHeader, 'bg-nav': bgNav}" id="tongzhou">
        <navbar @setLayout="setLayout"/>
        <tags-view v-if="needTagsView"/>
      </div>
      <app-main/>
      <settings ref="settingRef"/>
    </div>
  </div>
</template>

<script>
import { AppMain, Navbar, Settings, Sidebar, TagsView } from './components'
import ResizeMixin from './mixin/ResizeHandler'
import { mapState } from 'vuex'
import variables from '@/assets/styles/variables.scss'

export default {
  name: 'Layout',
  components: {
    AppMain,
    Navbar,
    Settings,
    Sidebar,
    TagsView
  },
  mixins: [ResizeMixin],
  computed: {
    ...mapState({
      theme: state => state.settings.theme,
      sideTheme: state => state.settings.sideTheme,
      sidebar: state => state.app.sidebar,
      device: state => state.app.device,
      needTagsView: state => state.settings.tagsView,
      fixedHeader: state => state.settings.fixedHeader,
      bgNav: state => state.settings.bgNav
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened || !this.showSidebar,
        openSidebar: this.sidebar.opened && this.showSidebar,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    },
    variables() {
      return variables
    },
    // 是否显示侧边栏
    showSidebar() {
      // 检查当前路由是否是系统路由
      const currentPath = this.$route.path
      const isSystemRoute = currentPath.startsWith('/system') || currentPath.startsWith('/monitor')
      // 只有在系统路由下才显示侧边栏
      return isSystemRoute && !this.sidebar.hide
    }
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    },
    setLayout() {
      this.$refs.settingRef.openSetting()
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/assets/styles/mixin.scss";
  @import "~@/assets/styles/variables.scss";

  .app-wrapper {
    @include clearfix;
    position: relative;
    height: 100%;
    width: 100%;
    overflow: hidden;
    &.mobile.openSidebar {
      position: fixed;
      top: 0;
    }
  }
.app-wrapper.openSidebar {
  justify-content: space-between;
  display: flex;
  .main-container.hasTagsView {
    flex-grow: 1 !important;
  }
}

  .drawer-bg {
    background: #000;
    opacity: 0.3;
    width: 100%;
    top: 0;
    height: 100%;
    position: absolute;
    z-index: 999;
  }

  .fixed-header {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 9;
    width: calc(100% - #{$base-sidebar-width});
    transition: width 0.28s;
  }

  .hideSidebar .fixed-header {
    width: calc(100% - 54px);
  }

  .sidebarHide .fixed-header {
    width: 100%;
  }

  .mobile .fixed-header {
    width: 100%;
  }
  
  #tongzhou {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100%;
  height:104px;
  z-index: 3005;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-color:rgba(0, 96, 185, 1);

  background-position:left center;
  background-repeat:no-repeat;
  background-attachment:scroll;
  background-size:cover;
  background-origin:border-box;
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  .navbar {
    background:rgba(0, 96, 185, 1);
    background-size: cover; /* 或者使用 contain */
    background-position: center;
    background-repeat: no-repeat;
    height: 70px;
    background-image: url("../assets/images/tz_nav.svg");
  }
}
</style>
