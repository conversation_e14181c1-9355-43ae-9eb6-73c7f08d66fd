const systemMenu = {
  namespaced: true,
  state: {
    // 是否处于系统菜单模式
    isSystemMenuMode: false,
    // 当前系统菜单类型 ('System' | 'Monitor')
    currentSystemMenu: null
  },
  mutations: {
    SET_SYSTEM_MENU_MODE: (state, mode) => {
      state.isSystemMenuMode = mode
    },
    SET_CURRENT_SYSTEM_MENU: (state, menuType) => {
      state.currentSystemMenu = menuType
    }
  },
  actions: {
    // 设置系统菜单模式
    setSystemMenuMode({ commit }, mode) {
      commit('SET_SYSTEM_MENU_MODE', mode)
    },
    // 设置当前系统菜单
    setCurrentSystemMenu({ commit }, menuType) {
      commit('SET_CURRENT_SYSTEM_MENU', menuType)
    },
    // 退出系统菜单模式
    exitSystemMenuMode({ commit }) {
      commit('SET_SYSTEM_MENU_MODE', false)
      commit('SET_CURRENT_SYSTEM_MENU', null)
    }
  },
  getters: {
    isSystemMenuMode: state => state.isSystemMenuMode,
    currentSystemMenu: state => state.currentSystemMenu
  }
}

export default systemMenu
