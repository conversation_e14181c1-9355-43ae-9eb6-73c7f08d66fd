/**
 * 双击复制指令
 * 使用方法：v-copy-on-dblclick
 * 
 * 功能：
 * 1. 双击元素时选中文本内容
 * 2. 自动复制到剪贴板
 * 3. 显示复制成功提示
 */

import { Message } from 'element-ui'

const copyOnDblclick = {
  bind(el, binding, vnode) {
    // 添加双击事件监听器
    el.addEventListener('dblclick', function(event) {
      // 阻止事件冒泡
      event.stopPropagation()
      
      try {
        // 获取要复制的文本内容
        let textToCopy = ''
        
        // 如果指令有值，使用指令值
        if (binding.value) {
          textToCopy = binding.value
        } else {
          // 否则使用元素的文本内容
          textToCopy = el.textContent || el.innerText || ''
        }
        
        // 去除首尾空白字符
        textToCopy = textToCopy.trim()
        
        if (!textToCopy) {
          Message.warning('没有可复制的内容')
          return
        }
        
        // 选中文本内容
        selectText(el)
        
        // 复制到剪贴板
        copyToClipboard(textToCopy).then(() => {
          Message.success('复制成功')
        }).catch((error) => {
          console.error('复制失败:', error)
          Message.error('复制失败，请手动复制')
        })
        
      } catch (error) {
        console.error('双击复制处理失败:', error)
        Message.error('复制失败')
      }
    })
    
    // 添加样式提示用户可以双击复制
    el.style.cursor = 'pointer'
    el.title = el.title || '双击复制'
    
    // 添加hover效果
    const originalBackground = el.style.backgroundColor
    
    el.addEventListener('mouseenter', function() {
      if (!originalBackground || originalBackground === 'transparent' || originalBackground === '') {
        el.style.backgroundColor = '#f5f7fa'
      }
    })
    
    el.addEventListener('mouseleave', function() {
      if (!originalBackground || originalBackground === 'transparent' || originalBackground === '') {
        el.style.backgroundColor = ''
      }
    })
  },
  
  unbind(el) {
    // 清理事件监听器
    el.removeEventListener('dblclick', arguments.callee)
    el.removeEventListener('mouseenter', arguments.callee)
    el.removeEventListener('mouseleave', arguments.callee)
  }
}

/**
 * 选中元素中的文本
 * @param {Element} element - 要选中文本的元素
 */
function selectText(element) {
  try {
    if (document.selection) {
      // IE
      const range = document.body.createTextRange()
      range.moveToElementText(element)
      range.select()
    } else if (window.getSelection) {
      // 其他浏览器
      const selection = window.getSelection()
      const range = document.createRange()
      range.selectNodeContents(element)
      selection.removeAllRanges()
      selection.addRange(range)
    }
  } catch (error) {
    console.warn('选中文本失败:', error)
  }
}

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 * @returns {Promise}
 */
function copyToClipboard(text) {
  return new Promise((resolve, reject) => {
    // 优先使用现代的 Clipboard API
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(text)
        .then(resolve)
        .catch(reject)
    } else {
      // 降级使用传统方法
      try {
        const textArea = document.createElement('textarea')
        textArea.value = text
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()
        
        const successful = document.execCommand('copy')
        document.body.removeChild(textArea)
        
        if (successful) {
          resolve()
        } else {
          reject(new Error('execCommand copy failed'))
        }
      } catch (error) {
        reject(error)
      }
    }
  })
}

export default copyOnDblclick
